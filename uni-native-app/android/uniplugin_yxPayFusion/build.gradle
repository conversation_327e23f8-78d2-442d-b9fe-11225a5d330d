plugins {
    id 'com.android.library'
}

android {
//    namespace 'com.yoshiclub.yxpayfusion'
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 21
        ndk {
            abiFilters 'x86', 'armeabi-v7a', 'x86_64'
        }
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
}
//导入aar需要的配置
//repositories {
//    flatDir {
//        dirs 'libs'
//    }
//}

dependencies {
    //必须添加的依赖
    compileOnly 'androidx.recyclerview:recyclerview:1.0.0'
    compileOnly 'androidx.legacy:legacy-support-v4:1.0.0'
    compileOnly 'androidx.appcompat:appcompat:1.0.0'
    compileOnly 'com.alibaba:fastjson:1.1.46.android'
    implementation 'cz.msebera.android:httpclient:4.4.1.1'
    compileOnly fileTree(include: ['uniapp-v8-release.aar'], dir: '../app/libs')
    compileOnly fileTree(include: ['mPaySDK.aar'], dir: '../app/libs')
}
//dependencies {
//
//    implementation 'androidx.appcompat:appcompat:1.6.1'
//    implementation 'com.google.android.material:material:1.10.0'
//    testImplementation 'junit:junit:4.13.2'
//    targetSdkVersion 'androidx.test.ext:junit:1.1.5'
//    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
//}
