package com.yoshiclub.yxpayfusion;
import java.math.BigDecimal;
import java.math.RoundingMode;
import com.mpay.sdk.MpayService;
import com.mpay.sdk.MpayPay;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.widget.Toast;
import com.alibaba.fastjson.JSONObject;

public class YxpayfusionActivity extends Activity {

  private static final String TAG = "YxpayfusionActivity";

  // For testing
  MpayService mpayService;

  String version = ""; // version
  String merchantId = ""; // merchantid
  String merchantTId = "001"; // merchant_tid
  String orderNum = ""; // ordernum
  String datetime = ""; // datetime
  String amt = ""; // amt
  String currency = ""; // currency
  String customizedData = ""; // customizeddata
  String returnUrl = ""; // returnurl, not null or empty
  String notifyUrl = ""; // notifyurl, not null or empty
  String locale = ""; // locale
  String storeId = ""; // storeid
  String cardNum = ""; // cardnum
  String extraField1 = ""; // extrafield1
  String extraField2 = ""; // extrafield2
  String extraField3 = ""; // extrafield3
  String salt = ""; // salt
  String hash = ""; // hash
  String payMethod = ""; // Ref. mPay paymethod
  String requestUrl = "";

  @Override
  protected void onCreate(Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);

    try {
      Intent intent = getIntent();
      String paymentOptionsString = intent.getStringExtra("paymentOptions");

      if (paymentOptionsString != null) {
        JSONObject paymentOptions = JSONObject.parseObject(paymentOptionsString);
        JSONObject params = paymentOptions.getJSONObject("params");
        Log.e(TAG, "paymentOptions--" + paymentOptions);
        Log.e(TAG, "params--" + params);

        // 使用传递的参数
        amt = params.getString("amt");
        Log.e(TAG, "amt--" + amt);
        currency = params.getString("currency");
        payMethod = params.getString("paymethod"); // 从外部参数获取的支付类型
        returnUrl = params.getString("extrafield1"); // 从外部参数获取的返回 URL
        requestUrl= paymentOptions.getString("url"); // 从外部参数获取的请求 URL
        notifyUrl = params.getString("notifyurl");
        version = params.getString("version");
        merchantId = params.getString("merchantid");
        merchantTId = params.getString("merchant_tid");
        orderNum = params.getString("ordernum");
        datetime = params.getString("datetime");
        customizedData = params.getString("customizeddata");
        locale = params.getString("locale");
        storeId = params.getString("storeid");
        salt = params.getString("salt");
        hash = params.getString("hash");
        extraField1= params.getString("extrafield1");

        // 获取原始金额
        String amtStr = params.getString("amt");
        if (amtStr != null && !amtStr.isEmpty()) {
          // 将金额保留两位小数
          amtStr = new BigDecimal(amtStr).setScale(2, RoundingMode.HALF_UP).toString();
        } else {
          amtStr = "0.00"; // 默认值或处理异常
        }
        amt = amtStr;

        Log.e(TAG, "Received payment options: " + paymentOptions);

        // 初始化 MpayService
        mpayService = new MpayService(requestUrl, MpayService.Env.TESTING);

        startPayment();
      }
    } catch (Exception e) {
      Log.e(TAG, "Error in onCreate:", e);
      Toast.makeText(this, "Error in onCreate: " + e.getMessage(), Toast.LENGTH_LONG).show();
    }
  }

  public void startPayment() {
    try {
      // 根据支付方式设置 extraField1
//      if ("58".equals(payMethod)) {
//        extraField1 = "mpaysdkdemo://com.mpay.MPay-SDK-demo"; // for Atome
//      } else if ("27".equals(payMethod)) {
//        extraField1 = "yoshiclub://pages/home/<USER>"; // for Octopus
//      }

      MpayPay.Request payRequest = new MpayPay.Request(
        version, merchantId, merchantTId, orderNum, datetime, amt, currency,
        payMethod, customizedData, returnUrl, notifyUrl, locale, storeId,
        cardNum, extraField1, extraField2, extraField3, salt, hash
      );

      Log.e(TAG, "Requesting payment with URL: " + requestUrl);
      Log.e(TAG, "Payment request details: " + payRequest.toString());

      mpayService.pay(this, payRequest, new MpayPay.Callback() {
        @Override
        public void onResponse(final MpayPay.Response response) {
          runOnUiThread(new Runnable() {
            @Override
            public void run() {
              Log.d(TAG, "response:" + response);
              if (response == null || response.getRspCode() == null || response.getRspCode().isEmpty()) {
                Log.d(TAG, "Invalid response, call API to confirm payment status");
                Toast.makeText(getApplicationContext(), "Invalid response, call API to confirm payment status", Toast.LENGTH_LONG).show();
              } else {
                Log.d(TAG, "on response, " + response.toString());
                Toast.makeText(getApplicationContext(), "on response, " + response.toString(), Toast.LENGTH_LONG).show();
                if ("100".equals(response.getRspCode())) {
                  Log.d(TAG, "Payment response code is 100, confirming via merchant API.");
                  confirmPaymentWithMerchantAPI(response);
                }
              }
            }
          });
        }
      });
    } catch (Exception e) {
      Log.e(TAG, "Error in startPayment:", e);
      Toast.makeText(this, "Error in startPayment: " + e.getMessage(), Toast.LENGTH_LONG).show();
    }
  }

  private void confirmPaymentWithMerchantAPI(MpayPay.Response response) {
    // 在这里调用你的商户 API 来确认支付结果
    // 使用 response 对象中的相关信息来构建请求
    Log.d(TAG, "Confirming payment with merchant API for response: " + response.toString());
    // 你需要在这里实现具体的 API 调用逻辑
  }
}
