package com.yoshiclub.yxpayfusion;

import android.app.Activity;
import android.content.Intent;
import android.util.Log;

import com.alibaba.fastjson.JSONObject;

import io.dcloud.feature.uniapp.annotation.UniJSMethod;
import io.dcloud.feature.uniapp.bridge.UniJSCallback;
import io.dcloud.feature.uniapp.common.UniModule;

public class PayModule extends UniModule {

  String TAG = "processOctopusPayment";
  public static int REQUEST_CODE = 1000;

  // 运行在 UI 线程
  @UniJSMethod(uiThread = true)
  public void processOctopusPayment(JSONObject options, UniJSCallback callback) {
    try {
      Log.e(TAG, "processOctopusPayment--" + options);

      JSONObject data = new JSONObject();
      data.put("code", "success");

      if (mUniSDKInstance == null || mUniSDKInstance.getContext() == null) {
        throw new IllegalStateException("mUniSDKInstance or context is null");
      }

      if (!(mUniSDKInstance.getContext() instanceof Activity)) {
        throw new IllegalStateException("Context is not an instance of Activity");
      }

      Intent intent = new Intent(mUniSDKInstance.getContext(), YxpayfusionActivity.class);
      intent.putExtra("paymentOptions", options.toJSONString()); // 将参数传递给原生 Activity
      intent.putExtra("callbackData", data.toJSONString()); // 将data也传递给原生 Activity
      ((Activity) mUniSDKInstance.getContext()).startActivityForResult(intent, REQUEST_CODE);

      callback.invoke(data);
      // callback.invokeAndKeepAlive(data);
    } catch (Exception e) {
      Log.e(TAG, "Invoker processOctopusPayment exception:", e);
      if (callback != null) {
        JSONObject errorData = new JSONObject();
        errorData.put("code", "error");
        errorData.put("message", e.getMessage());
        callback.invoke(errorData);
      }
    }
  }
}
