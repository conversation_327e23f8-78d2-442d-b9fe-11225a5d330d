// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
  repositories {
    google()
    mavenCentral()
    maven { url 'https://maven.aliyun.com/repository/google' }
    maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
    maven { url 'https://maven.aliyun.com/repository/public' }
    maven { url 'https://maven.google.com' }
  }
  dependencies {
//    classpath 'com.android.tools.build:gradle:7.2.0'
//    classpath 'com.google.gms:google-services:4.3.8'
    classpath 'com.android.tools.build:gradle:4.1.1'
//    classpath 'com.google.gms:google-services:4.3.8'
//    classpath 'com.google.gms:google-services:4.2.0'
    // NOTE: Do not place your application dependencies here; they belong
    // in the individual module build.gradle files
  }
}

allprojects {
  repositories {
    google()
    mavenCentral()
    maven { url 'https://maven.aliyun.com/repository/google' }
    maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
    maven { url 'https://maven.aliyun.com/repository/public' }
//    maven {
//      url 'http://mvn.gt.igexin.com/nexus/content/repositories/releases/'
//    }
//    maven {
//      url "https://mvn.getui.com/nexus/content/repositories/releases/"
//    }
    maven { url 'https://maven.google.com' }
  }
}

task clean(type: Delete) {
  delete rootProject.buildDir
}
