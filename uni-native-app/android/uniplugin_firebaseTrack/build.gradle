plugins {
    id 'com.android.library'
}

android {
//    namespace 'com.my.uniplugin_firebasetrack'
  compileSdkVersion 34

  defaultConfig {
    minSdkVersion 21
    ndk {
      abiFilters 'x86', 'armeabi-v7a', 'x86_64'
    }
    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    consumerProguardFiles "consumer-rules.pro"

  }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
}

dependencies {
  compileOnly 'androidx.recyclerview:recyclerview:1.0.0'
  compileOnly 'androidx.legacy:legacy-support-v4:1.0.0'
  compileOnly 'androidx.appcompat:appcompat:1.0.0'
  compileOnly 'com.alibaba:fastjson:1.1.46.android'
  implementation 'cz.msebera.android:httpclient:*******'
  compileOnly fileTree(include: ['uniapp-v8-release.aar'], dir: '../app/libs')
  implementation('com.google.firebase:firebase-analytics:21.3.0') {
    exclude group: 'com.google.errorprone', module: 'error_prone_annotations'
  }
}
