package com.yoshiclub.firebase;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;

import com.alibaba.fastjson.JSONObject;
import com.google.firebase.FirebaseApp;
import com.google.firebase.analytics.FirebaseAnalytics;
import io.dcloud.feature.uniapp.annotation.UniJSMethod;
import io.dcloud.feature.uniapp.bridge.UniJSCallback;
import io.dcloud.feature.uniapp.common.UniModule;

public class Firebasetrack extends UniModule {

  private static FirebaseAnalytics mFirebaseAnalytics;
  private static final String TAG = "Firebasetrack";

  // 初始化 Firebase Analytics
  @UniJSMethod
  public static void initFirebase(Context context, UniJSCallback callback) {
    if (mFirebaseAnalytics == null) {
      try {
        FirebaseApp.initializeApp(context);  // 初始化 Firebase
        mFirebaseAnalytics = FirebaseAnalytics.getInstance(context);
        if (callback != null) {
          callback.invoke("success");
        }
      } catch (Exception e) {
        Log.e(TAG, "Error initializing Firebase: " + e.getMessage());
        if (callback != null) {
          callback.invoke("Error initializing Firebase");
        }
      }
    } else {
      if (callback != null) {
        callback.invoke("Firebase Already Initialized");
      }
    }
  }

  // 记录事件
  public static void logEvent(String eventName, String paramName, String paramValue) {
    if (mFirebaseAnalytics != null) {
      if (eventName == null || eventName.isEmpty() || paramName == null || paramName.isEmpty() || paramValue == null || paramValue.isEmpty()) {
        Log.e(TAG, "Invalid event parameters: eventName=" + eventName + ", paramName=" + paramName + ", paramValue=" + paramValue);
        return;
      }
      Bundle bundle = new Bundle();
      bundle.putString(paramName, paramValue);
      mFirebaseAnalytics.logEvent(eventName, bundle);
    }
  }

  // UniApp 调用的埋点方法
  @UniJSMethod(uiThread = true)
  public void trackEvent(JSONObject options, UniJSCallback callback) {
    try {
      String eventName = options.getString("eventName");
      String paramName = options.getString("paramName");
      String paramValue = options.getString("paramValue");
      logEvent(eventName, paramName, paramValue);
      if (callback != null) {
        callback.invoke("success");
      }
    } catch (Exception e) {
      Log.e(TAG, "Error tracking event: " + e.getMessage());
      if (callback != null) {
        callback.invoke("Error tracking event");
      }
    }
  }
}
