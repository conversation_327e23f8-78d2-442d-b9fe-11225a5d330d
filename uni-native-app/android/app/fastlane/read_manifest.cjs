const fs = require('fs')
const path = require('path')

// 获取项目根目录的绝对路径
const rootPath = path.resolve(__dirname, '../../../../')
const manifestPath = path.join(rootPath, 'dist/build/app/manifest.json')
const manifestConfigPath = path.join(rootPath, 'manifest.config.ts')

try {
  // 读取 manifest.json 文件
  const manifestContent = fs.readFileSync(manifestPath, 'utf8')
  const manifest = JSON.parse(manifestContent)

  // 读取 manifest.config.ts 文件
  const manifestConfigContent = fs.readFileSync(manifestConfigPath, 'utf8')
  const dcloudAppkeyMatch = manifestConfigContent.match(/dcloudAppkey:\s*['"]([^'"]+)['"]/)
  const dcloudAppkey = dcloudAppkeyMatch ? dcloudAppkeyMatch[1] : 'b2444fe6327461971d4c0a490e3ffe0e'

  // 提取版本信息
  const versionName = manifest.version?.name || '1.0.0'
  const versionCode = manifest.version?.code || '100'

  // 提取应用名称
  const appName = manifest.name || '超级门店端APP'

  // 提取环境变量
  const env = process.env.NODE_ENV || 'prod'

  // 提取应用ID
  const appId = manifest.id || '__UNI__2704FCB'

  // 提取密钥配置
  const keystoreConfig = {
    // storeFile: 'haidilao_shop.keystore',
    // storePassword: 'haidilao123',
    // keyAlias: 'haidilao_shop',
    // keyPassword: 'haidilao123',
    storeFile: 'haidilao_shop.keystore',
    storePassword: '4FnFGHOj',
    keyAlias: '__uni__2704fcb',
    keyPassword: '4FnFGHOj',
  }

  // 提取包名配置
  const packageConfig = {
    packageName: 'com.haidilao.shop',
  }

  // 合并配置
  const config = {
    versionName,
    versionCode,
    appName,
    env,
    appId,
    appid: appId,
    packageName: packageConfig.packageName,
    keystore: keystoreConfig,
    dcloudAppkey,
  }

  // 支持命令行参数覆盖配置
  const args = process.argv.slice(2)
  args.forEach((arg) => {
    const [key, value] = arg.split('=')
    if (key && value) {
      if (key.startsWith('keystore.')) {
        const keystoreKey = key.split('.')[1]
        config.keystore[keystoreKey] = value
      } else if (key.startsWith('package.')) {
        const packageKey = key.split('.')[1]
        config.package[packageKey] = value
      } else {
        config[key] = value
      }
    }
  })

  // 处理图标
  if (manifest.plus?.distribute?.icons) {
    const androidIcons = manifest.plus.distribute.icons.android || {}
    const dpiMap = {
      hdpi: 'drawable-hdpi',
      xhdpi: 'drawable-xhdpi',
      xxhdpi: 'drawable-xxhdpi',
      xxxhdpi: 'drawable-xxxhdpi',
    }

    Object.entries(androidIcons).forEach(([dpi, srcPath]) => {
      if (dpiMap[dpi]) {
        const destPath = path.join(
          rootPath,
          `uni-native-app/android/app/src/main/res/${dpiMap[dpi]}/icon.png`,
        )
        try {
          fs.copyFileSync(path.join(rootPath, srcPath), destPath)
        } catch (e) {
          console.warn(`拷贝 ${srcPath} 到 ${destPath} 失败:`, e.message)
        }
      }
    })
  }

  // 输出配置信息 - 确保字段名与 Fastfile 中的一致
  console.log(
    JSON.stringify({
      versionName: config.versionName,
      versionCode: config.versionCode,
      appName: config.appName,
      env: config.env,
      appId: config.appId,
      appid: config.appid,
      packageName: config.packageName,
      keystore: config.keystore,
      dcloudAppkey: config.dcloudAppkey,
    }),
  )
} catch (error) {
  console.error('Error reading manifest:', error.message)
  process.exit(1)
}
