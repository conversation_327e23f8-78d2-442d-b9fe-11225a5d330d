<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="00: default_platform" time="0.002014">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="01: export NVM_DIR=&quot;$HOME/.nvm&quot; &amp;&amp;       [ -s &quot;$NVM_DIR/nvm.sh&quot; ] &amp;&amp; . &quot;$NVM_DIR/nvm.sh&quot; &amp;&amp;       nvm use 20.12.0 &amp;&amp;       cd /Users/<USER>/Desktop/project/uni-super-shop-admin &amp;&amp; pnpm run build:app -- --mode uat" time="11.456138">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="02: export NVM_DIR=&quot;$HOME/.nvm&quot; &amp;&amp;       [ -s &quot;$NVM_DIR/nvm.sh&quot; ] &amp;&amp; . &quot;$NVM_DIR/nvm.sh&quot; &amp;&amp;       nvm use 20.12.0 &amp;&amp;       node /Users/<USER>/Desktop/project/uni-super-shop-admin/uni-native-app/android/app/fastlane/read_manifest.cjs " time="1.706217">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="03: ls -la /Users/<USER>/Desktop/project/uni-super-shop-admin/dist/build/app/" time="0.025304">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="04: mkdir -p /Users/<USER>/Desktop/project/uni-super-shop-admin/uni-native-app/android/app/src/main/assets/apps/__UNI__2704FCB/www" time="0.015222">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="05: ls -la /Users/<USER>/Desktop/project/uni-super-shop-admin/uni-native-app/android/app/src/main/assets/apps/__UNI__2704FCB/www" time="0.024404">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="06: rm -rf /Users/<USER>/Desktop/project/uni-super-shop-admin/uni-native-app/android/app/src/main/assets/apps/__UNI__2704FCB/www/*" time="0.046418">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="07: cp -rv /Users/<USER>/Desktop/project/uni-super-shop-admin/dist/build/app/* /Users/<USER>/Desktop/project/uni-super-shop-admin/uni-native-app/android/app/src/main/assets/apps/__UNI__2704FCB/www/" time="0.122433">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="08: ls -la /Users/<USER>/Desktop/project/uni-super-shop-admin/uni-native-app/android/app/src/main/assets/apps/__UNI__2704FCB/www" time="0.022034">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="09: clean assembleUatRelease" time="47.516089">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="10: pgyer" time="42.198117">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="11: cd /Users/<USER>/Desktop/project/uni-super-shop-admin &amp;&amp; node -e &quot;import(&apos;./scripts/dingding.mjs&apos;).then(dingding =&gt; dingding.noticeDingDingSuceess({
      appName: &apos;海底捞超级门店端 Android 客户端 UAT&apos;,
      commitMessage: &apos;UAT环境测试版本 - AppKey: edb6d7988e02446e08871b63d22e9bf4&apos;,
      commitUser: &apos;未知&apos;,
      env: &apos;uat&apos;,
      version: &apos;1.0.0&apos;,
      versionCode: &apos;100&apos;,
      publishPath: &apos;蒲公英测试平台&apos;,
      isExtApp: false
    }));&quot;" time="0.51295">
        
      </testcase>
    
  </testsuite>
</testsuites>
