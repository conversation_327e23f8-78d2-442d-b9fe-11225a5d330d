default_platform(:android)

platform :android do
  desc "部署新版本到 Google Play"
  lane :deploy do |options|
    # 获取项目根目录的绝对路径
    root_path = File.expand_path("../../../../../", __FILE__)
    android_path = "#{root_path}/uni-native-app/android"
    
    # 读取 manifest 配置，支持命令行参数覆盖
    sh("export NVM_DIR=\"$HOME/.nvm\" && \
      [ -s \"$NVM_DIR/nvm.sh\" ] && . \"$NVM_DIR/nvm.sh\" && \
      nvm use 20.12.0 && \
      node #{android_path}/app/fastlane/read_manifest.cjs #{options.map { |k,v| "#{k}=#{v}" }.join(' ')}")
    manifest_config = JSON.parse(`node #{android_path}/app/fastlane/read_manifest.cjs #{options.map { |k,v| "#{k}=#{v}" }.join(' ')}`)
    version_name = manifest_config['versionName']
    version_code = manifest_config['versionCode']
    app_name = manifest_config['appName']
    env = manifest_config['env']
    app_id = manifest_config['appId']
    package_name = manifest_config['packageName']
    keystore = manifest_config['keystore']
    dcloud_appid = manifest_config['appid']
    dcloud_appkey = manifest_config['dcloudAppkey']

    # 设置代理（同时设置 HTTP 和 HTTPS）
    # ENV["HTTP_PROXY"] = "http://192.168.33.13:3313"   # HTTP 代理
    # ENV["HTTPS_PROXY"] = "http://192.168.33.13:3313"  # HTTPS 代理

    # 设置 Clash 代理
    # clash_proxy = "http://127.0.0.1:7890"
    # ENV["HTTP_PROXY"] = clash_proxy
    # ENV["HTTPS_PROXY"] = clash_proxy
    # puts "已设置 Clash 代理为: #{clash_proxy}"

    # 验证代理连接
    begin
      sh("curl -I https://play.google.com")
      puts "代理连接测试成功！"
    rescue => e
      puts "警告: 代理连接测试失败，请确认 Clash 是否正常运行"
      puts "错误信息: #{e.message}"
    end

    ENV["FASTLANE_SKIP_UPDATE_CHECK"] = "1"  # 跳过更新检查
    ENV["SUPPLY_UPLOAD_MAX_RETRIES"] = "5"   # 设置最大重试次数
    ENV["SUPPLY_TIMEOUT"] = "600"            # 设置超时时间（秒）

    # 1. 先执行 HBuilderX 打包 app-plus，使用正确的参数
    sh("export NVM_DIR=\"$HOME/.nvm\" && \
      [ -s \"$NVM_DIR/nvm.sh\" ] && . \"$NVM_DIR/nvm.sh\" && \
      nvm use 20.12.0 && \
      cd #{root_path} && pnpm run build:app -- --mode #{env}")

    # 打印源目录内容
    puts "Source directory contents:"
    sh("ls -la #{root_path}/dist/build/app/")

    # 2. 确保目标目录存在
    target_dir = "#{android_path}/app/src/main/assets/apps/#{app_id}/www"
    sh("mkdir -p #{target_dir}")  # 创建目录（如果不存在）

    # 打印目标目录内容（复制前）
    puts "Target directory contents before copy:"
    sh("ls -la #{target_dir}")

    # 3. 复制打包后的文件到 www 目录
    sh("rm -rf #{target_dir}/*")  # 清空原有的 www 目录
    sh("cp -rv #{root_path}/dist/build/app/* #{target_dir}/")  # 复制新的文件

    # 打印目标目录内容（复制后）
    puts "Target directory contents after copy:"
    sh("ls -la #{target_dir}")

    # 4. 执行 Android 打包
    gradle(
      task: "clean bundleProdRelease",
      project_dir: android_path,
      print_command: true,
      properties: {
        "android.injected.build.model.only.versioned" => "3",
        "versionName" => version_name,
        "versionCode" => version_code,
        "packageName" => package_name,  # 传递包名参数
        "dcloud_appid" => app_id,  # 添加 dcloud_appid
        "dcloud_appkey" => dcloud_appkey,  # 添加 dcloud_appkey
        "KEYSTORE_FILE" => keystore['file'],
        "KEYSTORE_PASSWORD" => keystore['password'],
        "KEY_ALIAS" => keystore['alias'],
        "KEY_PASSWORD" => keystore['keyPassword']
      }
    )

    # 打印当前目录和文件信息
    puts "Current directory: #{Dir.pwd}"
    puts "Looking for AAB in: #{android_path}/app/build/outputs/bundle/prodRelease/"

    # 列出目录内容
    puts "Directory contents:"
    Dir.glob("#{android_path}/app/build/outputs/bundle/prodRelease/*").each do |file|
      puts file
    end

    aab_path = "#{android_path}/app/build/outputs/bundle/prodRelease/app-prod-release.aab"

    puts "Full AAB path: #{aab_path}"
    puts "File exists?: #{File.exist?(aab_path)}"

    # 检查 service_account.json 是否存在
    json_key_path = "#{android_path}/app/service_account.json"
    puts "JSON key path: #{json_key_path}"
    puts "JSON key exists?: #{File.exist?(json_key_path)}"

    # 添加重试机制
    begin
      retries ||= 0
      puts "Attempting upload (attempt #{retries + 1})"

      upload_to_play_store(
        track: 'internal',
        aab: aab_path,
        json_key: json_key_path,
        package_name: package_name,  # 使用动态包名
        skip_upload_metadata: true,
        skip_upload_images: true,
        skip_upload_screenshots: true,
        release_status: "completed",
        verbose: true,
        timeout: 600
      )

      # 发送钉钉通知（成功）
      sh("cd #{root_path} && node -e \"import('./scripts/dingding.mjs').then(dingding => dingding.noticeDingDingSuceess({
        appName: '#{app_name} Android 客户端',
        commitMessage: '#{ENV['commitMessage'] || '版本更新'}',
        commitUser: '#{ENV['commitUser'] || '未知'}',
        env: '#{env}',
        version: '#{version_name}',
        versionCode: '#{version_code}',
        publishPath: 'Google Play 内部测试通道',
        isExtApp: false
      }));\"")

    rescue => e
      puts "Error: #{e.message}"

      # 发送钉钉通知（失败）
      sh("cd #{root_path} && node -e \"import('./scripts/dingding.mjs').then(dingding => dingding.noticeDingDingFail({
        appName: '#{app_name} Android 客户端'
      }));\"")

      retry if (retries += 1) < 5
      raise "Failed after 5 attempts: #{e.message}"
    end
  end

  desc "部署 UAT 版本到蒲公英"
  lane :deploy_uat do |options|
    # 获取项目根目录的绝对路径
    root_path = File.expand_path("../../../../../", __FILE__)
    android_path = "#{root_path}/uni-native-app/android"

    # 1. 先执行 HBuilderX 打包 app-plus，使用 UAT 环境参数
    sh("export NVM_DIR=\"$HOME/.nvm\" && \
      [ -s \"$NVM_DIR/nvm.sh\" ] && . \"$NVM_DIR/nvm.sh\" && \
      nvm use 20.12.0 && \
      cd #{root_path} && pnpm run build:app -- --mode uat")

    # 2. 再读取 manifest 配置
    sh("export NVM_DIR=\"$HOME/.nvm\" && \
      [ -s \"$NVM_DIR/nvm.sh\" ] && . \"$NVM_DIR/nvm.sh\" && \
      nvm use 20.12.0 && \
      node #{android_path}/app/fastlane/read_manifest.cjs #{options.map { |k,v| "#{k}=#{v}" }.join(' ')}")
    manifest_config = JSON.parse(`node #{android_path}/app/fastlane/read_manifest.cjs #{options.map { |k,v| "#{k}=#{v}" }.join(' ')}`)
    version_name = manifest_config['versionName']
    version_code = manifest_config['versionCode']
    app_name = manifest_config['appName']
    env = manifest_config['env']
    app_id = manifest_config['appid']
    package_name = manifest_config['packageName'] || "com.haidilao.shop"  # 添加默认值
    keystore = manifest_config['keystore']
    dcloud_appid = manifest_config['appid']
    dcloud_appkey = manifest_config['dcloudAppkey']

    # 打印源目录内容
    puts "Source directory contents:"
    sh("ls -la #{root_path}/dist/build/app/")

    # 2. 确保目标目录存在
    target_dir = "#{android_path}/app/src/main/assets/apps/#{app_id}/www"
    sh("mkdir -p #{target_dir}")  # 创建目录（如果不存在）

    # 打印目标目录内容（复制前）
    puts "Target directory contents before copy:"
    sh("ls -la #{target_dir}")

    # 3. 复制打包后的文件到 www 目录
    sh("rm -rf #{target_dir}/*")  # 清空原有的 www 目录
    sh("cp -rv #{root_path}/dist/build/app/* #{target_dir}/")  # 复制新的文件

    # 打印目标目录内容（复制后）
    puts "Target directory contents after copy:"
    sh("ls -la #{target_dir}")

    # 4. 执行 Android APK 打包 - 明确指定使用 UAT flavor
    gradle(
      task: "clean assembleUatRelease",  # 修改为明确使用 UAT flavor
      project_dir: android_path,
      print_command: true,
      properties: {
        "android.injected.build.model.only.versioned" => "3",
        "versionName" => version_name,
        "versionCode" => version_code,
        "packageName" => package_name,  # 传递包名参数
        "dcloud_appid" => app_id,  # 添加 dcloud_appid
        "dcloud_appkey" => dcloud_appkey,  # 添加 dcloud_appkey
        "KEYSTORE_FILE" => keystore['file'],
        "KEYSTORE_PASSWORD" => keystore['password'],
        "KEY_ALIAS" => keystore['alias'],
        "KEY_PASSWORD" => keystore['keyPassword']
      }
    )

    # 获取最新的 APK 文件 - 更新路径以匹配 UAT flavor
    apk_dir = "#{android_path}/app/build/outputs/apk/uat/release"
    apk_path = Dir.glob("#{apk_dir}/*.apk").sort_by { |f| File.mtime(f) }.last

    puts "Full APK path: #{apk_path}"
    puts "File exists?: #{File.exist?(apk_path)}"

    # 打印使用的 appkey
    puts "用于 UAT 构建的 AppKey: edb6d7988e02446e08871b63d22e9bf4"

    # 5. 上传到蒲公英
    pgyer(
      api_key: "195588832957e32daeb11a000d79d955",  # 替换为你的蒲公英 API Key
      apk: apk_path,
      update_description: "UAT 环境测试版本 - AppKey: edb6d7988e02446e08871b63d22e9bf4",  # 在描述中包含 appkey
      password: "123456",  # 设置安装密码，可选
      install_type: "2",   # 安装类型，2 表示密码安装
    )

    # 发送钉钉通知（成功）
    sh("cd #{root_path} && node -e \"import('./scripts/dingding.mjs').then(dingding => dingding.noticeDingDingSuceess({
      appName: '#{app_name} Android 客户端 UAT',
      commitMessage: '#{ENV['commitMessage'] || 'UAT环境测试版本 - AppKey: edb6d7988e02446e08871b63d22e9bf4'}',
      commitUser: '#{ENV['commitUser'] || '未知'}',
      env: 'uat',
      version: '#{version_name}',
      versionCode: '#{version_code}',
      publishPath: '蒲公英测试平台',
      isExtApp: false
    }));\"")

  rescue => e
    puts "Error uploading to PGYER: #{e.message}"

    # 发送钉钉通知（失败）
    sh("cd #{root_path} && node -e \"import('./scripts/dingding.mjs').then(dingding => dingding.noticeDingDingFail({
      appName: '#{app_name} Android 客户端'
    }));\"")

    raise e
  end

  desc "测试不同的 appkey 配置"
  lane :test_appkey do |options|
    # 获取项目根目录的绝对路径
    root_path = File.expand_path("../../../../../", __FILE__)
    android_path = "#{root_path}/uni-native-app/android"

    # 动态替换 build.gradle 中的 appkey
    appkey = options[:appkey] || "9bfae817b9688613240a510b2a827c5c"  # 默认使用 order-app 的 appkey

    # 修改 build.gradle 文件中的 appkey
    gradle_file = "#{android_path}/app/build.gradle"

    # 读取原 build.gradle 文件
    build_gradle_content = File.read(gradle_file)

    # 替换 UAT flavor 的 appkey
    updated_content = build_gradle_content.gsub(/(uat\s*\{.*?dcloud_appkey:\s*")[^"]*(".*?\})/m, "\\1#{appkey}\\2")

    # 写回文件
    File.write(gradle_file, updated_content)

    puts "已设置 UAT appkey 为: #{appkey}"

    # 1. 先执行 HBuilderX 打包 app-plus，使用 UAT 环境参数
    sh("export NVM_DIR=\"$HOME/.nvm\" && \
      [ -s \"$NVM_DIR/nvm.sh\" ] && . \"$NVM_DIR/nvm.sh\" && \
      nvm use 20.12.0 && \
      cd #{root_path} && pnpm run build:app -- --mode uat")

    # 准备打包目录
    target_dir = "#{android_path}/app/src/main/assets/apps/__UNI__2704FCB/www"
    sh("mkdir -p #{target_dir}")  # 创建目录（如果不存在）
    sh("rm -rf #{target_dir}/*")  # 清空原有的 www 目录
    sh("cp -rv #{root_path}/dist/build/app/* #{target_dir}/")  # 复制新的文件

    # 4. 执行 Android APK 打包 - 明确指定使用 UAT flavor
    gradle(
      task: "clean assembleUatRelease",  # 修改为明确使用 UAT flavor
      project_dir: android_path,
      print_command: true,
      properties: {
        "android.injected.build.model.only.versioned" => "3",
      }
    )

    # 获取最新的 APK 文件 - 更新路径以匹配 UAT flavor
    apk_dir = "#{android_path}/app/build/outputs/apk/uat/release"
    apk_path = Dir.glob("#{apk_dir}/*.apk").sort_by { |f| File.mtime(f) }.last

    puts "Full APK path: #{apk_path}"
    puts "File exists?: #{File.exist?(apk_path)}"

    # 5. 上传到蒲公英
    pgyer(
      api_key: "195588832957e32daeb11a000d79d955",  # 替换为你的蒲公英 API Key
      apk: apk_path,
      update_description: "UAT 环境测试版本 - AppKey: #{appkey}",
      password: "123456",
      install_type: "2",
    )

    puts "已使用 AppKey: #{appkey} 上传测试版本，请在蒲公英平台查看和测试"
  end
end