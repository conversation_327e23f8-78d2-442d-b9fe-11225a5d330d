<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
     package="com.haidilao.shop"
     xmlns:tools="http://schemas.android.com/tools"
     >
    <!--按下面方式配置需要自定义添加的权限-->
    <!-- 使用网络权限 -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <!-- 读写SD卡权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- 读取设备标识权限 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- 拍照权限 -->
    <uses-permission android:name="android.permission.CAMERA"/>
    <!-- 定位权限 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
<!--    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"/>-->
    <!-- <uses-permission android:name="android.permission.ACCESS_MOCK_LOCATION"/> -->
    <!-- 联系人权限 -->
<!--    <uses-permission android:name="android.permission.READ_CONTACTS" />-->
<!--    <uses-permission android:name="android.permission.WRITE_CONTACTS" />-->
    <!-- 蓝牙权限 -->
<!--    <uses-permission android:name="android.permission.BLUETOOTH"/>-->
<!--    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>-->
    <!-- 短信权限 -->
    <!-- <uses-permission android:name="android.permission.RECEIVE_SMS"/> -->
    <!-- <uses-permission android:name="android.permission.SEND_SMS"/>
    <uses-permission android:name="android.permission.WRITE_SMS"/>
    <uses-permission android:name="android.permission.READ_SMS"/> -->
    <!-- 安装应用权限 -->
    <!-- <uses-permission android:name="android.permission.INSTALL_PACKAGES"/>
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> -->
    <!-- 快捷方式权限 -->
<!--    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT"/>-->
<!--    <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT"/>-->
<!--    <uses-permission android:name="com.miui.securitycenter.permission.AppPermissionsEditor" />-->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
  <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" tools:node="remove" />
  <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" tools:node="remove" />
  <!-- <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
  <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> -->
<!--  <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>-->
  <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" tools:node="remove" />
  <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" tools:node="remove" />

<!--去除谷歌广告id权限-->
<uses-permission android:name ="com.google.android.gms.permission.AD_ID" tools:node="replace" />
  <uses-permission android:name="android.permission.CALL_PHONE"/>


<!-- 极光推送相关权限 -->
<uses-permission android:name="${applicationId}.permission.JPUSH_MESSAGE" />
<uses-permission android:name="android.permission.RECEIVE_USER_PRESENT" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_SETTINGS"
  tools:ignore="ProtectedPermissions" />
<uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
  tools:ignore="ProtectedPermissions" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

<!-- 华为角标权限 -->
<uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />

<!-- 基础功能权限 -->
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

<!-- 定位相关权限 -->
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />

<!-- 网络状态权限 -->
<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />

<!-- 应用信息权限 -->
<uses-permission android:name="android.permission.GET_TASKS" />
<uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
  tools:ignore="QueryAllPackagesPermission" />

<!-- 厂商推送通道权限 -->
<uses-permission android:name="${applicationId}.permission.MIPUSH_RECEIVE" />
<uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />
<uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE" />
  <application
        android:allowBackup="true"
        android:allowClearUserData="true"
        android:icon="@drawable/icon"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:supportsRtl="true"
      tools:replace="android:allowBackup"
      >
        <activity
            android:name="io.dcloud.PandoraEntry"
            android:configChanges="orientation|keyboardHidden|keyboard|navigation"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:hardwareAccelerated="true"
            android:theme="@style/TranslucentTheme"
            android:screenOrientation="user"
            android:exported="true"
            android:windowSoftInputMode="adjustResize"
            >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="io.dcloud.PandoraEntryActivity"
            android:launchMode="singleTask"
            android:configChanges="orientation|keyboardHidden|screenSize|mcc|mnc|fontScale|keyboard|smallestScreenSize|screenLayout|screenSize|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="user"
            android:theme="@style/DCloudTheme"
            android:exported="true"
            android:windowSoftInputMode="adjustResize">
          <intent-filter android:autoVerify="true">
            <category android:name="android.intent.category.DEFAULT" />
            <category android:name="android.intent.category.BROWSABLE" />
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="yoshiclub" />
          </intent-filter>
            <!-- 处理主域名 -->
          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW" />
              <category android:name="android.intent.category.DEFAULT" />
              <category android:name="android.intent.category.BROWSABLE" />
              <data
                  android:scheme="https"
                  android:host="uatodr.yoshinoya.com.hk"
                   />
          </intent-filter>
          <!-- 处理主域名 -->
          <intent-filter android:autoVerify="true">
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.DEFAULT" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data
              android:scheme="https"
              android:host="eordering.yoshinoya.com.hk"
        />
          </intent-filter>
          <!-- 处理主域名 -->
          <intent-filter android:autoVerify="true">
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.DEFAULT" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data
              android:scheme="https"
              android:host="pilotodr.yoshinoya.com.hk"
              />
          </intent-filter>
          <!-- 处理主域名 -->
          <intent-filter android:autoVerify="true">
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.DEFAULT" />
            <category android:name="android.intent.category.BROWSABLE" />
            <data
              android:scheme="https"
              android:host="onlineordering.yoshinoya.com.hk"
              />
          </intent-filter>
        </activity>


    <meta-data android:name="DCLOUD_PUSH_PRIVACY" android:value="false"/>
    <meta-data
        tools:node="replace"
        android:name="heytap.msp-push.VERSION"
        android:value="3.0.0"
        />

        <meta-data
            android:name="dcloud_appkey"
            android:value="b2444fe6327461971d4c0a490e3ffe0e" />
    <meta-data
      android:name="JPUSH_APPKEY"
      android:value="${JPUSH_APPKEY}" />
    <meta-data
      android:name="JPUSH_CHANNEL"
      android:value="${JPUSH_CHANNEL}" />

    <meta-data
      android:name="XIAOMI_APPKEY"
      android:value="${XIAOMI_APPKEY}" />
    <meta-data
      android:name="XIAOMI_APPID"
      android:value="${XIAOMI_APPID}" />

    <meta-data
      android:name="OPPO_APPKEY"
      android:value="${OPPO_APPKEY}" />
    <meta-data
      android:name="OPPO_APPID"
      android:value="${OPPO_APPID}" />
    <meta-data
      android:name="OPPO_APPSECRET"
      android:value="${OPPO_APPSECRET}" />

    <meta-data
      android:name="com.vivo.push.api_key"
      android:value="${VIVO_APPKEY}" />
    <meta-data
      android:name="com.vivo.push.app_id"
      android:value="${VIVO_APPID}" />

    <meta-data
      android:name="MEIZU_APPKEY"
      android:value="${MEIZU_APPKEY}" />
    <meta-data
      android:name="MEIZU_APPID"
      android:value="${MEIZU_APPID}" />
    <meta-data
      android:name="com.hihonor.push.sdk_version"
      android:value="7.0.61.302"
      tools:replace="android:value" />
    <meta-data
      android:name="sdkVersion"
      android:value="3100"
      tools:replace="android:value" />

    </application>

</manifest>
