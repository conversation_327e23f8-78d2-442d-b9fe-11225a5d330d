apply plugin: 'com.android.application'

android {
  compileSdkVersion 30
  buildToolsVersion '30.0.3'
  aaptOptions.cruncherEnabled = false
  aaptOptions.useNewCruncher = false
  flavorDimensions "environment"  // 定义一个名为 "environment" 的维度
  defaultConfig {
    // 从 manifest 配置中读取包名
    applicationId project.hasProperty('packageName') ? project.packageName : "com.haidilao.shop"
    minSdkVersion 21
    targetSdkVersion 34
    versionCode project.hasProperty('versionCode') ? project.versionCode.toInteger() : 356
    versionName project.hasProperty('versionName') ? project.versionName : "3.17.0"
    multiDexEnabled true

    ndk {
      abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
    }

    compileOptions {
      sourceCompatibility JavaVersion.VERSION_1_8
      targetCompatibility JavaVersion.VERSION_1_8
    }

    android.applicationVariants.all { variant ->
      variant.outputs.all {
        def createTime = new Date().format("YYYYMMddhhmm", TimeZone.getTimeZone("GMT+08:00"))
        def fileName = "${project.name}_${variant.name}_v${defaultConfig.versionName}_${createTime}.apk"
        outputFileName = fileName
      }
    }
    // fa88db66d2a2f590d1222374  com.engagelab.app 正式环境
    manifestPlaceholders = [
      dcloud_appid: project.hasProperty('dcloud_appid') ? project.dcloud_appid : "__UNI__2704FCB",
      dcloud_appkey: project.hasProperty('dcloud_appkey') ? project.dcloud_appkey : "b2444fe6327461971d4c0a490e3ffe0e",
     JPUSH_PKGNAME      : applicationId,
     JPUSH_APPKEY       : "b3487b90a9af23469ec1f169",
     JPUSH_CHANNEL      : "developer-default",
     XIAOMI_APPKEY      : "MI-xx",
     XIAOMI_APPID       : "MI-xx",
     OPPO_APPKEY        : "OP-xx",
     OPPO_APPID         : "OP-xx",
     OPPO_APPSECRET     : "OP-xx",
     VIVO_APPKEY        : "xx",
     VIVO_APPID         : "xx",
     MEIZU_APPKEY       : "MZ-xx",
     MEIZU_APPID        : "MZ-xx",


    ]

  }

  signingConfigs {
    releaseConfig {
      storeFile file(System.getenv('KEYSTORE_FILE') ?: 'haidilao_shop.keystore')
      storePassword System.getenv('KEYSTORE_PASSWORD') ?: '4FnFGHOj'
      keyAlias System.getenv('KEY_ALIAS') ?: '__uni__2704fcb'
      keyPassword System.getenv('KEY_PASSWORD') ?: '4FnFGHOj'
      v1SigningEnabled true
      v2SigningEnabled true
    }
  }
  productFlavors {
    uat {
      dimension "environment"  // 将 uat flavor 归类到 "environment" 维度
      // applicationId project.hasProperty('packageName') ? project.packageName : "com.haidilao.shop"  // 使用动态的包名
      applicationId "com.haidilao.shop"
      manifestPlaceholders = [
        dcloud_appid: project.hasProperty('dcloud_appid') ? project.dcloud_appid : "__UNI__2704FCB",
        dcloud_appkey: project.hasProperty('dcloud_appkey') ? project.dcloud_appkey : "b2444fe6327461971d4c0a490e3ffe0e" , // 使用动态的 appkey
        JPUSH_PKGNAME      : applicationId,
        JPUSH_APPKEY       : "b3487b90a9af23469ec1f169",
        JPUSH_CHANNEL      : "developer-default",
        XIAOMI_APPKEY      : "MI-xx",
        XIAOMI_APPID       : "MI-xx",
        OPPO_APPKEY        : "OP-xx",
        OPPO_APPID         : "OP-xx",
        OPPO_APPSECRET     : "OP-xx",
        VIVO_APPKEY        : "xx",
        VIVO_APPID         : "xx",
        MEIZU_APPKEY       : "MZ-xx",
        MEIZU_APPID        : "MZ-xx",
      ]
      signingConfig signingConfigs.releaseConfig
    }
    prod {
      dimension "environment"  // 将 uat flavor 归类到 "environment" 维度
      applicationId project.hasProperty('packageName') ? project.packageName : "com.haidilao.shop"  // 使用动态的包名
      manifestPlaceholders = [
        dcloud_appid: project.hasProperty('dcloud_appid') ? project.dcloud_appid : "__UNI__2704FCB",
        dcloud_appkey: project.hasProperty('dcloud_appkey') ? project.dcloud_appkey : "b2444fe6327461971d4c0a490e3ffe0e",  // 使用动态的 appkey        JPUSH_PKGNAME      : applicationId,
        JPUSH_APPKEY       : "b3487b90a9af23469ec1f169",
        JPUSH_CHANNEL      : "developer-default",
        XIAOMI_APPKEY      : "MI-xx",
        XIAOMI_APPID       : "MI-xx",
        OPPO_APPKEY        : "OP-xx",
        OPPO_APPID         : "OP-xx",
        OPPO_APPSECRET     : "OP-xx",
        VIVO_APPKEY        : "xx",
        VIVO_APPID         : "xx",
        MEIZU_APPKEY       : "MZ-xx",
        MEIZU_APPID        : "MZ-xx",

      ]
      signingConfig signingConfigs.releaseConfig
    }
  }

  buildTypes {
    debug {
      signingConfig signingConfigs.releaseConfig

      minifyEnabled false
      proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }
    release {
      signingConfig signingConfigs.releaseConfig

      minifyEnabled false
      proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
    }
  }

  aaptOptions {
    additionalParameters '--auto-add-overlay'
    ignoreAssetsPattern "!.svn:!.git:.*:!CVS:!thumbs.db:!picasa.ini:!*.scc:*~"
  }

  packagingOptions {
    exclude 'META-INF/DEPENDENCIES'
    exclude 'META-INF/NOTICE'
    exclude 'META-INF/LICENSE'
    exclude 'META-INF/LICENSE.txt'
    exclude 'META-INF/NOTICE.txt'
    exclude 'META-INF/ASL2.0'
    exclude 'META-INF/DEPENDENCIES.txt'
    exclude 'META-INF/notice.txt'
    exclude 'META-INF/license.txt'
    exclude 'play-services-base.properties'
  }
}

ext {
  playServicesVersion = "18.0.1" // 使用统一版本
}

dependencies {
  implementation fileTree(dir: 'libs', include: ['*.aar', '*.jar'], exclude: [])

  implementation 'androidx.appcompat:appcompat:1.1.0'
  implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.0.0'
  implementation 'androidx.core:core:1.1.0'
  implementation "androidx.fragment:fragment:1.1.0"
  implementation 'androidx.recyclerview:recyclerview:1.1.0'
  implementation 'com.alipay.sdk:alipaysdk-android:15.8.11'
  implementation 'com.tencent.mm.opensdk:wechat-sdk-android-without-mta:6.8.0'
  implementation 'com.alibaba:fastjson:1.2.83'
  implementation 'androidx.webkit:webkit:1.3.0'


  implementation "com.squareup.okhttp3:okhttp:3.12.12"
  implementation 'com.facebook.soloader:soloader:0.10.4'

  implementation 'com.android.support:multidex:1.0.1'


  // Fresco 库
  implementation 'com.facebook.fresco:fresco:1.13.0'
  implementation "com.facebook.fresco:animated-gif:1.13.0"

  // Glide 库
  implementation 'com.github.bumptech.glide:glide:4.9.0'



}
configurations.all {
  resolutionStrategy {
    force 'com.google.android.gms:play-services-basement:18.4.0'
    force 'com.google.android.gms:play-services-tasks:18.2.0'
  }
}

