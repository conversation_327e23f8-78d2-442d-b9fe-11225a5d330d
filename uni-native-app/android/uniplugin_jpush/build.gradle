apply plugin: 'com.android.library'

android {
    compileSdkVersion 28

    lintOptions {
                 abortOnError false
             }

    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 28
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

}

repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    releaseCompileOnly fileTree(dir: 'libs', include: ['*.jar',"*.aar"])
    debugCompileOnly fileTree(dir: 'libs', include: ['*.jar',"*.aar"])

    compileOnly 'com.android.support:recyclerview-v7:28.0.0'
    compileOnly 'com.android.support:support-v4:28.0.0'
    compileOnly 'com.android.support:appcompat-v7:28.0.0'
//    compileOnly 'com.alibaba:fastjson:1.1.46.android'
    compileOnly 'com.alibaba:fastjson:2.0.12.graal'
//  compileOnly 'com.alibaba.fastjson2:fastjson2:2.0.12'

    compileOnly fileTree(dir: '../app/libs', include: ['uniapp-v8-release.aar'])
}
