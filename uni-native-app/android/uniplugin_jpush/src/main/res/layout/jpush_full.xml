<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

    <ImageView
        android:id="@+id/image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        />

    <FrameLayout
        android:id="@+id/countdown_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right"
        android:layout_marginTop="20dp"
        android:layout_marginRight="16dp">

        <TextView
            android:layout_gravity="right"
            android:id="@+id/btn_countdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/jpush_btn_grey_bg"
            android:paddingLeft="12dp"
            android:paddingTop="5dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingRight="12dp"
            android:paddingBottom="5dp"
            android:text=""
            android:textColor="#ff2c6bff"
            android:textSize="20sp" />

        <ImageView
            android:id="@+id/image_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/jpush_close" />
    </FrameLayout>


</FrameLayout>
