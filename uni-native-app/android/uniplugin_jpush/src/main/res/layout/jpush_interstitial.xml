<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#66000000"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/frame"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp">

        <LinearLayout
            android:id="@+id/content_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical">

            <cn.jpush.android.ui.RoundedImageView
                android:id="@+id/image"
                android:layout_width="match_parent"
                android:layout_height="140dp"
                android:scaleType="fitXY"
                android:visibility="gone" />

            <LinearLayout
                android:id="@+id/bg_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/jpush_interstitial_bg"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/margeview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/text_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:gravity="center"
                            android:text=""
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:textColor="#ff253044"
                            android:textSize="22sp" />


                        <TextView
                            android:id="@+id/text_content"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="20dp"
                            android:maxHeight="130dp"
                            android:scrollbars="vertical"
                            android:text=""
                            android:textColor="#ad253044"
                            android:textSize="18sp"

                            />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/btn_parent_view"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/btn_one"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:background="@drawable/jpush_cancel_btn_bg"
                            android:gravity="center"
                            android:paddingTop="10dp"
                            android:paddingBottom="10dp"
                            android:layout_marginRight="10dp"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:textColor="#d6253044"
                            android:textSize="20sp" />



                        <TextView
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:id="@+id/btn_two"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:background="@drawable/jpush_btn_blue_bg"
                            android:gravity="center"
                            android:paddingTop="9dp"
                            android:paddingBottom="9dp"
                            android:textColor="#ffffffff"
                            android:textSize="20sp" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <cn.jpush.android.ui.RoundedImageView
            android:id="@+id/image_only"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scaleType="fitXY"
            android:visibility="gone" />
    </FrameLayout>

    <ImageView
        android:id="@+id/img_top_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/frame"
        android:layout_alignParentRight="true"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="6dp"
        android:src="@drawable/jpush_close"
        android:visibility="gone"

        />

    <ImageView
        android:id="@+id/img_bottom_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/frame"
        android:layout_centerInParent="true"
        android:layout_marginTop="16dp"
        android:src="@drawable/jpush_close"
        android:visibility="gone" />
</RelativeLayout>