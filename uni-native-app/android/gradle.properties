# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4096m  -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
android.bundle.enableUncompressedNativeLibs = false

android.useAndroidX=true
android.enableJetifier=true
#systemProp.http.proxyHost=*************
#systemProp.http.proxyPort=3313


# ?? HTTP ??
#systemProp.http.proxyHost=127.0.0.1
#systemProp.http.proxyPort=7890
#systemProp.https.proxyHost=127.0.0.1
#systemProp.https.proxyPort=7890
#
## ???? SOCKS5 ???Clash ?????
#systemProp.socksProxyHost=127.0.0.1
#systemProp.socksProxyPort=7891
