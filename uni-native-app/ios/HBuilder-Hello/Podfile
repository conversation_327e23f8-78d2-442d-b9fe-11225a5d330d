project '吉野家 Yoshinoya.xcodeproj'

platform :ios, '12.0'

# 使用静态库链接方式，避免动态库问题
use_frameworks! :linkage => :static

# 删除 Pods 目录并重新安装
install! 'cocoapods', :deterministic_uuids => false

target 'Yoshinoya' do
  # 使用 Firebase 核心功能
  pod 'FirebaseAnalytics'
  pod 'FirebaseMessaging'
  pod 'FirebaseCrashlytics'

  # 使用 GoogleUtilities
  pod 'GoogleUtilities'
end

post_install do |installer|
  # 为所有目标设置构建选项
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)']
      config.build_settings['LIBRARY_SEARCH_PATHS'] ||= ['$(inherited)']
      config.build_settings['OTHER_LDFLAGS'] ||= ['$(inherited)']
      config.build_settings['CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER'] = 'NO'

      # 添加以下设置以解决构建问题
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
      config.build_settings['ENABLE_BITCODE'] = 'NO'

      # 仅对非系统框架配置
      if target.name != 'Pods-Yoshinoya'
        # 禁用代码签名，让主应用处理
        config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
        config.build_settings['CODE_SIGNING_REQUIRED'] = 'NO'
        config.build_settings['EXPANDED_CODE_SIGN_IDENTITY'] = ''

        # Xcode 14+ 需要的设置
        config.build_settings['DEAD_CODE_STRIPPING'] = 'YES'
        config.build_settings['ONLY_ACTIVE_ARCH'] = 'NO'
      end
    end
  end
end
