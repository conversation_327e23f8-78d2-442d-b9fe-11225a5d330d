<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>${EXECUTABLE_NAME}</string>
	<key>CFBundleIcons</key>
	<dict/>
	<key>CFBundleIcons~ipad</key>
	<dict/>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>${PRODUCT_NAME}</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>2.5.3</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>tencentopenapi</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>tencent1101318457</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.tencent</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wb801494298</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>alixpay</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>alixpayhbilderhello</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wxbe7f03382358338d</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.weibo</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wb3721101999</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>20503</string>
	<key>DCLOUD_AD_ID</key>
	<string>13453452345</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>JCore</key>
	<dict>
		<key>APP_KEY</key>
		<string>4fcc3e237eec4c4fb804ad49</string>
		<key>CHANNEL</key>
		<string>test</string>
	</dict>
	<key>JPush</key>
	<dict>
		<key>ADVERTISINGID</key>
		<string></string>
		<key>DEFAULTINITJPUSH</key>
		<string>false</string>
		<key>ISPRODUCTION</key>
		<string></string>
	</dict>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>tencentweiboSdkv2</string>
		<string>weibosdk2.5</string>
		<string>sinaweibo</string>
		<string>sinaweibohd</string>
		<string>alipay</string>
		<string>safepay</string>
		<string>cydia</string>
		<string>weixin</string>
		<string>wechat</string>
		<string>weibosdk</string>
		<string>mqq</string>
		<string>mqqapi</string>
		<string>mqzone</string>
		<string>wtloginmqq2</string>
		<string>mqqopensdkapiV3</string>
		<string>mqqwpa</string>
		<string>mqqopensdkapiV2</string>
		<string>mqqOpensdkSSoLogin</string>
		<string>hbuilder</string>
		<string>streamapp</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MiSDKAppID</key>
	<string>去官方申请对应的appid</string>
	<key>MiSDKAppKey</key>
	<string>去官方申请对应的key</string>
	<key>MiSDKRun</key>
	<string>debug</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>You need to provide camera access to scanning the QR Code.</string>
	<key>NSContactsUsageDescription</key>
	<string>You need to provide contact access to link and manage contacts.</string>
<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>This app requires access to your location to provide relevant services and features.</string>
<key>NSLocationAlwaysUsageDescription</key>
<string>We need to access your location at all times to ensure the best experience.</string>
	<key>NSLocationWhenInUseDescription</key>
	<string></string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Rest assured that granting this permission will not access your privacy information on other sites. This permission is only used to identify the device and ensure service security and enhanced browsing experience.</string>
	<key>NSMicrophoneUsageDescription</key>
		<string>The microphone is required for voice messages and voice input within the app’s chat features.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>You need to provide photo album access to upload profile image.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>We need your location to show nearby restaurants."</string>
	<key>StatusBarBackground</key>
	<string>#FFFFFF</string>
	<key>UIApplicationShortcutItems</key>
	<array>
		<dict>
			<key>UIApplicationShortcutItemIconType</key>
			<string>UIApplicationShortcutIconTypeShare</string>
			<key>UIApplicationShortcutItemSubtitle</key>
			<string>分享到微信、微博、QQ</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>分 享</string>
			<key>UIApplicationShortcutItemType</key>
			<string>share</string>
		</dict>
		<dict>
			<key>UIApplicationShortcutItemIconFile</key>
			<string>Pandora/apps/HelloH5/www/sa.png</string>
			<key>UIApplicationShortcutItemSubtitle</key>
			<string>www.dcloud.io</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>关 于</string>
			<key>UIApplicationShortcutItemType</key>
			<string>about</string>
			<key>UIApplicationShortcutItemUserInfo</key>
			<dict>
				<key>key3</key>
				<string>value3</string>
			</dict>
		</dict>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>Enabling remote notifications allows users to receive important updates and alerts even when the app is not actively open.</string>
	</array>
	<key>UILaunchImages</key>
	<array>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>12.0</string>
			<key>UILaunchImageName</key>
			<string>Default-896h</string>
			<key>UILaunchImageOrientation</key>
			<string>Portrait</string>
			<key>UILaunchImageSize</key>
			<string>{414, 896}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>12.0</string>
			<key>UILaunchImageName</key>
			<string>Default-896h</string>
			<key>UILaunchImageOrientation</key>
			<string>Portrait</string>
			<key>UILaunchImageSize</key>
			<string>{414, 896}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>12.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Landscape-896h</string>
			<key>UILaunchImageOrientation</key>
			<string>Landscape</string>
			<key>UILaunchImageSize</key>
			<string>{414, 896}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>12.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Landscape-896h</string>
			<key>UILaunchImageOrientation</key>
			<string>Landscape</string>
			<key>UILaunchImageSize</key>
			<string>{414, 896}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>8.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Landscape-736h</string>
			<key>UILaunchImageOrientation</key>
			<string>Landscape</string>
			<key>UILaunchImageSize</key>
			<string>{414, 736}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>8.0</string>
			<key>UILaunchImageName</key>
			<string>Default-736h</string>
			<key>UILaunchImageOrientation</key>
			<string>Portrait</string>
			<key>UILaunchImageSize</key>
			<string>{414, 736}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>11.0</string>
			<key>UILaunchImageName</key>
			<string>Default-812h</string>
			<key>UILaunchImageOrientation</key>
			<string>Portrait</string>
			<key>UILaunchImageSize</key>
			<string>{375, 812}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>11.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Landscape-812h</string>
			<key>UILaunchImageOrientation</key>
			<string>Landscape</string>
			<key>UILaunchImageSize</key>
			<string>{375, 812}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>8.0</string>
			<key>UILaunchImageName</key>
			<string>Default-667h</string>
			<key>UILaunchImageOrientation</key>
			<string>Portrait</string>
			<key>UILaunchImageSize</key>
			<string>{375, 667}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>8.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Landscape-667h</string>
			<key>UILaunchImageOrientation</key>
			<string>Landscape</string>
			<key>UILaunchImageSize</key>
			<string>{320, 667}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>7.0</string>
			<key>UILaunchImageName</key>
			<string>Default</string>
			<key>UILaunchImageOrientation</key>
			<string>Portrait</string>
			<key>UILaunchImageSize</key>
			<string>{320, 480}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>7.0</string>
			<key>UILaunchImageName</key>
			<string>Default-568h</string>
			<key>UILaunchImageOrientation</key>
			<string>Portrait</string>
			<key>UILaunchImageSize</key>
			<string>{320, 568}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>7.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Landscape-568h</string>
			<key>UILaunchImageOrientation</key>
			<string>Landscape</string>
			<key>UILaunchImageSize</key>
			<string>{320, 568}</string>
		</dict>
	</array>
	<key>UILaunchImages~ipad</key>
	<array>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>7.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Landscape7</string>
			<key>UILaunchImageOrientation</key>
			<string>Landscape</string>
			<key>UILaunchImageSize</key>
			<string>{768, 1024}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>7.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Portrait7</string>
			<key>UILaunchImageOrientation</key>
			<string>Portrait</string>
			<key>UILaunchImageSize</key>
			<string>{768, 1024}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>10.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Portrait7-1366h</string>
			<key>UILaunchImageOrientation</key>
			<string>Portrait</string>
			<key>UILaunchImageSize</key>
			<string>{1024, 1366}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>10.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Landscape-1366h</string>
			<key>UILaunchImageOrientation</key>
			<string>Landscape</string>
			<key>UILaunchImageSize</key>
			<string>{1024, 1366}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>10.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Portrait7-1194h</string>
			<key>UILaunchImageOrientation</key>
			<string>Portrait</string>
			<key>UILaunchImageSize</key>
			<string>{834, 1194}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>10.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Landscape-1194h</string>
			<key>UILaunchImageOrientation</key>
			<string>Landscape</string>
			<key>UILaunchImageSize</key>
			<string>{834, 1194}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>8.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Portrait7-1112h</string>
			<key>UILaunchImageOrientation</key>
			<string>Portrait</string>
			<key>UILaunchImageSize</key>
			<string>{834, 1112}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>8.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Landscape-1112h</string>
			<key>UILaunchImageOrientation</key>
			<string>Landscape</string>
			<key>UILaunchImageSize</key>
			<string>{834, 1112}</string>
		</dict>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	<key>UIStatusBarTintParameters</key>
	<dict>
		<key>UINavigationBar</key>
		<dict>
			<key>Style</key>
			<string>UIBarStyleDefault</string>
			<key>Translucent</key>
			<false/>
		</dict>
	</dict>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>amap</key>
	<dict>
		<key>appkey</key>
		<string>去官方申请对应的key</string>
	</dict>
	<key>baidu</key>
	<dict>
		<key>appkey</key>
		<string>去官方申请对应的key</string>
	</dict>
	<key>baiduspeech</key>
	<dict>
		<key>API_KEY</key>
		<string>去官方申请对应的key</string>
		<key>APP_ID</key>
		<string>去官方申请对应的id</string>
		<key>SECRET_KEY</key>
		<string>去官方申请获取对应的值</string>
	</dict>
	<key>dcloud_appkey</key>
	<string>f138b355a3bcfe7e87722e0e7cda5d09</string>
	<key>dcloud_uninview_background</key>
	<true/>
	<key>dcloud_uniplugins</key>
	<array>
		<dict>
			<key>hooksClass</key>
			<string>JPushProxy</string>
			<key>plugins</key>
			<array>
				<dict>
					<key>class</key>
					<string>JPushModule</string>
					<key>name</key>
					<string>JG-JPush</string>
					<key>type</key>
					<string>module</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>hooksClass</key>
			<string></string>
			<key>plugins</key>
			<array>
				<dict>
					<key>class</key>
					<string>JCoreModule</string>
					<key>name</key>
					<string>JG-JCore</string>
					<key>type</key>
					<string>module</string>
				</dict>
			</array>
		</dict>
	</array>
	<key>weixin</key>
	<dict>
		<key>appSecret</key>
		<string>去官方申请对应的值</string>
		<key>appid</key>
		<string>去官方申请对应的appid</string>
	</dict>
</dict>
</plist>
