<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="14313.18" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14283.14"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="aN7-qZ-KCL"/>
                        <viewControllerLayoutGuide type="bottom" id="5o6-IB-QEo"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="z0m-pv-cfa">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="app" translatesAutoresizingMaskIntoConstraints="NO" id="chI-mp-JJn">
                                <rect key="frame" x="147" y="120" width="81" height="81"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="81" id="1H3-O0-44D"/>
                                    <constraint firstAttribute="height" constant="81" id="clJ-nz-OrU"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="10"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="吉野家 Yoshinoya" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lAp-n0-0hk">
                                <rect key="frame" x="0.0" y="221" width="375" height="24"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="24" id="uFn-lJ-cpp"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="20"/>
                                <color key="textColor" cocoaTouchSystemColor="darkTextColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="chI-mp-JJn" firstAttribute="centerX" secondItem="z0m-pv-cfa" secondAttribute="centerX" id="42Z-Cr-3Xf"/>
                            <constraint firstItem="chI-mp-JJn" firstAttribute="top" secondItem="aN7-qZ-KCL" secondAttribute="bottom" constant="100" id="AMC-3E-oVh"/>
                            <constraint firstItem="lAp-n0-0hk" firstAttribute="leading" secondItem="z0m-pv-cfa" secondAttribute="leading" id="Dbn-k8-Zle"/>
                            <constraint firstAttribute="trailing" secondItem="lAp-n0-0hk" secondAttribute="trailing" id="XC1-Es-H4H"/>
                            <constraint firstItem="lAp-n0-0hk" firstAttribute="top" secondItem="chI-mp-JJn" secondAttribute="bottom" constant="20" id="xri-EJ-2Z7"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52" y="374.66266866566718"/>
        </scene>
    </scenes>
    <resources>
        <image name="app" width="60" height="60"/>
    </resources>
</document>
