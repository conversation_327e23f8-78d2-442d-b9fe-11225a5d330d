<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Payment Page</title>
  </head>
  <body>
    <!-- <script src="https://cdn.bootcss.com/vConsole/3.3.4/vconsole.min.js"></script>
    <script>
      // #ifndef MP-ALIPAY
      var vConsole = new VConsole();
      console.log('Hello world');
      // #endif
    </script> -->
    <script>
      function getQueryParams() {
        const params = {};
        const queryString = window.location.search.substring(1);
        const regex = /([^&=]+)=([^&]*)/g;
        let m;

        while ((m = regex.exec(queryString))) {
          params[decodeURIComponent(m[1])] = decodeURIComponent(m[2]);
        }
        console.log('getQueryParams', params);
        return params;
      }

      window.onload = function () {
        // const params = getQueryParams();
        const formHtml = decodeURIComponent(window.location.search.substring(1));
        console.log('formHtml', formHtml, window.location);
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = formHtml;
        const form = tempDiv.querySelector('form');

        if (form) {
          form.style.display = 'none'; // 隐藏表单
          document.body.appendChild(form); // 将表单添加到文档中
          form.submit(); // 提交表单
        } else {
          console.error('No form found in the provided HTML');
        }
      };
    </script>
  </body>
</html>
