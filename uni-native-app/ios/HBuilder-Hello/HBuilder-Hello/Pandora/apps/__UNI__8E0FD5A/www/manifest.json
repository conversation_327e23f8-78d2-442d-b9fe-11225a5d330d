{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__8E0FD5A", "name": "吉野家", "version": {"name": "1.0.0", "code": "100"}, "description": "", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#fff"}, "usingComponents": true, "compatible": {"ignoreVersion": true}, "distribute": {"google": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "apple": {"dSYMs": false}, "plugins": {"ad": {}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "plugins": {}, "nativePlugins": {}, "appid": "wxd376b64083f5c155", "allowsInlineMediaPlayback": true, "safearea": {"background": "#ffffff", "bottom": {"offset": "auto"}}, "uni-app": {"compilerVersion": "4.29", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "tabBar": {"textColor": "##222222", "selectedColor": "#333333", "backgroundColor": "#ffffff", "iconWidth": "18px", "borderStyle": "rgba(255,255,255,0.4)", "list": [{"pagePath": "pages/home/<USER>", "iconPath": "static/tabbar/home.png", "selectedIconPath": "static/tabbar/home-active.png", "text": "首頁"}, {"pagePath": "pages/store/goods", "iconPath": "static/tabbar/drink.png", "selectedIconPath": "/static/tabbar/drink-active.png", "text": "點餐"}, {"pagePath": "memberMinVolume/pages/collectPrice/index", "iconPath": "static/tabbar/premium.png", "selectedIconPath": "static/tabbar/premium-active.png", "text": "獎賞"}, {"pagePath": "pages/member/mine/index", "iconPath": "static/tabbar/user.png", "selectedIconPath": "static/tabbar/user-active.png", "text": "我的"}], "midButton": {"width": "58px", "height": "58px", "text": "", "backgroundImage": "/static/tabbar/qrcode3.png", "iconWidth": "50px"}, "height": "50px", "child": ["lauchwebview"], "selected": 0}, "launch_path": "__uniappview.html"}, "locale": "zh-Han<PERSON>", "fallbackLocale": "zh-Han<PERSON>"}