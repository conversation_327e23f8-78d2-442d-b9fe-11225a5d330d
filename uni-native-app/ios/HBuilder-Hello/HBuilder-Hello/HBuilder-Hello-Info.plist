<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>吉野家 Yoshinoya</string>
	<key>CFBundleExecutable</key>
	<string>${EXECUTABLE_NAME}</string>
	<key>CFBundleIconName</key>
	<string></string>
	<key>CFBundleIcons</key>

	<dict/>
	<key>CFBundleIcons~ipad</key>
	<dict/>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>${PRODUCT_NAME}</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>tencentopenapi</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>tencent1101318457</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.tencent</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wb801494298</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>alixpay</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>alix2102020153222632</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wx6aa027c931178a3a</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleIdentifier</key>
			<string></string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.weibo</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wb3721101999</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLIconFile</key>
			<string>dclogo@2x</string>
			<key>CFBundleURLName</key>
			<string>yoshiclub</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>yoshiclub</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>facebook</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb1140617207241131</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>google_url</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.894038315898-fdebsefdfn3f6sq0g8aicdt571knl5n9</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>payme</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>payme</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>DCLOUD_AD_ID</key>
	<string>去dcloud官方网站申请对应的值</string>
	<key>Enabling remote notifications allows users to receive important updates and alerts even when the app is not actively open.</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>FacebookAppID</key>
	<string>1140617207241131</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>GIDClientID</key>
	<string>894038315898-fdebsefdfn3f6sq0g8aicdt571knl5n9.apps.googleusercontent.com</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>tencentweiboSdkv2</string>
		<string>weibosdk2.5</string>
		<string>sinaweibo</string>
		<string>sinaweibohd</string>
		<string>alipay</string>
		<string>safepay</string>
		<string>cydia</string>
		<string>weixin</string>
		<string>weixinULAPI</string>
		<string>weibosdk</string>
		<string>mqq</string>
		<string>mqqapi</string>
		<string>mqzone</string>
		<string>wtloginmqq2</string>
		<string>mqqopensdkapiV3</string>
		<string>mqqwpa</string>
		<string>mqqopensdkapiV2</string>
		<string>mqqOpensdkSSoLogin</string>
		<string>hbuilder</string>
		<string>streamapp</string>
		<string>iosamap</string>
		<string>baidumap</string>
		<string>octopus</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MTPush</key>
	<dict>
		<key>ADVERTISINGID</key>
		<string></string>
		<key>APP_KEY</key>
		<string>eb0afbb2aac42b7e98845b4a</string>
		<key>CHANNEL</key>
		<string>test</string>
		<key>DEFAULTINIT</key>
		<string></string>
		<key>ISPRODUCTION</key>
		<string>true</string>
	</dict>
	<key>MiSDKAppID</key>
	<string>去官方网站申请对应的值</string>
	<key>MiSDKAppKey</key>
	<string>去官方网站申请对应的值</string>
	<key>MiSDKRun</key>
	<string>debug</string>
	<key>MinimumOSVersion</key>
	<string>12.0</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>You need to provide camera access to scanning the QR Code.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We need access to your location to: 1. Send real-time notifications about nearby store offers 2. Navigate you to the closest restaurants 3. Provide accurate delivery services. Your location data is solely used to improve your service experience.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>We need to access your location at all times to ensure the best experience.</string>
	<key>NSLocationWhenInUseDescription</key>
	<string></string>
  <key>NSLocationWhenInUseUsageDescription</key>
  <string>We need access to your location to: 1. Find nearest restaurants 2. Provide location-based promotions 3. Give you directions to our stores. Your location data is used only within the app and never shared with third parties.</string>
	<key>NSLocationWhenInUseUsageDescription - 2</key>
	<string>Location access allows the app to provide location-based services, such as finding nearby stores and relevant offers.</string>
	<key>NSMicrophoneUsageDescription</key>
  <string>We need access to your microphone for voice input and recording features within the app. All recordings are stored locally and will not be uploaded or shared with third parties without your consent.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>You need to provide photo album access to upload profile image.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>We request access to your device identifier to provide personalized services and experiences. Your data will only be used to enhance our services, including nearby store recommendations and relevant offers. You can opt-out anytime in Settings.</string>
	<key>StatusBarBackground</key>
	<string>#FFFFFF</string>
	<key>UIApplicationShortcutItems</key>
	<array/>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchImages~ipad</key>
	<array>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>7.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Landscape7</string>
			<key>UILaunchImageOrientation</key>
			<string>Landscape</string>
			<key>UILaunchImageSize</key>
			<string>{768, 1024}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>7.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Portrait7</string>
			<key>UILaunchImageOrientation</key>
			<string>Portrait</string>
			<key>UILaunchImageSize</key>
			<string>{768, 1024}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>10.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Portrait7-1366h</string>
			<key>UILaunchImageOrientation</key>
			<string>Portrait</string>
			<key>UILaunchImageSize</key>
			<string>{1024, 1366}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>10.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Landscape-1366h</string>
			<key>UILaunchImageOrientation</key>
			<string>Landscape</string>
			<key>UILaunchImageSize</key>
			<string>{1024, 1366}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>10.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Portrait7-1194h</string>
			<key>UILaunchImageOrientation</key>
			<string>Portrait</string>
			<key>UILaunchImageSize</key>
			<string>{834, 1194}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>10.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Landscape-1194h</string>
			<key>UILaunchImageOrientation</key>
			<string>Landscape</string>
			<key>UILaunchImageSize</key>
			<string>{834, 1194}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>8.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Portrait7-1112h</string>
			<key>UILaunchImageOrientation</key>
			<string>Portrait</string>
			<key>UILaunchImageSize</key>
			<string>{834, 1112}</string>
		</dict>
		<dict>
			<key>UILaunchImageMinimumOSVersion</key>
			<string>8.0</string>
			<key>UILaunchImageName</key>
			<string>Default-Landscape-1112h</string>
			<key>UILaunchImageOrientation</key>
			<string>Landscape</string>
			<key>UILaunchImageSize</key>
			<string>{834, 1112}</string>
		</dict>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen.storyboard</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	<key>UIStatusBarTintParameters</key>
	<dict>
		<key>UINavigationBar</key>
		<dict>
			<key>Style</key>
			<string>UIBarStyleDefault</string>
			<key>Translucent</key>
			<false/>
		</dict>
	</dict>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UniversalLinks</key>
	<string>https://eordering.yoshinoya.com.hk/</string>
	<key>amap</key>
	<dict>
		<key>appkey</key>
		<string>去官方网站申请对应的值</string>
	</dict>
	<key>baidu</key>
	<dict>
		<key>appkey</key>
		<string>去官方网站申请对应的值</string>
	</dict>
	<key>baiduspeech</key>
	<dict>
		<key>API_KEY</key>
		<string>去官方网站申请对应的值</string>
		<key>APP_ID</key>
		<string>去官方网站申请对应的值</string>
		<key>SECRET_KEY</key>
		<string>去官方网站申请对应的值</string>
	</dict>
	<key>dcloud_appkey</key>
	<string>df66e880dffa7a30618697df2fd7aa61</string>
	<key>dcloud_uninview_background</key>
	<true/>
	<key>dcloud_uniplugins</key>
	<array>
		<dict>
			<key>hooksClass</key>
			<string>MTPushProxy</string>
			<key>plugins</key>
			<array>
				<dict>
					<key>class</key>
					<string>MTPushModule</string>
					<key>name</key>
					<string>EL-MTPush</string>
					<key>type</key>
					<string>module</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>hooksClass</key>
			<string>DCRichAlertProxy</string>
			<key>plugins</key>
			<array>
				<dict>
					<key>class</key>
					<string>DCRichAlertModule</string>
					<key>name</key>
					<string>DCloud-RichAlert</string>
					<key>type</key>
					<string>module</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>hooksClass</key>
			<string></string>
			<key>plugins</key>
			<array>
				<dict>
					<key>class</key>
					<string>Firebasetrack</string>
					<key>name</key>
					<string>YSFirebaseUniPlugin-Firebasetrack</string>
					<key>type</key>
					<string>module</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>hooksClass</key>
			<string></string>
			<key>plugins</key>
			<array>
				<dict>
					<key>class</key>
					<string>AppStoreReview</string>
					<key>name</key>
					<string>YoshiReview</string>
					<key>type</key>
					<string>module</string>
				</dict>
			</array>
		</dict>
	</array>
	<key>getui</key>
	<dict>
		<key>appid</key>
		<string>去官方网站申请对应的appid</string>
		<key>appkey</key>
		<string>去官方网站申请对应的key</string>
		<key>appsecret</key>
		<string>去官方网站申请对应的值</string>
	</dict>
	<key>marketChannel</key>
	<string>按照文档格式配置对应的值</string>
	<key>sinaweibo</key>
	<dict>
		<key>appSecret</key>
		<string>去官方网站申请对应的值</string>
		<key>appkey</key>
		<string>去官方网站申请对应的值</string>
		<key>redirectURI</key>
		<string>填写自己的</string>
	</dict>
	<key>umeng</key>
	<dict>
		<key>appkey</key>
		<string>去官方网站申请对应的值</string>
	</dict>
	<key>weixin</key>
	<dict>
		<key>appSecret</key>
		<string>去官方网站申请对应的值</string>
		<key>appid</key>
		<string>wx6aa027c931178a3a</string>
	</dict>
	<key>xiaomioauth</key>
	<dict>
		<key>appid</key>
		<string>去官方网站申请对应的appid</string>
		<key>appsecret</key>
		<string>去官方网站申请对应的值</string>
		<key>redirectURI</key>
		<string>填写自己的</string>
	</dict>
</dict>
</plist>
