<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="00: default_platform" time="0.000644">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="01: find /Users/<USER>/Desktop/project/uni-order-shuying-jiyejia -name &apos;._*&apos; -type f -delete" time="16.060382">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="02: find /Users/<USER>/Desktop/project/uni-order-shuying-jiyejia -name &apos;.DS_Store&apos; -type f -delete" time="17.176235">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="03: cd /Users/<USER>/Desktop/project/uni-order-shuying-jiyejia &amp;&amp; npm run build:app-plus -- name=jiyejia cmd=true robot=prod programType=order" time="151.384716">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="04: mkdir -p /Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/uni-native-app/ios/HBuilder-Hello/HBuilder-Hello/Pandora/apps/__UNI__8E0FD5A/www" time="0.121194">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="05: rm -rf /Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/uni-native-app/ios/HBuilder-Hello/HBuilder-Hello/Pandora/apps/__UNI__8E0FD5A/www/*" time="0.04841">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="06: cp -rv /Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/dist/build/app-plus/* /Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/uni-native-app/ios/HBuilder-Hello/HBuilder-Hello/Pandora/apps/__UNI__8E0FD5A/www/" time="0.084549">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="07: find /Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/uni-native-app/ios/HBuilder-Hello/HBuilder-Hello/Pandora/apps/__UNI__8E0FD5A/www -name &apos;._*&apos; -type f -delete" time="0.028484">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="08: cocoapods" time="7.484977">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="09: gym" time="188.219968">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="10: rm -rf /Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/uni-native-app/ios/HBuilder-Hello/fastlane/../../build/temp_cleanup" time="0.104225">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="11: mkdir -p /Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/uni-native-app/ios/HBuilder-Hello/fastlane/../../build/temp_cleanup" time="0.015726">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="12: unzip -q &quot;/Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/uni-native-app/ios/HBuilder-Hello/fastlane/../../build/Yoshinoya_AppStore.ipa&quot; -d &quot;/Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/uni-native-app/ios/HBuilder-Hello/fastlane/../../build/temp_cleanup&quot;" time="1.280343">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="13: find &quot;/Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/uni-native-app/ios/HBuilder-Hello/fastlane/../../build/temp_cleanup&quot; -name &apos;._*&apos; -type f -delete" time="0.04294">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="14: find &quot;/Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/uni-native-app/ios/HBuilder-Hello/fastlane/../../build/temp_cleanup&quot; -name &apos;.DS_Store&apos; -type f -delete" time="0.031725">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="15: cp &quot;/Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/uni-native-app/ios/HBuilder-Hello/fastlane/../../build/Yoshinoya_AppStore.ipa&quot; &quot;/Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/uni-native-app/ios/HBuilder-Hello/fastlane/../../build/Yoshinoya_AppStore_backup.ipa&quot;" time="0.043769">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="16: cd &quot;/Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/uni-native-app/ios/HBuilder-Hello/fastlane/../../build/temp_cleanup&quot; &amp;&amp; /usr/bin/zip -X -r &quot;../clean.ipa&quot; ./*" time="6.519242">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="17: mv &quot;/Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/uni-native-app/ios/HBuilder-Hello/fastlane/../../build/clean.ipa&quot; &quot;/Users/<USER>/Desktop/project/uni-order-shuying-jiyejia/uni-native-app/ios/HBuilder-Hello/fastlane/../../build/Yoshinoya_AppStore.ipa&quot;" time="0.034468">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="18: upload_to_app_store" time="169.985674">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="19: cd /Users/<USER>/Desktop/project/uni-order-shuying-jiyejia &amp;&amp; node -e &quot;const dingding = require(&apos;./help/utils/dingding&apos;); dingding.noticeDingDingSuceess({
        appName: &apos;吉野家 iOS 客户端&apos;,
        commitMessage: &apos;App Store正式版本&apos;,
        commitUser: &apos;未知&apos;,
        env: &apos;release&apos;,
        version: &apos;3.17.3&apos;,
        buildNumber: &apos;1&apos;,
        publishPath: &apos;App Store&apos;,
        isExtApp: false
      });&quot;" time="0.976829">
        
      </testcase>
    
  </testsuite>
</testsuites>
