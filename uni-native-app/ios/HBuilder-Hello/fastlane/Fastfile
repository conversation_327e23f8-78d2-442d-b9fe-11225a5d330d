default_platform(:ios)
platform :ios do
  desc "部署 UAT 版本到蒲公英"
  lane :deploy_uat do
    # 获取项目根目录的绝对路径
    root_path = File.expand_path("../../../../../", __FILE__)

    # 1. 先执行 HBuilderX 打包 app-plus，使用 UAT 环境参数
    sh("cd #{root_path} && npm run build:app-plus -- name=jiyejia_uat cmd=true robot=uat programType=order")

    # 打印源目录内容
    puts "Source directory contents:"
    sh("ls -la #{root_path}/dist/build/app-plus/")

    # 2. 确保目标目录存在
    target_dir = "#{root_path}/uni-native-app/ios/HBuilder-Hello/HBuilder-Hello/Pandora/apps/__UNI__8E0FD5A/www"
    sh("mkdir -p #{target_dir}")  # 创建目录（如果不存在）

    # 打印目标目录内容（复制前）
    puts "Target directory contents before copy:"
    sh("ls -la #{target_dir}")

    # 3. 复制打包后的文件到 www 目录
    sh("rm -rf #{target_dir}/*")  # 清空原有的 www 目录
    sh("cp -rv #{root_path}/dist/build/app-plus/* #{target_dir}/")  # 复制新的文件

    # 打印目标目录内容（复制后）
    puts "Target directory contents after copy:"
    sh("ls -la #{target_dir}")

    # 确保 Pods 已经安装
    cocoapods(
      clean_install: true,
      podfile: "./Podfile"
    )

    # 4. 构建 IPA
    begin
      gym(
        workspace: "吉野家 Yoshinoya.xcworkspace",
        scheme: "Yoshinoya",
        export_method: "ad-hoc",  # 使用 ad-hoc 分发方式
        configuration: "Release",
        clean: true,
        output_directory: "../build",
        output_name: "Yoshinoya_UAT.ipa",
        xcargs: "-UseModernBuildSystem=YES",
        export_options: {
          method: "ad-hoc",
          provisioningProfiles: {
            "com.yoshinoya.ios" => "Yoshinoya_iOS_Adhoc_2025"  # 使用新的 Profile 名称
          },
          teamID: "786Q353TMH",
          signingCertificate: "Apple Distribution: YOSHINOYA FAST FOOD (HONG KONG) LIMITED (786Q353TMH)"
        }
      )

      # 5. 上传到蒲公英
      pgyer(
        api_key: "195588832957e32daeb11a000d79d955",
        ipa: "../build/Yoshinoya_UAT.ipa",
        update_description: "UAT 环境版本",
        password: "123456",
        install_type: "2"
      )

      # 获取版本信息 - 修复路径
      begin
        # 尝试不同可能的Info.plist位置
        info_plist_paths = [
          "#{root_path}/uni-native-app/ios/HBuilder-Hello/HBuilder-Hello/Info.plist",
          "#{root_path}/uni-native-app/ios/HBuilder-Hello/Info.plist",
          "#{Dir.pwd}/../HBuilder-Hello/Info.plist",
          "#{Dir.pwd}/../Info.plist"
        ]

        version = "未知"
        build = "未知"

        # 尝试每个可能的路径
        info_plist_paths.each do |plist_path|
          if File.exist?(plist_path)
            puts "找到Info.plist文件: #{plist_path}"
            version = sh("/usr/libexec/PlistBuddy -c 'Print CFBundleShortVersionString' '#{plist_path}'").strip rescue "未知"
            build = sh("/usr/libexec/PlistBuddy -c 'Print CFBundleVersion' '#{plist_path}'").strip rescue "未知"
            break if version != "未知" && build != "未知"
          end
        end

        # 如果没有找到有效的版本号，使用build.gradle中的版本号
        if version == "未知"
          puts "无法从Info.plist获取版本信息，尝试从Xcode项目设置获取..."
          version = `xcodebuild -showBuildSettings -workspace "#{Dir.pwd}/../吉野家 Yoshinoya.xcworkspace" -scheme "Yoshinoya" | grep MARKETING_VERSION | sed 's/.*= //'`.strip rescue "3.17.0"
          build = `xcodebuild -showBuildSettings -workspace "#{Dir.pwd}/../吉野家 Yoshinoya.xcworkspace" -scheme "Yoshinoya" | grep CURRENT_PROJECT_VERSION | sed 's/.*= //'`.strip rescue "356"
        end

        puts "使用的版本号: #{version}, Build号: #{build}"
      rescue => e
        puts "获取版本信息失败: #{e.message}"
        version = "3.17.0"  # 使用默认版本号
        build = "356"       # 使用默认构建号
      end

      # 发送钉钉通知（成功）
      sh("cd #{root_path} && node -e \"const dingding = require('./help/utils/dingding'); dingding.noticeDingDingSuceess({
        appName: '吉野家 iOS 客户端',
        commitMessage: '#{ENV['commitMessage'] || 'UAT环境测试版本'}',
        commitUser: '#{ENV['commitUser'] || '未知'}',
        env: 'uat',
        version: '#{version}',
        buildNumber: '#{build}',
        publishPath: '蒲公英测试平台',
        isExtApp: false
      });\"")
    rescue => e
      puts "Error: #{e.message}"

      # 发送钉钉通知（失败）
      sh("cd #{root_path} && node -e \"const dingding = require('./help/utils/dingding'); dingding.noticeDingDingFail({
        appName: '吉野家 iOS 客户端'
      });\"")

      raise e
    end
  end

  desc "部署生产版本到蒲公英"
  lane :deploy_prod do
    # 获取项目根目录的绝对路径
    root_path = File.expand_path("../../../../../", __FILE__)

    # 1. 先执行 HBuilderX 打包 app-plus，使用生产环境参数
    sh("cd #{root_path} && npm run build:app-plus -- name=jiyejia cmd=true robot=prod programType=order")

    # 打印源目录内容
    puts "Source directory contents:"
    sh("ls -la #{root_path}/dist/build/app-plus/")

    # 2. 确保目标目录存在
   target_dir = "#{root_path}/uni-native-app/ios/HBuilder-Hello/HBuilder-Hello/Pandora/apps/__UNI__8E0FD5A/www"
    sh("mkdir -p #{target_dir}")  # 创建目录（如果不存在）

    # 打印目标目录内容（复制前）
    puts "Target directory contents before copy:"
    sh("ls -la #{target_dir}")

    # 3. 复制打包后的文件到 www 目录
    sh("rm -rf #{target_dir}/*")  # 清空原有的 www 目录
    sh("cp -rv #{root_path}/dist/build/app-plus/* #{target_dir}/")  # 复制新的文件

    # 打印目标目录内容（复制后）
    puts "Target directory contents after copy:"
    sh("ls -la #{target_dir}")

    # 确保 Pods 已经安装
    cocoapods(
      clean_install: true,
      podfile: "./Podfile"
    )

    # 4. 构建 IPA
    begin
      gym(
        workspace: "吉野家 Yoshinoya.xcworkspace", # 使用 workspace 而不是 project
        scheme: "Yoshinoya",
        export_method: "app-store",  # 改为 app-store
        configuration: "Release",
        clean: true,
        output_directory: "../build",
        output_name: "Yoshinoya_PROD.ipa",
        xcargs: "-UseModernBuildSystem=YES", # 使用现代构建系统
        export_options: {
          method: "app-store",
          provisioningProfiles: {
            "com.yoshinoya.ios" => "Yoshinoya_AppStore_2025_connect" # 你的 App Store Provisioning Profile 名称
          },
          teamID: "786Q353TMH",
          signingStyle: "manual",
          signingCertificate: "42B634DC36429D3CCD690050587194317C6B2EB0" # 使用证书 ID
        },
        codesigning_identity: "Apple Distribution: YOSHINOYA FAST FOOD (HONG KONG) LIMITED (786Q353TMH)"
      )

      # 5. 上传到蒲公英
      pgyer(
        api_key: "195588832957e32daeb11a000d79d955",
        ipa: "../build/Yoshinoya_PROD.ipa",
        update_description: "生产环境版本",
        password: "123456",
        install_type: "2"
      )

      # 获取版本信息 - 修复路径
      begin
        # 尝试不同可能的Info.plist位置
        info_plist_paths = [
          "#{root_path}/uni-native-app/ios/HBuilder-Hello/HBuilder-Hello/Info.plist",
          "#{root_path}/uni-native-app/ios/HBuilder-Hello/Info.plist",
          "#{Dir.pwd}/../HBuilder-Hello/Info.plist",
          "#{Dir.pwd}/../Info.plist"
        ]

        version = "未知"
        build = "未知"

        # 尝试每个可能的路径
        info_plist_paths.each do |plist_path|
          if File.exist?(plist_path)
            puts "找到Info.plist文件: #{plist_path}"
            version = sh("/usr/libexec/PlistBuddy -c 'Print CFBundleShortVersionString' '#{plist_path}'").strip rescue "未知"
            build = sh("/usr/libexec/PlistBuddy -c 'Print CFBundleVersion' '#{plist_path}'").strip rescue "未知"
            break if version != "未知" && build != "未知"
          end
        end

        # 如果没有找到有效的版本号，使用build.gradle中的版本号
        if version == "未知"
          puts "无法从Info.plist获取版本信息，尝试从Xcode项目设置获取..."
          version = `xcodebuild -showBuildSettings -workspace "#{Dir.pwd}/../吉野家 Yoshinoya.xcworkspace" -scheme "Yoshinoya" | grep MARKETING_VERSION | sed 's/.*= //'`.strip rescue "3.17.0"
          build = `xcodebuild -showBuildSettings -workspace "#{Dir.pwd}/../吉野家 Yoshinoya.xcworkspace" -scheme "Yoshinoya" | grep CURRENT_PROJECT_VERSION | sed 's/.*= //'`.strip rescue "356"
        end

        puts "使用的版本号: #{version}, Build号: #{build}"
      rescue => e
        puts "获取版本信息失败: #{e.message}"
        version = "3.17.0"  # 使用默认版本号
        build = "356"       # 使用默认构建号
      end

      # 发送钉钉通知（成功）
      sh("cd #{root_path} && node -e \"const dingding = require('./help/utils/dingding'); dingding.noticeDingDingSuceess({
        appName: '吉野家 iOS 客户端',
        commitMessage: '#{ENV['commitMessage'] || '生产环境版本'}',
        commitUser: '#{ENV['commitUser'] || '未知'}',
        env: 'prod',
        version: '#{version}',
        buildNumber: '#{build}',
        publishPath: '蒲公英测试平台',
        isExtApp: false
      });\"")
    rescue => e
      puts "Error: #{e.message}"

      # 发送钉钉通知（失败）
      sh("cd #{root_path} && node -e \"const dingding = require('./help/utils/dingding'); dingding.noticeDingDingFail({
        appName: '吉野家 iOS 客户端'
      });\"")

      raise e
    end
  end

  desc "发布 TestFlight 测试版"
  lane :beta do
    # 获取项目根目录的绝对路径
    root_path = File.expand_path("../../../../../", __FILE__)

    # 1. 先执行 HBuilderX 打包 app-plus，使用生产环境参数
    sh("cd #{root_path} && npm run build:app-plus -- name=jiyejia cmd=true robot=prod programType=order")

    # 2. 确保目标目录存在并复制文件
    target_dir = "#{root_path}/uni-native-app/ios/HBuilder-Hello/HBuilder-Hello/Pandora/apps/__UNI__8E0FD5A/www"
    sh("mkdir -p #{target_dir}")
    sh("rm -rf #{target_dir}/*")
    sh("cp -rv #{root_path}/dist/build/app-plus/* #{target_dir}/")

    # 确保 Pods 已经安装
    cocoapods(
      clean_install: true,
      podfile: "./Podfile"
    )

    # 3. 构建并上传到 TestFlight
    begin
      gym(
        workspace: "吉野家 Yoshinoya.xcworkspace", # 使用 workspace 而不是 project
        scheme: "Yoshinoya",
        export_method: "app-store",
        configuration: "Release",
        clean: true,
        output_directory: "../build",
        output_name: "Yoshinoya_TestFlight.ipa",
        xcargs: "-UseModernBuildSystem=YES", # 使用现代构建系统
        export_options: {
          method: "app-store",
          provisioningProfiles: {
            "com.yoshinoya.ios" => "Yoshinoya_AppStore_2025_connect"
          },
          teamID: "786Q353TMH"
        }
      )

      # 获取版本信息 - 修复路径
      begin
        # 尝试不同可能的Info.plist位置
        info_plist_paths = [
          "#{root_path}/uni-native-app/ios/HBuilder-Hello/HBuilder-Hello/Info.plist",
          "#{root_path}/uni-native-app/ios/HBuilder-Hello/Info.plist",
          "#{Dir.pwd}/../HBuilder-Hello/Info.plist",
          "#{Dir.pwd}/../Info.plist"
        ]

        version = "未知"
        build = "未知"

        # 尝试每个可能的路径
        info_plist_paths.each do |plist_path|
          if File.exist?(plist_path)
            puts "找到Info.plist文件: #{plist_path}"
            version = sh("/usr/libexec/PlistBuddy -c 'Print CFBundleShortVersionString' '#{plist_path}'").strip rescue "未知"
            build = sh("/usr/libexec/PlistBuddy -c 'Print CFBundleVersion' '#{plist_path}'").strip rescue "未知"
            break if version != "未知" && build != "未知"
          end
        end

        # 如果没有找到有效的版本号，使用build.gradle中的版本号
        if version == "未知"
          puts "无法从Info.plist获取版本信息，尝试从Xcode项目设置获取..."
          version = `xcodebuild -showBuildSettings -workspace "#{Dir.pwd}/../吉野家 Yoshinoya.xcworkspace" -scheme "Yoshinoya" | grep MARKETING_VERSION | sed 's/.*= //'`.strip rescue "3.17.0"
          build = `xcodebuild -showBuildSettings -workspace "#{Dir.pwd}/../吉野家 Yoshinoya.xcworkspace" -scheme "Yoshinoya" | grep CURRENT_PROJECT_VERSION | sed 's/.*= //'`.strip rescue "356"
        end

        puts "使用的版本号: #{version}, Build号: #{build}"
      rescue => e
        puts "获取版本信息失败: #{e.message}"
        version = "3.17.0"  # 使用默认版本号
        build = "356"       # 使用默认构建号
      end

      # 发送钉钉通知（成功）
      sh("cd #{root_path} && node -e \"const dingding = require('./help/utils/dingding'); dingding.noticeDingDingSuceess({
        appName: '吉野家 iOS 客户端',
        commitMessage: '#{ENV['commitMessage'] || 'TestFlight测试版本'}',
        commitUser: '#{ENV['commitUser'] || '未知'}',
        env: 'beta',
        version: '#{version}',
        buildNumber: '#{build}',
        publishPath: 'TestFlight测试平台',
        isExtApp: false
      });\"")
    rescue => e
      puts "Error: #{e.message}"

      # 发送钉钉通知（失败）
      sh("cd #{root_path} && node -e \"const dingding = require('./help/utils/dingding'); dingding.noticeDingDingFail({
        appName: '吉野家 iOS 客户端'
      });\"")

      raise e
    end
  end

  desc "发布到 App Store"
  lane :release do
    # 直接设置 Apple ID 应用特定密码
    ENV["FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD"] = "bgqj-fdtf-cpke-mcyg"

    # 设置环境变量禁止生成 ._* 文件
    ENV["COPYFILE_DISABLE"] = "1"
    ENV["COPYFILE_EXTENDED_ATTRIBUTES_DISABLE"] = "1"

    # 添加磁盘空间检查和清理
    free_space_mb = `df -m / | tail -1 | awk '{print $4}'`.strip.to_i
    puts "当前可用磁盘空间: #{free_space_mb} MB"

    if free_space_mb < 5000  # 如果小于 5GB，执行清理
      puts "可用空间不足，正在清理..."

      # 清理 Xcode 派生数据
      sh("rm -rf ~/Library/Developer/Xcode/DerivedData/*")

      # 清理 iOS 模拟器缓存
      sh("xcrun simctl delete unavailable")

      # 清理构建目录
      sh("rm -rf #{Dir.pwd}/../build/*")

      # 清理下载的 pods
      sh("rm -rf ~/Library/Caches/CocoaPods/*")

      free_space_mb_after = `df -m / | tail -1 | awk '{print $4}'`.strip.to_i
      puts "清理后可用磁盘空间: #{free_space_mb_after} MB (增加了 #{free_space_mb_after - free_space_mb} MB)"
    end

    # 获取项目根目录的绝对路径
    root_path = File.expand_path("../../../../../", __FILE__)

    # 清理隐藏文件，特别是 ._Symbols
    sh("find #{root_path} -name '._*' -type f -delete")
    sh("find #{root_path} -name '.DS_Store' -type f -delete")
    puts "已清理隐藏文件"

    # 1. 先执行 HBuilderX 打包 app-plus，使用生产环境参数
    sh("cd #{root_path} && npm run build:app-plus -- name=jiyejia cmd=true robot=prod programType=order")

    # 2. 确保目标目录存在并复制文件
    target_dir = "#{root_path}/uni-native-app/ios/HBuilder-Hello/HBuilder-Hello/Pandora/apps/__UNI__8E0FD5A/www"
    sh("mkdir -p #{target_dir}")
    sh("rm -rf #{target_dir}/*")
    sh("cp -rv #{root_path}/dist/build/app-plus/* #{target_dir}/")

    # 再次清理构建文件中的隐藏文件
    sh("find #{target_dir} -name '._*' -type f -delete")
    puts "已清理构建目录的隐藏文件"

    # 确保 Pods 已经安装
    cocoapods(
      clean_install: true,
      podfile: "./Podfile"
    )

    # 3. 构建 IPA 文件
    begin
      gym(
        workspace: "吉野家 Yoshinoya.xcworkspace", # 使用 workspace 而不是 project
        scheme: "Yoshinoya",
        export_method: "app-store",
        configuration: "Release",
        clean: true,
        output_directory: "../build",
        output_name: "Yoshinoya_AppStore.ipa",
        xcargs: "-UseModernBuildSystem=YES", # 使用现代构建系统
        export_options: {
          method: "app-store",
          provisioningProfiles: {
            "com.yoshinoya.ios" => "Yoshinoya_AppStore_2025_connect"
          },
          teamID: "786Q353TMH"
        }
      )

      # 4. 彻底清理 IPA 文件中的隐藏文件
      ipa_path = "#{Dir.pwd}/../../build/Yoshinoya_AppStore.ipa"
      temp_dir = "#{Dir.pwd}/../../build/temp_cleanup"

      # 创建临时目录
      sh("rm -rf #{temp_dir}")
      sh("mkdir -p #{temp_dir}")

      # 检查 IPA 文件是否存在
      if File.exist?(ipa_path)
        # 解压 IPA
        sh("unzip -q \"#{ipa_path}\" -d \"#{temp_dir}\"")

        # 删除所有隐藏文件
        sh("find \"#{temp_dir}\" -name '._*' -type f -delete")
        sh("find \"#{temp_dir}\" -name '.DS_Store' -type f -delete")

        # 特别检查 Symbols 目录
        symbols_dir = "#{temp_dir}/Payload/Yoshinoya.app/Symbols"
        if File.directory?(symbols_dir)
          sh("find \"#{symbols_dir}\" -name '._*' -type f -delete")
        end

        # 备份原始 IPA
        sh("cp \"#{ipa_path}\" \"#{Dir.pwd}/../../build/Yoshinoya_AppStore_backup.ipa\"")

        # 重新打包 IPA (使用 -X 选项排除 Mac 资源文件)
        sh("cd \"#{temp_dir}\" && /usr/bin/zip -X -r \"../clean.ipa\" ./*")

        # 替换原始 IPA
        sh("mv \"#{Dir.pwd}/../../build/clean.ipa\" \"#{ipa_path}\"")

        puts "IPA 文件成功清理"
      else
        puts "警告: IPA 文件不存在 (#{ipa_path})，跳过清理步骤"
      end

      # 5. 上传到 App Store
      upload_to_app_store(
        username: "<EMAIL>",
        app_identifier: "com.yoshinoya.ios",
        team_id: "786Q353TMH",
        ipa: ipa_path,
        skip_metadata: true,
        skip_screenshots: true,
        skip_app_version_update: true,
        precheck_include_in_app_purchases: false,
        submit_for_review: false,
        force: true,
        platform: "ios"
      )

      # 获取版本信息 - 修复路径
      begin
        # 尝试不同可能的Info.plist位置
        info_plist_paths = [
          "#{root_path}/uni-native-app/ios/HBuilder-Hello/HBuilder-Hello/Info.plist",
          "#{root_path}/uni-native-app/ios/HBuilder-Hello/Info.plist",
          "#{Dir.pwd}/../HBuilder-Hello/Info.plist",
          "#{Dir.pwd}/../Info.plist"
        ]

        version = "未知"
        build = "未知"

        # 尝试每个可能的路径
        info_plist_paths.each do |plist_path|
          if File.exist?(plist_path)
            puts "找到Info.plist文件: #{plist_path}"
            version = sh("/usr/libexec/PlistBuddy -c 'Print CFBundleShortVersionString' '#{plist_path}'").strip rescue "未知"
            build = sh("/usr/libexec/PlistBuddy -c 'Print CFBundleVersion' '#{plist_path}'").strip rescue "未知"
            break if version != "未知" && build != "未知"
          end
        end

        # 如果没有找到有效的版本号，使用build.gradle中的版本号
        if version == "未知"
          puts "无法从Info.plist获取版本信息，尝试从Xcode项目设置获取..."
          version = `xcodebuild -showBuildSettings -workspace "#{Dir.pwd}/../吉野家 Yoshinoya.xcworkspace" -scheme "Yoshinoya" | grep MARKETING_VERSION | sed 's/.*= //'`.strip rescue "3.17.0"
          build = `xcodebuild -showBuildSettings -workspace "#{Dir.pwd}/../吉野家 Yoshinoya.xcworkspace" -scheme "Yoshinoya" | grep CURRENT_PROJECT_VERSION | sed 's/.*= //'`.strip rescue "356"
        end

        puts "使用的版本号: #{version}, Build号: #{build}"
      rescue => e
        puts "获取版本信息失败: #{e.message}"
        version = "3.17.0"  # 使用默认版本号
        build = "356"       # 使用默认构建号
      end

      # 发送钉钉通知（成功）
      sh("cd #{root_path} && node -e \"const dingding = require('./help/utils/dingding'); dingding.noticeDingDingSuceess({
        appName: '吉野家 iOS 客户端',
        commitMessage: '#{ENV['commitMessage'] || 'App Store正式版本'}',
        commitUser: '#{ENV['commitUser'] || '未知'}',
        env: 'release',
        version: '#{version}',
        buildNumber: '#{build}',
        publishPath: 'App Store',
        isExtApp: false
      });\"")
    rescue => e
      puts "Error: #{e.message}"

      # 发送钉钉通知（失败）
      sh("cd #{root_path} && node -e \"const dingding = require('./help/utils/dingding'); dingding.noticeDingDingFail({
        appName: '吉野家 iOS 客户端'
      });\"")

      raise e
    end
  end

  desc "发布 UAT 环境到 TestFlight"
  lane :beta_uat do
    # 直接设置 Apple ID 应用特定密码
    ENV["FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD"] = "bgqj-fdtf-cpke-mcyg"

    # 设置环境变量禁止生成 ._* 文件
    ENV["COPYFILE_DISABLE"] = "1"
    ENV["COPYFILE_EXTENDED_ATTRIBUTES_DISABLE"] = "1"

    # 获取项目根目录的绝对路径
    root_path = File.expand_path("../../../../../", __FILE__)

    # 清理隐藏文件，特别是 ._Symbols
    sh("find #{root_path} -name '._*' -type f -delete")
    sh("find #{root_path} -name '.DS_Store' -type f -delete")
    puts "已清理隐藏文件"

    # 1. 先执行 HBuilderX 打包 app-plus，使用 UAT 环境参数
    sh("cd #{root_path} && npm run build:app-plus -- name=jiyejia_uat cmd=true robot=uat programType=order")

    # 2. 确保目标目录存在并复制文件
    target_dir = "#{root_path}/uni-native-app/ios/HBuilder-Hello/HBuilder-Hello/Pandora/apps/__UNI__8E0FD5A/www"
    sh("mkdir -p #{target_dir}")
    sh("rm -rf #{target_dir}/*")
    sh("cp -rv #{root_path}/dist/build/app-plus/* #{target_dir}/")

    # 再次清理构建文件中的隐藏文件
    sh("find #{target_dir} -name '._*' -type f -delete")
    puts "已清理构建目录的隐藏文件"

    # 确保 Pods 已经安装
    cocoapods(
      clean_install: true,
      podfile: "./Podfile"
    )

    # 3. 构建并上传到 TestFlight
    begin
      # 定义输出目录和文件名
      output_dir = "#{root_path}/uni-native-app/ios/build"
      output_name = "Yoshinoya_TestFlight_UAT.ipa"

      gym(
        workspace: "吉野家 Yoshinoya.xcworkspace",
        scheme: "Yoshinoya",
        export_method: "app-store",
        configuration: "Release",
        clean: true,
        output_directory: output_dir,
        output_name: output_name,
        xcargs: "-UseModernBuildSystem=YES",
        export_options: {
          method: "app-store",
          provisioningProfiles: {
            "com.yoshinoya.ios" => "Yoshinoya_AppStore_2025_connect"
          },
          teamID: "786Q353TMH"
        }
      )

      # 4. 彻底清理 IPA 文件中的隐藏文件
      ipa_path = "#{output_dir}/#{output_name}"
      temp_dir = "#{output_dir}/temp_cleanup"

      # 创建临时目录
      sh("rm -rf #{temp_dir}")
      sh("mkdir -p #{temp_dir}")

      # 检查 IPA 文件是否存在
      if File.exist?(ipa_path)
        puts "找到 IPA 文件：#{ipa_path}"
        # 解压 IPA
        sh("unzip -q \"#{ipa_path}\" -d \"#{temp_dir}\"")

        # 删除所有隐藏文件
        sh("find \"#{temp_dir}\" -name '._*' -type f -delete")
        sh("find \"#{temp_dir}\" -name '.DS_Store' -type f -delete")

        # 特别检查 Symbols 目录
        symbols_dir = "#{temp_dir}/Payload/Yoshinoya.app/Symbols"
        if File.directory?(symbols_dir)
          sh("find \"#{symbols_dir}\" -name '._*' -type f -delete")
        end

        # 备份原始 IPA
        sh("cp \"#{ipa_path}\" \"#{output_dir}/Yoshinoya_TestFlight_UAT_backup.ipa\"")

        # 重新打包 IPA (使用 -X 选项排除 Mac 资源文件)
        sh("cd \"#{temp_dir}\" && /usr/bin/zip -X -r \"../clean.ipa\" ./*")

        # 替换原始 IPA
        sh("mv \"#{output_dir}/clean.ipa\" \"#{ipa_path}\"")

        puts "IPA 文件成功清理：#{ipa_path}"
      else
        puts "警告: IPA 文件不存在 (#{ipa_path})，跳过清理步骤"
        raise "找不到 IPA 文件，构建可能失败"
      end

      # 5. 上传到 TestFlight
      puts "开始上传到 TestFlight: #{ipa_path}"
      upload_to_app_store(
        username: "<EMAIL>",
        app_identifier: "com.yoshinoya.ios",
        team_id: "786Q353TMH",
        ipa: ipa_path,
        skip_metadata: true,
        skip_screenshots: true,
        skip_app_version_update: true,
        precheck_include_in_app_purchases: false,
        submit_for_review: false,
        force: true,
        platform: "ios"
      )

      # 获取版本信息 - 修复路径
      begin
        # 尝试不同可能的Info.plist位置
        info_plist_paths = [
          "#{root_path}/uni-native-app/ios/HBuilder-Hello/HBuilder-Hello/Info.plist",
          "#{root_path}/uni-native-app/ios/HBuilder-Hello/Info.plist",
          "#{Dir.pwd}/../HBuilder-Hello/Info.plist",
          "#{Dir.pwd}/../Info.plist"
        ]

        version = "未知"
        build = "未知"

        # 尝试每个可能的路径
        info_plist_paths.each do |plist_path|
          if File.exist?(plist_path)
            puts "找到Info.plist文件: #{plist_path}"
            version = sh("/usr/libexec/PlistBuddy -c 'Print CFBundleShortVersionString' '#{plist_path}'").strip rescue "未知"
            build = sh("/usr/libexec/PlistBuddy -c 'Print CFBundleVersion' '#{plist_path}'").strip rescue "未知"
            break if version != "未知" && build != "未知"
          end
        end

        # 如果没有找到有效的版本号，使用build.gradle中的版本号
        if version == "未知"
          puts "无法从Info.plist获取版本信息，尝试从Xcode项目设置获取..."
          version = `xcodebuild -showBuildSettings -workspace "#{Dir.pwd}/../吉野家 Yoshinoya.xcworkspace" -scheme "Yoshinoya" | grep MARKETING_VERSION | sed 's/.*= //'`.strip rescue "3.17.0"
          build = `xcodebuild -showBuildSettings -workspace "#{Dir.pwd}/../吉野家 Yoshinoya.xcworkspace" -scheme "Yoshinoya" | grep CURRENT_PROJECT_VERSION | sed 's/.*= //'`.strip rescue "356"
        end

        puts "使用的版本号: #{version}, Build号: #{build}"
      rescue => e
        puts "获取版本信息失败: #{e.message}"
        version = "3.17.0"  # 使用默认版本号
        build = "356"       # 使用默认构建号
      end

      # 发送钉钉通知（成功）
      sh("cd #{root_path} && node -e \"const dingding = require('./help/utils/dingding'); dingding.noticeDingDingSuceess({
        appName: '吉野家 iOS 客户端',
        commitMessage: '#{ENV['commitMessage'] || 'TestFlight UAT环境版本'}',
        commitUser: '#{ENV['commitUser'] || '未知'}',
        env: 'uat',
        version: '#{version}',
        buildNumber: '#{build}',
        publishPath: 'TestFlight测试平台',
        isExtApp: false
      });\"")
    rescue => e
      puts "Error: #{e.message}"

      # 发送钉钉通知（失败）
      sh("cd #{root_path} && node -e \"const dingding = require('./help/utils/dingding'); dingding.noticeDingDingFail({
        appName: '吉野家 iOS 客户端'
      });\"")

      raise e
    end
  end

  desc "上传已修复的 IPA 文件"
  lane :upload_fixed_ipa do
    # 直接设置 Apple ID 应用特定密码
    ENV["FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD"] = "bgqj-fdtf-cpke-mcyg"

    # 上传到 App Store
    upload_to_app_store(
      username: "<EMAIL>",
      app_identifier: "com.yoshinoya.ios",
      team_id: "786Q353TMH",
      ipa: "../build/Yoshinoya_AppStore.ipa",  # 使用已修复的 IPA
      skip_metadata: true,
      skip_screenshots: true,
      skip_app_version_update: true,
      precheck_include_in_app_purchases: false,
      submit_for_review: false,
      force: true,
      platform: "ios"
    )
  end

  desc "清理构建产物"
  lane :clean do
    # 清理构建目录
    sh("rm -rf ../build/*")

    # 清理 Xcode 派生数据
    sh("rm -rf ~/Library/Developer/Xcode/DerivedData/*")

    # 清理 iOS 模拟器缓存
    sh("xcrun simctl delete unavailable")

    # 清理下载的 pods
    sh("rm -rf ~/Library/Caches/CocoaPods/*")

    # 清理 fastlane 缓存
    sh("rm -rf ~/Library/Caches/fastlane/*")
  end
end
