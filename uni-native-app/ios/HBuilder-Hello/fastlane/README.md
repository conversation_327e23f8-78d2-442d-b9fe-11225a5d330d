fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## iOS

### ios deploy_uat

```sh
[bundle exec] fastlane ios deploy_uat
```

部署 UAT 版本到蒲公英

### ios deploy_prod

```sh
[bundle exec] fastlane ios deploy_prod
```

部署生产版本到蒲公英

### ios beta

```sh
[bundle exec] fastlane ios beta
```

发布 TestFlight 测试版

### ios release

```sh
[bundle exec] fastlane ios release
```

发布到 App Store

### ios beta_uat

```sh
[bundle exec] fastlane ios beta_uat
```

发布 UAT 环境到 TestFlight

### ios upload_fixed_ipa

```sh
[bundle exec] fastlane ios upload_fixed_ipa
```

上传已修复的 IPA 文件

### ios clean

```sh
[bundle exec] fastlane ios clean
```

清理构建产物

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
