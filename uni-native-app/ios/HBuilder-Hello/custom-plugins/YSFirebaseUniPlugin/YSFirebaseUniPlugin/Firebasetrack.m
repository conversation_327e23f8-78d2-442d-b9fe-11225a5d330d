#import "Firebasetrack.h"
#import <FirebaseCore/FirebaseCore.h>  // 确保导入 Firebase Core

@implementation Firebasetrack

// Firebase 初始化方法
UNI_EXPORT_METHOD(@selector(initFirebase:callback:))
- (void)initFirebase:(NSDictionary *)options callback:(UniModuleKeepAliveCallback)callback {
    @try {
        if (![FIRApp defaultApp]) {
            [FIRApp configure];  // 初始化 Firebase
        }
        if (callback) {
            callback(@{@"status": @"success", @"message": @"Firebase initialized successfully"}, NO);
        }
    } @catch (NSException *exception) {
        if (callback) {
            callback(@{@"status": @"error", @"message": exception.reason}, NO);
        }
    }
}

// 事件追踪方法
UNI_EXPORT_METHOD(@selector(trackEvent:callback:))
- (void)trackEvent:(NSDictionary *)options callback:(UniModuleKeepAliveCallback)callback {
    NSString *eventName = options[@"eventName"];
    NSString *paramName = options[@"paramName"];
    NSString *paramValue = options[@"paramValue"];
    
    if (eventName == nil || eventName.length == 0 || paramName == nil || paramName.length == 0 || paramValue == nil || paramValue.length == 0) {
        if (callback) {
            callback(@{@"status": @"error", @"message": @"Invalid event parameters"}, NO);
        }
        return;
    }

    @try {
        [FIRAnalytics logEventWithName:eventName parameters:@{paramName : paramValue}];
        if (callback) {
            callback(@{@"status": @"success", @"message": @"Event tracked successfully"}, NO);
        }
    } @catch (NSException *exception) {
        if (callback) {
            callback(@{@"status": @"error", @"message": exception.reason}, NO);
        }
    }
}

@end
