#import <Foundation/Foundation.h>
#import <FirebaseAnalytics/FirebaseAnalytics.h> // 导入 Firebase Analytics 头文件
#import "DCUniModule.h" // 引入 UniApp 插件框架头文件

NS_ASSUME_NONNULL_BEGIN

@interface Firebasetrack : DCUniModule

// 方法声明
- (void)initFirebase:(NSDictionary *)options callback:(UniModuleKeepAliveCallback)callback;
- (void)trackEvent:(NSDictionary *)options callback:(UniModuleKeepAliveCallback)callback;  // 修改为 trackEvent

@end

NS_ASSUME_NONNULL_END
