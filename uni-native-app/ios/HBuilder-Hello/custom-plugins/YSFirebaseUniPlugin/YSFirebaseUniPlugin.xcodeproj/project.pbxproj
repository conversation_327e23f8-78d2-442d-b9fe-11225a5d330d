// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A51244FA2D07DCC40010F57F /* Firebase.h in Frameworks */ = {isa = PBXBuildFile; fileRef = A51244F92D07DCC40010F57F /* Firebase.h */; };
		A5B4AC642D02EE2800F324B7 /* YSFirebaseUniPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = A5B4AC632D02EE2800F324B7 /* YSFirebaseUniPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		A5B4AC822D03008700F324B7 /* Firebasetrack.m in Sources */ = {isa = PBXBuildFile; fileRef = A5B4AC812D03008700F324B7 /* Firebasetrack.m */; };
		A5B4AC832D03008700F324B7 /* Firebasetrack.h in Headers */ = {isa = PBXBuildFile; fileRef = A5B4AC802D03008700F324B7 /* Firebasetrack.h */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A51244F92D07DCC40010F57F /* Firebase.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = Firebase.h; path = ../../../SDK/Libs/Firebase.h; sourceTree = "<group>"; };
		A5B4AC602D02EE2800F324B7 /* YSFirebaseUniPlugin.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = YSFirebaseUniPlugin.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A5B4AC632D02EE2800F324B7 /* YSFirebaseUniPlugin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = YSFirebaseUniPlugin.h; sourceTree = "<group>"; };
		A5B4AC802D03008700F324B7 /* Firebasetrack.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Firebasetrack.h; sourceTree = "<group>"; };
		A5B4AC812D03008700F324B7 /* Firebasetrack.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Firebasetrack.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A5B4AC5D2D02EE2800F324B7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A51244FA2D07DCC40010F57F /* Firebase.h in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A51244CF2D068A060010F57F /* Framework */ = {
			isa = PBXGroup;
			children = (
			);
			name = Framework;
			sourceTree = "<group>";
		};
		A51244EE2D06E6650010F57F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				A51244F92D07DCC40010F57F /* Firebase.h */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A5B4AC562D02EE2800F324B7 = {
			isa = PBXGroup;
			children = (
				A51244CF2D068A060010F57F /* Framework */,
				A5B4AC622D02EE2800F324B7 /* YSFirebaseUniPlugin */,
				A5B4AC612D02EE2800F324B7 /* Products */,
				A51244EE2D06E6650010F57F /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		A5B4AC612D02EE2800F324B7 /* Products */ = {
			isa = PBXGroup;
			children = (
				A5B4AC602D02EE2800F324B7 /* YSFirebaseUniPlugin.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A5B4AC622D02EE2800F324B7 /* YSFirebaseUniPlugin */ = {
			isa = PBXGroup;
			children = (
				A5B4AC632D02EE2800F324B7 /* YSFirebaseUniPlugin.h */,
				A5B4AC802D03008700F324B7 /* Firebasetrack.h */,
				A5B4AC812D03008700F324B7 /* Firebasetrack.m */,
			);
			path = YSFirebaseUniPlugin;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		A5B4AC5B2D02EE2800F324B7 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A5B4AC642D02EE2800F324B7 /* YSFirebaseUniPlugin.h in Headers */,
				A5B4AC832D03008700F324B7 /* Firebasetrack.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		A5B4AC5F2D02EE2800F324B7 /* YSFirebaseUniPlugin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A5B4AC672D02EE2800F324B7 /* Build configuration list for PBXNativeTarget "YSFirebaseUniPlugin" */;
			buildPhases = (
				A5B4AC5B2D02EE2800F324B7 /* Headers */,
				A5B4AC5C2D02EE2800F324B7 /* Sources */,
				A5B4AC5D2D02EE2800F324B7 /* Frameworks */,
				A5B4AC5E2D02EE2800F324B7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = YSFirebaseUniPlugin;
			productName = YSFirebaseUniPlugin;
			productReference = A5B4AC602D02EE2800F324B7 /* YSFirebaseUniPlugin.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A5B4AC572D02EE2800F324B7 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1540;
				TargetAttributes = {
					A5B4AC5F2D02EE2800F324B7 = {
						CreatedOnToolsVersion = 15.4;
					};
				};
			};
			buildConfigurationList = A5B4AC5A2D02EE2800F324B7 /* Build configuration list for PBXProject "YSFirebaseUniPlugin" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A5B4AC562D02EE2800F324B7;
			productRefGroup = A5B4AC612D02EE2800F324B7 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A5B4AC5F2D02EE2800F324B7 /* YSFirebaseUniPlugin */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A5B4AC5E2D02EE2800F324B7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A5B4AC5C2D02EE2800F324B7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A5B4AC822D03008700F324B7 /* Firebasetrack.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A5B4AC652D02EE2800F324B7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A5B4AC662D02EE2800F324B7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		A5B4AC682D02EE2800F324B7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 786Q353TMH;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/../../../SDK/Libs/**",
					"$(PROJECT_DIR)/**",
					"$(DEVELOPER_FRAMEWORKS_DIR)/**",
					"$(DEVELOPER_FRAMEWORKS_DIR)/**",
				);
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"\"$(SRCROOT)/../../../SDK/Libs\"/**",
					"\"$(SRCROOT)/../../../SDK/inc\"/**",
					"\"$(SRCROOT)/../../../SDK/Libs/Firebase.h\"/**",
				);
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = (
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SRCROOT)/../../../SDK/Libs",
					"$(PROJECT_DIR)",
				);
				MACH_O_TYPE = staticlib;
				MACOSX_DEPLOYMENT_TARGET = 14.5;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.yoshiclub.ios.YSFirebaseUniPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = auto;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A5B4AC692D02EE2800F324B7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALLOW_TARGET_PLATFORM_SPECIALIZATION = YES;
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "Apple Development";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 786Q353TMH;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/../../../SDK/Libs/**",
					"$(PROJECT_DIR)/**",
					"$(DEVELOPER_FRAMEWORKS_DIR)/**",
					"$(DEVELOPER_FRAMEWORKS_DIR)/**",
				);
				GENERATE_INFOPLIST_FILE = YES;
				HEADER_SEARCH_PATHS = (
					"\"$(SRCROOT)/../../../SDK/Libs\"/**",
					"\"$(SRCROOT)/../../../SDK/inc\"/**",
					"\"$(SRCROOT)/../../../SDK/Libs/Firebase.h\"/**",
				);
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = (
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SRCROOT)/../../../SDK/Libs",
					"$(PROJECT_DIR)",
				);
				MACH_O_TYPE = staticlib;
				MACOSX_DEPLOYMENT_TARGET = 14.5;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = com.yoshiclub.ios.YSFirebaseUniPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = auto;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A5B4AC5A2D02EE2800F324B7 /* Build configuration list for PBXProject "YSFirebaseUniPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A5B4AC652D02EE2800F324B7 /* Debug */,
				A5B4AC662D02EE2800F324B7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A5B4AC672D02EE2800F324B7 /* Build configuration list for PBXNativeTarget "YSFirebaseUniPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A5B4AC682D02EE2800F324B7 /* Debug */,
				A5B4AC692D02EE2800F324B7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A5B4AC572D02EE2800F324B7 /* Project object */;
}
