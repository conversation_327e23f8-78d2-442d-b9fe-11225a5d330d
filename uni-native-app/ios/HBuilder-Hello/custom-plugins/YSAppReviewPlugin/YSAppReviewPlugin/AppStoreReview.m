#import "AppStoreReview.h"
#import <StoreKit/StoreKit.h>
#import <UIKit/UIKit.h>

@implementation AppStoreReview

// 应用内评分方法
UNI_EXPORT_METHOD(@selector(showReview:callback:))
- (void)showReview:(NSDictionary *)options callback:(UniModuleKeepAliveCallback)callback {
    if (@available(iOS 10.3, *)) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [SKStoreReviewController requestReview];
            if (callback) {
                callback(@{@"code": @0, @"message": @"success"}, NO);
            }
        });
    } else {
        // 对于不支持 SKStoreReviewController 的旧版本 iOS，回退到应用商店
        [self openStoreReview:options callback:callback];
    }
}

// 跳转到 App Store 评分页面
UNI_EXPORT_METHOD(@selector(openStoreReview:callback:))
- (void)openStoreReview:(NSDictionary *)options callback:(UniModuleKeepAliveCallback)callback {
    @try {
        NSString *appId = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleIdentifier"];
        NSString *urlString = [NSString stringWithFormat:@"itms-apps://itunes.apple.com/app/id%@?action=write-review", appId];
        NSURL *url = [NSURL URLWithString:urlString];

        dispatch_async(dispatch_get_main_queue(), ^{
            if ([[UIApplication sharedApplication] canOpenURL:url]) {
                if (@available(iOS 10.0, *)) {
                    [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
                } else {
                    [[UIApplication sharedApplication] openURL:url];
                }
                if (callback) {
                    callback(@{@"code": @0, @"message": @"success"}, NO);
                }
            } else {
                if (callback) {
                    callback(@{@"code": @-1, @"message": @"无法打开 App Store"}, NO);
                }
            }
        });
    } @catch (NSException *exception) {
        if (callback) {
            callback(@{@"code": @-1, @"message": exception.reason ?: @"未知错误"}, NO);
        }
    }
}

// 测试方法
UNI_EXPORT_METHOD(@selector(test:callback:))
- (void)test:(NSDictionary *)options callback:(UniModuleKeepAliveCallback)callback {
    if (callback) {
        callback(@{@"code": @0, @"message": @"Test successful"}, NO);
    }
}

@end