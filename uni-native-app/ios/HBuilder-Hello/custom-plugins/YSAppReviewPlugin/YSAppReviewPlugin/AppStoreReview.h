#import <Foundation/Foundation.h>
#import "DCUniModule.h" // 引入 UniApp 插件框架头文件

NS_ASSUME_NONNULL_BEGIN

@interface AppStoreReview : DCUniModule

// 方法声明
- (void)showReview:(NSDictionary *)options callback:(UniModuleKeepAliveCallback)callback;
- (void)openStoreReview:(NSDictionary *)options callback:(UniModuleKeepAliveCallback)callback;
- (void)test:(NSDictionary *)options callback:(UniModuleKeepAliveCallback)callback;

@end

NS_ASSUME_NONNULL_END