{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__8E0FD5A", "name": "吉野家", "version": {"name": "1.0.0", "code": "100"}, "description": "", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#fff"}, "usingComponents": true, "compatible": {"ignoreVersion": true}, "distribute": {"google": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "apple": {"dSYMs": false}, "plugins": {"ad": {}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "plugins": {}, "nativePlugins": {"JG-JCore": {"JPUSH_APPKEY_IOS": "", "JPUSH_CHANNEL_IOS": "", "JPUSH_APPKEY_ANDROID": "8e6e88f96769f8754ed76fc6", "JPUSH_CHANNEL_ANDROID": "", "__plugin_info__": {"name": "JG-JCore", "description": "极光推送JCore插件", "platforms": "Android,iOS", "url": "", "android_package_name": "com.yoshiclub.orderapp", "ios_bundle_id": "", "isCloud": false, "bought": -1, "pid": "", "parameters": {"JPUSH_APPKEY_IOS": {"des": "[iOS]极光portal配置应用信息时分配的AppKey", "key": "JCore:APP_KEY", "value": ""}, "JPUSH_CHANNEL_IOS": {"des": "[iOS]用于统计分发渠道，不需要可填默认值developer-default", "key": "JCore:CHANNEL", "value": ""}, "JPUSH_APPKEY_ANDROID": {"des": "[Android]极光portal配置应用信息时分配的AppKey", "key": "JPUSH_APPKEY", "value": ""}, "JPUSH_CHANNEL_ANDROID": {"des": "[Android]用于统计分发渠道，不需要可填默认值developer-default", "key": "JPUSH_CHANNEL", "value": ""}}}}, "JG-JPush": {"JPUSH_ISPRODUCTION_IOS": "", "JPUSH_ADVERTISINGID_IOS": "", "JPUSH_DEFAULTINITJPUSH_IOS": "", "JPUSH_OPPO_APPKEY": "", "JPUSH_OPPO_APPID": "", "JPUSH_OPPO_APPSECRET": "", "JPUSH_VIVO_APPKEY": "", "JPUSH_VIVO_APPID": "", "JPUSH_MEIZU_APPKEY": "", "JPUSH_MEIZU_APPID": "", "JPUSH_XIAOMI_APPKEY": "", "JPUSH_XIAOMI_APPID": "", "JPUSH_HUAWEI_APPID": "", "JPUSH_HONOR_APPID": "", "JPUSH_GOOGLE_API_KEY": "", "JPUSH_GOOGLE_APP_ID": "", "JPUSH_GOOGLE_PROJECT_NUMBER": "", "JPUSH_GOOGLE_PROJECT_ID": "", "JPUSH_GOOGLE_STORAGE_BUCKET": "", "__plugin_info__": {"name": "J<PERSON><PERSON><PERSON><PERSON>", "description": "极光推送Hbuilder插件", "platforms": "Android,iOS", "url": "", "android_package_name": "", "ios_bundle_id": "", "isCloud": false, "bought": -1, "pid": "", "parameters": {"JPUSH_ISPRODUCTION_IOS": {"des": "[iOS]是否是生产环境，是填true，不是填false或者不填", "key": "JPush:ISPRODUCTION", "value": ""}, "JPUSH_ADVERTISINGID_IOS": {"des": "[iOS]广告标识符（IDFA）如果不需要使用IDFA，可不填", "key": "JPush:ADVERTISINGID", "value": ""}, "JPUSH_DEFAULTINITJPUSH_IOS": {"des": "[iOS]是否默认初始化，是填true，不是填false或者不填", "key": "JPush:DEFAULTINITJPUSH", "value": ""}, "JPUSH_OPPO_APPKEY": {"des": "厂商OPPO-appkey,示例：***********", "key": "OPPO_APPKEY", "value": ""}, "JPUSH_OPPO_APPID": {"des": "厂商OPPO-appId,示例：***********", "key": "OPPO_APPID", "value": ""}, "JPUSH_OPPO_APPSECRET": {"des": "厂商OPPO-appSecret,示例：***********", "key": "OPPO_APPSECRET", "value": ""}, "JPUSH_VIVO_APPKEY": {"des": "厂商VIVO-appkey,示例：12345678", "key": "com.vivo.push.api_key", "value": ""}, "JPUSH_VIVO_APPID": {"des": "厂商VIVO-appId,示例：12345678", "key": "com.vivo.push.app_id", "value": ""}, "JPUSH_MEIZU_APPKEY": {"des": "厂商MEIZU-<PERSON><PERSON><PERSON>,示例：MZ-12345678", "key": "MEIZU_APPKEY", "value": ""}, "JPUSH_MEIZU_APPID": {"des": "厂商MEIZU-appId,示例：MZ-12345678", "key": "MEIZU_APPID", "value": ""}, "JPUSH_XIAOMI_APPKEY": {"des": "厂商XIAOMI-<PERSON><PERSON><PERSON>,示例：MI-12345678", "key": "XIAOMI_APPKEY", "value": ""}, "JPUSH_XIAOMI_APPID": {"des": "厂商XIAOMI-appId,示例：MI-12345678", "key": "XIAOMI_APPID", "value": ""}, "JPUSH_HUAWEI_APPID": {"des": "厂商HUAWEI-appId,示例：appid=12346578", "key": "com.huawei.hms.client.appid", "value": ""}, "JPUSH_HONOR_APPID": {"des": "厂商HONOR-appId,示例：12346578", "key": "com.hihonor.push.app_id", "value": ""}, "JPUSH_GOOGLE_API_KEY": {"des": "厂商google api_key,示例:g-12346578", "key": "google_api_key", "value": ""}, "JPUSH_GOOGLE_APP_ID": {"des": "厂商google mobilesdk_app_id,示例：g-12346578", "key": "google_app_id", "value": ""}, "JPUSH_GOOGLE_PROJECT_NUMBER": {"des": "厂商google project_number,示例：g-12346578", "key": "gcm_defaultSenderId", "value": ""}, "JPUSH_GOOGLE_PROJECT_ID": {"des": "厂商google project_id ,示例：g-12346578", "key": "project_id", "value": ""}, "JPUSH_GOOGLE_STORAGE_BUCKET": {"des": "厂商google storage_bucket,示例：g-12346578", "key": "google_storage_bucket", "value": ""}}}}}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#fff", "bottom": {"offset": "auto"}}, "uni-app": {"compilerVersion": "4.15", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "tabBar": {"textColor": "#cccccc", "selectedColor": "#333333", "backgroundColor": "#fff", "custom": true, "list": [{"pagePath": "pages/home/<USER>", "iconPath": "static/tabbar/home.png", "selectedIconPath": "static/tabbar/home-active.png", "text": "首頁"}, {"pagePath": "pages/store/goods", "iconPath": "static/tabbar/drink.png", "selectedIconPath": "/static/tabbar/drink-active.png", "text": "點餐"}, {"pagePath": "pages/integralMall/index", "iconPath": "static/tabbar/qrcode.png", "selectedIconPath": "/static/tabbar/qrcode.png", "text": ""}, {"pagePath": "pages/integralMall/index", "iconPath": "static/tabbar/premium.png", "selectedIconPath": "static/tabbar/premium-active.png", "text": "獎賞"}, {"pagePath": "pages/member/mine/index", "iconPath": "static/tabbar/user.png", "selectedIconPath": "static/tabbar/user-active.png", "text": "我的"}], "borderStyle": "rgba(0,0,0,0.4)", "height": "50px", "child": ["lauchwebview"], "selected": 0}, "launch_path": "__uniappview.html"}}