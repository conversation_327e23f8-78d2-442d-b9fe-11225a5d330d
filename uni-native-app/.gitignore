# Dependencies
node_modules/
package-lock.json
yarn.lock
pnpm-lock.yaml
.npm
.yarn
.pnp.*

# Build output
dist/
build/
out/
.unpackage/
.hbuilderx/

# Local env files
.env.local
.env.*.local
.env.development
.env.production

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
*.log

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw*
.project
.classpath
.settings/
*.sublime-workspace
*.sublime-project

# System files
.DS_Store
Thumbs.db
desktop.ini

# iOS
ios/Pods/
ios/build/
ios/*.xcworkspace/
ios/*.xcodeproj/
ios/*.pbxuser
ios/*.mode1v3
ios/*.mode2v3
ios/*.perspectivev3
ios/*.xccheckout
ios/*.moved-aside
ios/DerivedData/
ios/.hmap
ios/.svn/
ios/.git/
ios/.gitignore
ios/.fuse_hidden*
ios/.DS_Store
ios/.AppleDouble
ios/.LSOverride
ios/._*
ios/Bundles
ios/build
ios/SDK


# Android
android/.gradle/
android/build/
android/app/build/
android/local.properties
android/*.iml
android/.idea/
android/.settings/
android/.project
android/.classpath
android/.externalNativeBuild/
android/captures/
android/gradlew
android/gradlew.bat
android/gradle/wrapper/gradle-wrapper.jar
android/.DS_Store
android/.local/
android/.fuse_hidden*
android/.AppleDouble
android/.LSOverride
android/._*
android/app/src/main/assets/
android/app/libs
android/app/release



# Uni-app specific
src/manifest.json
.gitlab-ci.yml
preview-qrcode.jpg
distinctive_1/*
