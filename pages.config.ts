import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: 'unibest',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  tabBar: {
    custom: true,
    color: '#999999',
    selectedColor: '#018d71',
    backgroundColor: '#F8F8F8',
    borderStyle: 'black',
    height: '80px',
    fontSize: '40px',
    iconWidth: '40px',
    spacing: '3px',
    list: [
      // 注意tabbar路由需要使用 layout:tabbar 布局
      {
        pagePath: 'pages/index/index',
        // text: '订单',
        icon: '/static/tabbar/order.png',
        iconSelected: '/static/tabbar/order-selected.png',
        iconType: 'local',
      },
      // {
      //   pagePath: 'pages/about/about',
      //   // text: '堂食',
      //   icon: '/static/tabbar/dinein.png',
      //   iconSelected: '/static/tabbar/dinein-selected.png',
      //   iconType: 'local',
      // },
      {
        pagePath: 'pages/dishManagement/index',
        // text: '菜品',
        icon: '/static/tabbar/dishes.png',
        iconSelected: '/static/tabbar/dishes-selected.png',
        iconType: 'local',
      },
      {
        pagePath: 'pages/review/index',
        // text: '评价',
        icon: '/static/tabbar/review.png',
        iconSelected: '/static/tabbar/review-selected.png',
        iconType: 'local',
      },
      {
        pagePath: 'pages/storeDetail/index',
        // text: '门店',
        icon: '/static/tabbar/store.png',
        iconSelected: '/static/tabbar/store-selected.png',
        iconType: 'local',
      },
    ],
  },
})
