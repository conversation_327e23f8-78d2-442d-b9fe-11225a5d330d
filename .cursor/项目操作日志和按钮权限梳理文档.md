# 接单助手小程序 - 操作日志和按钮权限梳理文档

## 1. 操作日志系统

### 1.1 整体架构

操作日志系统通过 `static/js/utils/operationLog.js` 工具类实现，主要包含以下部分：

- **核心方法**: `reportOperation()` - 统一的操作日志上报方法
- **接口**: `user/v1/business/operateLog/saveBatch` - 批量保存操作日志
- **数据比较**: `compareOldAndNewData()` - 比较新旧数据，避免无效上报

### 1.2 支持的操作类型

#### 1.2.1 门店管理类

```javascript
// 门店营业状态
updateShopBusinessStatus: '修改门店营业状态';

// 门店营业时间
updataShopBusinessTime: '修改门店营业时间';

// 门店繁忙设置
updateShopBusyStatus: '修改门店繁忙设置';

// 门店电话
updateShopPhone: '修改门店电话';

// 自动接单规则
updateReceiveOrderMode: '修改自动接单规则';
```

#### 1.2.2 订单管理类

```javascript
// 订单接单
updateOrderAcceptance: '订单接单';

// 订单退款
updateOrderRefund: '订单退款';

// 订单取消
updateOrderCancel: '订单取消';

// 退款审核
updateOrderRefundAudit: '订单退款审核';

// 订单完成
updateOrderComplete: '订单完成';

// 配送管理
updateOrderCancelDeliver: '订单取消配送';
updateOrderCallDeliveryer: '订单通知配送';
updateOrderSetDeliverying: '订单设置配送中';
```

#### 1.2.3 商品管理类

```javascript
// 商品上下架
updateProductStatus: '修改商品上下架';
updateSaleProductStatus: '修改在售商品上下架';

// 库存管理
updateProductInventory: '在售商品修改库存';

// 售罄状态
updateProductSellOut: '在售商品修改售罄状态';
```

### 1.3 使用场景和代码示例

#### 1.3.1 门店营业状态修改

**文件**: `settingPage/businessStatus/index.js`

```javascript
import { ReportTypeEnum, reportOperation } from '../../static/js/utils/operationLog';

// 营业状态开关
reportOperation(ReportTypeEnum.updateShopBusinessStatus, {
  shopName: that.data.store.title,
  shopCode: that.data.store?.code || wx.getStorageSync('shopCode'),
  beforeValue: value ? '关闭' : '开启',
  afterValue: value ? '开启' : '关闭'
});
```

#### 1.3.2 商品上下架操作

**文件**: `pages/goods/index.js`

```javascript
// 商品状态变更
reportOperation(ReportTypeEnum.updateProductStatus, {
  shopName: app.globalData.shopTitle,
  beforeValue: soldOut == 1 ? '下架' : '上架',
  afterValue: soldOut == 1 ? '上架' : '下架',
  operateType: '修改',
  extension1: JSON.stringify({ spuCode, skuCode }),
  shopCode: that.data.options?.shopCode || wx.getStorageSync('shopCode'),
  shopId: that.data.options?.storeId || wx.getStorageSync('__sid'),
  modifyObject: `在售商品（全部渠道-${title}${skuName ? '-' + skuName : ''}）`
});
```

#### 1.3.3 订单操作日志

**文件**: `pages/order/index.js`

```javascript
// 订单完成
reportOperation(ReportTypeEnum.updateOrderComplete, {
  shopName: app.globalData.shopTitle,
  beforeValue: statuscn,
  afterValue: '已完成',
  operateType: '修改',
  modifyObject: `订单（${ordersn}）`
});

// 设置配送中
reportOperation(ReportTypeEnum.updateOrderSetDeliverying, {
  beforeValue: statuscn,
  afterValue: '配送中',
  modifyObject: `订单（${ordersn}）`
});
```

### 1.4 日志数据结构

```javascript
{
  appName: '接单助手',           // 应用名称
  operateMenu: '订单管理',       // 操作菜单
  operateEvent: '订单接单',      // 操作事件
  operateType: '修改',           // 操作类型
  operateTime: '2024-01-01 12:00:00', // 操作时间
  modifyMsg: '订单状态',         // 修改内容
  modifyObject: '订单（123456）', // 修改对象
  shopName: '门店名称',          // 门店名称
  shopId: 'shop_id',             // 门店ID
  shopCode: 'shop_code',         // 门店编码
  beforeValue: '待接单',         // 修改前值
  afterValue: '已接单',          // 修改后值
  extension1: '{"key":"value"}'  // 扩展字段
}
```

## 2. 按钮权限系统

### 2.1 整体架构

按钮权限系统基于角色和权限码进行控制，主要组件：

- **权限组件**: `components/AuthButton` - 统一的权限按钮组件
- **权限数据**: `app.globalData.permissions` - 全局权限数据
- **权限解析**: `util.parsePermissions()` - 权限数据解析方法
- **权限接口**: `mini-app/v1/manage/shop/access` - 获取用户权限列表

### 2.2 权限级别定义

```javascript
// permissions 字段值含义：
// 1: 只读权限（查看权限）
// 2: 操作权限（可编辑）
// 3: 完全权限（可编辑+删除）
```

### 2.3 AuthButton 组件使用

#### 2.3.1 导航类按钮

```xml
<AuthBtn bindcustomevent="onPermission"
         item="{{item}}"
         code="shop-statistics"
         url="pages/shop/statistics/index">
  <view class="btn-content">统计报表</view>
</AuthBtn>
```

#### 2.3.2 开关类按钮

```xml
<AuthBtn bindcustomevent="onPermission"
         type="switch"
         store="{{store}}"
         item="{{item}}"
         code="setting-business-status">
  <view class="switch-label">营业状态</view>
</AuthBtn>
```

### 2.4 主要权限码和控制场景

#### 2.4.1 门店管理权限

```javascript
// 门店基础信息
'setting-shop': {
  permissions: 2,  // 可编辑
  scenes: ['门店信息编辑', '门店电话修改']
}

// 营业状态设置
'setting-business-status': {
  permissions: 2,
  scenes: ['营业状态开关', '繁忙状态设置']
}

// 营业时间设置
'setting-business-time': {
  permissions: 2,
  scenes: ['营业时间配置', '特殊营业时间设置']
}

// 开店确认
'setting-shop-confirm': {
  permissions: 2,
  scenes: ['开店确认操作']
}
```

#### 2.4.2 商品管理权限

```javascript
// 商品管理
'shop-goods': {
  permissions: 3,
  scenes: ['商品列表查看', '商品编辑', '商品上下架']
}

// 在售商品管理
'shop-sale-goods': {
  permissions: 2,
  scenes: ['在售商品状态控制', '库存管理', '售罄设置']
}

// 商品组合套餐
'goods-group': {
  permissions: 2,
  scenes: ['套餐创建', '套餐编辑', '套餐管理']
}
```

#### 2.4.3 订单管理权限

```javascript
// 订单管理
'shop-order': {
  permissions: 3,
  scenes: ['订单查看', '订单接单', '订单退款', '订单取消']
}

// 配送管理
'delivery-manage': {
  permissions: 2,
  scenes: ['配送状态设置', '配送员分配', '配送取消']
}
```

#### 2.4.4 统计报表权限

```javascript
// 门店统计
'shop-statistics': {
  permissions: 1,  // 只读
  scenes: ['日结报表查看', '营业数据统计']
}
```

### 2.5 权限检查逻辑

#### 2.5.1 AuthButton 组件权限检查

```javascript
// components/AuthButton/index.js
onTap(e = {}) {
  const { authItem } = this.data;

  // 检查权限：无权限控制 或 有操作权限 或 有只读权限且允许只读操作
  if (authItem.noAuth ||
      [2, 3].includes(authItem.permissions) ||
      (authItem.readAuth && authItem.permissions === 1)) {

    // 执行操作
    this.triggerEvent('customevent', eventData);
  } else {
    // 无权限提示
    wx.showToast({
      title: '暂无操作权限，请先开通！',
      icon: 'none'
    });
  }
}
```

#### 2.5.2 页面级权限控制

```javascript
// pages/shop/home.js
setIsLoadedPermissions() {
  if (app.globalData.isLoadedPermissions) {
    this.setData({
      isLoadedPermissions: true,
      isShowShopStatistics: app.globalData.permissions['shop-statistics']
    });
  } else {
    // 权限未加载完成时的回调处理
    app.globalData.permissionsCallback.push(() => {
      this.setData({
        isLoadedPermissions: true,
        isShowShopStatistics: app.globalData.permissions['shop-statistics']
      });
    });
  }
}
```

### 2.6 权限数据流

#### 2.6.1 权限获取流程

```javascript
// static/js/app.js
async queryPermissions() {
  return new Promise((resolve, reject) => {
    util.javaRequest({
      url: app.api.auth.getPermissionListJava,
      method: 'GET',
      data: { phone: clerkInfo.mobile },
      success: async (res) => {
        if (res.errno == '0') {
          const { userAccessVo = {} } = res.data || {};
          if (userAccessVo && userAccessVo.accessSet) {
            const { btn } = util.parsePermissions(userAccessVo.accessSet);
            app.globalData.permissions = btn;
            app.globalData.isLoadedPermissions = true;

            // 执行权限加载完成的回调
            if (app.globalData.permissionsCallback &&
                app.globalData.permissionsCallback.length > 0) {
              for (const cb of app.globalData.permissionsCallback) {
                cb && (await cb());
              }
              app.globalData.permissionsCallback = [];
            }
          }
        }
      }
    });
  });
}
```

#### 2.6.2 权限数据解析

```javascript
// static/js/utils/util.js
util.parsePermissions = (accessSet) => {
  if (!accessSet || accessSet.length == 0) return {};
  let btn = {};
  accessSet.map((item) => {
    if (item.buttonDto) {
      let buttonDto = item.buttonDto;
      btn[buttonDto.code] = {
        permissions: item.permissions,
        ...buttonDto
      };
    }
  });
  return { btn };
};
```

### 2.7 权限控制的 WXML 模板使用

#### 2.7.1 条件渲染权限控制

```xml
<!-- pages/shop/home.wxml -->
<block wx:if="{{isLoadedPermissions}}">
  <block wx:for="{{gridList}}" wx:key="item">
    <AuthBtn wx:if="{{!item.hide}}"
             bindcustomevent="onPermission"
             item="{{item}}"
             url="{{item.url}}?storeId={{store.id}}">
      <view class="store-item">
        <view class="business-icon {{item.style}}"></view>
        <view class="store-title">{{item.title}}</view>
      </view>
    </AuthBtn>
  </block>
</block>
```

#### 2.7.2 开关组件权限控制

```xml
<!-- 营业状态开关，根据权限控制是否可操作 -->
<van-switch
  checked="{{store.is_in_business}}"
  disabled="{{authItem.permissions == 1}}"
  bind:change="onTap" />
```

## 3. 总结

### 3.1 操作日志特点

- **完整性**: 覆盖门店、订单、商品三大核心业务模块
- **可追溯**: 记录操作前后值变化，支持操作溯源
- **智能化**: 自动比较新旧数据，避免无效日志
- **标准化**: 统一的日志格式和上报方式

### 3.2 权限系统特点

- **组件化**: 通过 AuthButton 组件统一权限控制
- **层级化**: 支持只读、操作、完全三级权限
- **异步化**: 支持权限异步加载和回调处理
- **灵活性**: 支持页面级和组件级权限控制

### 3.3 最佳实践建议

1. **操作日志**: 在关键业务操作后及时调用 reportOperation
2. **权限控制**: 使用 AuthButton 组件替代原生按钮实现权限控制
3. **错误处理**: 权限不足时提供友好的提示信息
4. **性能优化**: 合理使用权限回调，避免不必要的重复检查
