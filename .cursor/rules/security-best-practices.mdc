---
description: 
globs: 
alwaysApply: true
---
# uni-super-shop-admin 安全与最佳实践规范

## Cursor 交互安全规范

### 🚨 严禁操作列表

#### 禁止执行的命令类型
```bash
# ❌ 严禁启动开发服务器
pnpm dev
pnpm serve  
npm run dev
yarn dev

# ❌ 严禁构建项目
pnpm build
npm run build
yarn build

# ❌ 严禁安装/更新依赖
pnpm install
pnpm add
npm install
yarn install

# ❌ 严禁运行任何脚本
pnpm start
npm start
node scripts/xxx.js

# ❌ 严禁启动端口监听
http-server
live-server
```

#### ✅ 允许的操作
- 代码分析和静态检查
- 文件读取、编辑、创建、删除
- 代码重构和优化建议
- 组件和页面生成
- 样式调整和修改
- 类型定义和接口设计

### 安全边界说明
- **Cursor AI 只能进行静态代码操作**
- **不能执行任何会启动进程的命令**
- **不能修改项目配置或依赖**
- **不能访问网络或外部服务**

## 代码安全规范

### 环境变量管理
```ts
// ✅ 正确：使用环境变量
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL
const APP_ENV = import.meta.env.VITE_APP_ENV

// ❌ 错误：硬编码敏感信息
const API_BASE_URL = 'https://api.production.com'
const API_KEY = 'sk-1234567890abcdef'
```

### 数据验证与过滤
```ts
// 用户输入验证
const validateUserInput = (input: string): boolean => {
  // 防止 XSS 攻击
  const dangerousChars = /<script|javascript:|data:/i
  if (dangerousChars.test(input)) {
    return false
  }
  
  // 长度限制
  if (input.length > 1000) {
    return false
  }
  
  return true
}

// 数据清理
const sanitizeHTML = (input: string): string => {
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
}
```

### 敏感信息处理
```ts
// ✅ 正确：不在日志中输出敏感信息
const login = async (credentials: LoginReq) => {
  try {
    console.log('开始登录流程') // 不输出用户名密码
    const result = await http.post('/api/login', credentials)
    return result
  } catch (error) {
    console.error('登录失败') // 不输出具体错误信息
    throw error
  }
}

// ❌ 错误：在日志中暴露敏感信息
const login = async (credentials: LoginReq) => {
  console.log('登录参数:', credentials) // 会暴露密码
  const result = await http.post('/api/login', credentials)
  console.log('登录响应:', result) // 会暴露 token
  return result
}
```

## 网络安全规范

### HTTPS 强制使用
```ts
// ✅ 正确：确保 HTTPS
const validateURL = (url: string): boolean => {
  try {
    const urlObj = new URL(url)
    return urlObj.protocol === 'https:'
  } catch {
    return false
  }
}

// API 配置
const API_CONFIG = {
  baseURL: 'https://api.example.com', // 强制 HTTPS
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest' // 防 CSRF
  }
}
```

### 请求头安全
```ts
// http 工具中的安全配置
const secureHeaders = {
  'Content-Type': 'application/json',
  'X-Requested-With': 'XMLHttpRequest',
  'Cache-Control': 'no-cache',
  'Pragma': 'no-cache'
}

// 避免在 URL 中传递敏感参数
// ✅ 正确：通过 body 传递
const updatePassword = (data: { oldPassword: string; newPassword: string }) => {
  return http.post('/api/user/password', data)
}

// ❌ 错误：通过 URL 传递敏感信息
const updatePassword = (oldPwd: string, newPwd: string) => {
  return http.get(`/api/user/password?old=${oldPwd}&new=${newPwd}`)
}
```

## App 端特殊安全规范

### 组件引入安全
```ts
// ✅ 正确：同步组件引入（App 端必须）
import UserProfile from '@/components/UserProfile/UserProfile.vue'
import OrderList from '@/components/OrderList/OrderList.vue'

// 在组件中注册
export default {
  components: {
    UserProfile,
    OrderList
  }
}

// ❌ 错误：异步组件引入（App 端会白屏）
const UserProfile = defineAsyncComponent(() => import('@/components/UserProfile/UserProfile.vue'))
const OrderList = () => import('@/components/OrderList/OrderList.vue')
```

### Store 使用安全
```ts
// ✅ 正确：在函数内部调用 Store
const handleUserLogin = async () => {
  const userStore = useUserStore() // 安全调用
  await userStore.login(loginForm.value)
}

onMounted(() => {
  const userStore = useUserStore() // 生命周期中调用是安全的
  userStore.initUserData()
})

// ❌ 错误：顶层调用 Store（App 端可能白屏）
import { useUserStore } from '@/store/user'
const userStore = useUserStore() // 危险操作

export const getUserInfo = () => {
  return userStore.userInfo // 可能导致白屏
}
```

### 平台差异处理
```ts
// 平台特性检测
const getPlatform = (): string => {
  // #ifdef H5
  return 'h5'
  // #endif
  
  // #ifdef MP-WEIXIN
  return 'mp-weixin'
  // #endif
  
  // #ifdef APP-PLUS
  return 'app-plus'
  // #endif
  
  return 'unknown'
}

// 平台特定的安全处理
const platformSafeOperation = () => {
  const platform = getPlatform()
  
  switch (platform) {
    case 'app-plus':
      // App 端特殊处理：禁用某些危险操作
      break
    case 'mp-weixin':
      // 小程序特殊处理：权限检查
      break
    case 'h5':
      // H5 特殊处理：跨域检查
      break
  }
}
```

## 性能与稳定性最佳实践

### 内存管理
```ts
// 组件销毁时清理资源
onUnmounted(() => {
  // 清理定时器
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
  
  // 清理事件监听
  uni.off('networkStatusChange', handleNetworkChange)
  
  // 清理大对象引用
  largeDataRef.value = null
})

// 避免内存泄漏的写法
const useTimer = () => {
  const timer = ref<NodeJS.Timeout | null>(null)
  
  const startTimer = () => {
    if (timer.value) clearInterval(timer.value)
    timer.value = setInterval(() => {
      // 定时器逻辑
    }, 1000)
  }
  
  const stopTimer = () => {
    if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
    }
  }
  
  onUnmounted(() => {
    stopTimer()
  })
  
  return { startTimer, stopTimer }
}
```

### 错误边界处理
```ts
// 全局错误处理
const setupGlobalErrorHandler = () => {
  // Vue 错误处理
  app.config.errorHandler = (error, instance, info) => {
    console.error('Vue 错误:', error, info)
    
    // 上报错误（生产环境）
    if (import.meta.env.PROD) {
      reportError({
        type: 'vue-error',
        error: error.message,
        stack: error.stack,
        info
      })
    }
  }
  
  // Promise 未捕获错误
  window.addEventListener('unhandledrejection', (event) => {
    console.error('未捕获的 Promise 错误:', event.reason)
    
    if (import.meta.env.PROD) {
      reportError({
        type: 'promise-error',
        error: event.reason
      })
    }
  })
}

// 组件级错误处理
const safeAsyncOperation = async (operation: () => Promise<any>) => {
  try {
    return await operation()
  } catch (error) {
    console.error('操作失败:', error)
    
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    })
    
    throw error
  }
}
```

### 性能监控
```ts
// 页面性能监控
const performanceMonitor = {
  // 页面加载时间
  markPageStart() {
    performance.mark('page-start')
  },
  
  markPageEnd() {
    performance.mark('page-end')
    performance.measure('page-load', 'page-start', 'page-end')
    
    const measure = performance.getEntriesByName('page-load')[0]
    console.log(`页面加载耗时: ${measure.duration}ms`)
  },
  
  // API 请求时间
  async measureApiCall<T>(apiCall: () => Promise<T>): Promise<T> {
    const start = performance.now()
    try {
      const result = await apiCall()
      const end = performance.now()
      console.log(`API 请求耗时: ${end - start}ms`)
      return result
    } catch (error) {
      const end = performance.now()
      console.log(`API 请求失败耗时: ${end - start}ms`)
      throw error
    }
  }
}
```

## 代码质量保障

### TypeScript 严格模式
```ts
// tsconfig.json 严格配置
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true
  }
}

// 代码中避免 any 类型
// ❌ 错误
const processData = (data: any) => {
  return data.someProperty
}

// ✅ 正确
interface DataType {
  someProperty: string
  otherProperty?: number
}

const processData = (data: DataType) => {
  return data.someProperty
}
```

### ESLint 规则遵循
```ts
// 推荐的代码风格
// ✅ 正确：使用 const/let，避免 var
const API_URL = 'https://api.example.com'
let currentUser: User | null = null

// ✅ 正确：明确的函数返回类型
const calculateTotal = (items: CartItem[]): number => {
  return items.reduce((sum, item) => sum + item.price * item.quantity, 0)
}

// ✅ 正确：避免魔法数字
const MAX_RETRY_COUNT = 3
const CACHE_EXPIRE_TIME = 5 * 60 * 1000 // 5分钟

// ✅ 正确：使用有意义的变量名
const isUserLoggedIn = checkUserLoginStatus()
const formattedOrderDate = formatDate(order.createTime)
```

## 代码审查检查清单

### 安全检查
- [ ] 没有硬编码的敏感信息（密码、密钥、URL）
- [ ] 用户输入已进行验证和过滤
- [ ] 网络请求使用 HTTPS
- [ ] 没有在日志中输出敏感信息
- [ ] App 端使用同步组件引入
- [ ] Store 调用位置安全（非顶层）

### 性能检查
- [ ] 避免不必要的重复渲染
- [ ] 长列表使用虚拟滚动或分页
- [ ] 图片资源已优化压缩
- [ ] 合理使用缓存策略
- [ ] 避免内存泄漏（定时器、事件监听器清理）

### 代码质量检查
- [ ] TypeScript 类型定义完整
- [ ] 函数职责单一，复杂度可控
- [ ] 变量命名有意义
- [ ] 注释充分且准确
- [ ] 错误处理完善
- [ ] 测试覆盖充分

### 兼容性检查
- [ ] 多端兼容性测试通过
- [ ] 不同设备尺寸适配正常
- [ ] 安全区域处理正确
- [ ] 平台特性正确使用

## 部署安全

### 环境配置
```ts
// .env.production
VITE_API_BASE_URL=https://api.production.com
VITE_APP_ENV=production
VITE_LOG_LEVEL=error

// .env.development  
VITE_API_BASE_URL=https://api.dev.com
VITE_APP_ENV=development
VITE_LOG_LEVEL=debug
```

### 构建优化
```ts
// vite.config.ts 生产配置
export default defineConfig({
  build: {
    // 移除 console
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    
    // 代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'pinia'],
          utils: ['@/utils/http', '@/utils/cache']
        }
      }
    }
  }
})
```

## 监控与日志

### 错误上报
```ts
interface ErrorReport {
  type: string
  message: string
  stack?: string
  url: string
  timestamp: number
  userAgent: string
  userId?: string
}

const reportError = (error: Partial<ErrorReport>) => {
  // 仅在生产环境上报
  if (import.meta.env.PROD) {
    const report: ErrorReport = {
      type: error.type || 'unknown',
      message: error.message || 'Unknown error',
      stack: error.stack,
      url: window.location.href,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      userId: getCurrentUserId()
    }
    
    // 发送到错误监控服务
    fetch('/api/error-report', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(report)
    }).catch(() => {
      // 静默处理上报失败
    })
  }
}
```

### 性能监控
```ts
const reportPerformance = () => {
  if (!window.performance) return
  
  const timing = window.performance.timing
  const metrics = {
    // 页面加载时间
    pageLoad: timing.loadEventEnd - timing.navigationStart,
    // DNS 查询时间
    dnsLookup: timing.domainLookupEnd - timing.domainLookupStart,
    // TCP 连接时间
    tcpConnect: timing.connectEnd - timing.connectStart,
    // 首字节时间
    ttfb: timing.responseStart - timing.navigationStart,
    // DOM 解析时间
    domParse: timing.domContentLoadedEventEnd - timing.domLoading
  }
  
  // 上报性能数据
  if (import.meta.env.PROD) {
    fetch('/api/performance-report', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(metrics)
    }).catch(() => {
      // 静默处理上报失败
    })
  }
}
```

## 最佳实践总结

### 开发阶段
1. **严格遵循类型安全**：使用 TypeScript 严格模式
2. **代码安全审查**：避免硬编码敏感信息
3. **性能优先考虑**：避免不必要的重复渲染
4. **错误处理完善**：每个可能失败的操作都要有错误处理

### 测试阶段
1. **多端兼容性测试**：H5、App、小程序
2. **安全性测试**：输入验证、XSS 防护
3. **性能测试**：页面加载速度、内存使用
4. **用户体验测试**：流程完整性、错误反馈

### 部署阶段
1. **环境隔离**：开发、测试、生产环境配置分离
2. **敏感信息保护**：使用环境变量管理密钥
3. **监控部署**：错误监控、性能监控
4. **回滚预案**：部署失败时的快速回滚方案
