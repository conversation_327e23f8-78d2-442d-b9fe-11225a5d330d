---
description: 
globs: 
alwaysApply: true
---
# 开发工作流规范

## Git 工作流

### 分支管理策略

```
main          # 主分支，保持稳定，用于生产部署
├── dev       # 开发分支，集成所有功能分支
├── feature/* # 功能分支，开发新功能
├── bugfix/*  # 修复分支，修复已知问题
├── hotfix/*  # 热修复分支，紧急修复生产问题
└── release/* # 发布分支，准备发布版本
```

### 分支命名规范

```bash
# 功能分支
feature/user-management
feature/order-system
feature/payment-integration

# 修复分支
bugfix/login-error
bugfix/ui-layout-issue

# 热修复分支
hotfix/critical-security-fix
hotfix/payment-failure

# 发布分支
release/v1.2.0
release/v2.0.0-beta
```

### 提交规范

#### Conventional Commits 格式

```bash
<type>(<scope>): <subject>

<body>

<footer>
```

#### 提交类型

```bash
feat:     # 新功能
fix:      # 修复bug
docs:     # 文档变更
style:    # 代码格式（不影响代码运行的变动）
refactor: # 重构（即不是新增功能，也不是修改bug的代码变动）
perf:     # 性能优化
test:     # 增加测试
chore:    # 构建过程或辅助工具的变动
ci:       # CI/CD 相关变更
build:    # 构建系统或外部依赖项的更改
```

#### 提交示例

```bash
# 新功能
feat(auth): add user login functionality

# 修复问题
fix(order): resolve payment calculation error

# 文档更新
docs(readme): update installation instructions

# 代码重构
refactor(components): extract common popup component

# 性能优化
perf(api): optimize user data fetching
```

### 使用 czg 进行规范化提交

```bash
# 安装 czg
pnpm add -D czg

# 使用 czg 提交
pnpm exec czg
# 或
npx czg
```

## 代码审查流程

### Pull Request 流程

#### 1. 创建功能分支

```bash
# 从 dev 分支创建功能分支
git checkout dev
git pull origin dev
git checkout -b feature/new-feature
```

#### 2. 开发和提交

```bash
# 开发完成后提交
git add .
git commit -m "feat(feature): implement new feature"
git push origin feature/new-feature
```

#### 3. 创建 Pull Request

```markdown
## 功能描述
简要描述本次开发的功能和变更。

## 变更类型
- [ ] 新功能
- [ ] 问题修复
- [ ] 文档更新
- [ ] 代码重构
- [ ] 性能优化

## 测试情况
- [ ] 已通过单元测试
- [ ] 已通过集成测试
- [ ] 已在开发环境验证
- [ ] 已在测试环境验证

## 检查清单
- [ ] 代码符合项目规范
- [ ] 已添加必要的注释
- [ ] 已更新相关文档
- [ ] 无新增的 ESLint 错误
- [ ] 无新增的 TypeScript 错误

## 截图/演示
如有 UI 变更，请提供截图或演示视频。

## 相关问题
关联的 Issue 或需求编号。
```

### 代码审查检查清单

#### 功能性检查
- [ ] 功能是否按需求正确实现
- [ ] 是否处理了异常情况
- [ ] 是否考虑了边界条件
- [ ] 用户交互是否友好

#### 代码质量检查
- [ ] 代码是否清晰易读
- [ ] 是否遵循项目编码规范
- [ ] 是否有适当的注释
- [ ] 是否存在代码重复

#### 安全性检查
- [ ] 是否有硬编码的敏感信息
- [ ] 用户输入是否经过验证
- [ ] 是否有潜在的安全漏洞
- [ ] 权限控制是否正确

#### 性能检查
- [ ] 是否有性能瓶颈
- [ ] 资源使用是否合理
- [ ] 是否有内存泄漏风险
- [ ] 网络请求是否优化

#### 兼容性检查
- [ ] 是否在目标平台测试通过
- [ ] 是否考虑了不同设备适配
- [ ] 是否兼容不同网络环境
- [ ] 是否处理了降级方案

## 测试规范

### 单元测试

#### 测试文件结构

```
src/
├── components/
│   ├── UserCard/
│   │   ├── UserCard.vue
│   │   ├── UserCard.test.ts
│   │   └── index.ts
└── utils/
    ├── formatDate.ts
    └── formatDate.test.ts
```

#### 测试示例

```ts
// src/utils/formatDate.test.ts
import { describe, it, expect } from 'vitest'
import { formatDate } from './formatDate'

describe('formatDate', () => {
  it('should format date correctly', () => {
    const date = new Date('2023-12-25T10:30:00')
    const result = formatDate(date, 'YYYY-MM-DD')
    expect(result).toBe('2023-12-25')
  })

  it('should handle invalid date', () => {
    const result = formatDate(null, 'YYYY-MM-DD')
    expect(result).toBe('')
  })

  it('should use default format', () => {
    const date = new Date('2023-12-25T10:30:00')
    const result = formatDate(date)
    expect(result).toBe('2023-12-25 10:30:00')
  })
})
```

### 组件测试

```ts
// src/components/UserCard/UserCard.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import UserCard from './UserCard.vue'

describe('UserCard', () => {
  it('should render user information', () => {
    const wrapper = mount(UserCard, {
      props: {
        user: {
          id: '1',
          name: '张三',
          email: '<EMAIL>'
        }
      }
    })

    expect(wrapper.text()).toContain('张三')
    expect(wrapper.text()).toContain('<EMAIL>')
  })

  it('should emit edit event when edit button clicked', async () => {
    const wrapper = mount(UserCard, {
      props: {
        user: { id: '1', name: '张三', email: '<EMAIL>' }
      }
    })

    await wrapper.find('.edit-button').trigger('click')
    expect(wrapper.emitted('edit')).toBeTruthy()
    expect(wrapper.emitted('edit')?.[0]).toEqual(['1'])
  })
})
```

### 端到端测试

```ts
// tests/e2e/login.spec.ts
import { test, expect } from '@playwright/test'

test('用户登录流程', async ({ page }) => {
  // 访问登录页面
  await page.goto('/login')

  // 填写登录表单
  await page.fill('[data-testid="username"]', 'testuser')
  await page.fill('[data-testid="password"]', 'password123')

  // 点击登录按钮
  await page.click('[data-testid="login-button"]')

  // 验证登录成功
  await expect(page).toHaveURL('/dashboard')
  await expect(page.locator('[data-testid="user-avatar"]')).toBeVisible()
})
```

## 构建与部署

### 环境管理

#### 环境配置文件

```bash
# .env.development
NODE_ENV=development
VUE_APP_API_BASE_URL=http://localhost:3000
VUE_APP_DEBUG=true

# .env.testing
NODE_ENV=testing
VUE_APP_API_BASE_URL=https://api-test.example.com
VUE_APP_DEBUG=true

# .env.production
NODE_ENV=production
VUE_APP_API_BASE_URL=https://api.example.com
VUE_APP_DEBUG=false
```

### 构建脚本

```json
{
  "scripts": {
    "dev": "uni",
    "build": "uni build",
    "build:h5": "uni build --platform h5",
    "build:mp-weixin": "uni build --platform mp-weixin",
    "build:app": "uni build --platform app",
    "type-check": "vue-tsc --noEmit",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "lint:style": "stylelint **/*.{vue,css,scss} --fix",
    "test": "vitest",
    "test:coverage": "vitest --coverage",
    "test:e2e": "playwright test"
  }
}
```

### 发布流程

#### 版本发布步骤

1. **代码审查通过**
   ```bash
   # 确保所有 PR 已合并到 dev 分支
   git checkout dev
   git pull origin dev
   ```

2. **创建发布分支**
   ```bash
   git checkout -b release/v1.2.0
   ```

3. **版本号更新**
   ```bash
   # 更新 package.json 中的版本号
   pnpm version 1.2.0
   ```

4. **运行完整测试**
   ```bash
   pnpm run lint
   pnpm run type-check
   pnpm run test
   pnpm run test:e2e
   ```

5. **构建生产版本**
   ```bash
   pnpm run build
   ```

6. **合并到主分支**
   ```bash
   git checkout main
   git merge release/v1.2.0
   git tag v1.2.0
   git push origin main --tags
   ```

7. **部署到生产环境**
   ```bash
   # 使用 CI/CD 系统自动部署
   # 或手动部署
   ```

## 持续集成/持续部署 (CI/CD)

### GitHub Actions 配置

```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [main, dev]
  pull_request:
    branches: [main, dev]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Run linting
        run: pnpm run lint

      - name: Run type checking
        run: pnpm run type-check

      - name: Run tests
        run: pnpm run test:coverage

      - name: Upload coverage
        uses: codecov/codecov-action@v3

  build:
    needs: test
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build project
        run: pnpm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: dist/
```

## 项目维护

### 定期维护任务

#### 每周任务
- [ ] 检查并更新依赖包
- [ ] 运行安全扫描
- [ ] 检查构建状态
- [ ] 清理过期分支

```bash
# 依赖更新
pnpm outdated
pnpm update

# 安全扫描
pnpm audit
pnpm audit fix

# 清理分支
git branch --merged | grep -v main | xargs git branch -d
```

#### 每月任务
- [ ] 性能分析和优化
- [ ] 代码质量报告
- [ ] 文档更新
- [ ] 备份重要数据

### 版本管理

#### 语义化版本 (Semantic Versioning)

```
MAJOR.MINOR.PATCH

1.0.0 → 1.0.1  # PATCH: 修复bug
1.0.1 → 1.1.0  # MINOR: 新增功能（向下兼容）
1.1.0 → 2.0.0  # MAJOR: 破坏性变更
```

#### 版本发布规律

```bash
# 开发版本
1.0.0-alpha.1
1.0.0-beta.1
1.0.0-rc.1

# 正式版本
1.0.0
1.0.1
1.1.0
2.0.0
```

## 团队协作

### 沟通规范

#### 代码讨论
- 在 PR 中进行技术讨论
- 使用 Issue 跟踪问题和需求
- 在代码中留下清晰的注释

#### 文档维护
- 及时更新 README.md
- 维护 CHANGELOG.md
- 编写组件使用文档

### 知识分享

#### 定期分享
- 技术方案分享
- 问题解决方案
- 最佳实践总结

#### 文档沉淀
- 技术文档归档
- 问题解决记录
- 开发经验总结

## 质量保障

### 代码质量指标

```bash
# 代码覆盖率目标
单元测试覆盖率: >= 80%
集成测试覆盖率: >= 60%
端到端测试覆盖率: >= 40%

# 代码质量目标
ESLint 错误: 0
TypeScript 错误: 0
重复代码率: < 5%
圈复杂度: < 10
```

### 性能指标

```bash
# 构建性能
构建时间: < 2分钟
包大小: < 2MB
首屏加载时间: < 3秒
页面响应时间: < 500ms
```

## 总结

1. **规范化流程**：严格遵循 Git 工作流和代码审查流程
2. **自动化测试**：完善的测试覆盖，确保代码质量
3. **持续集成**：自动化构建、测试和部署流程
4. **质量保障**：代码质量和性能指标监控
5. **团队协作**：良好的沟通和知识分享机制
6. **持续改进**：定期维护和优化开发流程
