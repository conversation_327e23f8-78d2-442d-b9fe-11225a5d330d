# AI 助手交互提示词模板指南

本文档提供了一个标准化的提示词模板，用于在编写复杂代码或组件时与 AI 助手进行高效交互。通过使用此模板，可以确保 AI 助手能够准确理解需求并提供符合项目规范的代码实现。

## 模板使用场景

此模板特别适用于以下场景：

- 开发复杂的组件或功能
- 实现多个功能点的业务逻辑
- 需要遵循项目规范的代码生成
- 将大型任务拆分为小功能包进行开发

## 提示词模板

```markdown
---
**任务概览**
---

**1. 本次任务目标 (Goal/Objective):**

- (清晰描述你希望通过这次交互完成的核心功能或组件。)
- 示例: "我需要创建一个渠道筛选组件，用于在商品列表页面根据不同的销售渠道过滤商品。"

**2. 上下文/背景 (Context/Background):**

- **所属模块/页面:** (例如: `pages-sub/goods-management/list/index.vue`)
- **父组件 (如有):** (例如: `GoodsListFilterSection`)
- **相关设计稿链接 (MasterGo):** (如有，请提供链接或关键截图说明)
- **相关后端接口文档/说明:** (如有，请提供)
- **项目规范相关章节:** (自动引用或提示你需要特别注意的 `project.mdc` 中的章节，例如：组件封装规范、 UnoCSS 使用规范、请求规范等)

---

## **功能点拆解与具体需求 (Specific Requirements/Functionality)**

**(针对当前小功能包/子任务)**

**3. 功能点 1: [功能点名称]**

- **输入 (Inputs):** (描述此功能需要什么数据作为输入？例如：`props`、用户操作、来自 store 的数据)
- **处理逻辑 (Processing):** (详细描述此功能的具体实现逻辑步骤。)
- **输出/行为 (Outputs/Side Effects):** (描述此功能执行后应产生什么结果？例如：`emit` 事件、更新 `ref`、调用 API、更新 store)
- **UI 表现:** (描述该功能点在界面上如何展示和交互，可结合 UnoCSS 原子类和 Wot Design Uni 组件进行描述)
- **示例:**
  - **功能点:** "渠道列表展示"
  - **输入:** `props.availableChannels` (类型: `Array<{id: string, name: string}>`)
  - **处理逻辑:** 遍历 `availableChannels`，为每个渠道渲染一个可选的标签。
  - **输出/行为:** 用户点击标签时，触发 `update:selectedChannels` 事件。
  - **UI 表现:** 使用 `wd-tag` 组件展示渠道，选中的渠道高亮。使用 `flex` 布局。

**4. 功能点 2: [功能点名称] (如有更多功能点，按此格式添加)**

- ...

---

## **技术栈与约束 (Technologies/Libraries & Constraints)**

**5. 必须使用的技术/库:**

- (根据 `project.mdc` 自动提示或你指定，例如：Vue3 Composition API, `<script setup lang="ts">`, Pinia, UnoCSS, Wot Design Uni)
- **特定 Wot Design Uni 组件:** (例如: `wd-popup`, `wd-checkbox-group`, `wd-button`)

**6. 文件结构与命名:**

- **组件文件:** (遵循 `project.mdc` 的 PascalCase 命名，如 `ChannelFilterPopup.vue`)
- **逻辑文件 (如适用):** (如 `channel-filter.ts`)
- **样式文件 (如适用):** (如 `channel-filter.scss`)
- **存放路径:** (例如: `src/components/ChannelFilterPopup/`)

**7. API 交互 (如有):**

- **相关 service 函数:** (例如: `src/service/channel/index.ts` 中的 `getChannelList`)
- **请求参数:**
- **响应数据结构及类型:**
- **错误处理:** (遵循 `project.mdc` 请求规范，特别是 401 处理)

**8. 状态管理 (Pinia Store, 如有):**

- **涉及 Store:** (例如: `useChannelStore`)
- **读取的 State/Getters:**
- **调用的 Actions:**
- **初始状态定义:** (确保 store 包含完整初始状态)

**9. 样式要求:**

- **主要方式:** (优先 UnoCSS 原子类)
- **SCSS 使用场景:** (复杂或重复样式组合，遵循 `project.mdc` 规范，如类名前缀)
- **设计稿像素转换:** (提示：MasterGo 1px = 代码 2rpx)

**10. 其他特殊要求/注意事项:** - (例如：性能考虑、可访问性、特定平台兼容性等) - (对我的代码风格、注释详细程度等有无特殊偏好？)

---

## **验收标准 (Definition of Done)**

**11. 如何判断此任务完成:** - (列出可测试的点，例如：渠道数据能正确加载并展示；选择渠道后，筛选条件能正确更新；UI 符合设计稿。)

---

## **我的具体问题 (Specific questions for the AI assistant)**

**12. 我希望你协助的具体方面:** - (例如："请帮我生成 `ChannelFilterPopup.vue` 的基本骨架，包含 props 定义和渠道列表的展示逻辑。") - (例如："我对如何组织 `handleChannelSelect` 这个函数逻辑有些疑问，请给出建议。") - (例如："请检查我下面的代码草稿是否符合项目规范 `project.mdc` 的要求。")
```

## 如何使用此模板

1. **复制模板**: 将上面的 Markdown 模板复制到一个你方便编辑的地方。
2. **填写信息**: 针对你当前要开发的复杂代码或组件，尽可能详细地填写模板中的各个部分。
   - **重点关注"功能点拆解"**: 这是你将大任务拆分为小功能包的核心体现。每个小功能包都可以套用这个模板的一部分来提问。
   - **参考 `project.mdc`**: 在填写时，主动回顾项目规范文档，确保你的需求描述与规范一致。
3. **清晰提问**: 在"我的具体问题"部分，明确指出你需要AI助手提供什么帮助。
4. **迭代交互**: AI助手会根据你提供的信息给出代码、建议或方案。你可以在此基础上进一步提问，或者针对新的小功能点再次使用这个模板。

## 使用此模板的好处

- **提高沟通效率**: 标准化的信息输入能让AI助手更快、更准确地理解你的需求。
- **确保规范落地**: 模板中包含了对项目规范 (`project.mdc`) 的提示，有助于在开发初期就遵循规范。
- **聚焦小型任务**: 非常适合你将复杂功能拆解成小包的开发习惯。每次可以针对一个小包进行提问。
- **结构化思考**: 填写模板的过程本身也能帮助你梳理思路，确保考虑周全。
- **更高质量的输出**: AI助手提供给你的代码和建议会更加贴合你的项目实际情况和规范要求。

## 模板使用提示

- **不必每次都填写所有字段**: 对于简单的问题，可以只填写相关的部分。
- **可以逐步迭代**: 先提供基本信息，然后根据AI助手的回复再补充细节。
- **保持对话连贯性**: 在同一个功能开发过程中，可以引用之前的对话内容，避免重复信息。
- **明确指出规范要求**: 特别是当你需要AI助手生成符合特定规范的代码时。

## 最佳实践

- 在开始一个新功能开发前，先使用此模板进行需求梳理和功能拆解。
- 对于复杂组件，先使用模板描述整体结构，再针对各个子组件或功能点单独提问。
- 将设计稿中的尺寸信息直接填入模板，便于AI助手准确转换为代码中的rpx单位。
- 在API交互部分明确指出错误处理的要求，确保代码健壮性。
- 在验收标准部分列出明确的测试点，帮助确保功能完整性。
