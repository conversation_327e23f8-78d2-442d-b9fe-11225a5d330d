---
description: 
globs: 
alwaysApply: true
---
# uni-super-shop-admin 组件开发规范

## 组件文件结构

每个组件应包含以下文件：
- `组件名.vue`：组件模板、逻辑和样式
- `组件逻辑文件.ts`：组件属性、类型定义和辅助函数
- `README.md`：组件文档和使用说明

## 组件命名规范

### 文件命名
- 组件文件：`PascalCase`（如：`SyPopup.vue`）
- 逻辑文件：`camelCase`（如：`popup.ts`）
- 样式文件：`camelCase`（如：`popup.scss`）

### 组件前缀
所有自定义组件使用 `Sy` 前缀，区分第三方组件

```
src/components/
├── SyPopup/         # 弹窗组件
├── SyButton/        # 按钮组件
└── SyDateSelector/  # 日期选择器组件
```

## 组件属性定义

### 属性文件结构
```ts
// popup.ts
import type { PropType } from 'vue'

/** 弹窗组件属性 */
export const popupProps = {
  /** 弹窗标题 */
  title: {
    type: String,
    default: '提示'
  },
  /** 是否显示弹窗 */
  visible: {
    type: Boolean,
    default: false
  },
  /** 确认按钮文本 */
  confirmText: {
    type: String,
    default: '确定'
  },
  /** 取消按钮文本 */
  cancelText: {
    type: String,
    default: '取消'
  }
}

/** 弹窗组件类型定义 */
export interface PopupInstance {
  show: () => void
  hide: () => void
}
```

### JSDoc 注释规范
- 每个属性必须包含详细的 JSDoc 注释
- 说明属性的用途、类型、默认值
- 复杂属性需要提供使用示例

## 组件编写规范

### 基本结构
```vue
<template>
  <wd-popup
    v-model="show"
    position="bottom"
    custom-style="border-radius:24rpx 24rpx 0 0;"
  >
    <view class="sy-popup-container">
      <!-- 弹窗头部 -->
      <view class="sy-popup-header">
        <text class="sy-popup-title">{{ title }}</text>
        <view class="sy-popup-close" @click="handleCancel">
          <wd-icon name="close" size="36rpx" />
        </view>
      </view>
      
      <!-- 弹窗内容 -->
      <view class="sy-popup-content">
        <slot>
          <!-- 默认内容 -->
        </slot>
      </view>
      
      <!-- 弹窗按钮 -->
      <view class="sy-popup-actions">
        <wd-button @click="handleCancel">{{ cancelText }}</wd-button>
        <wd-button type="primary" @click="handleConfirm">{{ confirmText }}</wd-button>
      </view>
      
      <!-- 安全区域 -->
      <view 
        class="sy-popup-safe-area"
        :style="{ height: isTabbarPage ? '80rpx' : '0rpx' }"
      ></view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { popupProps, type PopupInstance } from './popup'

// 组件属性
const props = defineProps(popupProps)

// 组件事件
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'confirm': []
  'cancel': []
}>()

// 响应式数据
const isTabbarPage = ref(false)

// 计算属性
const show = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 事件处理函数
const handleConfirm = () => {
  emit('confirm')
  show.value = false
}

const handleCancel = () => {
  emit('cancel')
  show.value = false
}

// 页面类型检测
const checkTabbarPage = () => {
  try {
    const pages = getCurrentPages()
    if (pages && pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage.route || ''
      
      const tabbarPages = [
        'pages/index/index',
        'pages/about/about',
        'pages/dishManagement/index',
        'pages/my/index',
        'pages/storeDetail/index',
      ]
      
      isTabbarPage.value = tabbarPages.some((path) => currentRoute.includes(path))
    }
  } catch (error) {
    console.error('检测 tabbar 页面失败:', error)
    isTabbarPage.value = false
  }
}

// 组件方法
const show = () => {
  show.value = true
}

const hide = () => {
  show.value = false
}

// 暴露组件方法
defineExpose<PopupInstance>({
  show,
  hide
})

// 生命周期
onMounted(() => {
  checkTabbarPage()
})
</script>

<style lang="scss" scoped>
.sy-popup {
  &-container {
    width: 100%;
    overflow: hidden;
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;
  }

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx;
    border-bottom: 1px solid #f0f0f0;
  }

  &-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  &-close {
    padding: 8rpx;
  }

  &-content {
    padding: 32rpx;
  }

  &-actions {
    display: flex;
    gap: 24rpx;
    padding: 24rpx 32rpx;
    border-top: 1px solid #f0f0f0;
  }

  &-safe-area {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background-color: #fff;
  }
}
</style>
```

## 事件处理规范

### 事件命名
- 事件处理函数以 `handle` 开头：`handleClick`、`handleConfirm`
- 内部方法以 `on` 开头：`onItemClick`、`onDataChange`

### 事件定义
```ts
// 使用 defineEmits 定义事件
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'confirm': [data: any]
  'cancel': []
  'change': [value: string, label: string]
}>()
```

## v-model 双向绑定

### 标准 v-model 实现
```ts
// 组件属性
const props = defineProps({
  modelValue: {
    type: [String, Number, Boolean],
    default: ''
  }
})

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()

// 计算属性
const value = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
```

### 多个 v-model 实现
```ts
const props = defineProps({
  visible: Boolean,
  selectedValue: String
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'update:selectedValue': [value: string]
}>()

const show = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const value = computed({
  get: () => props.selectedValue,
  set: (val) => emit('update:selectedValue', val)
})
```

## 组件方法调用规范

### ❌ 错误用法
```vue
<!-- 避免直接在组件上调用方法 -->
<sy-channel-selector @confirm="handleChannelConfirm" />
```

### ✅ 正确用法
```vue
<!-- 使用事件监听和处理函数 -->
<sy-channel-selector @confirm="onChannelConfirm" />

<script setup lang="ts">
const onChannelConfirm = (value, labels) => {
  // 处理逻辑
}
</script>
```

## 组件插槽使用

### 默认插槽
```vue
<template>
  <view class="sy-card">
    <view class="sy-card-header">
      <slot name="header">
        <!-- 默认头部内容 -->
      </slot>
    </view>
    
    <view class="sy-card-content">
      <slot>
        <!-- 默认内容 -->
      </slot>
    </view>
    
    <view class="sy-card-footer">
      <slot name="footer">
        <!-- 默认底部内容 -->
      </slot>
    </view>
  </view>
</template>
```

### 作用域插槽
```vue
<template>
  <view class="sy-list">
    <view
      v-for="(item, index) in items"
      :key="item.id"
      class="sy-list-item"
    >
      <slot :item="item" :index="index">
        <!-- 默认渲染 -->
        <text>{{ item.title }}</text>
      </slot>
    </view>
  </view>
</template>
```

## 样式规范

### BEM 命名法
- 块（Block）：`sy-popup`
- 元素（Element）：`sy-popup-header`、`sy-popup-content`
- 修饰符（Modifier）：`sy-popup--large`、`sy-popup-header--fixed`

### 样式隔离
```scss
.sy-popup {
  // 组件根样式
  
  &-header {
    // 头部样式
  }
  
  &-content {
    // 内容样式
  }
  
  &--large {
    // 大尺寸修饰符
  }
}
```

## App端兼容性规范

### 同步组件引入
```ts
// ✅ 正确：同步引入
import SyPopup from '@/components/SyPopup/SyPopup.vue'

// ❌ 错误：异步引入（App端不支持）
const SyPopup = defineAsyncComponent(() => import('@/components/SyPopup/SyPopup.vue'))
```

### Store 使用安全
```ts
// ✅ 正确：在函数内部调用
const handleClick = () => {
  const userStore = useUserStore()
  // 操作...
}

// ❌ 错误：顶层调用（可能导致白屏）
const userStore = useUserStore()
```

## 组件文档规范

### README.md 结构
```markdown
# SyPopup 弹窗组件

## 功能描述
通用弹窗组件，支持自定义内容、按钮文本等。

## 属性 Props
| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| visible | Boolean | false | 是 | 是否显示弹窗 |
| title | String | '提示' | 否 | 弹窗标题 |

## 事件 Events
| 事件名 | 参数 | 说明 |
|--------|------|------|
| confirm | - | 点击确认按钮 |
| cancel | - | 点击取消按钮 |

## 插槽 Slots
| 插槽名 | 说明 |
|--------|------|
| default | 弹窗内容 |

## 使用示例
\`\`\`vue
<template>
  <sy-popup
    v-model:visible="showPopup"
    title="确认删除"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    确定要删除这条记录吗？
  </sy-popup>
</template>
\`\`\`
```

## 最佳实践

1. **组件职责单一**：每个组件专注于一个功能
2. **事件驱动通信**：组件间通过事件和属性通信，避免直接调用方法
3. **类型安全**：完整的 TypeScript 类型定义
4. **样式隔离**：使用 BEM 命名法，避免样式污染
5. **文档完善**：提供详细的使用说明和示例
