## **uni-super-shop-admin 权限控制技术方案**

### 1. 概述

本方案旨在为项目实现一个全面的前端权限控制系统，覆盖 **菜单权限（TabBar）** 和 **按钮/元素权限** 两大方面。系统核心思想是：

1.  **统一管理**：用户登录后，从后端获取权限码列表，并由 `Pinia` 进行全局状态管理。
2.  **动态渲染**：无论是 TabBar 菜单还是页面内按钮，都根据全局权限状态动态判断是否渲染。
3.  **组件化与组合式**：提供权限组件 `<SyAuth />` 和组合式函数 `useAuth()` 两种方式，方便在不同场景下灵活使用。

### 2. 核心设计与数据流

#### 2.1 数据流

```mermaid
graph TD
    A[用户登录成功] --> B{调用 action: fetchPermissions};
    B --> C[API请求: v1/user/access];
    C --> D{获取权限码列表};
    D --> E[存入 Pinia userStore];
    subgraph 权限应用
        E --> F[TabBar菜单: sy-tabbar 组件];
        E --> G[页面元素: SyAuth组件 / useAuth钩子];
        F --> H[动态渲染 TabBar];
        G --> I[动态渲染/禁用按钮];
    end
```

#### 2.2 Pinia 状态管理 (`src/store/user.ts`)

我们将利用 Pinia 来存储和管理权限数据。建议在 `src/store` 中创建一个新的 `auth.ts` 文件，专门负责权限逻辑。

**文件: `src/store/auth.ts` (新建)**

```typescript
import { defineStore } from 'pinia'
// 假设已在 service/user/index.ts 中创建了 getAccess 接口
import { getAccess } from '@/service/user'

interface AuthState {
  /** 权限码列表 */
  permissionCodes: string[]
  /** 权限数据是否已加载 */
  isPermissionLoaded: boolean
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    permissionCodes: [],
    isPermissionLoaded: false,
  }),
  getters: {
    /**
     * 检查用户是否拥有指定权限码
     * @param state
     * @returns (code: string | string[]) => boolean
     */
    hasPermission(state) {
      return (code: string | string[]): boolean => {
        // 在权限加载完成前，默认无权限，防止页面元素因权限未加载而闪烁
        if (!state.isPermissionLoaded) {
          return false
        }

        const codes = Array.isArray(code) ? code : [code]
        if (codes.length === 0) {
          return true // 如果未要求任何权限，则默认有权限
        }

        // 检查是否拥有codes数组中的所有权限
        return codes.every((c) => state.permissionCodes.includes(c))
      }
    },
  },
  actions: {
    /**
     * 从后端获取用户权限码并存储
     */
    async fetchPermissions() {
      if (this.isPermissionLoaded) return
      try {
        // 调用 v1/user/access 接口，假设返回的数据结构为 { data: ['code1', 'code2', ...] }
        const res = await getAccess()
        this.permissionCodes = res.data || []
      } catch (error) {
        console.error('获取用户权限失败:', error)
        this.permissionCodes = [] // 发生错误时清空权限
      } finally {
        this.isPermissionLoaded = true // 无论成功失败，都标记为已加载
      }
    },

    /**
     * 清理权限信息（退出登录时调用）
     */
    clearPermissions() {
      this.permissionCodes = []
      this.isPermissionLoaded = false
    },
  },
})
```

#### 2.3 登录流程集成

在用户登录成功后，立即调用 `fetchPermissions`。

**文件: `src/pages/login/index/index.ts` (或相关登录逻辑文件)**

```typescript
// ...
import { useAuthStore } from '@/store/auth'

async function handleLogin() {
  // ... 登录逻辑成功后
  const authStore = useAuthStore()
  await authStore.fetchPermissions() // 获取权限
  // 跳转到首页
  uni.switchTab({ url: '/pages/index/index' })
}
// ...
```

### 3. 菜单权限实现 (TabBar)

由于 `pages.config.ts` 中的 `tabBar` 是编译时配置，无法运行时动态修改。我们将利用项目中的自定义 TabBar 组件 `sy-tabbar` 来实现动态渲染。

#### 3.1. 为 TabBar 数据添加权限码

**文件: `src/components/sy-tabbar/sy-tabbar.ts` (修改)**

```typescript
// ...
export const tabbarList = [
  {
    pagePath: 'pages/index/index',
    text: '订单',
    icon: '/static/tabbar/order.png',
    iconSelected: '/static/tabbar/order-selected.png',
    permissionCode: 'jidanbao_menu_index', // 订单Tab权限码 (示例)
  },
  {
    pagePath: 'pages/dishManagement/index',
    text: '菜品',
    icon: '/static/tabbar/dishes.png',
    iconSelected: '/static/tabbar/dishes-selected.png',
    permissionCode: 'jidanbao_menu_dishManagement', // 菜品Tab权限码 (示例)
  },
  {
    pagePath: 'pages/review/index',
    text: '评价',
    icon: '/static/tabbar/review.png',
    iconSelected: '/static/tabbar/review-selected.png',
    permissionCode: 'jidanbao_menu_review_replybtn', // 复用评价页的按钮权限码
  },
  {
    pagePath: 'pages/storeDetail/index',
    text: '门店',
    icon: '/static/tabbar/store.png',
    iconSelected: '/static/tabbar/store-selected.png',
    permissionCode: 'jidanbao_menu_storeDetail_settingbtn', // 复用门店页的按钮权限码
  },
]
// ...
```

> **说明**: 我根据您的需求，为 TabBar 项目添加了 `permissionCode` 属性。部分权限码我直接复用了您提供的页面内按钮权限码，您可以根据后端的实际情况进行调整。

#### 3.2. `sy-tabbar` 组件动态渲染

**文件: `src/components/sy-tabbar/sy-tabbar.vue` (修改)**

```vue
<script setup lang="ts">
import { computed } from 'vue'
import { useAuthStore } from '@/store/auth'
import { tabbarList as fullTabbarList } from './sy-tabbar'

// ... (原有逻辑)

const authStore = useAuthStore()

const filteredTabbarList = computed(() => {
  if (!authStore.isPermissionLoaded) {
    return [] // 权限加载中，不显示任何项
  }
  return fullTabbarList.filter((item) => {
    // 如果item没有配置permissionCode，则默认展示
    if (!item.permissionCode) return true
    return authStore.hasPermission(item.permissionCode)
  })
})

// ... (原有逻辑)
</script>

<template>
  <!-- 将 v-for 的源数据从 tabbarList 改为 filteredTabbarList -->
  <view
    v-for="(item, index) in filteredTabbarList"
    :key="index"
    class="tabbar-item"
    @click="switchTab(item, index)"
  >
    <!-- ... 内部结构不变 ... -->
  </view>
</template>
```

### 4. 按钮/元素权限实现

我们提供 **组合式函数(Composable)** 和 **权限组件** 两种方式，您可以根据场景选用。

#### 4.1. 组合式函数 `useAuth` (推荐)

这种方式更灵活，符合 Vue3 开发范式，可以在 `<script>` 中进行逻辑判断。

**文件: `src/hooks/useAuth.ts` (新建)**

```typescript
import { useAuthStore } from '@/store/auth'

export function useAuth() {
  const authStore = useAuthStore()

  /**
   * 检查是否拥有指定权限
   * @param code - 所需的权限码或权限码数组
   * @returns boolean
   */
  const hasPermission = (code: string | string[]): boolean => {
    return authStore.hasPermission(code)
  }

  return {
    hasPermission,
  }
}
```

#### 4.2. 权限组件 `<SyAuth />`

这是一个包装组件，用于在模板中直接控制元素的显示/隐藏，支持插槽，非常直观。

**文件: `src/components/sy-auth/SyAuth.vue` (新建)**

```vue
<script setup lang="ts">
import { computed } from 'vue'
import { useAuth } from '@/hooks/useAuth'

const props = defineProps<{
  /**
   * 所需的权限码或权限码数组
   * 若为数组，则需要同时拥有所有权限才会显示
   */
  code: string | string[]
}>()

const { hasPermission } = useAuth()

const isAllowed = computed(() => hasPermission(props.code))
</script>

<template>
  <slot v-if="isAllowed" />
</template>
```

### 5. 应用示例

#### 5.1 订单页面

**文件: `src/pages/order/index.vue` (或其子组件)**

```vue
<script setup lang="ts">
import { useAuth } from '@/hooks/useAuth'
const { hasPermission } = useAuth()
</script>

<template>
  <!-- 1. 操作类型切换 -->
  <view class="tabs-container">
    <view v-if="hasPermission('jidanbao_menu_index_order')" class="tab-item">订单</view>
    <view v-if="hasPermission('jidanbao_menu_index_afterSale')" class="tab-item">售后</view>
    <view v-if="hasPermission('jidanbao_menu_index_damageAppeal')" class="tab-item">申诉</view>
  </view>

  <!-- 2. 按钮权限，使用 SyAuth 组件 -->
  <view class="order-card">
    <!-- ... -->
    <view class="actions">
      <sy-auth code="jidanbao_menu_index_order_acceptbtn">
        <button type="primary">立即接单</button>
      </sy-auth>
      <sy-auth code="jidanbao_menu_index_order_refundbtn">
        <button>退款</button>
      </sy-auth>
      <sy-auth code="jidanbao_menu_index_order_afterSaleActionsbtn">
        <button>审核退款</button>
      </sy-auth>
    </view>
  </view>
</template>
```

#### 5.2 评价与门店页面

**评价页面 (`src/pages/review/index.vue`)**

```vue
<template>
  <!-- ... -->
  <sy-auth code="jidanbao_menu_review_replybtn">
    <button>回复</button>
  </sy-auth>
  <!-- ... -->
</template>
```

**门店页面 (`src/pages/storeDetail/index.vue`)**

```vue
<template>
  <!-- ... -->
  <sy-auth code="jidanbao_menu_storeDetail_settingbtn">
    <button>设置</button>
  </sy-auth>
  <!-- ... -->
</template>
```

#### 5.3 设置页面

**文件: `src/pages/general/sysSetting/index.vue` (假设是这个页面)**

```vue
<template>
  <view class="page-container">
    <sy-auth code="jidanbao_menu_storeDetail_messageRing">
      <wd-cell title="消息和铃声设置" is-link />
    </sy-auth>
    <!-- ...其他设置项... -->
  </view>
</template>
```

---

### 6. 总结

本方案提供了一套完整的、从数据获取到前端应用的权限控制流程。

- **数据层**: 使用 Pinia (`useAuthStore`) 统一管理，职责清晰。
- **菜单层**: 通过改造 `sy-tabbar` 组件，实现了 TabBar 的动态权限控制。
- **元素层**: 提供了 `useAuth` 钩子和 `SyAuth` 组件，兼顾了逻辑判断的灵活性和模板使用的便捷性。
