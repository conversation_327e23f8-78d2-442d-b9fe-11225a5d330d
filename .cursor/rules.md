## **Project Rules for uni-super-shop-admin**

本项目基于 [unibest](https://unibest.tech/) 框架，技术栈：Vue3 + Vite5 + Pnpm + TypeScript，UI 框架：Element Plus。

### **1. 样式开发**

- **优先使用 UnoCSS 原子类**：

  - 遵循 [unibest 样式篇文档](https://unibest.tech/base/4-style)。
  - 常用类：`w-100rpx`、`h-40rpx`、`px-6`、`mt-8`、`text-green-400`、`rounded-6`、`flex` 等。

- **设计稿尺寸与样式换算**：

  - 设计稿宽度：375px（MasterGo设计稿），1px转换为2rpx（例如：设计稿100px应使用 `w-200rpx`）。
  - 所有设计稿尺寸需 _x2_ 后转换为 `rpx`，确保在不同设备上自适应。

- **自定义 UnoCSS 快捷类**：

  - 支持在 `uno.config.ts` 中配置自定义快捷类，如：`center = flex justify-center items-center`。

- **UI 和交互还原**：

  - 严格按照 MasterGo 的设计稿和交互逻辑开发，优先保证设计一致性。
  - 确保前端与设计稿中约定的 UI 元素、字体、间距、颜色完全一致。

---

### **2. 需求文档与接口管理**

- **需求文档结构**：

  - 需求文档应详细列出每个页面的功能要求、UI 设计元素和交互逻辑。
  - 每个页面需附上接口文档，确保接口请求的参数、响应字段与前端 UI 组件之间的映射关系清晰。
  - 明确列出每个接口的请求类型、参数格式、返回数据格式和成功/失败状态码。

- **接口联调与 Mock 数据**：

  - **Mock 数据**：在接口尚未开发完成时，前端应使用 mock 数据，确保 UI 开发不受阻。
  - **接口调试**：与后端接口调试时，优先确保接口数据结构和字段一致，确保能够正确与 UI 组件进行数据绑定。

- **接口与 UI 绑定**：

  - 需求文档中要明确标出接口输出字段与页面 UI 元素的关系，确保数据展示无误。
  - 当接口返回的数据格式变更时，应及时更新前端页面的处理逻辑，确保一致性。

---

### **3. 资源管理**

- **图片资源管理**：

  - 所有图片资源需上传至 `src/static/images/`，并按模块/页面进行分类管理。
  - 图片资源必须压缩后上传，避免不必要的文件冗余。
  - 后续图片统一上传至 OSS，确保资源的可访问性。

- **组件和页面命名**：

  - 页面和组件使用 PascalCase（组件）和 camelCase（变量/方法）命名规范，确保一致性。
  - 例如：`HomePage.vue`，`userProfileData`，`UserFormSubmit()`。

---

### **4. 代码规范**

- **类型安全**：

  - 所有代码必须严格定义类型声明，确保在开发过程中保持类型安全。
  - 使用 TypeScript 时，避免 `any` 类型的滥用。

- **代码注释**：

  - 逻辑复杂的部分需要使用中文注释详细说明。
  - 注释内容应简洁明了，避免过多冗余。

- **依赖管理**：

  - 新增依赖时，必须在 `package.json` 中说明用途并使用 `pnpm` 安装，确保团队成员一致使用相同版本的依赖。
  - 不要频繁更换依赖库，避免产生不必要的技术债务。

- **接口联调与 Mock**：

  - 前端开发应优先使用 Mock 数据，并明确标注 Mock 数据的来源与方案，确保前后端联调时接口一致。

---

### **5. 编码习惯**

- **简洁输出**：

  - 代码和回答内容应简洁明了，避免无关解释。
  - 只提交最小必要变更，避免将无关内容一并提交，减少合并冲突。

- **疑点处理**：

  - 当遇到不确定的地方，应先提问或讨论，确保处理方案准确后再进行编码。

---

### **6. 协作与版本管理**

- **Git 版本控制**：

  - 所有代码都应使用 Git 进行版本管理。
  - 每个任务（功能、修复）应在独立的分支上进行开发，确保主分支的稳定性。
  - 提交时，写清楚变更的目的和详细说明，避免提交模糊不清的提交信息。

- **需求变更评审**：

  - 每当需求文档变更时，开发人员必须与设计和后端团队确认变化，确保前后端一致。
  - 设计稿和接口变更后，及时同步并确保 UI 的及时更新。

---

### **7. 测试保障**

- **UI 适配测试**：

  - 必须对不同设备、浏览器和屏幕尺寸进行响应式测试，确保页面在各种情况下均能正确显示。
  - 每个页面和组件应进行单元测试，确保 UI 元素和交互行为无误。

- **接口与功能测试**：

  - 所有接口需通过单元测试和集成测试，确保后端服务的稳定性。
  - 前端在接入新接口时，应进行接口联调测试，确保接口与 UI 数据的正确映射。

- **回归测试**：

  - 每当新功能开发完成后，应进行回归测试，确保现有功能不受影响。

---

### **8. Bug 修复与文档更新**

- **Bug 描述**：

  - 遇到 bug 时，必须详细描述 bug 现象、原因分析及修复方法。
  - 在修复 bug 后，应对相关文档进行更新，确保文档与实际代码一致。

- **文档更新**：

  - 任何接口、功能、设计变更都应同步更新到需求文档、接口文档和设计文档中，确保项目中的每个成员都能及时获取最新信息。

---

### **9. 语言要求**

- **语言**：

  - 代码注释、文档及沟通应使用中文，保证团队成员理解一致。

---

### **总结**

- **设计与开发协作**：严格按照设计稿和需求文档进行开发，确保 UI 与接口输出一致性。
- **代码与依赖管理**：使用类型安全的代码，确保代码质量；合理管理依赖，避免频繁变更。
- **自动化测试与接口联调**：通过自动化测试确保代码的稳定性和接口的正确性，避免回归问题。
- **敏捷协作与版本管理**：采用 Git 进行版本控制，保持开发流程的清晰与高效。
