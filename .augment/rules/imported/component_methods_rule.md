---
type: "always_apply"
---

# 组件方法调用规范

## 问题描述

在 uni-app 项目中，特别是在非 H5 环境（如小程序、App）中，直接在组件上调用方法可能会导致错误。例如：

```vue
<!-- ❌ 错误用法 -->
<sy-channel-selector @confirm="handleChannelConfirm" />
```

这种写法在 H5 环境中可能正常工作，但在小程序或 App 环境中可能会导致方法未定义错误，因为方法可能无法正确传递或绑定。

## 正确做法

### 1. 使用事件监听和处理函数

不要直接在组件上调用方法，而是使用事件监听和处理函数：

```vue
<!-- ✅ 正确用法 -->
<sy-channel-selector @confirm="onChannelConfirm" />

<script setup>
// 在当前组件内定义处理函数
const onChannelConfirm = (value, labels) => {
  // 处理逻辑
}
</script>
```

### 2. 使用 v-model 进行数据双向绑定

对于需要双向绑定的场景，使用 v-model：

```vue
<!-- ✅ 正确用法 -->
<sy-channel-selector v-model:value="selectedChannels" v-model:visible="showChannelSelector" />
```

### 3. 避免使用 ref 直接调用组件方法

尽管可以使用 ref 获取组件实例并调用其方法，但这种做法在跨平台场景中可能不稳定：

```vue
<!-- ⚠️ 谨慎使用 -->
<sy-channel-selector ref="channelSelector" />

<script setup>
const channelSelector = ref(null)

// 可能在某些平台上不可靠
const showSelector = () => {
  channelSelector.value.someMethod()
}
</script>
```

### 4. 组件设计原则

组件应该设计为通过事件和属性进行通信，而不是依赖外部直接调用其方法：

- 使用 `defineEmits` 定义组件事件
- 使用 `defineProps` 接收外部属性
- 使用 `v-model` 实现双向绑定
- 组件内部逻辑应该自包含，不依赖外部直接调用方法

## 示例：渠道选择器组件的正确用法

```vue
<!-- 父组件 -->
<template>
  <sy-channel-selector
    v-model:value="selectedChannels"
    v-model:visible="showChannelSelector"
    :options="channelOptions"
    @confirm="onChannelConfirm"
    @cancel="onChannelCancel"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedChannels = ref([])
const showChannelSelector = ref(false)
const channelOptions = ref([
  /* 渠道选项数据 */
])

const onChannelConfirm = (value, labels) => {
  console.log('确认选择的渠道:', value, labels)
  // 处理确认逻辑
}

const onChannelCancel = () => {
  console.log('取消选择渠道')
  // 处理取消逻辑
}
</script>
```

## 常见错误及解决方法

| 错误模式                        | 问题                         | 解决方法                                      |
| ------------------------------- | ---------------------------- | --------------------------------------------- |
| `@confirm="handleConfirm"`      | 在非 H5 环境中可能找不到方法 | 使用本地定义的处理函数 `@confirm="onConfirm"` |
| `@click="component.someMethod"` | 组件方法无法直接访问         | 通过事件通信 `@custom-event="handleEvent"`    |
| 依赖 ref 调用组件方法           | 跨平台兼容性问题             | 使用事件和属性进行组件通信                    |

## 弹窗底部安全区域处理规范

### 问题描述

在移动端，特别是带有 TabBar 的页面中，底部弹窗可能会被 TabBar 或设备底部安全区域遮挡，影响用户体验。需要为弹窗底部添加适当的安全区域处理。

### 解决方案

#### 1. 页面类型检测机制

弹窗组件必须能够自动检测当前页面是否为 TabBar 页面，并据此调整安全区域高度：

```typescript
/**
 * 检测当前页面是否为 tabbar 页面
 */
const isTabbarPage = ref(false)

/**
 * 获取当前页面路径并判断是否为 tabbar 页面
 */
const checkTabbarPage = () => {
  try {
    const pages = getCurrentPages()
    if (pages && pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage.route || ''

      // 定义 tabbar 页面路径列表（根据实际项目的 tabbar 配置）
      const tabbarPages = [
        'pages/index/index',
        'pages/about/about', 
        'pages/dishManagement/index',
        'pages/my/index',
        'pages/storeDetail/index',
      ]

      isTabbarPage.value = tabbarPages.some((path) => currentRoute.includes(path))
    }
  } catch (error) {
    console.error('检测 tabbar 页面失败:', error)
    isTabbarPage.value = false
  }
}

// 在组件挂载时检测页面类型
onMounted(() => {
  checkTabbarPage()
})
```

#### 2. 动态安全区域高度控制（推荐实现）

根据页面类型动态设置安全区域高度：

```vue
<template>
  <wd-popup
    v-model="show"
    position="bottom"
    custom-style="border-radius:24rpx 24rpx 0 0;"
  >
    <view class="popup-container">
      <!-- 弹窗头部 -->
      <view class="popup-header">
        <text class="popup-title">弹窗标题</text>
        <view class="popup-close" @click="show = false">
          <wd-icon name="close" size="36rpx" />
        </view>
      </view>
      
      <!-- 弹窗内容 -->
      <view class="popup-content">
        <!-- 内容区域 -->
      </view>
      
      <!-- 底部操作区域（可选） -->
      <view class="popup-actions">
        <wd-button type="primary" block>确定</wd-button>
      </view>
      
      <!-- ✅ 动态安全区域：TabBar页面80rpx，非TabBar页面0rpx -->
      <view 
        class="popup-safe-area"
        :style="{ height: isTabbarPage ? '80rpx' : '0rpx' }"
      ></view>
    </view>
  </wd-popup>
</template>
```

#### 3. 安全区域样式实现

```scss
.popup-container {
  width: 100%;
  overflow: hidden;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.popup-safe-area {
  /* 高度由内联样式动态控制 */
  /* TabBar页面: 80rpx, 非TabBar页面: 0rpx */
  
  /* 设备底部安全区域处理（所有页面都需要） */
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  
  /* 确保背景色一致 */
  background-color: #fff;
}
```

#### 4. 完整实现示例

基于 `sy-order-card` 组件的最佳实践：

```vue
<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 检测当前页面是否为 tabbar 页面
const isTabbarPage = ref(false)

// 获取当前页面路径并判断是否为 tabbar 页面
const checkTabbarPage = () => {
  try {
    const pages = getCurrentPages()
    if (pages && pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage.route || ''

      // 定义 tabbar 页面路径列表（根据实际项目的 tabbar 配置）
      const tabbarPages = [
        'pages/index/index',
        'pages/about/about',
        'pages/dishManagement/index', 
        'pages/my/index',
        'pages/storeDetail/index',
      ]

      isTabbarPage.value = tabbarPages.some((path) => currentRoute.includes(path))
    }
  } catch (error) {
    console.error('检测 tabbar 页面失败:', error)
    isTabbarPage.value = false
  }
}

onMounted(() => {
  // 检测当前页面是否为 tabbar 页面
  checkTabbarPage()
})
</script>

<template>
  <!-- 订单跟踪弹窗示例 -->
  <wd-popup
    v-model="showTrackPopup"
    position="bottom"
    :close-on-click-modal="false"
    custom-style="border-radius:24rpx 24rpx 0 0;"
  >
    <view class="track-popup-container">
      <!-- 弹窗头部 -->
      <view class="track-popup-header flex justify-center items-center px-24rpx py-20rpx">
        <text class="text-30rpx font-semibold text-#222222">订单跟踪</text>
        <view class="track-popup-close" @click="showTrackPopup = false">
          <wd-icon name="close" size="36rpx" color="#333333"></wd-icon>
        </view>
      </view>

      <!-- 弹窗内容 -->
      <view class="track-popup-content px-32rpx py-24rpx">
        <!-- 具体内容 -->
      </view>

      <!-- 底部安全区域：tabbar页面需要额外预留tabbar高度（80rpx），非tabbar页面只需要基础安全区（0rpx） -->
      <view 
        class="track-popup-safe-area" 
        :style="{ height: isTabbarPage ? '80rpx' : '0rpx' }" 
      />
    </view>
  </wd-popup>

  <!-- 拨打骑手电话弹窗示例 -->
  <wd-popup
    v-model="showCallRiderPopup"
    position="bottom"
    :close-on-click-modal="true"
    custom-style="border-radius:24rpx 24rpx 0 0;"
  >
    <view class="call-rider-popup-container">
      <!-- 弹窗内容 -->
      <view class="call-rider-popup-content px-32rpx py-48rpx bg-white">
        <!-- 具体内容 -->
      </view>

      <!-- 底部操作按钮区域 -->
      <view class="call-rider-popup-footer bg-#F1F1F1 px-24rpx py-18rpx">
        <view class="cancel-btn w-full h-88rpx bg-transparent flex items-center justify-center">
          <text class="text-32rpx text-#F33429 font-medium">取消</text>
        </view>
      </view>

      <!-- 底部安全区域：tabbar页面需要额外预留tabbar高度（80rpx），非tabbar页面只需要基础安全区（0rpx） -->
      <view
        class="call-rider-popup-safe-area"
        :style="{ height: isTabbarPage ? '80rpx' : '0rpx' }"
      />
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
// 订单跟踪弹窗样式
.track-popup {
  &-container {
    width: 100%;
    overflow: hidden;
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;
  }

  &-safe-area {
    /* 高度由内联样式动态控制 */
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background-color: #fff;
  }
}

// 拨打骑手电话弹窗样式
.call-rider-popup {
  &-container {
    width: 100%;
    overflow: hidden;
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;
  }

  &-safe-area {
    /* 高度由内联样式动态控制 */
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background-color: #f1f1f1;
  }
}
</style>
```

#### 5. 滚动内容弹窗的特殊处理

对于包含滚动内容的弹窗：

```vue
<template>
  <wd-popup v-model="show" position="bottom">
    <view class="popup-container">
      <view class="popup-header">
        <!-- 固定头部 -->
      </view>
      
      <!-- 可滚动内容区域 -->
      <scroll-view scroll-y="true" class="popup-scroll-content">
        <!-- 滚动内容 -->
      </scroll-view>
      
      <!-- 固定底部安全区域 -->
      <view 
        class="popup-safe-area"
        :style="{ height: isTabbarPage ? '80rpx' : '0rpx' }"
      ></view>
    </view>
  </wd-popup>
</template>
```

```scss
.popup-container {
  height: 75vh; /* 限制总高度 */
  overflow: hidden;
}

.popup-scroll-content {
  flex: 1;
  max-height: calc(75vh - 120rpx); /* 减去头部和安全区域高度 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.popup-safe-area {
  /* 高度由内联样式动态控制 */
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #fff;
}
```

#### 6. 安全区域规则总结

1. **页面类型检测**：
   - TabBar 页面：安全区域基础高度为 `80rpx`
   - 非 TabBar 页面：安全区域基础高度为 `0rpx`

2. **设备安全区域**：
   - 所有页面类型都必须添加 `env(safe-area-inset-bottom)` 处理设备底部安全区域

3. **动态高度控制**：
   ```vue
   :style="{ height: isTabbarPage ? '80rpx' : '0rpx' }"
   ```

4. **CSS 样式规范**：
   ```scss
   .popup-safe-area {
     /* 高度由内联样式动态控制 */
     padding-bottom: constant(safe-area-inset-bottom);
     padding-bottom: env(safe-area-inset-bottom);
     background-color: #fff; /* 与弹窗背景色一致 */
   }
   ```

5. **组件初始化**：
   ```typescript
   onMounted(() => {
     checkTabbarPage() // 必须在组件挂载时检测页面类型
   })
   ```

#### 7. 最佳实践

1. **必须添加安全区域**：所有 `position="bottom"` 的弹窗都必须添加底部安全区域处理
2. **动态高度设置**：根据页面类型动态设置安全区域高度，避免在非 TabBar 页面产生多余空白
3. **CSS 环境变量**：使用 `env(safe-area-inset-bottom)` 处理设备安全区域
4. **背景色一致**：确保安全区域背景色与弹窗背景色一致
5. **兼容性处理**：同时使用 `constant()` 和 `env()` 确保不同版本兼容性
6. **页面检测**：在组件挂载时自动检测页面类型，无需外部传参

#### 8. 组件设计规范

在设计底部弹窗组件时，应该：

- 自动包含页面类型检测和安全区域处理，无需外部手动配置
- 提供 `safeAreaHeight` 属性允许自定义安全区域高度（可选）
- 支持 `showSafeArea` 属性控制是否显示安全区域（可选）
- 确保在所有目标平台上正确显示

```vue
<!-- 组件内部自动处理安全区域 -->
<template>
  <wd-popup v-model="show" position="bottom">
    <view class="sy-popup-container">
      <!-- 内容区域 -->
      <slot />
      
      <!-- 自动添加安全区域 -->
      <view 
        v-if="showSafeArea" 
        class="sy-popup-safe-area"
        :style="{ 
          height: isTabbarPage ? (safeAreaHeight || 80) + 'rpx' : '0rpx' 
        }"
      ></view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
// 组件属性
defineProps({
  safeAreaHeight: { type: Number, default: 80 }, // 自定义安全区域高度
  showSafeArea: { type: Boolean, default: true }, // 是否显示安全区域
})

// 自动检测页面类型
const isTabbarPage = ref(false)
onMounted(() => {
  checkTabbarPage()
})
</script>
```

## 总结

- 不要直接在组件上调用外部方法
- 使用事件监听和处理函数进行组件通信
- 优先使用 v-model 进行数据双向绑定
- 组件设计应遵循事件驱动和属性驱动原则
- 确保代码在所有目标平台（H5、小程序、App）上兼容
- **所有底部弹窗必须添加安全区域处理，避免被 TabBar 或设备安全区域遮挡**
- **使用独立的安全区域元素是推荐的实现方式**
- **安全区域的高度和样式应该与弹窗背景保持一致**
