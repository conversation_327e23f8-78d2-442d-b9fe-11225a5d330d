---
description: uni-super-shop-admin 项目开发规范
globs: ["**/*.{vue,ts,js,json,md}"]
alwaysApply: true
type: "always_apply"
---
## **Project Rules for uni-super-shop-admin**

本项目基于 [unibest](mdc:https:/unibest.tech) 框架，技术栈：Vue3 + Vite5 + Pnpm + TypeScript，UI 框架：Wot Design Uni。

### **1. src 目录结构与功能区说明**

```text
src/
├── components/      # 公共业务组件（全局/局部可复用UI组件，推荐使用 PascalCase 命名）
├── hooks/           # 组合式函数（如 useXxx，封装通用逻辑）
├── interceptors/    # 请求/响应拦截器（如 axios、uni.request 拦截逻辑）
├── layouts/         # 页面布局相关（如多页面通用布局组件）
├── pages/           # 主业务页面（一级路由页面，包含 tabbar 和登录页面）
├── pages-sub/       # 功能分包页面（按业务功能拆分的分包，除 tabbar 和登录页面外的其他页面）
│   └── module-a/    # 功能模块A相关页面
│   └── module-b/    # 功能模块B相关页面
├── service/         # 业务接口与服务（如 API 封装、数据请求、service 层逻辑）
│   └── app/         # 具体业务模块（如 app 相关接口）
│   └── index/       # 具体业务模块（如 index 相关接口）
├── static/          # 静态资源（图片、字体等，按用途和类型分类）
│   └── images/      # 图片资源目录
│       └── icons/   # 存放图标类图片资源
│       └── img/     # 存放一般图片资源（如logo、banner等）
├── store/           # 状态管理（Pinia store，推荐一个模块一个文件，使用 camelCase 命名）
├── style/           # 全局样式（如 reset、主题、变量、mixin 等）
├── types/           # TypeScript 类型定义（全局/模块类型，.d.ts 文件）
├── uni_modules/     # uni-app 插件/扩展模块（如第三方 uni_modules）
├── utils/           # 工具函数（如格式化、校验、通用方法，使用 camelCase 命名）
```

#### 说明与建议

- **components**：存放全局/局部可复用的 UI 组件，推荐一个组件一个文件夹，内含 `.vue`、`.ts`、`.scss`。
- **hooks**：存放自定义组合式函数（如 `useUser`、`useRequest`），便于逻辑复用。
- **interceptors**：统一管理请求和响应的拦截逻辑，便于维护和扩展。
- **layouts**：存放页面级布局组件，如顶部导航、底部 TabBar 等。
- **pages**：包含主包页面，主要是 tabbar 页面和登录页面，这些是用户最常访问的页面。
- **pages-sub**：按业务功能模块拆分的分包页面，每个子文件夹代表一个功能模块的分包，采用与主包相同的页面文件拆分结构（`.vue`、`.ts`、`.scss`）。分包可以减少小程序主包体积，提高首次加载速度。
- **service**：业务接口层，负责 API 封装、请求逻辑，按业务模块分类。
- **static**：静态资源，按用途和类型分类存放，便于查找和管理。
  - `static/images/`：图片资源目录
    - `static/images/icons/`：存放图标类图片资源
    - `static/images/img/`：存放一般图片资源（如logo、banner等）
  - `static/fonts/`：字体文件目录
  - 严禁在代码中直接引用第三方平台的远程图片URL
- **store**：Pinia 状态管理，推荐每个 store 单独一个文件，命名与业务模块一致。
- **style**：全局样式文件夹，包含重置样式、主题变量、mixin 等。
- **types**：全局和模块类型定义，统一管理 TypeScript 类型。
- **uni_modules**：uni-app 官方插件和第三方扩展模块。
- **utils**：通用工具函数，推荐按功能拆分文件。

### **2. 开发环境与工具**

- **Node.js 要求**：
  - Node.js 版本 >= 18
  - Pnpm 版本 >= 7.30
  - 使用 `pnpm` 作为包管理器，禁止使用 npm 或 yarn

- **IDE 配置**：
  - 推荐使用 VSCode
  - 必须安装的 VSCode 插件：
    - ESLint
    - Prettier
    - Stylelint
    - TypeScript Vue Plugin (Volar)
    - UnoCSS

### **3. 代码规范**

- **TypeScript 规范**：
  - 严格使用 TypeScript，避免使用 `any` 类型
  - 所有组件必须使用 `<script setup lang="ts">`
  - 使用 `@types` 确保类型安全
  - 使用 `@uni-helper/uni-types` 获取 uni-app 类型支持

- **Vue 组件规范**：
  - 使用 Vue 3 Composition API
  - 组件文件使用 PascalCase 命名（如：`UserProfile.vue`）
  - 组件属性使用 kebab-case（如：`user-name`）
  - 使用 `<script setup>` 语法糖

- **样式规范**：
  - 使用 UnoCSS 原子类优先
  - 遵循 [unibest 样式篇文档](mdc:https:/unibest.tech/base/4-style)
  - 使用 SCSS 预处理器
  - 遵循 Stylelint 规则

### **3.1 UnoCSS 使用规范**

- **基本原则**：
  - 优先使用 UnoCSS 原子类进行样式开发
  - 对于复杂或重复的样式组合，可以使用 SCSS 进行封装
  - 在模板中直接使用 UnoCSS 类名，避免创建不必要的 CSS 文件

- **常用原子类示例**：
  ```vue
  <template>
    <!-- 使用 UnoCSS 原子类 -->
    <view class="flex flex-col gap-4 p-4 bg-white rounded-lg shadow">
      <text class="text-lg font-bold text-primary">标题</text>
      <view class="flex items-center justify-between">
        <text class="text-sm text-gray-500">描述文本</text>
        <button class="bg-primary text-white px-4 py-2 rounded-full">按钮</button>
      </view>
    </view>
  </template>
  ```

- **自定义主题配置**：
  - 遵循项目的 UnoCSS 配置文件中定义的颜色、间距和断点
  - 使用预定义的颜色变量（如 `text-primary`、`bg-secondary`）
  - 避免使用硬编码的颜色值

- **响应式设计**：
  - 使用 UnoCSS 的响应式前缀（如 `sm:`、`md:`、`lg:`）
  - 示例：`<view class="w-full md:w-1/2 lg:w-1/3"></view>`

### **3.2 MasterGo MCP 设计稿与像素转换规范**

- **设计稿尺寸**：
  - MasterGo 设计稿统一使用 375px 宽度作为标准尺寸
  - 设计稿中的 1px 在代码中对应 2rpx
  - 从设计稿转换到代码时，所有尺寸需要乘以 2 转换为 rpx 单位

- **像素转换示例**：
  ```scss
  // 设计稿中元素宽度为 100px
  .element {
    width: 200rpx; // 100px × 2 = 200rpx
  }
  
  // 设计稿中字体大小为 16px
  .text {
    font-size: 32rpx; // 16px × 2 = 32rpx
  }
  
  // 设计稿中边距为 20px
  .container {
    padding: 40rpx; // 20px × 2 = 40rpx
  }
  ```

- **MasterGo 图片资源管理规范**：
  - 禁止在代码中直接引用 MasterGo 远程图片 URL（如 `https://image-resource.mastergo.com/...`）
  - 必须将所有 MasterGo 设计稿中的图片下载到本地，按以下目录结构存放：
    ```
    src/static/images/
    ├── icons/        # 存放图标类图片（如渠道图标、功能图标等）
    └── img/          # 存放其他类型图片（如logo、banner、背景图等）
    ```
  - 使用 MCP 工具获取图片资源后，必须下载到本地对应目录
  - 图片引用路径格式：
    ```ts
    // 图标类资源
    const iconPath = '/static/images/icons/icon-name.png'
    
    // 一般图片资源
    const imagePath = '/static/images/img/image-name.png'
    ```
  - 图片命名规则：
    - 使用全小写英文
    - 多个单词间使用连字符（-）连接
    - 名称应反映图片用途或内容（如 `channel-mt.png`, `logo-main.png`）

### **3.3 iconfont 使用规范**

- **基本原则**：
  - 项目统一使用 `iconfont` 的 `Font class` 方式管理和使用图标，以确保跨平台兼容性（H5、App、小程序）。
  - 为避免在 App 和小程序端的兼容性问题，**必须使用 `Base64` 格式的 `iconfont` CSS**。

- **操作流程**：
  1. **登录 iconfont**：访问[阿里巴巴矢量图标库](mdc:https:/www.iconfont.cn)并登录。
  2. **选择/上传图标**：将项目所需图标添加入库，并添加至项目 (当前项目ID: `4274847`)。
  3. **生成 Base64 CSS**：在 "我的项目" 中，选择 `Font class`，并**勾选 `Base64` 选项**，然后点击 "查看在线链接" 生成代码。
  4. **引入项目**：
     - 复制生成的 `@font-face` 和 `.iconfont` 相关 CSS 代码。
     - 将代码**覆盖**到 `src/style/iconfont.css` 文件中。
     - 确保 `src/style/index.scss` 中已引入 `iconfont.css`：`@import './iconfont.css';`。

- **使用方法**：
  - 在 Vue 组件中，使用 `<view>` 或 `<i>` 标签，并添加 `iconfont` 和对应的图标类名。
  - 类名由 `iconfont` 和图标名称组成（如：`icon-facebook`）。

- **代码示例**：
  ```vue
  <template>
    <view>
      <!-- 正确使用 iconfont -->
      <view class="iconfont icon-facebook"></view>
      
      <!-- 结合 UnoCSS 修改颜色和大小 -->
      <i class="iconfont icon-twitter text-blue text-48rpx"></i>
    </view>
  </template>
  ```

- **注意事项**：
  - 每当在 `iconfont.cn` 项目中新增或修改图标后，都需要重新生成 `Base64` CSS 代码并更新到 `src/style/iconfont.css` 文件中。
  - 当前 `src/style/iconfont.css` 的 `url` 使用了 `//at.alicdn.com/...` 协议，在 App 端可能导致问题。切换到 `Base64` 模式可以彻底解决此问题。

### **3.4 页面布局与安全区规范**

- **目的**：确保在所有设备（特别是刘海屏、全面屏手机）上，页面内容都能显示在安全区域内，避免被系统 UI（如状态栏、底部 Home 指示条）遮挡。
- **实现方式**：
  - **页面根元素**：建议所有页面的根 `<view>` 元素添加一个特定的类名，如 `page-container`，方便统一处理。
  - **底部安全区**：
    - 对于需要通栏展示到底部的内容（如包含固定在底部的按钮、tabbar等），必须为其容器增加 `padding-bottom` 以避开底部安全区。
    - 推荐使用 UnoCSS 的 `pb-safe` 原子类（需在 `uno.config.ts` 中预设，例如 `safe: 'env(safe-area-inset-bottom)'`）。
    - 如果不使用 UnoCSS 原子类，可以直接在 SCSS 中处理：
      ```scss
      .page-container {
        padding-bottom: constant(safe-area-inset-bottom);
        padding-bottom: env(safe-area-inset-bottom);
      }
      ```
  - **顶部安全区**：
    - 除非使用自定义导航栏，否则 `uni-app` 默认导航栏会自动处理顶部安全区。
    - 如果使用自定义导航栏，需要为页面容器增加 `padding-top` 以避开状态栏。
    - 推荐使用 UnoCSS 的 `pt-safe-top` 或类似原子类。
    - SCSS 处理方式：
      ```scss
      .page-container {
        padding-top: constant(safe-area-inset-top);
        padding-top: env(safe-area-inset-top);
      }
      ```
- **最佳实践**：
  - 在生成新页面时，自动为根元素添加 `page-container` 类，并在对应的 `scss` 文件中默认添加安全区处理代码。
  - 对于页面底部有固定操作栏的场景，应将安全区 `padding` 应用于操作栏的父容器，而不是页面主内容区域，以确保操作栏本身不被遮挡。

### **4. 组件封装规范（基于sy-popup示例）**

#### **4.1 文件结构**

Each component should contain the following files:
- `组件名.vue`：组件模板、逻辑和样式
- `组件逻辑文件.ts`：组件属性、类型定义和辅助函数

例如：
```
src/components/sy-popup/
├── sy-popup.vue    # 组件模板和主逻辑
└── popup.ts        # 组件属性定义和类型
```

#### **4.2 属性与类型定义**

- 所有组件属性应在独立的TS文件中定义
- 使用JSDoc注释详细说明每个属性的用途
- 为所有属性提供合理的默认值
- 使用TypeScript确保类型安全

```ts
/**
 * 组件属性定义
 */
export const componentProps = {
  /** 属性说明 */
  propName: {
    type: PropType,
    default: defaultValue,
    required: isRequired,
  },
}
```

#### **4.3 组件编写规范**

- 使用`<script setup lang="ts">`语法
- 从外部TS文件导入属性定义
- 使用`defineProps`和`defineEmits`声明属性和事件
- 使用`defineExpose`明确暴露需要的属性和方法
- 组件内部逻辑按照功能分组并添加注释

```vue
<script setup lang="ts">
import { componentProps } from './component'

// 定义组件属性
const props = defineProps(componentProps)

// 定义事件
const emit = defineEmits(['event1', 'event2'])

// 暴露属性和方法
defineExpose({
  methodName,
})
</script>
```

#### **4.4 样式规范**

- 使用`lang="scss"`进行样式编写
- 所有样式类名应以组件名为前缀（如`sy-popup-content`）
- 避免使用全局样式，确保样式隔离
- 样式按照布局结构有序组织

#### **4.5 事件处理**

- 事件处理函数应以`on`开头（如`onConfirmClick`）
- 通过`emit`触发外部事件
- 支持`v-model`双向绑定时应遵循Vue3标准实践

#### **4.6 组件插槽**

- 提供默认插槽和/或具名插槽以增强组件灵活性
- 为插槽提供合理的默认内容（如有必要）

#### **4.7 使用示例**

```vue
<template>
  <sy-popup
    v-model="showPopup"
    title="提示"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    自定义内容
  </sy-popup>
</template>
```

### **5. 项目结构**

- **目录规范**：
  ```
  src/
  ├── api/          # API 接口定义
  ├── components/   # 公共组件
  ├── composables/  # 组合式函数
  ├── pages/        # 页面文件
  │   └── module/   # 模块目录
  │       ├── index.vue    # 页面模板
  │       ├── index.ts     # 页面逻辑
  │       └── index.scss   # 页面样式
  ├── static/       # 静态资源
  ├── stores/       # Pinia 状态管理
  ├── styles/       # 全局样式
  ├── types/        # TypeScript 类型定义
  └── utils/        # 工具函数
  ```

- **文件命名**：
  - 页面文件：kebab-case（如：`user-profile.vue`）
  - 组件文件：PascalCase（如：`UserAvatar.vue`）
  - 工具函数：camelCase（如：`formatDate.ts`）

### **6. Wot Design Uni 组件使用规范**

- **组件选择**：
  - 优先使用 [Wot Design Uni](mdc:https:/wot-design-uni.netlify.app) 提供的组件
  - 使用组件时参考官方文档的示例和 API 说明
  - 避免重复造轮子，优先使用组件库提供的功能

- **样式覆盖规范**：
  ```scss
  .page-container {
    // 最外层容器类名
    :deep() {
      // 使用 :deep() 选择器包裹所有组件样式覆盖
      .wd-component {
        // 组件样式覆盖
      }
    }
  }
  ```

- **组件样式隔离**：
  - 每个页面的样式必须使用唯一的容器类名包裹
  - 使用 `:deep()` 选择器处理组件样式覆盖
  - 避免全局样式污染

- **组件属性规范**：
  - 使用 TypeScript 类型定义组件 props
  - 必须的 props 要设置 `required: true`
  - 提供默认值的 props 要设置 `default`

### **7. 文件组织规范**

- **页面文件拆分**：
  - 将页面拆分为三个独立文件：
    - `index.vue`: 模板和组件引用
    - `index.ts`: 业务逻辑和数据处理
    - `index.scss`: 样式定义
  - **样式引入规范**:
    - 在 `.vue` 文件中，必须使用 `src` 方式引入独立的 `.scss` 文件，示例如下：
      ```vue
      <style lang="scss" src="./index.scss" scoped></style>
      ```

- **组件文件拆分**：
  - 复杂组件同样拆分为三个文件：
    - `ComponentName.vue`
    - `ComponentName.ts`
    - `ComponentName.scss`

- **类型定义**：
  - 在 `types` 目录下创建对应的类型定义文件
  - 使用 `.d.ts` 扩展名
  - 导出类型供其他文件使用

### **8. 页面路由与导航栏配置规范**

- **页面标题配置**：
  - 所有页面必须在 `<route>` 标签中配置页面标题
  - 使用 `navigationBarTitleText` 属性设置页面标题
  - 页面标题应简洁明了，准确描述页面功能

- **标准导航栏配置**：
  ```vue
  <route lang="json5">
  {
    style: {
      navigationBarTitleText: '页面标题',
    },
  }
  </route>
  ```

- **自定义导航栏配置**：
  - 仅在特殊需求下使用自定义导航栏
  - 如登录页面、启动页等需要完全自定义的页面
  - 自定义导航栏配置示例：
  ```vue
  <route lang="json5">
  {
    style: {
      navigationBarTitleText: '',
      navigationStyle: 'custom',
      'app-plus': {
        titleNView: false,
      },
    },
  }
  </route>
  ```

- **导航栏配置规范**：
  - 优先使用系统默认导航栏，保持应用一致性
  - 自定义导航栏时必须考虑不同平台的适配
  - 导航栏标题应与页面内容相符
  - 避免过长的标题文本，建议不超过10个字符

- **路由配置最佳实践**：
  - 每个页面的 `<route>` 配置应放在文件顶部
  - 使用 `json5` 格式以支持注释和更好的可读性
  - 配置项按功能分组，保持代码整洁
  - 特殊配置项应添加注释说明用途

### **9. 状态管理**

- **Pinia 使用规范**：
  - 使用 Pinia 进行状态管理
  - Store 文件使用 camelCase 命名
  - 每个模块创建独立的 store
  - 使用 `defineStore` 定义 store
  - 为提高应用稳定性，store 中应包含完整的初始状态定义

- **示例代码**：
  
  ❌ **错误用法**（可能导致白屏）：
  ```ts
  // 顶层直接调用 useStore - 严禁这样做
  import { useUserStore } from '@/store/user'
  const userStore = useUserStore()
  
  export function getUserInfo() {
    return userStore.userInfo
  }
  ```
  
  ✅ **正确用法**：
  ```ts
  // 仅在函数内部调用 useStore
  import { useUserStore } from '@/store/user'
  
  export function getUserInfo() {
    const userStore = useUserStore()
    return userStore.userInfo
  }
  ```
  
  ✅ **在 App.vue 中的正确用法**：
  ```vue
  <script setup lang="ts">
  import { useUserStore } from '@/store/user'
  import { onLaunch } from '@dcloudio/uni-app'
  
  onLaunch(() => {
    // 在生命周期函数内部调用 useStore
    setTimeout(() => {
      const userStore = useUserStore()
      // 进行操作...
    }, 0)
  })
  </script>
  ```
  
  ✅ **确保 store 包含完整初始状态**：
  ```ts
  // user.ts
  export const useUserStore = defineStore('user', {
    state: () => ({
      userInfo: null,
      token: '', // 确保包含所有可能被访问的属性
      isLogin: false,
      // ...其他属性
    }),
    // ...actions 和 getters
  })
  ```

### **10. API 与数据请求**

- **API 规范**：
  - 使用 `@tanstack/vue-query` 进行数据请求
  - API 接口统一在 `src/api` 目录下管理
  - 使用 TypeScript 定义接口类型
  - 统一错误处理

### **11. Git 工作流**

- **分支管理**：
  - `main`: 主分支，保持稳定
  - `dev`: 开发分支
  - `feature/*`: 功能分支
  - `bugfix/*`: 修复分支

- **提交规范**：
  - 使用 `czg` 进行规范化提交
  - 遵循 Conventional Commits 规范
  - 提交前必须通过 ESLint 和 Stylelint 检查

### **12. 构建与部署**

- **构建命令**：
  - 开发环境：`pnpm dev`
  - 生产构建：`pnpm build`
  - 类型检查：`pnpm type-check`

- **环境配置**：
  - 使用 `.env` 文件管理环境变量
  - 区分开发、测试、生产环境

### **13. 性能优化**

- **代码分割**：
  - 路由级别的代码分割
  - 组件按需加载
  - 合理使用异步组件

- **资源优化**：
  - 图片资源压缩
  - 使用 CDN 加速
  - 合理使用缓存策略

### **14. 测试规范**

- **单元测试**：
  - 使用 `@dcloudio/uni-automator` 进行自动化测试
  - 关键业务逻辑必须编写测试用例
  - 保持测试覆盖率

### **15. 文档规范**

- **代码注释**：
  - 使用 JSDoc 规范注释
  - 复杂逻辑必须添加注释
  - 组件必须包含使用说明

- **文档维护**：
  - 及时更新 README.md
  - 维护 CHANGELOG.md
  - 编写组件使用文档

### **16. 安全规范**

- **数据安全**：
  - 敏感数据加密存储
  - 使用 HTTPS 请求
  - 防止 XSS 和 CSRF 攻击

- **代码安全**：
  - 避免硬编码敏感信息
  - 使用环境变量管理配置
  - 定期更新依赖包

### **17. 发布流程**

- **版本管理**：
  - 遵循语义化版本（Semantic Versioning）
  - 主版本号：不兼容的 API 修改
  - 次版本号：向下兼容的功能性新增
  - 修订号：向下兼容的问题修正

- **发布检查清单**：
  - 代码审查通过
  - 测试用例通过
  - 文档更新完成
  - 性能测试达标
  - 安全检查通过

### **18. AI 助手交互规范**

#### **18.1 复杂任务提示词模板**

- **目的**：为了更高效地与 AI 编程助手协作，特别是在处理复杂组件或功能时，建议使用标准化的提示词模板。
- **模板位置**：请参考项目 `.cursor/rules/prompt_template_guideline.md` 文件中定义的提示词模板。
- **使用方式**：在向 AI 助手提出复杂需求前，请先在该模板的副本中填写相关信息，然后将填写完整的内容作为提问的主要部分。
- **益处**：有助于 AI 更准确地理解需求、遵循项目规范，并生成更符合预期的代码和建议。

#### **18.2 重点参考规范**
- 在与 AI 交互时，请特别提示 AI 注意本 `project.mdc` 文档中定义的以下核心规范：
    - [ ] 组件封装规范 (章节 4)
    - [ ] UnoCSS 使用规范 (章节 3.1)
    - [ ] MasterGo 设计稿与像素转换规范 (章节 3.2)
    - [ ] 文件组织与命名规范 (章节 5, 7)
    - [ ] Wot Design Uni 组件使用规范 (章节 6)
    - [ ] 页面路由与导航栏配置规范 (章节 8)
    - [ ] 状态管理 (Pinia) 规范 (章节 9)
    - [ ] API 与数据请求规范 (章节 10 及 请求规范部分)

#### **18.3 小功能包开发方法**

- **拆分原则**：将复杂功能拆分为多个独立的小功能包，每个功能包专注于一个明确的功能点。
- **提问方式**：使用提示词模板针对单个功能包提问，确保每次交互都有明确的目标和范围。
- **迭代开发**：基于 AI 助手的回答，逐步完善和扩展功能，而不是一次尝试解决所有问题。
- **代码集成**：在获得各功能包的代码后，按照项目架构将它们整合到最终的组件或页面中。

#### **18.4 AI 助手使用最佳实践**

- **明确任务边界**：清晰定义每次交互的目标和范围，避免过于宽泛的问题。
- **提供足够上下文**：包括相关文件路径、组件关系、数据流向等信息。
- **参考现有代码**：引导 AI 助手参考项目中类似的实现，保持代码风格一致。
- **渐进式交互**：从基本框架开始，逐步添加功能和优化，而不是一次性生成完整解决方案。
- **验证与反馈**：对 AI 助手生成的代码进行验证，并提供具体反馈以便进一步改进。

#### **18.5 MasterGo 图片资源处理最佳实践**

- **使用MCP工具获取图片后的处理流程**：
  1. 不要直接在代码中使用MasterGo的图片URL
  2. 将所有图片下载到本地，按照图片类型分别存放到`/src/static/images/icons/`或`/src/static/images/img/`目录
  3. 使用统一的路径格式引用本地图片：`/static/images/icons/图标名.png`或`/static/images/img/图片名.png`
  4. 图片命名使用全小写，多单词使用连字符（-）连接
  
- **MCP图片处理命令示例**：
  ```bash
  # 1. 创建目录结构（如果不存在）
  mkdir -p src/static/images/icons src/static/images/img
  
  # 2. 下载图片到对应目录
  curl -o src/static/images/icons/icon-name.png https://image-resource.mastergo.com/xxx
  
  # 3. 在代码中使用本地路径
  const iconPath = '/static/images/icons/icon-name.png'
  ```

### **19. Cursor 交互安全规范**

- **禁止启动代码**：
  - 在使用 Cursor AI 助手解决问题的过程中，严禁有任何形式的代码启动、运行、构建、热更新、开发服务器启动等动作。
  - 仅允许进行代码分析、编辑、重构、生成、重命名、删除等静态操作。
  - 如需运行、构建或启动项目，必须由开发者手动在本地终端执行，AI 助手不得主动发起相关命令。

### **20. 组件引入与加载规范（App端专用）**

- **禁止使用异步组件**  
  - 本项目为 App 端项目，为保证兼容性和运行时性能，**所有组件必须以同步方式引入和注册**，严禁使用 Vue 的 `defineAsyncComponent`、`import()` 等异步组件加载方式。
  - 组件引用必须采用同步 import 语法，例如：
    ```ts
    // 正确示例
    import MyComponent from '@/components/MyComponent.vue'
    ```
  - 禁止如下写法：
    ```ts
    // 错误示例
    const MyComponent = defineAsyncComponent(() => import('@/components/MyComponent.vue'))
    ```
  - 页面、业务模块、全局组件等，均需遵循同步引入规范。

- **原因说明**  
  - App 端（如 uni-app、原生小程序等）不支持运行时动态加载 JS 资源，异步组件会导致白屏、组件无法渲染等问题。
  - 同步组件加载可确保所有依赖在编译期打包进主包，提升稳定性和兼容性。

- **代码审查要求**  
  - 代码评审时需重点检查组件引入方式，发现异步组件写法必须整改为同步引入。

---

### **总结**

- 严格遵循以上规范，确保代码质量和项目可维护性
- 优先使用 Wot Design Uni 组件库
- 保持文件结构清晰，遵循文件拆分原则
- 定期进行代码审查和规范更新
- 保持团队沟通，及时同步规范变更
- 持续优化开发流程和工具链

# uni-super-shop-admin 请求规范

## 1. 统一请求封装

- 所有 API 请求必须统一使用 `src/utils/http.ts` 中的 `http`、`http.get`、`http.post` 方法进行封装和调用。
- 禁止直接调用 `uni.request`，禁止使用 `src/utils/request.ts` 的 request 适配模式。

**推荐用法示例：**
```ts
import { http } from '@/utils/http'

// GET 请求
http.get<UserInfo>('/api/user/info').then(res => {
  // 处理数据
})

// POST 请求
http.post<LoginResult>('/api/login', { username, password }).then(res => {
  // 处理数据
})
```

## 2. 401 错误码统一处理

- 当后端返回 HTTP 状态码 401（未登录/登录失效），或响应体中 `resultCode` 为 401 时，必须统一做如下处理：
  1. 调用 `userStore.clearUserInfo()` 清理本地用户信息和 token。
  2. 自动跳转到登录页 `/pages/login/index/index`。
  3. 可选：在控制台输出调试信息，便于排查。
- 该逻辑已在 `src/utils/http.ts` 的 `http` 方法内部实现，无需在业务代码中重复处理。

## 3. 错误提示与异常处理

- 除 401 外的其他错误，自动弹出后端返回的错误信息（`msg` 字段），如无则显示"请求错误"。
- 网络异常时，自动弹出"网络错误，换个网络试试"提示。

## 4. 目录与命名规范

- 所有 API 封装、请求逻辑统一放在 `src/service/` 目录下，按业务模块拆分。
- 工具函数统一放在 `src/utils/`，如 `http.ts`。
- 禁止在业务代码中直接调用 `uni.request`，必须通过 `http` 封装。

## 5. API 调用示例

**service 层示例**
```ts
// src/service/user/index.ts
import { http } from '@/utils/http'

export function getUserInfo() {
  return http.get<UserInfo>('/api/user/info')
}
```

**页面调用示例**
```ts
import { getUserInfo } from '@/service/user'

onMounted(async () => {
  const res = await getUserInfo()
  // 处理用户信息
})
```

---

- 只允许使用 `src/utils/http.ts` 的 http 封装模式。
- 401 错误码自动清理用户信息并跳转登录页，无需业务层重复处理。
- 其他错误自动弹 toast，网络异常有统一提示。
- 所有 API 相关代码按模块归类到 `src/service/`，工具函数放 `src/utils/`。
- 禁止直接用 `uni.request`，禁止用 `request.ts` 的 request 适配。
