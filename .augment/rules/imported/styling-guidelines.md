---
type: "always_apply"
---

# uni-super-shop-admin 样式开发规范

## UnoCSS 使用规范

### 基本原则
- **原子类优先**：优先使用 UnoCSS 原子类进行样式开发
- **SCSS 补充**：复杂或重复的样式组合使用 SCSS 封装
- **直接使用**：在模板中直接使用 UnoCSS 类名

### 常用原子类示例
```vue
<template>
  <!-- 布局相关 -->
  <view class="flex flex-col gap-4 p-4 bg-white rounded-lg shadow">
    <!-- 文本样式 -->
    <text class="text-lg font-bold text-primary">标题</text>
    
    <!-- 交互元素 -->
    <view class="flex items-center justify-between">
      <text class="text-sm text-gray-500">描述文本</text>
      <button class="bg-primary text-white px-4 py-2 rounded-full">按钮</button>
    </view>
  </view>
</template>
```

### 主题配置
- 使用项目预定义的颜色变量：`text-primary`、`bg-secondary`
- 避免硬编码颜色值
- 遵循设计系统的间距和断点规范

### 响应式设计
```vue
<template>
  <!-- 响应式布局 -->
  <view class="w-full md:w-1/2 lg:w-1/3">
    <!-- 响应式文本 -->
    <text class="text-sm md:text-base lg:text-lg">响应式文本</text>
  </view>
</template>
```

## MasterGo 设计稿转换规范

### 像素转换规则
- **设计稿标准**：MasterGo 设计稿统一使用 375px 宽度
- **转换公式**：设计稿 1px = 代码 2rpx
- **示例转换**：
  ```scss
  // 设计稿中元素宽度为 100px
  .element {
    width: 200rpx; // 100px × 2 = 200rpx
  }
  
  // 设计稿中字体大小为 16px
  .text {
    font-size: 32rpx; // 16px × 2 = 32rpx
  }
  
  // 设计稿中边距为 20px
  .container {
    padding: 40rpx; // 20px × 2 = 40rpx
  }
  ```

### MasterGo 图片资源处理
#### ❌ 严禁操作
```ts
// 严禁直接引用 MasterGo 远程图片URL
const wrongPath = 'https://image-resource.mastergo.com/xxx'
```

#### ✅ 正确流程
1. **下载图片**：将 MasterGo 设计稿中的图片下载到本地
2. **分类存放**：按图片类型存放到对应目录
3. **本地引用**：使用本地路径引用图片

```ts
// 图标类资源
const iconPath = '/static/images/icons/channel-mt.png'

// 一般图片资源
const imagePath = '/static/images/img/logo-main.png'
```

#### 图片目录结构
```
src/static/images/
├── icons/        # 图标类图片
│   ├── channel-mt.png
│   ├── channel-ele.png
│   └── ...
└── img/          # 一般图片资源
    ├── logo-main.png
    ├── banner-home.png
    └── ...
```

#### 图片命名规则
- 使用全小写英文
- 多单词使用连字符（-）连接
- 名称反映图片用途（如：`channel-mt.png`、`logo-main.png`）

## iconfont 使用规范

### 基本配置
- **项目ID**：4274847
- **使用方式**：Font class 模式
- **格式要求**：必须使用 Base64 格式确保跨平台兼容性

### 操作流程
1. **登录 iconfont**：访问 [阿里巴巴矢量图标库](mdc:https:/www.iconfont.cn)
2. **管理图标**：在项目中添加/修改图标
3. **生成代码**：选择 Font class，勾选 Base64 选项
4. **更新项目**：将生成的 CSS 覆盖到 `src/style/iconfont.css`

### 使用方法
```vue
<template>
  <!-- 基础使用 -->
  <view class="iconfont icon-facebook"></view>
  
  <!-- 结合 UnoCSS 修改样式 -->
  <i class="iconfont icon-twitter text-blue text-48rpx"></i>
  
  <!-- 在按钮中使用 -->
  <button class="flex items-center gap-2">
    <i class="iconfont icon-add text-white"></i>
    <text>添加</text>
  </button>
</template>
```

### 样式覆盖
```scss
.custom-icon {
  &.iconfont {
    font-size: 48rpx;
    color: #1890ff;
    
    &::before {
      margin-right: 8rpx;
    }
  }
}
```

## Wot Design Uni 组件样式覆盖规范

### 覆盖原则
- 使用 `:deep()` 选择器包裹所有组件样式覆盖
- 每个页面使用唯一的容器类名
- 避免全局样式污染

### 标准覆盖方式
```scss
.page-container {
  // 页面样式
  
  :deep() {
    // Wot Design Uni 组件样式覆盖
    .wd-button {
      border-radius: 12rpx;
      
      &--primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
    }
    
    .wd-popup {
      &__content {
        border-radius: 24rpx 24rpx 0 0;
      }
    }
    
    .wd-cell {
      padding: 24rpx 32rpx;
      
      &__title {
        font-size: 32rpx;
        font-weight: 500;
      }
    }
  }
}
```

### 组件主题定制
```scss
// 全局组件主题（在 style/index.scss 中）
:root {
  --wd-color-primary: #1890ff;
  --wd-color-success: #52c41a;
  --wd-color-warning: #faad14;
  --wd-color-danger: #ff4d4f;
}

// 局部组件主题
.custom-theme {
  :deep() {
    .wd-button--primary {
      --wd-button-primary-bg: #722ed1;
      --wd-button-primary-border: #722ed1;
    }
  }
}
```

## 安全区域处理规范

### 页面安全区域
```scss
.page-container {
  // 底部安全区域（适用于所有页面）
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  
  // 顶部安全区域（自定义导航栏时）
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
```

### 弹窗底部安全区域处理

#### 页面类型检测
```ts
// 检测当前页面是否为 TabBar 页面
const isTabbarPage = ref(false)

const checkTabbarPage = () => {
  try {
    const pages = getCurrentPages()
    if (pages && pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage.route || ''
      
      // 定义 TabBar 页面路径
      const tabbarPages = [
        'pages/index/index',
        'pages/about/about',
        'pages/dishManagement/index',
        'pages/my/index',
        'pages/storeDetail/index',
      ]
      
      isTabbarPage.value = tabbarPages.some((path) => currentRoute.includes(path))
    }
  } catch (error) {
    console.error('检测 tabbar 页面失败:', error)
    isTabbarPage.value = false
  }
}

onMounted(() => {
  checkTabbarPage()
})
```

#### 动态安全区域实现
```vue
<template>
  <wd-popup
    v-model="show"
    position="bottom"
    custom-style="border-radius:24rpx 24rpx 0 0;"
  >
    <view class="popup-container">
      <!-- 弹窗内容 -->
      <view class="popup-content">
        <!-- 具体内容 -->
      </view>
      
      <!-- 动态安全区域 -->
      <view 
        class="popup-safe-area"
        :style="{ height: isTabbarPage ? '80rpx' : '0rpx' }"
      ></view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
.popup-safe-area {
  /* 高度由内联样式动态控制 */
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #fff; /* 与弹窗背景色一致 */
}
</style>
```

### 安全区域规则总结
1. **TabBar 页面**：安全区域基础高度 `80rpx`
2. **非 TabBar 页面**：安全区域基础高度 `0rpx`
3. **设备安全区域**：所有页面都需要 `env(safe-area-inset-bottom)`
4. **背景色一致**：确保安全区域背景色与弹窗一致

## SCSS 预处理器规范

### 变量定义
```scss
// 颜色变量
$primary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;
$text-color: #333333;
$text-color-secondary: #666666;
$border-color: #e8e8e8;
$background-color: #f5f5f5;

// 尺寸变量
$border-radius-base: 8rpx;
$border-radius-large: 16rpx;
$spacing-xs: 8rpx;
$spacing-sm: 16rpx;
$spacing-md: 24rpx;
$spacing-lg: 32rpx;
$spacing-xl: 48rpx;

// 字体变量
$font-size-xs: 24rpx;
$font-size-sm: 28rpx;
$font-size-base: 32rpx;
$font-size-lg: 36rpx;
$font-size-xl: 40rpx;
```

### Mixin 定义
```scss
// 省略号文本
@mixin ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

// 居中布局
@mixin center($direction: both) {
  position: absolute;
  @if $direction == both {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  } @else if $direction == horizontal {
    left: 50%;
    transform: translateX(-50%);
  } @else if $direction == vertical {
    top: 50%;
    transform: translateY(-50%);
  }
}
```

### 使用示例
```scss
.product-title {
  @include ellipsis(2);
  font-size: $font-size-lg;
  color: $text-color;
  margin-bottom: $spacing-md;
}

.loading-overlay {
  @include center();
  z-index: 999;
}
```

## 响应式布局规范

### 断点定义
```scss
// 断点变量
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

// 媒体查询 mixin
@mixin media($breakpoint) {
  @if $breakpoint == sm {
    @media (min-width: #{$breakpoint-sm}) {
      @content;
    }
  } @else if $breakpoint == md {
    @media (min-width: #{$breakpoint-md}) {
      @content;
    }
  } @else if $breakpoint == lg {
    @media (min-width: #{$breakpoint-lg}) {
      @content;
    }
  } @else if $breakpoint == xl {
    @media (min-width: #{$breakpoint-xl}) {
      @content;
    }
  }
}
```

### 响应式使用
```scss
.product-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-md;
  
  @include media(md) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @include media(lg) {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

## 样式性能优化

### CSS 优化原则
1. **避免深层嵌套**：SCSS 嵌套不超过 3 层
2. **合理使用选择器**：避免使用复杂的选择器
3. **减少重排重绘**：避免频繁修改会引起重排的属性
4. **使用 GPU 加速**：合理使用 `transform` 和 `opacity`

### 性能优化示例
```scss
// ✅ 好的做法
.list-item {
  transform: translateZ(0); // 开启 GPU 加速
  will-change: transform; // 提前声明变化属性
  
  &:hover {
    transform: translateY(-2px); // 使用 transform 而非 top
  }
}

// ❌ 避免的做法
.list-item {
  &:hover {
    top: -2px; // 会引起重排
    box-shadow: 0 4px 8px rgba(0,0,0,0.15); // 复杂阴影影响性能
  }
}
```

## 样式规范检查清单

### 开发阶段检查
- [ ] 优先使用 UnoCSS 原子类
- [ ] MasterGo 设计稿像素值正确转换（1px = 2rpx）
- [ ] 图片资源已下载到本地对应目录
- [ ] iconfont 使用 Base64 格式
- [ ] 弹窗组件包含安全区域处理
- [ ] 使用 `:deep()` 覆盖第三方组件样式
- [ ] SCSS 变量和 mixin 正确使用
- [ ] 响应式布局在不同设备上测试通过

### 代码审查检查
- [ ] 无全局样式污染
- [ ] BEM 命名法正确使用
- [ ] 样式文件结构清晰
- [ ] 无冗余或未使用的样式
- [ ] 兼容性和性能考虑到位
