---
type: "always_apply"
---

# uni-super-shop-admin 项目架构规范

## 项目概述

本项目基于 [unibest](mdc:https:/unibest.tech) 框架构建，是一个高质量的 uni-app 开发模板。

### 技术栈
- **框架**: Vue 3 + TypeScript + Vite 5
- **UI组件库**: Wot Design Uni
- **样式方案**: UnoCSS + SCSS
- **状态管理**: Pinia
- **数据请求**: @tanstack/vue-query
- **包管理**: pnpm

### 环境要求
- Node.js >= 18
- pnpm >= 7.30

## 目录结构规范

### 核心目录结构
```
src/
├── components/      # 公共业务组件
├── pages/           # 主包页面（TabBar页面、登录页）
├── pages-sub/       # 功能分包页面
├── service/         # API服务层
├── store/           # Pinia状态管理
├── static/          # 静态资源
├── style/           # 全局样式
├── utils/           # 工具函数
├── types/           # TypeScript类型定义
├── hooks/           # 组合式函数
├── layouts/         # 页面布局
└── interceptors/    # 请求拦截器
```

### 目录功能说明

#### `/components` - 公共组件
- 存放可复用的业务组件
- 组件使用 PascalCase 命名（如：`SyPopup.vue`）
- 每个组件包含：`ComponentName.vue` + `component-name.ts`

#### `/pages` - 主包页面
- 仅包含 TabBar 页面和登录页面
- 减少主包体积，提高首次加载速度

#### `/pages-sub` - 分包页面
- 按功能模块拆分的分包页面
- 目录结构：`/pages-sub/module-name/page-name/`

#### `/service` - API服务层
- 按业务模块组织API接口
- 统一使用 `src/utils/http.ts` 进行请求封装

#### `/static` - 静态资源
```
static/
├── images/
│   ├── icons/       # 图标类图片
│   └── img/         # 一般图片资源
├── mp3/             # 音频文件
└── tabbar/          # TabBar图标
```

## 文件命名规范

### 页面文件
- 使用 kebab-case 命名（如：`user-profile.vue`）
- 每个页面包含三个文件：
  - `index.vue` - 模板和组件引用
  - `index.ts` - 业务逻辑
  - `index.scss` - 样式定义

### 组件文件
- 使用 PascalCase 命名（如：`UserAvatar.vue`）
- 组件属性文件使用 kebab-case（如：`user-avatar.ts`）

### 工具函数
- 使用 camelCase 命名（如：`formatDate.ts`）

## 页面文件组织规范

### 页面三文件分离
```
pages/example/
├── index.vue        # 模板和组件引用
├── index.ts         # 业务逻辑和数据处理  
└── index.scss       # 样式定义
```

### index.vue 模板文件
```vue
<route lang="json5">
{
  style: {
    navigationBarTitleText: '页面标题',
  },
}
</route>

<template>
  <view class="page-container">
    <!-- 页面内容 -->
  </view>
</template>

<script setup lang="ts">
// 导入业务逻辑
import './index'
</script>

<style lang="scss" src="./index.scss" scoped></style>
```

### index.ts 逻辑文件
```ts
// 页面业务逻辑
import { ref, onMounted } from 'vue'

// 响应式数据
const data = ref()

// 生命周期
onMounted(() => {
  // 初始化逻辑
})

// 方法定义
const handleAction = () => {
  // 处理逻辑
}
```

## 路由与导航配置

### 页面标题配置
- 所有页面必须在 `<route>` 标签中配置页面标题
- 使用 `navigationBarTitleText` 设置标题

```vue
<route lang="json5">
{
  style: {
    navigationBarTitleText: '页面标题',
  },
}
</route>
```

### 自定义导航栏
仅在特殊需求下使用：
```vue
<route lang="json5">
{
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>
```

## 分包配置规范

### 分包拆分原则
- 主包：TabBar页面 + 登录页面
- 分包：按功能模块拆分其他页面
- 控制主包体积，提高加载性能

### 分包目录示例
```
pages-sub/
├── order/           # 订单管理模块
│   ├── list/
│   ├── detail/
│   └── search/
├── product/         # 商品管理模块
│   ├── list/
│   └── detail/
└── settings/        # 设置模块
    ├── profile/
    └── security/
```

## 静态资源管理

### 图片资源规范
- **禁止使用远程图片URL**（如MasterGo链接）
- 所有图片必须下载到本地static目录
- 图片命名使用小写+连字符（如：`icon-home.png`）

### 图片目录结构
```
static/images/
├── icons/           # 图标类图片
│   ├── icon-home.png
│   └── icon-user.png
└── img/             # 一般图片
    ├── logo.png
    └── banner.jpg
```

### 图片引用方式
```ts
// 正确的图片引用
const iconPath = '/static/images/icons/icon-home.png'
const imagePath = '/static/images/img/logo.png'
```

## 开发工具配置

### 必需的VSCode插件
- ESLint
- Prettier  
- Stylelint
- TypeScript Vue Plugin (Volar)
- UnoCSS

### 推荐设置
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.fixAll.stylelint": true
  }
}
```

## 性能优化建议

### 代码分割
- 合理使用分包减少主包体积
- 按需引入第三方库
- 避免在主包中引入过多依赖

### 资源优化
- 图片资源压缩
- 音频文件使用合适格式和质量
- 合理使用缓存策略

### 内存管理
- 及时清理定时器和事件监听
- 避免内存泄漏
- 合理使用reactive和ref
