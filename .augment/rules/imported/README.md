---
type: "always_apply"
---

# uni-super-shop-admin Cursor Rules

本项目的 Cursor AI 开发规范文件集合，旨在为 AI 助手和开发者提供统一、详细的开发指导。

## 技术栈概览

- **框架**: unibest (uni-app) + Vue3 + TypeScript
- **构建**: Vite5 + Pnpm
- **UI**: Wot Design Uni + UnoCSS + SCSS
- **状态管理**: Pinia
- **代码规范**: ESLint + Prettier + Stylelint

## 规范文件说明

### 📁 [project-architecture.mdc](mdc:project-architecture.mdc)
- 项目目录结构与文件组织规范
- 技术栈要求与开发环境配置
- 文件命名与分包管理规则

### 🧩 [component-development.mdc](mdc:component-development.mdc)
- Vue3 组件开发规范与最佳实践
- 组件属性定义与类型安全
- 事件处理与 v-model 实现规范

### 🎨 [styling-guidelines.mdc](mdc:styling-guidelines.mdc)
- UnoCSS 原子类使用规范
- MasterGo 设计稿转换规则 (1px = 2rpx)
- Wot Design Uni 组件样式覆盖
- 安全区域处理与响应式布局

### 🔌 [api-data-management.mdc](mdc:api-data-management.mdc)
- 统一请求封装与错误处理
- Pinia 状态管理安全规范
- API 服务层组织与类型定义

### 🔒 [security-best-practices.mdc](mdc:security-best-practices.mdc)
- Cursor AI 交互安全规范
- App 端兼容性要求
- 代码安全与性能最佳实践

### 🔄 [development-workflow.mdc](mdc:development-workflow.mdc)
- Git 工作流与提交规范
- 代码审查与测试流程
- 构建部署与版本管理

## 关键开发规范

### 🚨 安全规范
- **严禁启动代码**: AI 助手不得执行任何运行、构建、启动命令
- **App 端要求**: 禁止使用异步组件，必须同步引入
- **Store 安全**: 禁止在顶层直接调用 useStore

### 📂 目录结构
```
src/
├── components/    # 组件 (PascalCase)
├── pages/         # 主包页面
├── pages-sub/     # 分包页面  
├── service/       # API 服务层
├── static/images/ # 静态资源 (禁用远程图片)
└── utils/         # 工具函数
```

### 🎨 样式开发
- **UnoCSS 优先**: 使用原子类进行样式开发
- **设计稿转换**: MasterGo 1px = 代码 2rpx
- **安全区域**: 底部弹窗必须处理 TabBar 和设备安全区

### 🧩 组件开发
- **三文件拆分**: `.vue` + `.ts` + `.scss`
- **事件驱动**: 避免直接调用组件方法，使用事件通信
- **类型安全**: 完整的 TypeScript 类型定义

## 快速开始

### 创建新组件
```bash
mkdir src/components/SyNewComponent
touch src/components/SyNewComponent/{SyNewComponent.vue,component.ts,README.md}
```

### 创建新页面
```bash
mkdir src/pages/new-page
touch src/pages/new-page/{index.vue,index.ts,index.scss}
```

### 添加 API 服务
```bash
mkdir src/service/new-module
touch src/service/new-module/{index.ts,types.ts}
```

## AI 助手交互指南

### ✅ 推荐操作
- 代码分析、编辑、重构、生成
- 文件创建、重命名、删除
- 样式调整、组件开发
- 类型定义、接口封装

### ❌ 严禁操作
- 运行、启动、构建命令
- 热更新、开发服务器
- 包安装、环境配置
- 任何可执行操作

### 💡 最佳实践
1. 明确任务边界，专注单一功能
2. 遵循项目规范，保持代码一致性
3. 提供完整示例，包含类型定义
4. 考虑 App 端兼容性

## 常见问题 FAQ

### Q: 如何处理 MasterGo 图片资源？
A: 下载到本地 `/src/static/images/` 目录，禁止直接引用远程 URL

### Q: 底部弹窗如何处理安全区域？
A: 检测 TabBar 页面，动态设置安全区域高度 (80rpx/0rpx)

### Q: 组件间如何通信？
A: 使用事件和 v-model，避免直接调用组件方法

### Q: 如何确保 App 端兼容性？
A: 使用同步组件引入，避免异步加载和动态 import

## 相关链接

- [unibest 官方文档](mdc:https:/unibest.tech)
- [Wot Design Uni 组件库](mdc:https:/wot-design-uni.netlify.app)
- [UnoCSS 文档](mdc:https:/unocss.dev)
- [Vue3 Composition API](mdc:https:/cn.vuejs.org/guide/composition-api-introduction.html)
