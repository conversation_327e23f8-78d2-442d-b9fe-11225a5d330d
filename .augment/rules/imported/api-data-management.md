---
type: "always_apply"
---

# uni-super-shop-admin API与数据管理规范

## 统一请求封装规范

### 强制使用 http 工具
**所有 API 请求必须使用 `src/utils/http.ts` 中的封装方法**

```ts
import { http } from '@/utils/http'

// ✅ 正确用法
export function getUserInfo() {
  return http.get<UserInfo>('/api/user/info')
}

export function updateUserInfo(data: UpdateUserReq) {
  return http.post<UpdateUserRes>('/api/user/update', data)
}

// ❌ 严禁直接使用
uni.request({
  url: '/api/user/info',
  method: 'GET'
})
```

### http 工具方法
```ts
// GET 请求
http.get<ResponseType>(url: string, params?: object): Promise<ResponseType>

// POST 请求  
http.post<ResponseType>(url: string, data?: object): Promise<ResponseType>

// PUT 请求
http.put<ResponseType>(url: string, data?: object): Promise<ResponseType>

// DELETE 请求
http.delete<ResponseType>(url: string): Promise<ResponseType>

// 通用请求
http<ResponseType>(options: RequestOptions): Promise<ResponseType>
```

### 使用示例
```ts
// service/user/index.ts
import { http } from '@/utils/http'
import type { UserInfo, LoginReq, LoginRes } from './types'

/** 获取用户信息 */
export function getUserInfo() {
  return http.get<UserInfo>('/api/user/info')
}

/** 用户登录 */
export function login(data: LoginReq) {
  return http.post<LoginRes>('/api/auth/login', data)
}

/** 退出登录 */
export function logout() {
  return http.post('/api/auth/logout')
}
```

## API 服务层组织规范

### 目录结构
```
src/service/
├── user/           # 用户相关接口
│   ├── index.ts    # 接口方法
│   └── types.ts    # 类型定义
├── order/          # 订单相关接口
│   ├── index.ts
│   └── types.ts
├── product/        # 商品相关接口
│   ├── index.ts
│   └── types.ts
└── common/         # 通用接口
    ├── index.ts
    └── types.ts
```

### 接口方法定义
```ts
// service/order/index.ts
import { http } from '@/utils/http'
import type { 
  OrderList, 
  OrderDetail, 
  CreateOrderReq,
  UpdateOrderReq,
  OrderSearchParams 
} from './types'

/** 获取订单列表 */
export function getOrderList(params: OrderSearchParams) {
  return http.get<OrderList>('/api/orders', params)
}

/** 获取订单详情 */
export function getOrderDetail(orderId: string) {
  return http.get<OrderDetail>(`/api/orders/${orderId}`)
}

/** 创建订单 */
export function createOrder(data: CreateOrderReq) {
  return http.post<OrderDetail>('/api/orders', data)
}

/** 更新订单状态 */
export function updateOrderStatus(orderId: string, data: UpdateOrderReq) {
  return http.put<OrderDetail>(`/api/orders/${orderId}`, data)
}

/** 取消订单 */
export function cancelOrder(orderId: string) {
  return http.delete(`/api/orders/${orderId}`)
}
```

### 类型定义规范
```ts
// service/order/types.ts

/** 订单状态枚举 */
export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PREPARING = 'preparing',
  READY = 'ready',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled'
}

/** 订单基础信息 */
export interface OrderBase {
  id: string
  orderNo: string
  status: OrderStatus
  totalAmount: number
  createTime: string
  updateTime: string
}

/** 订单详情 */
export interface OrderDetail extends OrderBase {
  customer: CustomerInfo
  items: OrderItem[]
  address: DeliveryAddress
  remark?: string
}

/** 订单列表响应 */
export interface OrderList {
  list: OrderBase[]
  total: number
  page: number
  pageSize: number
}

/** 创建订单请求 */
export interface CreateOrderReq {
  customerId: string
  items: CreateOrderItem[]
  addressId: string
  remark?: string
}

/** 更新订单请求 */
export interface UpdateOrderReq {
  status?: OrderStatus
  remark?: string
}

/** 订单搜索参数 */
export interface OrderSearchParams {
  page?: number
  pageSize?: number
  status?: OrderStatus
  keyword?: string
  startTime?: string
  endTime?: string
}
```

## 统一错误处理机制

### 401 自动处理
`src/utils/http.ts` 已内置 401 错误处理：

```ts
// 当遇到 401 错误时，自动执行：
// 1. 调用 userStore.clearUserInfo() 清理用户信息
// 2. 跳转到登录页 '/pages/login/index/index'
// 3. 业务代码无需额外处理
```

### 其他错误处理
```ts
// 业务代码中的错误处理
try {
  const result = await getUserInfo()
  // 处理成功结果
} catch (error) {
  // http 工具已自动弹出错误提示
  // 这里只需处理特殊业务逻辑
  console.error('获取用户信息失败:', error)
}
```

### 自定义错误处理
```ts
// 如需自定义错误处理，可以使用 try-catch
async function handleCustomError() {
  try {
    const result = await http.get('/api/sensitive-operation')
    return result
  } catch (error: any) {
    // 自定义错误处理逻辑
    if (error.code === 'PERMISSION_DENIED') {
      uni.showModal({
        title: '权限不足',
        content: '您没有执行此操作的权限',
        showCancel: false
      })
    }
    throw error // 继续抛出错误
  }
}
```

## Pinia 状态管理规范

### Store 安全使用规范

#### ❌ 严禁顶层调用
```ts
// 严禁在模块顶层直接调用 useStore（会导致白屏）
import { useUserStore } from '@/store/user'
const userStore = useUserStore() // ❌ 错误！

export function getUserInfo() {
  return userStore.userInfo
}
```

#### ✅ 正确使用方式
```ts
// 仅在函数内部调用 useStore
import { useUserStore } from '@/store/user'

export function getUserInfo() {
  const userStore = useUserStore() // ✅ 正确！
  return userStore.userInfo
}

// 在组件中使用
onMounted(() => {
  const userStore = useUserStore() // ✅ 正确！
  userStore.fetchUserInfo()
})
```

### Store 定义规范
```ts
// store/user.ts
import { defineStore } from 'pinia'
import type { UserInfo } from '@/service/user/types'

export const useUserStore = defineStore('user', {
  state: () => ({
    // 确保包含完整的初始状态
    userInfo: null as UserInfo | null,
    token: '',
    isLogin: false,
    permissions: [] as string[],
  }),

  getters: {
    // 获取用户角色
    userRole: (state) => {
      return state.userInfo?.role || 'guest'
    },
    
    // 检查是否有权限
    hasPermission: (state) => {
      return (permission: string) => {
        return state.permissions.includes(permission)
      }
    }
  },

  actions: {
    // 设置用户信息
    setUserInfo(userInfo: UserInfo) {
      this.userInfo = userInfo
      this.isLogin = true
    },

    // 设置 Token
    setToken(token: string) {
      this.token = token
      // 持久化存储
      uni.setStorageSync('token', token)
    },

    // 清理用户信息（401 错误时自动调用）
    clearUserInfo() {
      this.userInfo = null
      this.token = ''
      this.isLogin = false
      this.permissions = []
      
      // 清理本地存储
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
    },

    // 获取用户信息
    async fetchUserInfo() {
      try {
        const userStore = useUserStore() // 在 action 内部调用是安全的
        const userInfo = await getUserInfo()
        this.setUserInfo(userInfo)
        return userInfo
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.clearUserInfo()
        throw error
      }
    }
  }
})
```

### Store 在页面中的使用
```ts
// pages/user/index.ts
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/user'

// 页面数据
const userInfo = ref(null)

// 获取用户信息
const handleGetUserInfo = async () => {
  try {
    const userStore = useUserStore() // 在函数内部调用
    await userStore.fetchUserInfo()
    userInfo.value = userStore.userInfo
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 生命周期
onMounted(() => {
  handleGetUserInfo()
})
```

## 页面中的数据请求模式

### 基本请求模式
```ts
// pages/order/list/index.ts
import { ref, onMounted } from 'vue'
import { getOrderList } from '@/service/order'
import type { OrderList, OrderSearchParams } from '@/service/order/types'

// 响应式数据
const orderList = ref<OrderList>({
  list: [],
  total: 0,
  page: 1,
  pageSize: 20
})

const loading = ref(false)
const searchParams = ref<OrderSearchParams>({
  page: 1,
  pageSize: 20
})

// 获取订单列表
const fetchOrderList = async (refresh = false) => {
  if (refresh) {
    searchParams.value.page = 1
  }
  
  loading.value = true
  try {
    const result = await getOrderList(searchParams.value)
    
    if (refresh) {
      orderList.value = result
    } else {
      // 分页加载
      orderList.value.list.push(...result.list)
      orderList.value.total = result.total
      orderList.value.page = result.page
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索订单
const handleSearch = (keyword: string) => {
  searchParams.value.keyword = keyword
  fetchOrderList(true)
}

// 加载更多
const handleLoadMore = () => {
  if (orderList.value.list.length < orderList.value.total) {
    searchParams.value.page++
    fetchOrderList(false)
  }
}

// 初始化
onMounted(() => {
  fetchOrderList(true)
})
```

### 表单提交模式
```ts
// pages/order/create/index.ts
import { ref, reactive } from 'vue'
import { createOrder } from '@/service/order'
import type { CreateOrderReq } from '@/service/order/types'

// 表单数据
const formData = reactive<CreateOrderReq>({
  customerId: '',
  items: [],
  addressId: '',
  remark: ''
})

const submitting = ref(false)

// 提交订单
const handleSubmit = async () => {
  // 表单验证
  if (!formData.customerId) {
    uni.showToast({
      title: '请选择客户',
      icon: 'none'
    })
    return
  }

  if (!formData.items.length) {
    uni.showToast({
      title: '请添加订单项',
      icon: 'none'
    })
    return
  }

  submitting.value = true
  try {
    const result = await createOrder(formData)
    
    uni.showToast({
      title: '订单创建成功',
      icon: 'success'
    })
    
    // 跳转到订单详情页
    uni.navigateTo({
      url: `/pages/order/detail/index?id=${result.id}`
    })
  } catch (error) {
    console.error('创建订单失败:', error)
  } finally {
    submitting.value = false
  }
}
```

## 数据缓存与同步策略

### 本地缓存使用
```ts
// utils/cache.ts
class CacheManager {
  /** 设置缓存 */
  static set(key: string, value: any, expire?: number) {
    const data = {
      value,
      expire: expire ? Date.now() + expire : null
    }
    uni.setStorageSync(key, JSON.stringify(data))
  }

  /** 获取缓存 */
  static get<T>(key: string): T | null {
    try {
      const dataStr = uni.getStorageSync(key)
      if (!dataStr) return null

      const data = JSON.parse(dataStr)
      
      // 检查是否过期
      if (data.expire && Date.now() > data.expire) {
        this.remove(key)
        return null
      }

      return data.value
    } catch (error) {
      console.error('获取缓存失败:', error)
      return null
    }
  }

  /** 删除缓存 */
  static remove(key: string) {
    uni.removeStorageSync(key)
  }

  /** 清空缓存 */
  static clear() {
    uni.clearStorageSync()
  }
}

export default CacheManager
```

### 缓存在服务层的应用
```ts
// service/common/index.ts
import CacheManager from '@/utils/cache'

/** 获取字典数据（带缓存） */
export async function getDictData(type: string) {
  const cacheKey = `dict_${type}`
  
  // 先从缓存获取
  let data = CacheManager.get(cacheKey)
  if (data) {
    return data
  }

  // 从服务器获取
  data = await http.get(`/api/dict/${type}`)
  
  // 缓存 30 分钟
  CacheManager.set(cacheKey, data, 30 * 60 * 1000)
  
  return data
}

/** 清理字典缓存 */
export function clearDictCache() {
  // 清理所有字典缓存
  const storage = uni.getStorageInfoSync()
  storage.keys.forEach(key => {
    if (key.startsWith('dict_')) {
      CacheManager.remove(key)
    }
  })
}
```

## TypeScript 类型安全规范

### 接口响应类型定义
```ts
// types/api.ts

/** API 响应基础结构 */
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

/** 分页响应结构 */
export interface PageResponse<T = any> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

/** 分页请求参数 */
export interface PageParams {
  page?: number
  pageSize?: number
  keyword?: string
}
```

### 业务类型定义
```ts
// service/user/types.ts

/** 用户信息 */
export interface UserInfo {
  id: string
  username: string
  nickname: string
  avatar?: string
  email?: string
  phone?: string
  role: UserRole
  status: UserStatus
  createTime: string
  updateTime: string
}

/** 用户角色 */
export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  OPERATOR = 'operator'
}

/** 用户状态 */
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned'
}

/** 登录请求 */
export interface LoginReq {
  username: string
  password: string
  captcha?: string
}

/** 登录响应 */
export interface LoginRes {
  token: string
  userInfo: UserInfo
  permissions: string[]
}
```

### 类型守卫使用
```ts
// utils/typeGuards.ts

/** 检查是否为有效的用户信息 */
export function isValidUserInfo(data: any): data is UserInfo {
  return (
    data &&
    typeof data.id === 'string' &&
    typeof data.username === 'string' &&
    typeof data.nickname === 'string' &&
    Object.values(UserRole).includes(data.role)
  )
}

/** 检查是否为有效的订单 */
export function isValidOrder(data: any): data is OrderDetail {
  return (
    data &&
    typeof data.id === 'string' &&
    typeof data.orderNo === 'string' &&
    Object.values(OrderStatus).includes(data.status)
  )
}
```

## 最佳实践总结

### 开发检查清单
- [ ] 使用 `src/utils/http.ts` 进行所有 API 请求
- [ ] API 服务按模块组织，包含完整类型定义
- [ ] Store 使用安全，避免顶层调用
- [ ] 401 错误由 http 工具自动处理
- [ ] 合理使用本地缓存和数据同步
- [ ] 完整的 TypeScript 类型定义
- [ ] 错误处理和用户反馈到位

### 代码审查要点
- [ ] 禁止直接使用 `uni.request`
- [ ] Store 调用位置正确
- [ ] 类型定义完整且准确
- [ ] 错误处理逻辑完善
- [ ] 缓存策略合理
- [ ] 接口命名和组织清晰
