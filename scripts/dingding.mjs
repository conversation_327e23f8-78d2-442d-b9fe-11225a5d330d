import Robot from 'dingtalk-robot-sdk'
import http from 'http'

const config = {
  accessToken: '3ec4d9e4b5882f8967a4d42e23a67a761a8e86d823bbc70b608dd801be302f24', // 钉钉机器人设置完成后生成
  secret: 'SEC7182756a0e8d19d82e448ee14d25add6683b780556a5a62a3085a7d9eacd79f1', // 钉钉机器人签名
}

const robot = new Robot(config)

const Link = Robot.Link
const Text = Robot.Text
const FeedCard = Robot.FeedCard
const Markdown = Robot.Markdown

const paltform = process.env.PLATFORM || 'mp-weixin'

const paltformName = {
  'mp-weixin': '微信',
  'mp-alipay': '支付宝',
  android: 'Android',
  ios: 'iOS',
}

const sendMessage = function (params) {
  const text = new Text(`${params.appName}小程序已经推送到微信，请及时进行测试验证`)
  robot.send(text)
}

const noticeDingDingFail = function (params) {
  // 检查是否是移动应用
  if (params.appName && (params.appName.includes('Android') || params.appName.includes('iOS'))) {
    const text = new Text(`${params.appName}构建失败，请检查错误日志`)
    robot.send(text)
    return
  }

  const text = new Text(`${params.appName}小程序推送到${paltformName[paltform]}失败`)
  robot.send(text)
}

const noticeDingDingNo = function (params) {
  const text = new Text(
    `${params.appName}小程序不参与全量发布${paltformName[paltform]}平台,需要联系客户成功提供密钥`,
  )
  robot.send(text)
}

const noticeDingDingSuceess = function (params) {
  // 处理 Android 通知
  if (params.appName && params.appName.includes('Android')) {
    const markDown = new Markdown()
    const environmentDesc = params.env === 'uat' ? 'UAT测试环境' : '生产环境'
    const publishPathDesc = params.publishPath || '蒲公英平台'

    markDown
      .setTitle('Android 应用构建通知')
      .add(`### 【${params.appName}】${environmentDesc}已构建完成`)
      .add(`#### 版本号：${params.version || '未知'} (${params.versionCode || '未知'})`)
      .add(`#### 构建环境：${environmentDesc}`)
      .add(`#### 发布平台：${publishPathDesc}`)
      .add(`#### 版本更新人员：${params.commitUser || '未知'}`)
      .add(`#### 版本更新时间：${new Date().toLocaleString()}`)
      .add(`#### 版本更新内容：${params.commitMessage || '版本更新'}`)

    if (params.dcloudAppkey) {
      markDown.add(`#### DCloud AppKey: ${params.dcloudAppkey}`)
    }

    robot.send(markDown)
    return
  }

  // 处理 iOS 通知
  if (params.appName && params.appName.includes('iOS')) {
    const markDown = new Markdown()
    const environmentDesc =
      params.env === 'uat'
        ? 'UAT测试环境'
        : params.env === 'beta'
          ? 'TestFlight测试版'
          : params.env === 'release'
            ? 'App Store正式版'
            : '生产环境'
    const publishPathDesc = params.publishPath || '蒲公英平台'

    markDown
      .setTitle('iOS 应用构建通知')
      .add(`### 【${params.appName}】${environmentDesc}已构建完成`)
      .add(`#### 版本号：${params.version || '未知'} (Build ${params.buildNumber || '未知'})`)
      .add(`#### 构建环境：${environmentDesc}`)
      .add(`#### 发布平台：${publishPathDesc}`)
      .add(`#### 版本更新人员：${params.commitUser || '未知'}`)
      .add(`#### 版本更新时间：${new Date().toLocaleString()}`)
      .add(`#### 版本更新内容：${params.commitMessage || '版本更新'}`)

    robot.send(markDown)
    return
  }

  // 以下是原有的微信小程序通知逻辑
  if (paltform === 'mp-weixin') {
    const text = new Text(
      `新版本CI：${params.appName}小程序已经推送到${params.isExtApp ? 'XDP' : '微信'}，版本号：${params.version || '暂无'
      }，请及时到CI机器人${params.robot}进行测试验证；
版本更新人员：${params.commitUser}，版本更新时间：${new Date().toLocaleString()}，版本更新内容：${params.commitMessage}`,
    )
    robot.send(text)
    postVersionInfo([
      {
        appid: params.appId,
        version: params.version,
        description: params.desc,
      },
    ])
  }
  if (paltform === 'mp-alipay') {
    const markDown = new Markdown()
    const title = params.experience
      ? `【${params.appName}】已经推送到支付宝 ，请扫码验证`
      : `【${params.appName}】已经推送到支付宝`
    markDown
      .setTitle(`支付宝小程序构建通知`)
      .add(`### ${title}`)
      .add(`#### 版本号： ${params.packageVersion || '暂无'} `)
      .add(`#### 版本更新人员：${params.commitUser}`)
      .add(`#### 版本更新时间：${new Date().toLocaleString()}`)
      .add(`#### 版本更新内容：${params.commitMessage}`)
    if (params.experience) {
      markDown.add(`![](${params.qrCodeUrl})`)
    }
    robot.send(markDown)
  }
  if (paltform === 'mp-toutiao') {
    const title = `【${params.appName}】已经推送到【抖音】，请扫码验证`
    const markDown = new Markdown()

    markDown.setTitle('构建通知').add(title).add(`![](${params.picURL})`)

    robot.send(markDown)
  }
  if (paltform === 'h5') {
    const markDown = new Markdown()
    markDown
      .setTitle('新版本 CI 通知')
      .add(`- **应用名称**: ${params.appName}H5\n`)
      .add(`- **推送平台**: ${params.isExtApp ? 'XDP' : '服务器'}\n`)
      .add(`- **推送路径**: ${params.publishPath}\n`)
      .add(`- **推送环境**: ${params.env}\n\n`)
      .add(`- **版本更新人员**: ${params.commitUser ?? '未知'}\n`)
      .add(`- **版本更新时间**: ${new Date().toLocaleString()}\n`)
      .add(`- **版本更新内容**: ${params.commitMessage ?? '未知'}\n`)

    robot.send(markDown)

    postVersionInfo([
      {
        description: params.desc,
      },
    ])
  }
}

/**
 * 发送版本信息到自动发版工程
 * @param {Array} versionList [{appId: String, version: String, description: String}]
 */
async function postVersionInfo(versionList) {
  const data = JSON.stringify({
    type: 'versionLog',
    list: versionList,
  })

  const options = {
    hostname: '*************',
    port: 3001,
    path: '/server/miniprogram/receiveVersion',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(data),
    },
  }

  const req = http.request(options, (res) => {
    res.on('data', (data) => {
      console.log(`\n版本信息已发送，返回结果：\n${data.toString()}\n`)
    })
  })

  req.on('error', (error) => {
    console.error('版本信息发送失败:', error)
  })

  req.write(data)
  req.end()
}

export { sendMessage, noticeDingDingFail, noticeDingDingNo, noticeDingDingSuceess }
