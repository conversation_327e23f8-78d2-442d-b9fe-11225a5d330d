# MQTT兼容性优化总结

## 问题背景

在uni-super-shop-admin项目中，MQTT连接需要处理 `wx.connectSocket` 的promisify兼容问题。原来的代码直接在 `src/main.ts` 中实现，现在已优化为更规范的写法。

## 优化方案

### 1. 代码结构优化

**原始写法（在main.ts中）：**

```javascript
// 处理 wx.connectSocket promisify 兼容问题，强制返回 SocketTask
uni.connectSocket = (function (connectSocket) {
  return function (options) {
    console.log(options)
    options.success = options.success || function () {}
    return connectSocket.call(this, options)
  }
})(uni.connectSocket)
```

**优化后写法（在App.vue中封装为函数）：**

```typescript
/**
 * 初始化 MQTT 兼容性补丁
 * 解决 uni.connectSocket 在某些平台上的兼容性问题
 */
function initMqttPolyfill() {
  try {
    // 检查是否存在 uni.connectSocket 方法
    if (!uni.connectSocket) {
      console.warn('uni.connectSocket 方法不存在，跳过 MQTT 兼容性补丁')
      return
    }

    // 保存原始的 connectSocket 方法
    const originalConnectSocket = uni.connectSocket

    // 重写 uni.connectSocket 方法，强制返回 SocketTask
    uni.connectSocket = function (options: UniApp.ConnectSocketOption): UniApp.SocketTask {
      console.log('MQTT兼容性补丁：uni.connectSocket 调用参数:', options)

      // 确保 success 回调存在
      if (!options.success) {
        options.success = function (res: any) {
          console.log('MQTT兼容性补丁：WebSocket 连接成功', res)
        }
      }

      // 确保 fail 回调存在
      if (!options.fail) {
        options.fail = function (err: any) {
          console.error('MQTT兼容性补丁：WebSocket 连接失败', err)
        }
      }

      // 调用原始方法并返回结果
      try {
        const result = originalConnectSocket.call(this, options)
        console.log('MQTT兼容性补丁：uni.connectSocket 调用成功')
        return result
      } catch (error) {
        console.error('MQTT兼容性补丁：uni.connectSocket 调用失败', error)
        throw error
      }
    }

    console.log('MQTT兼容性补丁已应用')
  } catch (error) {
    console.error('应用MQTT兼容性补丁失败:', error)
  }
}
```

### 2. 新增诊断功能

添加了MQTT环境诊断功能，帮助开发者快速定位问题：

```typescript
/**
 * 打印MQTT环境诊断信息
 */
function printMqttDiagnostics() {
  console.group('🔍 MQTT环境诊断信息')

  // 平台信息
  const systemInfo = uni.getSystemInfoSync()
  console.log('📱 平台信息:', {
    platform: systemInfo.platform,
    system: systemInfo.system,
    version: systemInfo.version,
  })

  // WebSocket支持情况
  const wsSupport = {
    connectSocket: typeof uni.connectSocket === 'function',
    sendSocketMessage: typeof uni.sendSocketMessage === 'function',
    closeSocket: typeof uni.closeSocket === 'function',
  }
  console.log('🌐 WebSocket支持:', wsSupport)

  console.groupEnd()
}
```

### 3. 集成到应用启动流程

在 `App.vue` 的 `onLaunch` 生命周期中调用：

```typescript
onLaunch(() => {
  console.log('App Launch')

  // 初始化 MQTT 兼容性补丁
  initMqttPolyfill()

  // 打印 MQTT 环境诊断信息（开发环境）
  // #ifdef H5
  if (import.meta.env.DEV) {
    printMqttDiagnostics()
  }
  // #endif

  // ... 其他初始化代码
})
```

### 4. 类型定义补充

创建了 `src/types/mqtt-compat.d.ts` 文件，添加了相关类型定义：

```typescript
declare global {
  namespace UniApp {
    interface ConnectSocketOption {
      url: string
      data?: any
      header?: object
      protocols?: string[]
      method?: string
      success?: (result: any) => void
      fail?: (result: any) => void
      complete?: (result: any) => void
    }

    interface SocketTask {
      send(option: {
        /* ... */
      }): void
      close(option?: {
        /* ... */
      }): void
      onOpen(callback: (result: any) => void): void
      onClose(callback: (result: any) => void): void
      onError(callback: (result: any) => void): void
      onMessage(callback: (result: any) => void): void
    }
  }
}
```

## 优化特点

### 1. 更好的错误处理

- 添加了try-catch包装，避免补丁应用失败影响应用启动
- 提供了详细的错误日志和状态信息

### 2. 更完善的回调处理

- 确保success和fail回调始终存在，避免未定义错误
- 添加了详细的调试日志

### 3. 类型安全

- 使用TypeScript类型定义，提供更好的开发体验
- 添加了完整的参数类型检查

### 4. 开发环境诊断

- 仅在开发环境打印诊断信息，避免生产环境日志污染
- 提供平台信息和WebSocket支持情况检查

### 5. 模块化设计

- 将MQTT兼容性处理封装为独立函数
- 易于维护和扩展

## 使用效果

1. **解决兼容性问题**：确保uni.connectSocket在所有平台正常工作
2. **提供调试信息**：开发环境下自动输出诊断信息
3. **提升代码质量**：更好的错误处理和类型安全
4. **便于维护**：代码结构清晰，功能模块化

## 注意事项

1. 该补丁会在应用启动时自动应用，无需手动调用
2. 诊断信息仅在H5开发环境显示，不会影响生产环境
3. 如果出现MQTT连接问题，请检查控制台的诊断信息
4. 类型定义已自动包含，无需额外配置

这样优化后，MQTT兼容性代码更加规范、安全和易于维护。
