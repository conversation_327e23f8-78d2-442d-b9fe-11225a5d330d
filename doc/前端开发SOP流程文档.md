# 前端开发SOP流程文档

## 文档信息

- **文档版本**: v1.0
- **创建日期**: 2025-05-22
- **适用项目**: uni-super-shop-admin 及相关项目
- **目标用户**: 前端开发团队

---

## 1. 概述与目标

### 1.1 SOP适用范围

本SOP适用于基于 `uni-super-shop-admin` 框架的前端开发项目，涵盖需求分析、开发实现、测试交付的完整流程。

### 1.2 开发流程总览

```
需求接收 → 文档拆解 → 技术方案 → 开发实现 → 自测验收 → 代码审查 → 集成测试 → 交付上线
```

### 1.3 质量标准

- 代码质量：通过ESLint、Stylelint检查
- 功能完整度：100%覆盖需求功能点
- 交互体验：符合设计稿要求
- 性能指标：首屏加载时间 < 2s

---

## 2. 需求分析与文档拆解流程

### 2.1 需求文档接收流程

#### 2.1.1 文档审查检查点

- [ ] 需求背景是否清晰
- [ ] 功能范围是否明确
- [ ] 设计稿是否完整
- [ ] 接口文档是否齐全
- [ ] 验收标准是否明确

#### 2.1.2 疑问收集与澄清

- 创建疑问清单文档
- 组织需求澄清会议
- 确认最终需求范围

### 2.2 功能模块拆解方法

#### 2.2.1 拆解原则

- **业务独立性**: 每个模块应具备独立的业务价值
- **技术内聚性**: 相关功能应归类到同一模块
- **开发可行性**: 每个模块应能独立开发和测试

#### 2.2.2 拆解步骤

1. **识别核心功能模块**

   ```
   示例：海底捞项目拆解
   ├── 权限管理模块
   ├── 登录认证模块
   ├── 门店管理模块
   ├── 订单管理模块
   ├── 商品管理模块
   ├── 售后服务模块
   └── 商家申请模块
   ```

2. **创建功能包文档**

   - 为每个模块创建独立的markdown文档
   - 文档命名规范：`{模块名}功能包.md`

3. **功能包内容模板**

   ```markdown
   # {模块名}功能包

   ## 需求背景

   （摘录原需求文档相关内容）

   ## 辅助材料

   （设计稿、原型图、参考资料）

   ## 前端技术实现要点

   - 页面：列出所有相关页面
   - 功能点：核心功能清单
   - 交互：关键交互设计
   - 接口：API接口清单
   - 其他：特殊技术要求
   ```

### 2.3 技术方案输出

#### 2.3.1 技术方案文档结构

```
├── 总体技术方案
│   ├── 技术选型说明
│   ├── 架构设计
│   └── 开发计划
├── 各模块详细方案
│   ├── UI结构设计
│   ├── 状态管理方案
│   ├── 接口对接方案
│   └── 组件封装计划
└── 风险评估与预案
```

---

## 3. 标准化开发模板

### 3.1 功能开发三段式模板

#### 3.1.1 UI结构 (第一段)

```markdown
### UI结构

- **页面布局**: 描述整体布局方式
- **主要组件**: 列出核心UI组件
- **响应式适配**: 不同尺寸下的适配方案
- **组件层级**: 组件嵌套关系

示例：

- 页面布局：上下结构，顶部导航+内容区域
- 主要组件：登录表单、验证码按钮、提交按钮
- 响应式适配：移动端优先，适配pad和桌面端
- 组件层级：LoginPage > LoginForm > InputField/Button
```

#### 3.1.2 主要交互 (第二段)

```markdown
### 主要交互

- **用户操作流程**: 完整的用户操作路径
- **状态变化**: 各状态间的切换逻辑
- **异常处理**: 错误情况的处理方式
- **反馈机制**: 操作反馈的表现形式

示例：

- 用户操作流程：输入手机号 → 点击获取验证码 → 输入验证码 → 点击登录
- 状态变化：未登录 → 验证中 → 已登录 / 登录失败
- 异常处理：网络错误提示、验证码错误提示、登录失败提示
- 反馈机制：Loading状态、Toast提示、按钮状态变化
```

#### 3.1.3 接口说明 (第三段)

```markdown
### 接口说明

- **接口清单**: 所需的后端接口
- **请求参数**: 详细的参数说明
- **响应数据**: 返回数据结构
- **错误码**: 可能的错误情况

示例：

- 接口清单：
  - POST /api/auth/send-code 发送验证码
  - POST /api/auth/login 用户登录
- 请求参数：
  - send-code: { phone: string }
  - login: { phone: string, code: string }
- 响应数据：
  - send-code: { success: boolean, message: string }
  - login: { success: boolean, token: string, userInfo: object }
```

### 3.2 模板使用指南

#### 3.2.1 模板填写步骤

1. **复制模板结构**到具体功能文档
2. **填写UI结构部分**，参考设计稿完成
3. **详化交互流程**，考虑所有用户场景
4. **对接接口文档**，确认技术可行性
5. **评审确认**，与产品、后端同步

#### 3.2.2 质量检查标准

- [ ] UI结构描述清晰完整
- [ ] 交互流程覆盖所有场景
- [ ] 接口对接信息准确
- [ ] 异常处理考虑周全

---

## 4. 具体功能实现流程

### 4.1 设计稿对接流程

#### 4.1.1 设计稿分析

- **设计尺寸确认**: 确认设计稿基准尺寸（375px标准）
- **像素换算规则**: 设计稿 1px = 代码 2rpx
- **组件识别**: 识别可复用的UI组件
- **交互状态梳理**: 各种状态下的视觉表现

#### 4.1.2 技术实现规划

```typescript
// 示例：登录按钮实现规划
interface LoginButtonProps {
  loading?: boolean // 加载状态
  disabled?: boolean // 禁用状态
  type?: 'primary' | 'secondary' // 按钮类型
  onClick?: () => void // 点击事件
}

// 样式规划（基于设计稿）
// 设计稿按钮宽度: 300px → 代码: 600rpx
// 设计稿按钮高度: 44px → 代码: 88rpx
// 设计稿字体大小: 16px → 代码: 32rpx
```

### 4.2 组件开发规范

#### 4.2.1 组件文件结构

```
src/components/sy-button/
├── sy-button.vue     # 组件模板和主逻辑
├── button.ts         # 组件属性定义和类型
└── button.scss       # 组件样式（如需要）
```

#### 4.2.2 组件开发步骤

1. **创建组件目录和文件**
2. **定义组件属性和类型**（button.ts）
3. **实现组件模板和逻辑**（sy-button.vue）
4. **编写组件样式**（优先使用UnoCSS原子类）
5. **编写使用示例和文档**

#### 4.2.3 组件质量标准

- [ ] 支持所有设计稿中的状态
- [ ] 属性类型定义完整
- [ ] 事件处理正确
- [ ] 样式适配各种场景
- [ ] 使用示例清晰

### 4.3 页面开发规范

#### 4.3.1 页面文件拆分

```
src/pages/login/
├── index.vue      # 页面模板
├── index.ts       # 页面逻辑
└── index.scss     # 页面样式
```

#### 4.3.2 页面开发步骤

1. **创建页面文件结构**
2. **实现页面布局**（index.vue）
3. **编写业务逻辑**（index.ts）
4. **处理页面样式**（index.scss + UnoCSS）
5. **集成API接口**
6. **处理状态管理**（如需要）

### 4.4 样式实现标准

#### 4.4.1 样式优先级

1. **UnoCSS原子类** - 优先使用
2. **组件内部样式** - 特殊需求时使用
3. **全局样式** - 避免使用

#### 4.4.2 像素转换规则

```scss
// 设计稿中的尺寸 → 代码实现
// 100px → 200rpx
// 16px → 32rpx
// 20px → 40rpx

// 实际代码示例
.login-container {
  width: 750rpx; // 设计稿 375px
  padding: 40rpx; // 设计稿 20px

  .title {
    font-size: 36rpx; // 设计稿 18px
    margin-bottom: 60rpx; // 设计稿 30px
  }
}
```

---

## 5. 代码规范与质量控制

### 5.1 uni-super-shop-admin项目规范

#### 5.1.1 技术栈要求

- **框架**: Vue3 + Vite5 + TypeScript
- **包管理**: Pnpm（禁用npm/yarn）
- **UI框架**: Wot Design Uni
- **状态管理**: Pinia
- **样式方案**: UnoCSS + SCSS

#### 5.1.2 目录结构规范

```
src/
├── components/      # 公共组件（PascalCase命名）
├── hooks/           # 组合式函数（useXxx）
├── pages/           # 主包页面（tabbar + 登录）
├── pages-sub/       # 分包页面（按业务模块）
├── service/         # API接口服务
├── store/           # Pinia状态管理
├── types/           # TypeScript类型定义
└── utils/           # 工具函数
```

#### 5.1.3 命名规范

- **组件文件**: PascalCase（如：`UserProfile.vue`）
- **页面文件**: kebab-case（如：`user-profile.vue`）
- **工具函数**: camelCase（如：`formatDate.ts`）
- **类型定义**: PascalCase（如：`UserInfo`）

### 5.2 代码审查检查点

#### 5.2.1 基础规范检查

- [ ] ESLint检查通过
- [ ] Stylelint检查通过
- [ ] TypeScript类型检查通过
- [ ] 无console.log等调试代码

#### 5.2.2 业务逻辑检查

- [ ] 功能实现完整
- [ ] 异常处理到位
- [ ] 性能优化合理
- [ ] 代码注释清晰

#### 5.2.3 UI/UX检查

- [ ] 设计稿还原度90%+
- [ ] 交互体验流畅
- [ ] 响应式适配正确
- [ ] 无明显性能问题

### 5.3 测试验收标准

#### 5.3.1 功能测试

- [ ] 正常流程测试
- [ ] 异常流程测试
- [ ] 边界条件测试
- [ ] 性能压力测试

#### 5.3.2 兼容性测试

- [ ] 微信小程序
- [ ] 支付宝小程序
- [ ] H5端
- [ ] App端（iOS/Android）

---

## 6. 协作与交付流程

### 6.1 前后端协作规范

#### 6.1.1 接口对接流程

1. **接口文档评审**

   - 前端提出接口需求
   - 后端提供接口设计
   - 双方确认接口规范

2. **Mock数据阶段**

   - 前端使用Mock数据开发
   - 验证接口设计合理性
   - 并行开发提高效率

3. **接口联调阶段**
   - 替换真实接口
   - 处理联调问题
   - 确认数据格式

#### 6.1.2 问题协作机制

- **技术问题**: 通过技术群实时沟通
- **需求问题**: 抄送产品经理确认
- **进度问题**: 项目周会同步

### 6.2 文档维护要求

#### 6.2.1 开发期间

- **需求变更**: 及时更新需求文档
- **技术方案**: 记录重要技术决策
- **问题解决**: 记录问题和解决方案

#### 6.2.2 交付阶段

- **功能说明**: 完善功能使用说明
- **部署文档**: 更新部署相关文档
- **变更记录**: 维护CHANGELOG.md

### 6.3 版本管理流程

#### 6.3.1 Git工作流

```
main (生产环境)
  ↑
dev (开发环境)
  ↑
feature/* (功能分支)
bugfix/* (修复分支)
```

#### 6.3.2 提交规范

- 使用`czg`工具规范提交信息
- 遵循Conventional Commits规范
- 每次提交必须通过代码检查

#### 6.3.3 发布流程

1. **功能开发完成** → 合并到dev分支
2. **测试验收通过** → 合并到main分支
3. **生产环境部署** → 打tag标记版本
4. **上线验证** → 确认功能正常

---

## 7. 常见问题与解决方案

### 7.1 开发常见问题

#### 7.1.1 样式问题

**问题**: 设计稿还原度不高
**解决方案**:

- 严格按照像素转换规则（1px = 2rpx）
- 使用浏览器开发者工具精确对比
- 注意不同平台的样式差异

**问题**: UnoCSS类名冲突
**解决方案**:

- 使用组件作用域限制样式
- 合理使用`:deep()`选择器
- 避免全局样式污染

#### 7.1.2 功能问题

**问题**: 接口数据格式不匹配
**解决方案**:

- 完善TypeScript类型定义
- 使用数据转换适配器
- 与后端同学及时沟通

**问题**: 状态管理复杂
**解决方案**:

- 合理拆分Pinia store
- 使用组合式函数封装逻辑
- 避免过度设计

### 7.2 性能优化建议

#### 7.2.1 代码分割

- 按路由进行代码分割
- 组件按需加载
- 合理使用异步组件

#### 7.2.2 资源优化

- 图片资源压缩和格式优化
- 使用CDN加速静态资源
- 合理设置缓存策略

---

## 8. 培训与改进

### 8.1 新人培训流程

1. **规范学习**: 学习本SOP文档
2. **项目熟悉**: 了解项目结构和业务
3. **实践练习**: 完成示例功能开发
4. **代码审查**: 资深同事review代码
5. **独立开发**: 逐步承担独立功能

### 8.2 SOP持续改进

- **定期回顾**: 月度SOP效果评估
- **问题收集**: 收集开发过程中的问题
- **流程优化**: 基于实际情况优化流程
- **文档更新**: 及时更新SOP文档

---

## 附录

### A. 常用命令清单

```bash
# 开发环境启动
pnpm dev

# 代码检查
pnpm lint

# 类型检查
pnpm type-check

# 构建生产版本
pnpm build
```

### B. 代码模板示例

详见各功能包文档中的具体示例。

### C. 参考资料

- [unibest框架文档](https://unibest.tech)
- [Wot Design Uni组件库](https://wot-design-uni.netlify.app)
- [UnoCSS文档](https://unocss.dev)
- [Vue3官方文档](https://vuejs.org)

---

**文档维护人**: 前端技术团队  
**最后更新**: 2025-05-22  
**版本**: v1.0
