# 返回顶部按钮功能实现说明

## 功能概述

在首页订单列表中新增了返回顶部按钮，提供便捷的滚动回到顶部功能。

## 功能特性

### 1. 按钮样式

- **位置**: 左侧固定定位，距离左边 16rpx，距离底部 25% 高度
- **尺寸**: 宽高 68rpx
- **背景**: 白色 (#FFF)，圆形按钮
- **图标**: 向上箭头图标，黑色 (#000000)，14rpx 大小
- **阴影**: 轻微阴影效果，提升视觉层次

### 2. 交互行为

- **显示触发**: 滚动距离超过 800rpx 时自动显示
- **滚动效果**: 滚动时按钮隐藏一半到左侧（sliding 效果）
- **点击效果**: 点击时有轻微缩放动画反馈
- **回到位置**: 点击后滚动到第一个订单位置（如果有订单），否则回到页面顶部

### 3. 动画效果

- **渐入渐出**: 0.3s 过渡动画
- **滑动效果**: 滚动时按钮向左偏移 34rpx
- **点击反馈**: 按下时缩放至 95%

## 实现细节

### 模板部分 (index.vue)

```vue
<!-- 返回顶部按钮 -->
<view
  class="back-to-top-button"
  :class="{ show: showBackToTop, sliding: isScrolling }"
  @click="handleBackToTop"
>
  <wd-icon name="arrow-up" color="#000000" size="14rpx"></wd-icon>
</view>
```

### 样式部分 (index.scss)

```scss
.back-to-top-button {
  position: fixed;
  bottom: 25%;
  left: 16rpx;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 68rpx;
  height: 68rpx;
  visibility: hidden;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: all 0.3s ease;
  transform: translateX(-50rpx);

  // 显示状态
  &.show {
    visibility: visible;
    opacity: 1;
    transform: translateX(0);
  }

  // 滚动时隐藏一半到左侧
  &.sliding {
    transform: translateX(-34rpx);
  }

  // 点击效果
  &:active {
    transform: scale(0.95);
  }
}
```

### 逻辑部分 (index.ts)

#### 新增状态变量

```ts
// 返回顶部按钮状态
export const showBackToTop = ref(false)
export const isScrolling = ref(false)
export const currentScrollTop = ref(0)
```

#### 滚动监听函数

```ts
export const onScroll = (e: any) => {
  const scrollTop = e.detail.scrollTop

  // 超过800rpx时显示返回顶部按钮
  showBackToTop.value = scrollTop > 800

  // 检测滚动状态 - 滚动时显示sliding效果
  if (!isScrolling.value) {
    isScrolling.value = true
  }

  // 清除之前的定时器
  if (scrollingTimer) {
    clearTimeout(scrollingTimer)
  }

  // 设置新的定时器，300ms后清除滚动状态
  scrollingTimer = setTimeout(() => {
    isScrolling.value = false
  }, 300)

  currentScrollTop.value = scrollTop
}
```

#### 点击处理函数

```ts
export const handleBackToTop = () => {
  console.log('返回顶部按钮被点击')

  // 滚动到第一个订单位置
  scrollToFirstOrder()
}

export const scrollToFirstOrder = () => {
  try {
    // 如果有订单，滚动到第一个订单
    if (orderList.value && orderList.value.length > 0) {
      const firstOrderId = `order-${orderList.value[0].id}`
      console.log('滚动到第一个订单:', firstOrderId)

      scrollToView.value = firstOrderId

      // 延迟重置scrollToView，确保下次滚动能正常触发
      setTimeout(() => {
        scrollToView.value = ''
      }, 100)
    } else {
      // 如果没有订单，滚动到顶部
      scrollToTop()
    }
  } catch (error) {
    console.error('滚动到第一个订单失败:', error)
    // fallback到普通的滚动到顶部
    scrollToTop()
  }
}
```

## 技术要点

### 1. 滚动状态检测

- 使用定时器机制检测滚动状态
- 滚动结束后 300ms 清除滚动状态
- 防抖处理避免频繁触发

### 2. 动画性能优化

- 使用 CSS `transform` 而非 `left` 属性
- 利用 GPU 加速提升动画性能
- 合理的过渡时间确保用户体验

### 3. 资源清理

- 组件卸载时清理定时器
- 避免内存泄漏

### 4. 兼容性考虑

- 使用 uni-app 的 scroll-view 组件
- 支持各端平台的滚动事件

## 使用方式

功能已自动集成到首页，无需额外配置。用户在订单列表页面滚动超过 800rpx 时，左侧会自动显示返回顶部按钮，点击即可快速回到第一个订单位置。

## 扩展建议

1. **自定义显示阈值**: 可通过配置文件控制显示触发的滚动距离
2. **按钮位置配置**: 支持右侧显示或其他位置配置
3. **动画效果定制**: 支持更多动画效果选择
4. **多页面复用**: 抽取为通用组件，其他页面也可使用
