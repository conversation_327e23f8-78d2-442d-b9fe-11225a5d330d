# 需求拆解标准模板

```markdown
# 【页面/模块名称】

## 一、UI结构

- 页面布局、主要元素（如顶部、tab、表单、列表、弹窗等）
- 关键控件（输入框、按钮、下拉、图片等）
- 风格要求（如品牌色、移动端/PC端适配等）

## 二、主要交互

- 用户操作流程（如点击、切换、输入、弹窗等）
- 状态变化（按钮高亮/禁用、校验提示、加载、动画等）
- 异常与反馈（错误提示、确认弹窗、引导等）

## 三、接口说明

- 主要接口（接口地址、请求方式、参数、返回值）
- 前后端数据流转说明
- 需要特殊处理的接口（如鉴权、分页、上传等）
```

---

## 使用说明

1. 每次有新需求时，直接把产品文档、接口文档、设计稿说明或内容发给AI。
2. 备注："请按三段式模板拆解"或"请按标准模板输出"。
3. AI会自动帮你整理成上述结构，输出标准的 Markdown 文档。
