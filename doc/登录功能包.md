# 登录功能包

## 需求背景

- 用户通过账号密码或手机验证码登录系统，保障系统安全和用户身份唯一性。

---

## UI结构

- 页面名称：登录页
- 顶部标题：登录
- Tab切换：密码登录、验证码登录
- 密码登录Tab：
  - 账号输入框
  - 密码输入框（带显示/隐藏按钮）
  - 登录按钮（根据输入内容高亮或禁用）
  - 记住密码、自动登录复选框
  - 错误提示弹窗
  - 用户隐私条款弹窗
- 验证码登录Tab：
  - 手机号输入框
  - 获取验证码按钮
  - 验证码输入框
  - 登录按钮
  - 记住手机号、自动登录复选框
  - 错误提示弹窗
  - 用户隐私条款弹窗
- 底部：记住密码/自动登录未勾选时显示红色警告提示
- 整体风格：简洁、移动端适配

---

## 主要交互

- Tab切换：可在密码登录和验证码登录之间切换，切换时清空输入内容
- 输入账号和密码后，登录按钮高亮可点击，否则为禁用状态
- 密码输入框支持显示/隐藏密码
- 登录失败时弹出错误提示弹窗，内容为"错误提示，请输入正确的账号密码"，弹窗底部有"好的"按钮
- 首次登录或未同意隐私条款时，弹出"用户隐私条款"弹窗，需同意后才能继续
- 记住密码/自动登录未勾选时，底部显示红色警告提示
- 验证码登录时，手机号输入后可点击获取验证码，验证码输入正确后可登录
- 所有弹窗居中，遮罩背景，主按钮为红色

---

## 接口说明

### 一、账号密码登录流程

1. 账号密码登录
   - 接口地址：`POST /saas-gateway/api/oms-app/v1/users/login/ssoLogin`
   - 请求参数：
     ```json
     {
       "loginType": 1,
       "userName": "用户名",
       "password": "密码",
       "type": "user"
     }
     ```
   - 返回数据：
     ```json
     {
       "data": {
         "token": "JWT令牌",
         "userId": "SSO用户ID"
       },
       "resultCode": "0",
       "resultMsg": "success"
     }
     ```
2. 获取用户信息
   - 接口地址：`GET /saas-gateway/api/oms-app/v1/users/login/getUserInfoById/{ssoUserId}`
   - 返回数据：包含用户所属租户列表信息
3. 请求头信息
   - 需要在请求头中添加：
     - `mulTenantIds`: 租户ID列表（逗号分隔）
     - `mulUserIds`: 用户ID列表（逗号分隔）

### 二、手机验证码登录流程

1. 查询手机号关联租户
   - 接口地址：`GET /saas-gateway/api/oms-app/v1/users/login/ssouser/phone/query`
   - 参数：`phone=手机号码`
   - 返回：用户关联的所有租户信息
2. 发送验证码
   - 接口地址：`POST /saas-gateway/api/oms-app/v1/users/login/verify/send`
   - 请求参数：
     ```json
     {
       "phone": "手机号码",
       "tenantId": "租户ID",
       "instanceId": "",
       "type": 1
     }
     ```
3. 验证码登录
   - 接口地址：`POST /saas-gateway/api/oms-app/v1/users/login/ssoLogin`
   - 请求参数：
     ```json
     {
       "checkCode": "验证码",
       "checkCodeUniqueId": "验证码唯一标识",
       "loginType": "3",
       "phone": "手机号码",
       "tenantId": "租户ID",
       "type": "tenants"
     }
     ```
4. 获取用户信息与设置请求头
   - 验证码登录成功后，通常会返回 `ssoUserId` 或类似的用户标识。
   - 与账号密码登录流程类似，后续可调用 `GET /saas-gateway/api/oms-app/v1/users/login/getUserInfoById/{ssoUserId}` 获取详细用户信息（包括租户列表）。
   - 对于后续的业务接口调用，需要在请求头中添加：
     - `mulTenantIds`: 租户ID列表（逗号分隔）
     - `mulUserIds`: 用户ID列表（逗号分隔）
       （此处的用户ID通常指通过 `ssoUserId` 进一步查询得到的应用内用户ID）
