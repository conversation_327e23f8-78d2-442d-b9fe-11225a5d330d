# 订单卡片组件出餐用时功能扩展

## 修改概述

为 `sy-order-card` 组件添加了在备餐完成(`MEAL_READY`)和已接单(`ACCEPTED`)状态下显示出餐用时的功能。

## 修改内容

### 1. 新增计算属性 `shouldShowMealTime`

**位置**：`src/components/sy-order-card/sy-order-card.vue` 第1423行

**功能**：判断当前订单状态是否应该显示出餐用时信息

**支持的状态**：

- `WAIT_CELL_DELIVERY` - 待发配送（备餐完成）
- `WAIT_RIDER_ACCEPT` - 待骑手接单
- `WAIT_PICKUP` - 待取餐
- `DELIVERING` - 配送中
- `MEAL_READY` - 备餐完成 ✨ **新增**
- `ACCEPTED` - 已接单 ✨ **新增**
- `CONFIRM` - 已接单（确认） ✨ **新增**

### 2. 修改模板显示逻辑

**位置**：`src/components/sy-order-card/sy-order-card.vue` 第378行

**原有逻辑**：仅在 `['WAIT_CELL_DELIVERY', 'WAIT_RIDER_ACCEPT', 'WAIT_PICKUP', 'DELIVERING']` 状态下显示

**修改后**：使用 `shouldShowMealTime` 计算属性，支持更多状态

### 3. 导出新的计算属性

**位置**：`src/components/sy-order-card/sy-order-card.vue` defineExpose

**新增导出**：`shouldShowMealTime`

## 显示效果

当订单处于支持的状态时，会显示以下信息之一：

### 已上报出餐完成

```
已上报出餐完成，出餐用时 XX分钟
```

### 未上报出餐完成

```
未上报出餐完成，无出餐用时
```

## 数据依赖

组件读取 `orderData.orderStatus.mealReadyDuration` 字段来判断是否已上报出餐完成：

- 有值：显示具体的出餐用时
- 无值：显示"无出餐用时"

## 兼容性

此修改向下兼容，不会影响现有功能：

- 原有支持的状态依然正常显示
- 新增的状态按相同逻辑处理
- 不涉及 API 调用或数据结构变更

## 测试建议

1. **备餐完成状态** (`MEAL_READY`)

   - 已上报：验证显示出餐用时
   - 未上报：验证显示"无出餐用时"

2. **已接单状态** (`ACCEPTED`/`CONFIRM`)

   - 已上报：验证显示出餐用时
   - 未上报：验证显示"无出餐用时"

3. **其他状态**
   - 验证原有功能正常
   - 确保不显示出餐用时的状态依然正确

## 注意事项

- 此功能依赖后端提供准确的 `mealReadyDuration` 字段
- 状态码需要与后端保持一致
- 建议在不同订单类型（外卖/自提）下进行测试
