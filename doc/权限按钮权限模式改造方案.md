# 权限按钮权限模式改造方案

## 一、改造背景与目标

为提升用户体验和权限管理一致性，现对项目内所有涉及权限控制的按钮进行统一改造：

- 保留原有“无权限隐藏”逻辑（即 SyAuth 默认行为）。
- 新增 toast 模式：无权限时按钮依然显示，但视觉上置灰/禁用，点击只弹“暂无权限”toast，不执行实际操作。
- 目标：所有按钮权限控制方式统一为 toast 模式。

---

## 二、方案设计

### 1. SyAuth 组件支持双模式

- 新增 `mode` 属性：
  - `mode="hidden"`（默认）：无权限时隐藏 slot（原有逻辑）。
  - `mode="toast"`：无权限时显示 slot，自动加禁用样式，点击只弹 toast。

### 2. toast 模式说明

- 无权限时按钮依然显示。
- 按钮视觉上置灰、透明、不可用（如加 `filter: grayscale(1); opacity: 0.5; cursor: not-allowed;`）。
- 点击时不会执行原有操作，只弹出“暂无权限”toast。

### 3. 样式与交互

- 统一使用 `.sy-auth-no-permission` 类处理置灰、透明、不可用样式。
- SyAuth 组件内部拦截点击事件，无权限时只弹 toast。

---

## 三、页面/组件用法示例

### 1. 隐藏型（原有逻辑）

```vue
<SyAuth code="order:edit">
  <wd-button @click="onEdit">编辑</wd-button>
</SyAuth>
```

### 2. toast 模式（推荐/现行要求）

```vue
<SyAuth code="order:delete" mode="toast">
  <wd-button @click="onDelete">删除</wd-button>
</SyAuth>
```

- 有权限：显示并可点击。
- 无权限：显示置灰/禁用，点击只弹 toast。

---

## 四、推广要求

> **目前所有的按钮都要使用 toast 模式**
>
> - 所有涉及权限控制的按钮，必须统一采用 SyAuth 的 `mode="toast"`。
> - 禁止再使用“无权限隐藏”模式，确保无权限时按钮依然显示但不可用。
> - 页面和业务组件需逐步排查并替换为 toast 模式。

---

## 五、推广步骤建议

1. **升级 SyAuth 组件**，支持 `mode` 属性和 toast 逻辑。
2. **全局样式补充**，如 `.sy-auth-no-permission`。
3. **全项目排查**，将所有 `<SyAuth code="xxx">...</SyAuth>` 改为 `<SyAuth code="xxx" mode="toast">...</SyAuth>`。
4. **团队同步新用法，文档补充说明**。
5. **后续开发严格遵循 toast 模式权限控制。**

---

如需 SyAuth 组件实现参考、样式代码或批量替换脚本，请联系前端负责人。
