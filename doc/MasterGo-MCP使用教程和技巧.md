# MasterGo MCP 使用教程和技巧

## 文档信息

- **文档版本**: v1.0
- **创建日期**: 2025-05-22
- **适用场景**: 前端开发、设计稿对接、代码生成
- **目标用户**: 前端开发工程师、UI开发工程师

---

## 1. MCP 基础概念

### 1.1 什么是 MasterGo MCP

**MasterGo MCP (MasterGo Code Platform)** 是一个连接设计工具 MasterGo 与代码开发的桥梁平台，它能够：

- 📊 **解析设计稿数据**：从 MasterGo 设计文件中提取结构化数据
- 🔄 **自动化代码生成**：基于设计稿自动生成前端代码
- 📖 **组件文档获取**：获取设计系统中的组件使用文档
- 🌐 **网站配置提取**：获取完整网站的配置和页面信息

### 1.2 核心优势

- ⚡ **提高开发效率**：减少设计稿到代码的手工转换时间
- 🎯 **减少理解偏差**：直接从设计稿获取准确的设计意图
- 🔧 **标准化开发**：确保代码生成遵循项目规范
- 📚 **文档同步**：设计与开发文档保持一致

---

## 2. MCP 工具详解

### 2.1 核心工具概览

MasterGo MCP 提供四个核心工具：

| 工具名称                | 功能描述              | 适用场景           |
| ----------------------- | --------------------- | ------------------ |
| `getDsl`                | 获取设计文件 DSL 数据 | 单个组件/页面分析  |
| `getComponentLink`      | 获取组件文档链接      | 组件库对接         |
| `getMeta`               | 获取网站元信息        | 完整网站构建       |
| `getComponentGenerator` | 获取组件生成器        | Vue/React 组件开发 |

### 2.2 工具详细说明

#### 2.2.1 getDsl - 设计数据解析器

**功能**：获取 MasterGo 设计文件的 DSL（Domain Specific Language）数据

**参数**：

- `fileId`: MasterGo 设计文件 ID
- `layerId`: 具体图层/组件 ID

**使用场景**：

- 分析单个页面或组件的设计结构
- 获取设计元素的属性和样式信息
- 为代码生成提供基础数据

**示例 URL 解析**：

```
https://mastergo.com/file/123456789?layer_id=abcdef
```

- `fileId`: `123456789`
- `layerId`: `abcdef`

#### 2.2.2 getComponentLink - 组件文档获取器

**功能**：从 DSL 数据中的 `componentDocumentLinks` 获取组件文档

**参数**：

- `url`: 组件文档链接 URL

**使用场景**：

- 获取设计系统中组件的使用说明
- 了解组件的属性和配置
- 确保开发实现符合设计规范

#### 2.2.3 getMeta - 网站配置获取器

**功能**：获取完整网站的高级配置信息

**参数**：

- `fileId`: MasterGo 设计文件 ID
- `layerId`: 页面图层 ID

**使用场景**：

- 构建完整网站或应用
- 获取全局配置和页面结构
- 理解整体架构设计

#### 2.2.4 getComponentGenerator - 组件生成器

**功能**：为 Vue/React 组件开发提供结构化工作流

**参数**：

- `fileId`: MasterGo 设计文件 ID
- `layerId`: 组件图层 ID
- `rootPath`: 项目根路径

**使用场景**：

- 快速生成 Vue/React 组件
- 遵循最佳实践的组件开发
- 自动化组件文件结构创建

---

## 3. 使用流程与步骤

### 3.1 准备工作

#### 3.1.1 获取 MasterGo 设计文件信息

1. **打开 MasterGo 设计文件**
2. **复制文件 URL**，格式通常为：
   ```
   https://mastergo.com/file/{fileId}?layer_id={layerId}
   ```
3. **提取关键参数**：
   - `fileId`：文件标识符
   - `layerId`：图层标识符

#### 3.1.2 确定使用场景

根据需求选择合适的工具：

```mermaid
graph TD
    A[开始] --> B{需求类型?}
    B -->|分析单个组件| C[使用 getDsl]
    B -->|获取组件文档| D[使用 getComponentLink]
    B -->|构建完整网站| E[使用 getMeta]
    B -->|生成组件代码| F[使用 getComponentGenerator]
```

### 3.2 标准使用流程

#### 3.2.1 基础数据获取流程

```
1. 使用 getDsl 获取设计数据
   ↓
2. 分析返回的 DSL 数据结构
   ↓
3. 检查是否有 componentDocumentLinks
   ↓
4. 如有文档链接，使用 getComponentLink 获取详细文档
   ↓
5. 基于数据进行代码开发
```

#### 3.2.2 组件开发流程

```
1. 使用 getComponentGenerator 创建工作流
   ↓
2. 分析生成的组件结构
   ↓
3. 使用 getDsl 获取详细设计数据
   ↓
4. 实现组件代码
   ↓
5. 对接组件文档（如需要）
```

---

## 4. 实用技巧与最佳实践

### 4.1 URL 解析技巧

#### 4.1.1 快速提取参数

```javascript
// JavaScript 快速解析 MasterGo URL
function parseMasterGoUrl(url) {
  const urlObj = new URL(url)
  const pathParts = urlObj.pathname.split('/')

  return {
    fileId: pathParts[pathParts.length - 1],
    layerId: urlObj.searchParams.get('layer_id'),
  }
}

// 使用示例
const url = 'https://mastergo.com/file/123456789?layer_id=abcdef'
const { fileId, layerId } = parseMasterGoUrl(url)
```

#### 4.1.2 常见 URL 格式

| URL 格式                                           | 说明         | 解析方式                           |
| -------------------------------------------------- | ------------ | ---------------------------------- |
| `https://mastergo.com/file/123456789`              | 仅文件 ID    | fileId = 123456789, layerId = null |
| `https://mastergo.com/file/123456789?layer_id=abc` | 文件 + 图层  | fileId = 123456789, layerId = abc  |
| `https://mastergo.com/design/123456789/abc`        | 设计链接格式 | 需要转换为标准格式                 |

### 4.2 数据处理技巧

#### 4.2.1 DSL 数据结构分析

```typescript
// 典型的 DSL 数据结构
interface DSLData {
  // 基础信息
  name: string
  type: string

  // 样式属性
  style: {
    width: number
    height: number
    backgroundColor: string
    // ...其他样式
  }

  // 子元素
  children?: DSLData[]

  // 组件文档链接
  componentDocumentLinks?: string[]

  // 其他元数据
  metadata?: Record<string, any>
}
```

#### 4.2.2 样式转换技巧

```typescript
// MasterGo 样式 → CSS 样式转换
function convertMasterGoStyleToCSS(style: any) {
  const cssRules: string[] = []

  // 尺寸转换（375px 设计稿 → rpx）
  if (style.width) {
    cssRules.push(`width: ${style.width * 2}rpx`)
  }

  // 颜色转换
  if (style.backgroundColor) {
    cssRules.push(`background-color: ${style.backgroundColor}`)
  }

  // 字体转换
  if (style.fontSize) {
    cssRules.push(`font-size: ${style.fontSize * 2}rpx`)
  }

  return cssRules.join('; ')
}
```

### 4.3 错误处理技巧

#### 4.3.1 常见错误类型

```typescript
// 错误处理封装
async function safeMCPCall(mcpFunction: Function, params: any) {
  try {
    const result = await mcpFunction(params)
    return { success: true, data: result }
  } catch (error) {
    console.error('MCP 调用失败:', error)

    // 常见错误处理
    if (error.message.includes('fileId')) {
      return { success: false, error: '文件 ID 无效或文件不存在' }
    }

    if (error.message.includes('layerId')) {
      return { success: false, error: '图层 ID 无效或图层不存在' }
    }

    if (error.message.includes('permission')) {
      return { success: false, error: '没有访问权限，请检查文件权限设置' }
    }

    return { success: false, error: '未知错误，请稍后重试' }
  }
}
```

#### 4.3.2 数据验证技巧

```typescript
// DSL 数据验证
function validateDSLData(data: any): boolean {
  // 基础结构检查
  if (!data || typeof data !== 'object') {
    console.warn('DSL 数据格式无效')
    return false
  }

  // 必要字段检查
  const requiredFields = ['name', 'type']
  for (const field of requiredFields) {
    if (!(field in data)) {
      console.warn(`缺少必要字段: ${field}`)
      return false
    }
  }

  return true
}
```

---

## 5. 高级使用技巧

### 5.1 批量处理技巧

#### 5.1.1 多图层数据获取

```javascript
// 批量获取多个图层的数据
async function batchGetDSLData(fileId, layerIds) {
  const results = []

  for (const layerId of layerIds) {
    try {
      const data = await getDsl({ fileId, layerId })
      results.push({
        layerId,
        success: true,
        data,
      })
    } catch (error) {
      results.push({
        layerId,
        success: false,
        error: error.message,
      })
    }

    // 避免请求过于频繁
    await new Promise((resolve) => setTimeout(resolve, 100))
  }

  return results
}
```

#### 5.1.2 组件文档批量获取

```javascript
// 从 DSL 数据批量获取组件文档
async function batchGetComponentDocs(dslData) {
  const docs = []

  if (dslData.componentDocumentLinks) {
    for (const url of dslData.componentDocumentLinks) {
      try {
        const doc = await getComponentLink({ url })
        docs.push({ url, success: true, data: doc })
      } catch (error) {
        docs.push({ url, success: false, error: error.message })
      }
    }
  }

  return docs
}
```

### 5.2 缓存优化技巧

#### 5.2.1 本地缓存实现

```javascript
// 简单的内存缓存实现
class MCPCache {
  constructor(ttl = 300000) {
    // 5分钟过期
    this.cache = new Map()
    this.ttl = ttl
  }

  set(key, value) {
    this.cache.set(key, {
      data: value,
      timestamp: Date.now(),
    })
  }

  get(key) {
    const item = this.cache.get(key)
    if (!item) return null

    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  generateKey(fileId, layerId) {
    return `${fileId}-${layerId}`
  }
}

const mcpCache = new MCPCache()

// 带缓存的 DSL 数据获取
async function getCachedDSLData(fileId, layerId) {
  const key = mcpCache.generateKey(fileId, layerId)

  // 尝试从缓存获取
  const cached = mcpCache.get(key)
  if (cached) {
    console.log('从缓存获取数据')
    return cached
  }

  // 缓存未命中，请求新数据
  const data = await getDsl({ fileId, layerId })
  mcpCache.set(key, data)

  return data
}
```

### 5.3 数据转换技巧

#### 5.3.1 DSL 转 Vue 组件

```javascript
// DSL 数据转换为 Vue 组件结构
function dslToVueComponent(dslData) {
  const template = generateTemplate(dslData)
  const script = generateScript(dslData)
  const style = generateStyle(dslData)

  return {
    template,
    script,
    style,
    filename: `${dslData.name}.vue`,
  }
}

function generateTemplate(data) {
  // 根据 DSL 数据生成 Vue 模板
  const tag = getVueTag(data.type)
  const attrs = getVueAttributes(data)
  const children = data.children?.map((child) => generateTemplate(child)).join('\n') || ''

  return `<${tag}${attrs}>\n${children}\n</${tag}>`
}

function getVueTag(type) {
  const tagMap = {
    FRAME: 'view',
    TEXT: 'text',
    RECTANGLE: 'view',
    BUTTON: 'button',
  }
  return tagMap[type] || 'view'
}
```

#### 5.3.2 样式提取与转换

```javascript
// 提取并转换样式
function extractStyles(dslData) {
  const styles = {}

  // 布局样式
  if (dslData.style?.width) {
    styles.width = `${dslData.style.width * 2}rpx`
  }

  if (dslData.style?.height) {
    styles.height = `${dslData.style.height * 2}rpx`
  }

  // 颜色样式
  if (dslData.style?.backgroundColor) {
    styles.backgroundColor = dslData.style.backgroundColor
  }

  // 文字样式
  if (dslData.style?.fontSize) {
    styles.fontSize = `${dslData.style.fontSize * 2}rpx`
  }

  // 边距样式
  if (dslData.style?.padding) {
    styles.padding = `${dslData.style.padding * 2}rpx`
  }

  return styles
}
```

---

## 6. 项目集成指南

### 6.1 与 uni-app 项目集成

#### 6.1.1 自动化页面生成

```javascript
// 基于 MCP 的自动化页面生成脚本
const fs = require('fs')
const path = require('path')

async function generateUniPage(fileId, layerId, pageName) {
  try {
    // 1. 获取设计数据
    const dslData = await getDsl({ fileId, layerId })

    // 2. 生成页面文件
    const pageDir = path.join('src/pages', pageName)
    if (!fs.existsSync(pageDir)) {
      fs.mkdirSync(pageDir, { recursive: true })
    }

    // 3. 生成 vue 文件
    const vueContent = generateUniPageVue(dslData)
    fs.writeFileSync(path.join(pageDir, 'index.vue'), vueContent)

    // 4. 生成样式文件
    const scssContent = generateUniPageStyle(dslData)
    fs.writeFileSync(path.join(pageDir, 'index.scss'), scssContent)

    // 5. 生成逻辑文件
    const tsContent = generateUniPageScript(dslData)
    fs.writeFileSync(path.join(pageDir, 'index.ts'), tsContent)

    console.log(`页面 ${pageName} 生成成功！`)
  } catch (error) {
    console.error('页面生成失败:', error)
  }
}
```

#### 6.1.2 组件库集成

```javascript
// MCP 与 uni-app 组件库集成
class UniMCPIntegration {
  constructor(projectRoot) {
    this.projectRoot = projectRoot
    this.componentsDir = path.join(projectRoot, 'src/components')
  }

  async generateComponent(fileId, layerId, componentName) {
    // 1. 使用组件生成器
    const generator = await getComponentGenerator({
      fileId,
      layerId,
      rootPath: this.projectRoot,
    })

    // 2. 获取详细设计数据
    const dslData = await getDsl({ fileId, layerId })

    // 3. 生成组件文件
    await this.createComponentFiles(componentName, dslData, generator)

    return {
      componentName,
      path: path.join(this.componentsDir, componentName),
      files: ['index.vue', 'types.ts', 'index.scss'],
    }
  }

  async createComponentFiles(name, dslData, generator) {
    const componentDir = path.join(this.componentsDir, name)
    fs.mkdirSync(componentDir, { recursive: true })

    // Vue 文件
    const vueContent = this.generateVueFile(dslData)
    fs.writeFileSync(path.join(componentDir, 'index.vue'), vueContent)

    // TypeScript 类型文件
    const typesContent = this.generateTypesFile(dslData)
    fs.writeFileSync(path.join(componentDir, 'types.ts'), typesContent)

    // 样式文件
    const styleContent = this.generateStyleFile(dslData)
    fs.writeFileSync(path.join(componentDir, 'index.scss'), styleContent)
  }
}
```

### 6.2 CI/CD 集成

#### 6.2.1 自动化构建脚本

```yaml
# .github/workflows/mcp-sync.yml
name: MCP Design Sync

on:
  schedule:
    - cron: '0 2 * * *' # 每天凌晨2点执行
  workflow_dispatch: # 手动触发

jobs:
  sync-design:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm install

      - name: Sync MasterGo Designs
        run: node scripts/sync-mastergo.js
        env:
          MASTERGO_TOKEN: ${{ secrets.MASTERGO_TOKEN }}

      - name: Create Pull Request
        if: success()
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add .
          git commit -m "chore: sync MasterGo designs" || exit 0
          git push origin HEAD:design-sync
```

---

## 7. 故障排除与调试

### 7.1 常见问题解决

#### 7.1.1 权限问题

**问题现象**：

```
Error: 403 Forbidden - Access denied
```

**解决方案**：

1. 检查 MasterGo 文件的分享权限
2. 确认文件是否设置为"任何人可查看"
3. 联系设计师确认文件权限设置

#### 7.1.2 参数错误

**问题现象**：

```
Error: Invalid fileId or layerId
```

**解决方案**：

```javascript
// 参数验证函数
function validateMCPParams(fileId, layerId) {
  const errors = []

  if (!fileId) {
    errors.push('fileId 不能为空')
  } else if (!/^[a-zA-Z0-9_-]+$/.test(fileId)) {
    errors.push('fileId 格式无效')
  }

  if (layerId && !/^[a-zA-Z0-9_-]+$/.test(layerId)) {
    errors.push('layerId 格式无效')
  }

  return errors
}

// 使用示例
const errors = validateMCPParams(fileId, layerId)
if (errors.length > 0) {
  throw new Error(`参数验证失败: ${errors.join(', ')}`)
}
```

### 7.2 调试技巧

#### 7.2.1 请求日志记录

```javascript
// MCP 请求日志记录
class MCPLogger {
  constructor() {
    this.logs = []
  }

  log(method, params, result, error = null) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      method,
      params,
      result: error ? null : result,
      error,
      duration: 0,
    }

    this.logs.push(logEntry)

    // 保持最近100条日志
    if (this.logs.length > 100) {
      this.logs.shift()
    }
  }

  getLogs() {
    return this.logs
  }

  exportLogs() {
    return JSON.stringify(this.logs, null, 2)
  }
}

const logger = new MCPLogger()

// 包装 MCP 调用
async function loggedMCPCall(method, params) {
  const startTime = Date.now()

  try {
    const result = await method(params)
    const duration = Date.now() - startTime

    logger.log(method.name, params, result)
    console.log(`✅ ${method.name} 成功 (${duration}ms)`)

    return result
  } catch (error) {
    const duration = Date.now() - startTime

    logger.log(method.name, params, null, error.message)
    console.error(`❌ ${method.name} 失败 (${duration}ms):`, error.message)

    throw error
  }
}
```

---

## 8. 进阶应用案例

### 8.1 设计系统同步

#### 8.1.1 自动生成设计 Token

```javascript
// 从 MasterGo 设计文件提取设计 Token
async function extractDesignTokens(fileId) {
  try {
    // 获取设计文件元数据
    const meta = await getMeta({ fileId, layerId: 'root' })

    // 提取颜色 Token
    const colorTokens = extractColorTokens(meta)

    // 提取字体 Token
    const fontTokens = extractFontTokens(meta)

    // 提取间距 Token
    const spacingTokens = extractSpacingTokens(meta)

    // 生成 Token 文件
    const tokens = {
      colors: colorTokens,
      fonts: fontTokens,
      spacing: spacingTokens,
    }

    // 输出为 CSS 变量
    const cssVariables = generateCSSVariables(tokens)

    // 输出为 SCSS 变量
    const scssVariables = generateSCSSVariables(tokens)

    return {
      tokens,
      cssVariables,
      scssVariables,
    }
  } catch (error) {
    console.error('设计 Token 提取失败:', error)
    throw error
  }
}

function generateCSSVariables(tokens) {
  let css = ':root {\n'

  // 颜色变量
  Object.entries(tokens.colors).forEach(([name, value]) => {
    css += `  --color-${name}: ${value};\n`
  })

  // 字体变量
  Object.entries(tokens.fonts).forEach(([name, value]) => {
    css += `  --font-${name}: ${value};\n`
  })

  // 间距变量
  Object.entries(tokens.spacing).forEach(([name, value]) => {
    css += `  --spacing-${name}: ${value};\n`
  })

  css += '}'
  return css
}
```

### 8.2 多平台代码生成

#### 8.2.1 跨平台组件生成

```javascript
// 多平台组件生成器
class MultiPlatformGenerator {
  constructor(dslData) {
    this.dslData = dslData
  }

  // 生成 Vue 组件
  generateVue() {
    return {
      template: this.generateVueTemplate(),
      script: this.generateVueScript(),
      style: this.generateVueStyle(),
    }
  }

  // 生成 React 组件
  generateReact() {
    return {
      jsx: this.generateReactJSX(),
      typescript: this.generateReactTypes(),
      style: this.generateReactStyle(),
    }
  }

  // 生成微信小程序组件
  generateWeapp() {
    return {
      wxml: this.generateWXML(),
      js: this.generateWeappJS(),
      wxss: this.generateWXSS(),
      json: this.generateWeappJSON(),
    }
  }

  generateVueTemplate() {
    return this.convertDSLToTemplate(this.dslData, 'vue')
  }

  generateReactJSX() {
    return this.convertDSLToTemplate(this.dslData, 'react')
  }

  convertDSLToTemplate(data, platform) {
    const tagMap = {
      vue: {
        FRAME: 'view',
        TEXT: 'text',
        BUTTON: 'button',
      },
      react: {
        FRAME: 'div',
        TEXT: 'span',
        BUTTON: 'button',
      },
    }

    const tag = tagMap[platform][data.type] || 'div'
    const attrs = this.generateAttributes(data, platform)

    return `<${tag}${attrs}>${this.generateChildren(data, platform)}</${tag}>`
  }
}
```

---

## 9. 团队协作最佳实践

### 9.1 设计师与开发者协作

#### 9.1.1 设计文件规范

**设计师需要遵循的规范**：

1. **图层命名规范**

   ```
   ✅ 推荐命名:
   - Button/Primary
   - Card/ProductCard
   - Layout/Header

   ❌ 避免命名:
   - 矩形 1
   - 组件
   - 未命名图层
   ```

2. **组件组织规范**

   - 使用组件库统一管理可复用组件
   - 为每个组件添加使用说明文档
   - 保持组件的状态完整（正常、悬停、禁用等）

3. **文件权限设置**
   - 确保开发团队有文件访问权限
   - 使用"任何人可查看"权限便于 MCP 访问

#### 9.1.2 开发者协作流程

**开发者协作 SOP**：

1. **设计评审阶段**

   - 使用 `getDsl` 预先分析设计复杂度
   - 识别可复用组件和特殊技术需求
   - 与设计师确认组件拆分和状态定义

2. **开发实现阶段**

   - 使用 `getComponentGenerator` 快速搭建组件结构
   - 通过 `getComponentLink` 获取详细组件文档
   - 保持与设计稿的像素级还原

3. **联调测试阶段**
   - 对比设计稿验证实现效果
   - 确保各平台适配正确
   - 性能测试和优化

### 9.2 版本控制集成

#### 9.2.1 设计版本同步

```javascript
// 设计版本同步脚本
class DesignVersionSync {
  constructor(config) {
    this.config = config
    this.versionFile = path.join(config.projectRoot, '.design-version.json')
  }

  // 检查设计文件是否有更新
  async checkForUpdates() {
    const currentVersion = this.getCurrentVersion()
    const latestVersion = await this.getLatestVersion()

    if (currentVersion !== latestVersion) {
      console.log(`🎨 设计文件有更新: ${currentVersion} → ${latestVersion}`)
      return true
    }

    return false
  }

  // 同步最新设计
  async syncLatestDesign() {
    try {
      const updates = await this.generateUpdates()

      if (updates.length > 0) {
        await this.applyUpdates(updates)
        await this.updateVersion()

        console.log(`✅ 同步完成，更新了 ${updates.length} 个文件`)
      }
    } catch (error) {
      console.error('❌ 设计同步失败:', error)
    }
  }

  async generateUpdates() {
    const updates = []

    for (const component of this.config.trackedComponents) {
      const latestDSL = await getDsl({
        fileId: component.fileId,
        layerId: component.layerId,
      })

      const currentDSL = this.loadCurrentDSL(component.name)

      if (this.hasChanges(currentDSL, latestDSL)) {
        updates.push({
          component: component.name,
          oldDSL: currentDSL,
          newDSL: latestDSL,
        })
      }
    }

    return updates
  }
}
```

---

## 10. 附录与资源

### 10.1 快速参考

#### 10.1.1 工具速查表

| 工具                  | 用途         | 必需参数                  | 可选参数 |
| --------------------- | ------------ | ------------------------- | -------- |
| getDsl                | 获取设计数据 | fileId, layerId           | -        |
| getComponentLink      | 获取组件文档 | url                       | -        |
| getMeta               | 获取网站配置 | fileId, layerId           | -        |
| getComponentGenerator | 生成组件结构 | fileId, layerId, rootPath | -        |

#### 10.1.2 常用代码片段

```javascript
// 1. 基础 DSL 数据获取
const dslData = await getDsl({
  fileId: 'your-file-id',
  layerId: 'your-layer-id',
})

// 2. 批量获取组件文档
if (dslData.componentDocumentLinks) {
  for (const url of dslData.componentDocumentLinks) {
    const doc = await getComponentLink({ url })
    console.log('组件文档:', doc)
  }
}

// 3. 生成组件工作流
const generator = await getComponentGenerator({
  fileId: 'your-file-id',
  layerId: 'your-layer-id',
  rootPath: '/path/to/project',
})

// 4. 获取网站配置
const meta = await getMeta({
  fileId: 'your-file-id',
  layerId: 'page-layer-id',
})
```

### 10.2 故障排除检查清单

- [ ] 确认 MasterGo 文件 URL 格式正确
- [ ] 检查文件和图层权限设置
- [ ] 验证 fileId 和 layerId 参数
- [ ] 确认网络连接正常
- [ ] 检查 MCP 工具版本兼容性
- [ ] 查看错误日志获取详细信息

### 10.3 相关资源链接

- 📖 **MasterGo 官方文档**: [mastergo.com/help](https://mastergo.com/help)
- 🛠️ **uni-app 官方文档**: [uniapp.dcloud.net.cn](https://uniapp.dcloud.net.cn)
- 🎨 **设计系统最佳实践**: [design-systems.com](https://design-systems.com)
- 💻 **前端开发规范**: 详见项目 SOP 文档

---

**文档维护人**: 前端技术团队  
**最后更新**: 2025-05-22  
**版本**: v1.0

> 💡 **提示**: 本文档会随着 MCP 功能更新而持续完善，建议定期关注更新内容。
