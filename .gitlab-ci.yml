# GitLab CI/CD 配置
# 支持 HBuilderX CLI 云打包

stages:
  - install
  - build
  - package

variables:
  NODE_VERSION: '18'
  PNPM_VERSION: '7.30.0'

# 缓存配置
cache:
  key: '$CI_COMMIT_REF_SLUG'
  paths:
    - node_modules/
    - .pnpm-store/

# 安装依赖
install:dependencies:
  stage: install
  image: node:${NODE_VERSION}
  before_script:
    - npm install -g pnpm@${PNPM_VERSION}
    - pnpm config set store-dir .pnpm-store
  script:
    - pnpm install
  artifacts:
    paths:
      - node_modules/
    expire_in: 1 hour

# UAT 环境构建
build:uat:
  stage: build
  image: node:${NODE_VERSION}
  dependencies:
    - install:dependencies
  before_script:
    - npm install -g pnpm@${PNPM_VERSION}
    - node scripts/hbuilderx-env-config.js init
  script:
    - pnpm build:app
  artifacts:
    paths:
      - dist/
      - unpackage/
    expire_in: 1 hour
  only:
    - develop
    - /^feature\/.*$/

# 生产环境构建
build:production:
  stage: build
  image: node:${NODE_VERSION}
  dependencies:
    - install:dependencies
  before_script:
    - npm install -g pnpm@${PNPM_VERSION}
    - node scripts/hbuilderx-env-config.js init
  script:
    - pnpm build:app -- --mode production
  artifacts:
    paths:
      - dist/
      - unpackage/
    expire_in: 1 hour
  only:
    - main
    - /^release\/.*$/

# Android UAT 云打包
package:android:uat:
  stage: package
  image: node:${NODE_VERSION}
  dependencies:
    - build:uat
  before_script:
    - npm install -g pnpm@${PNPM_VERSION}
    - npm install -g @dcloud/hbuilderx-cli
  script:
    - node scripts/hbuilderx-build.js --platform=android --env=uat
  artifacts:
    paths:
      - unpackage/release/apk/
    expire_in: 7 days
  only:
    - develop
  when: manual

# Android 生产云打包
package:android:production:
  stage: package
  image: node:${NODE_VERSION}
  dependencies:
    - build:production
  before_script:
    - npm install -g pnpm@${PNPM_VERSION}
    - npm install -g @dcloud/hbuilderx-cli
  script:
    - node scripts/hbuilderx-build.js --platform=android --env=prod
  artifacts:
    paths:
      - unpackage/release/apk/
    expire_in: 30 days
  only:
    - main
  when: manual

# iOS UAT 云打包
package:ios:uat:
  stage: package
  image: node:${NODE_VERSION}
  dependencies:
    - build:uat
  before_script:
    - npm install -g pnpm@${PNPM_VERSION}
    - npm install -g @dcloud/hbuilderx-cli
  script:
    - node scripts/hbuilderx-build.js --platform=ios --env=uat
  artifacts:
    paths:
      - unpackage/release/ipa/
    expire_in: 7 days
  only:
    - develop
  when: manual

# iOS 生产云打包
package:ios:production:
  stage: package
  image: node:${NODE_VERSION}
  dependencies:
    - build:production
  before_script:
    - npm install -g pnpm@${PNPM_VERSION}
    - npm install -g @dcloud/hbuilderx-cli
  script:
    - node scripts/hbuilderx-build.js --platform=ios --env=prod
  artifacts:
    paths:
      - unpackage/release/ipa/
    expire_in: 30 days
  only:
    - main
  when: manual

# 代码质量检查
lint:check:
  stage: build
  image: node:${NODE_VERSION}
  dependencies:
    - install:dependencies
  before_script:
    - npm install -g pnpm@${PNPM_VERSION}
  script:
    - pnpm run type-check
  allow_failure: true
  only:
    - merge_requests

# 清理缓存作业（手动触发）
cleanup:cache:
  stage: package
  image: node:${NODE_VERSION}
  script:
    - echo "清理构建缓存..."
    - rm -rf node_modules/
    - rm -rf .pnpm-store/
    - rm -rf dist/
    - rm -rf unpackage/
  when: manual
  allow_failure: true
