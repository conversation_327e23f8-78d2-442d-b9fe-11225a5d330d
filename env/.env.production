# 变量必须以 VITE_ 为前缀才能暴露给外部读取
NODE_ENV = 'production'
# 是否去除console 和 debugger
VITE_DELETE_CONSOLE = true
# 是否开启sourcemap
VITE_SHOW_SOURCEMAP = false
VITE_APP_TITLE = '海底捞全单汇'
VITE_APP_PORT = 9000

VITE_UNI_APPID = '__UNI__43DF9EF'
VITE_WX_APPID = 'wxa2abb91f64032a2b'
VITE_JG_JPUSH_APPKEY = '7a17464e8ff4aa4602e2748a'
VITE_PACKAGE_NAME = 'com.jidanbao.shop'



# https://imarketing.cloud-stage.dtyunxi.com/saas-gateway/api/oms-app/v1/users/login/getUserInfoById/{ssoUserId}
# h5部署网站的base，配置到 manifest.config.ts 里的 h5.router.base
VITE_APP_PUBLIC_BASE=/
# uat环境域名前缀
VITE_SERVER_BASEPRE="imarketing.cloud.dtyunxi.com"
VITE_SERVER_BASEURL = 'https://imarketing.cloud.dtyunxi.com/saas-gateway/api/oms-app'
VITE_UPLOAD_BASEURL = 'https://imarketing.cloud.dtyunxi.com/saas-gateway/api/oms-app/upload'
# https://imarketing.cloud-stage.dtyunxi.com/saas-gateway/api/oms-app/v1/users/login/getUserInfoById/{ssoUserId}
# h5部署网站的base，配置到 manifest.config.ts 里的 h5.router.base
VITE_APP_PUBLIC_BASE=/
# uat环境域名前缀
VITE_SERVER_BASEPRE="imarketing.cloud.dtyunxi.com"
VITE_SERVER_BASEURL = 'https://imarketing.cloud.dtyunxi.com/saas-gateway/api/oms-app'
VITE_UPLOAD_BASEURL = 'https://imarketing.cloud.dtyunxi.com/saas-gateway/api/oms-app/upload'

# 有些同学可能需要在微信小程序里面根据 develop、trial、release 分别设置上传地址，参考代码如下。
# 下面的变量如果没有设置，会默认使用 VITE_SERVER_BASEURL or VITE_UPLOAD_BASEURL
VITE_SERVER_BASEURL__WEIXIN_DEVELOP = 'https://imarketing.cloud.dtyunxi.com/saas-gateway/api/oms-app'
VITE_SERVER_BASEURL__WEIXIN_TRIAL = 'https://imarketing.cloud.dtyunxi.com/saas-gateway/api/oms-app'
VITE_SERVER_BASEURL__WEIXIN_RELEASE = 'https://imarketing.cloud.dtyunxi.com/saas-gateway/api/oms-app'
# 有些同学可能需要在微信小程序里面根据 develop、trial、release 分别设置上传地址，参考代码如下。
# 下面的变量如果没有设置，会默认使用 VITE_SERVER_BASEURL or VITE_UPLOAD_BASEURL
VITE_SERVER_BASEURL__WEIXIN_DEVELOP = 'https://imarketing.cloud.dtyunxi.com/saas-gateway/api/oms-app'
VITE_SERVER_BASEURL__WEIXIN_TRIAL = 'https://imarketing.cloud.dtyunxi.com/saas-gateway/api/oms-app'
VITE_SERVER_BASEURL__WEIXIN_RELEASE = 'https://imarketing.cloud.dtyunxi.com/saas-gateway/api/oms-app'

VITE_UPLOAD_BASEURL__WEIXIN_DEVELOP = 'https://imarketing.cloud.dtyunxi.com/saas-gateway/api/oms-app/upload'
VITE_UPLOAD_BASEURL__WEIXIN_TRIAL = 'https://imarketing.cloud.dtyunxi.com/saas-gateway/api/oms-app/upload'
VITE_UPLOAD_BASEURL__WEIXIN_RELEASE = 'https://imarketing.cloud.dtyunxi.com/saas-gateway/api/oms-app/upload'
VITE_MQTT_BASEURL = 'wx://mqtt-q5rajaxw-gz-public.mqtt.tencenttdmq.com:8888'
VITE_MQTT_USERNAME = 'saasprod'
VITE_MQTT_PASSWORD = 'sk8ba57c03349e9db8'
VITE_APP_UPDATE_BASEURL = 'https://saas-sy-miniprogram.dtyunxi.com/haidilao/config/prod/app/update.json'

# # h5是否需要配置代理
# VITE_APP_PROXY=false
# VITE_APP_PROXY_PREFIX = '/api'
