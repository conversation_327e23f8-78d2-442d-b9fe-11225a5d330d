<template>
  <wd-config-provider :themeVars="themeVars">
    <view class="layout-container">
      <view class="content-container">
        <slot />
      </view>
      <sy-tabbar />
    </view>
    <wd-toast />
    <wd-message-box />

    <!-- 401 错误弹窗 -->
    <SyPopup
      v-model="show401Popup"
      title="错误提示"
      confirm-text="退出登录"
      :show-cancel="false"
      :close-on-click-modal="false"
      @confirm="handle401Logout"
    >
      当前账号已在其他设备登录
    </SyPopup>

    <!-- 升级管理器 -->
    <SyUpgradeManager
      :use-custom-dialog="true"
      :auto-check="true"
      :check-delay="2000"
      @update-success="onUpdateSuccess"
      @update-fail="onUpdateFail"
      @no-update="onNoUpdate"
    />
  </wd-config-provider>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import { useUserStore } from '@/store'
import SyPopup from '@/components/sy-popup'
import SyUpgradeManager from '@/components/sy-upgrade-manager'

const themeVars: ConfigProviderThemeVars = {
  colorTheme: '#F33429',
  buttonPrimaryBgColor: '#F33429',
  // checkboxCheckColor: '#F33429',
  // colorTheme: 'red',
  // buttonPrimaryBgColor: '#07c160',
  // buttonPrimaryColor: '#07c160',
}

// 401 错误弹窗状态
const show401Popup = ref(false)

// 显示 401 弹窗的方法
const show401Dialog = () => {
  console.log('显示 401 错误弹窗')
  show401Popup.value = true
}

// 处理退出登录
const handle401Logout = async () => {
  console.log('用户点击退出登录')
  show401Popup.value = false

  // 使用统一的退出登录方法
  const { logoutAndClearAllStores } = await import('@/hooks/useAuth')
  await logoutAndClearAllStores('401 未授权退出登录')
}

// 监听全局 401 事件
const on401Event = (event: any) => {
  console.log('收到 401 事件:', event)
  show401Dialog()
}

// 升级管理器事件处理
const onUpdateSuccess = () => {
  console.log('✅ 应用更新成功')
}

const onUpdateFail = (error: any) => {
  console.error('❌ 应用更新失败:', error)
}

const onNoUpdate = () => {
  console.log('📱 当前已是最新版本')
}

// 页面初始化时注册事件监听
onMounted(() => {
  // 注册全局 401 事件监听
  uni.$on('show401Dialog', on401Event)
})

// 页面销毁时移除事件监听
onUnmounted(() => {
  // 移除全局 401 事件监听
  uni.$off('show401Dialog', on401Event)
})
</script>

<style lang="scss" scoped>
/* 确保布局容器占满整个视口高度 */
.layout-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding-bottom: 160rpx;
  overflow: hidden;
}
/* 内容区域自适应高度 */
.content-container {
  flex: 1;
  overflow-y: auto;
}
</style>
