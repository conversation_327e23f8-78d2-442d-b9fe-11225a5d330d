<template>
  <wd-popup
    :z-index="9999"
    v-model="show"
    :custom-style="customStyle"
    :close-on-click-modal="closeOnClickModal"
  >
    <view class="sy-popup-content">
      <view v-if="title" class="sy-popup-title">{{ title }}</view>
      <!-- 内容插槽 -->
      <view class="sy-popup-desc">
        <slot></slot>
      </view>
      <view class="sy-popup-actions">
        <!-- 取消按钮 -->
        <wd-button v-if="showCancel" block @click="onCancelClick" class="sy-popup-cancel">
          {{ cancelText }}
        </wd-button>
        <!-- 确认按钮 -->
        <wd-button type="primary" block @click="onConfirmClick" class="sy-popup-confirm">
          {{ confirmText }}
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { popupProps } from './popup'

// 定义组件属性
const props = defineProps(popupProps)

// 定义事件
const emit = defineEmits(['update:modelValue', 'confirm', 'cancel'])

// 计算属性：控制弹窗显示
const show = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

// 点击确认按钮
const onConfirmClick = () => {
  emit('confirm')
}

// 点击取消按钮
const onCancelClick = () => {
  emit('cancel')
  show.value = false
}

// 导出需要的属性和方法
defineExpose({
  show,
})

// 添加默认导出
defineOptions({
  name: 'SyPopup',
})
</script>

<style lang="scss">
.sy-popup-content {
  min-width: 540rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
}
.sy-popup-title {
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
}
.sy-popup-desc {
  margin-bottom: 32rpx;
  font-size: 28rpx;
  color: #222;
  text-align: center;
}
.sy-popup-actions {
  display: flex;
  gap: 24rpx;
  justify-content: space-around;
  margin-top: 0;
}
.sy-popup-actions > button {
  flex: 1;
}
.sy-popup-cancel {
  font-size: 28rpx !important;
  font-weight: 500 !important;
  color: #222 !important;
  background: #fff !important;
  border: 2rpx solid #e5e6eb !important;
}
.sy-popup-confirm {
  font-size: 28rpx !important;
  font-weight: 500 !important;
  color: #fff !important;
  background: #f33429 !important;
  border: none !important;
}
</style>
