/**
 * 弹窗组件属性定义
 */
export const popupProps = {
  /** 是否显示弹窗 */
  modelValue: {
    type: Boolean,
    default: false,
  },
  /** 弹窗标题 */
  title: {
    type: String,
    default: '',
  },
  /** 是否显示取消按钮 */
  showCancel: {
    type: Boolean,
    default: true,
  },
  /** 确认按钮文本 */
  confirmText: {
    type: String,
    default: '确定',
  },
  /** 取消按钮文本 */
  cancelText: {
    type: String,
    default: '取消',
  },
  /** 自定义弹窗样式 */
  customStyle: {
    type: String,
    default: 'border-radius:24rpx;',
  },
  /** 是否可以通过点击遮罩层关闭 */
  closeOnClickModal: {
    type: Boolean,
    default: true,
  },
}
