.sy-order-card {
  &-container {
    position: relative;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  &-gradient-bg {
    z-index: 0;
    height: 378rpx;
    pointer-events: none;
  }

  &-watermark {
    pointer-events: none;
  }

  &-divider {
    height: 2rpx;
    background: repeating-linear-gradient(
      to right,
      #bcbcbd 0,
      #bcbcbd 4rpx,
      transparent 4rpx,
      transparent 10rpx
    );
  }

  &-tenant-tag {
    font-weight: 500;
  }

  // 渠道图标样式
  &-channel-icon {
    border-radius: 50%;
  }

  &-serial-prefix {
    font-weight: 600;
  }

  &-serial-number {
    font-weight: 600;
  }

  &-arrow {
    display: inline-block;
    transform: rotate(90deg);
  }

  &-expand-arrow {
    display: inline-block;
    transform: rotate(90deg);
  }

  &-collapse-arrow {
    display: inline-block;
    transform: rotate(-90deg);
  }

  &-call-btn {
    transition: all 0.3s ease;

    &:active {
      background-color: #e5e5e5;
      transform: scale(0.95);
    }
  }

  &-accept-btn {
    font-family: PingFang SC;
    font-size: 24rpx;
    font-weight: 500;
    line-height: normal;
    text-align: center;
    letter-spacing: normal;
    transition: all 0.3s ease;

    &:active {
      opacity: 0.9;
      transform: scale(0.98);
    }
  }

  &-address {
    transition: all 0.3s ease;

    &:active {
      background-color: #f8f8f8;
    }
  }

  // 渠道标签样式
  &-type-tag {
    line-height: 28rpx;
  }

  // 展开区域过渡动画
  &-details {
    overflow: hidden;
    transition: max-height 0.3s ease;
  }

  &-action-btn {
    margin-left: 16rpx;
    font-family: PingFang SC;
    font-size: 24rpx;
    font-weight: 500;
    line-height: normal;
    text-align: center;
    letter-spacing: normal;
    transition: all 0.3s ease;

    &:active {
      opacity: 0.9;
      transform: scale(0.98);
    }
  }

  &-copy-btn {
    cursor: pointer;
    transition: all 0.3s ease;

    &:active {
      background-color: #e5e5e5;
      transform: scale(0.95);
    }
  }

  &-refund-btn {
    box-sizing: border-box;
    cursor: pointer;
    background: #ffffff;
    border: 1rpx solid #cccccc;
    border-radius: 27rpx;
    transition: all 0.3s ease;
    &:active {
      opacity: 0.9;
      transform: scale(0.98);
    }
  }

  &-track-btn {
    box-sizing: border-box;
    cursor: pointer;
    background: #ffffff;
    border: 1rpx solid #cccccc;
    border-radius: 27rpx;
    transition: all 0.3s ease;
    &:active {
      opacity: 0.9;
      transform: scale(0.98);
    }
  }

  &-remark-tag {
    font-family: PingFang SC;
    font-size: 34rpx;
    font-weight: 600;
    line-height: normal;
    color: #222222;
    letter-spacing: normal;
    background: #fefadf;
    border-radius: 8rpx;
  }

  // 售后模块样式
  &-after-sale {
    margin: 24rpx;
    background: #f5f6f7;

    border-radius: 8rpx;
    .after-sale-header {
      // 标题栏样式已通过UnoCSS类名实现
    }
    .compact-steps {
      // 紧凑型步骤条样式
      :deep() {
        .wd-step__header {
          display: none;
        }
        .compact-steps-item {
          font-family: PingFang SC;
          font-size: 22rpx;
          font-weight: 500;
          line-height: normal;
          color: #999999;
          letter-spacing: normal;
        }
        .compact-steps-item.is-process .wd-step__title {
          color: #222222;
        }
      }
    }
    // 主要信息区域样式
    .after-sale-main-info {
      // 背景和布局样式已通过UnoCSS类名实现
    }

    // URL链接区域样式
    .after-sale-url {
      // 样式已通过UnoCSS类名实现

      text:active {
        opacity: 0.7;
      }
    }

    // 操作按钮样式
    .after-sale-actions {
      .reject-btn {
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
          background-color: #f5f5f5;
          opacity: 0.9;
          transform: scale(0.98);
        }
      }

      .approve-btn {
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
          background-color: #e55d2b;
          opacity: 0.9;
          transform: scale(0.98);
        }
      }
    }
  }
}
.sy-order-card-type-tag {
  box-sizing: border-box;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 36rpx;
  /* 自动布局 */
  font-family: PingFang SC;
  font-size: 22rpx;
  font-weight: normal;
  line-height: 28rpx;
  color: #f33429;
  letter-spacing: normal;
  border: 1px solid #f33429;
  border-radius: 4rpx;
}

.sy-order-card-type-tag-yu {
  box-sizing: border-box;
  /* 自动布局 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
  margin-left: 8rpx;
  /* 自动布局 */
  font-family: PingFang SC;
  font-size: 22rpx;
  font-weight: normal;
  color: #fff;
  letter-spacing: normal;
  background-color: #f33429;
  border: 1px solid #f33429;
  border-radius: 6rpx;
}
.sy-order-card-remark {
  display: flex;
  justify-content: space-between;
  padding: 24rpx;
  font-family: PingFang SC;
  font-size: 22rpx;
  font-weight: 500;
  line-height: normal;
  color: #ffffff;
  letter-spacing: normal;
  background: #fefadf;
  border-radius: 8rpx;
}

// 订单跟踪弹窗样式
.track-popup {
  &-container {
    width: 100%;
    padding-bottom: env(safe-area-inset-bottom, 0);
    overflow: hidden;
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;
  }

  &-header {
    position: relative;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #eeeeee;
  }

  &-close {
    position: absolute;
    top: 50%;
    right: 24rpx;
    padding: 10rpx;
    transform: translateY(-50%);
  }

  &-content {
    max-height: 75vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;

    // 步骤条样式优化
    :deep(.wd-steps) {
      .wd-step {
        // 连接线颜色统一为灰色
        .wd-step__line {
          background-color: #cccccc !important;
        }

        // finished状态的步骤为灰色
        &.is-finished .wd-step__dot {
          color: #cccccc !important;
          background-color: #cccccc !important;
          border-color: #cccccc !important;
        }

        // process状态的步骤为品牌色
        &.is-process .wd-step__dot {
          color: #f33429 !important;
          background-color: #f33429 !important;
          border-color: #f33429 !important;
        }
      }
    }
  }

  &-safe-area {
    /* 高度由内联样式动态控制 */
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background-color: #fff;
  }

  &-tips {
    text-align: center;
  }
}

// 新的售后步骤条样式
.after-sale-main-wrapper {
  :deep(.after-sale-steps) {
    .wd-step {
      .wd-step__icon {
        font-size: 24rpx;
        color: #00c37f;
      }

      &.first-step .wd-step__icon {
        font-size: 24rpx;
        color: #00c37f;
      }

      &.other-step .wd-step__icon {
        font-size: 8rpx;
        color: #333333;
      }

      .wd-step__content {
        padding-left: 24rpx;

        .wd-step__description {
          font-size: 28rpx;
          line-height: 1.4;
          color: #333333;
        }
      }

      .wd-step__line {
        background-color: #d9d9d9;
      }
    }
  }
  .after-sale-item-view {
    position: relative;
    .status-img {
      position: absolute;
      top: 12rpx;
      left: 10rpx;
      z-index: 99;
    }
    .zj-img {
      position: absolute;
      top: 20rpx;
      left: 17rpx;
      z-index: 99;
    }
    .after-sale-item-line {
      position: relative;
      // border-left: 2rpx solid peru;
      &::after {
        position: absolute;
        top: 32rpx;
        left: 22rpx;
        width: 2rpx;
        height: calc(100% + 10rpx); /* 修改为100%继承父元素高度 */
        content: '';
        border-left: 2rpx dashed #333;
        transform: translateX(-50%); /* 只保留水平方向的位移 */
      }
    }
  }
}

// 拨打骑手电话弹窗样式
.call-rider-popup {
  &-container {
    width: 100%;
    overflow: hidden;
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;
  }

  &-header {
    text-align: center;
    border-radius: 24rpx 24rpx 0 0;
  }

  &-content {
    // 内容区域样式已在模板中使用UnoCSS类名
  }

  &-footer {
    border-top: 2rpx solid #e5e5e5;
  }

  &-safe-area {
    /* 高度由内联样式动态控制 */
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background-color: #f1f1f1;
  }
}

// 拨打隐私号客户电话弹窗样式
.call-customer-popup {
  &-container {
    width: 640rpx;
    min-height: 400rpx;
    overflow: hidden;
    background: #fff;
    border-radius: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  }

  &-header {
    position: relative;
    border-bottom: 1rpx solid #f0f0f0;
  }

  &-close {
    position: absolute;
    top: 50%;
    right: 32rpx;
    padding: 8rpx;
    cursor: pointer;
    border-radius: 50%;
    transition: background-color 0.3s ease;
    transform: translateY(-50%);

    &:active {
      background-color: #f5f5f5;
    }
  }

  &-content {
    // 内容区域样式已在模板中使用UnoCSS类名
  }
}

// 通用图片样式
image {
  object-fit: cover;
}

// 可点击图片样式
.after-sale-images image,
.clickable-image {
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    opacity: 0.8;
    transform: scale(0.95);
  }
}

// 骑手头像容器
.rider-avatar-container {
  overflow: hidden;
}

// 拨打电话按钮
.call-btn {
  transition: all 0.3s ease;

  &:active {
    opacity: 0.8;
    transform: scale(0.95);
  }
}

// 取消按钮
.cancel-btn {
  transition: all 0.3s ease;

  &:active {
    background-color: rgba(243, 52, 41, 0.05);
  }
}

.after-sale-record-item {
  display: flex;
  align-items: flex-start;
}

.step-indicator-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.step-dot {
  width: 24rpx;
  height: 24rpx;
  margin-bottom: 8rpx;
  border-radius: 50%;
}

.step-line {
  width: 2rpx;
  background-color: #d9d9d9;
}

.step-content {
  flex: 1;
}

.reason-content {
  font-size: 28rpx;
  line-height: 1.4;
  color: #666666;
}

.custom-step-wrapper {
  position: relative;
  width: 38rpx;
}

.step-icon-container {
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38rpx;
  height: 38rpx;
  margin-top: 8rpx;
}

.step-connector-line {
  position: absolute;
  top: 20rpx;
  left: 48%;
  height: 98rpx;
  // transform: translate(-50%, -50%);
}

.status-row {
  align-items: flex-start;
}

.status-dot {
  flex-shrink: 0;
  margin-top: 6rpx;
}

.status-text {
  display: flex;
  align-items: center;
  justify-content: end;
  font-weight: 600;
  line-height: 1.4;
  text-align: right;
}

.time-text {
  // font-size: 24rpx;
  line-height: 1.4;
  color: #666;
}

.title-text {
  font-weight: 600;
  line-height: 1.4;
  color: #222;
}
.title-text-other {
  font-size: 28rpx !important;
  font-weight: 400 !important;
}
.title-text-one {
  font-size: 26rpx !important;
  font-weight: 600 !important;
}
.title-text-view {
  width: auto;
  font-family: PingFang SC;
  // font-size: 24rpx;
  // font-weight: 600;
  line-height: 1.4;
  color: #666;
}
.title-info-view {
  text-align: right;
}

.reason-text {
  // font-size: 28rpx;
  // line-height: 1.4;
  line-height: 32rpx;
  color: #3d3d3d;
}
.custom-wd-table {
  :deep() {
    .wd-table__cell {
      background: transparent !important;
      border-color: #ccc !important;
    }

    .wd-table__content--header {
      .wd-table__cell {
        font-size: 24rpx;
        font-weight: 600;
        .wd-table__value {
          font-weight: 600 !important;
        }
      }
    }

    .wd-table__body {
      .wd-table__cell {
        font-size: 24rpx;
        border-color: #ccc;
      }
      .wd-table-col {
        font-size: 24rpx;
      }
    }
  }
}

// 售后商品表格样式
.after-sale-items1 {
  .sy-table {
    overflow: hidden;
    border-radius: 8rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

    // 表头样式
    .sy-table-header__cell {
      font-size: 24rpx;
      color: #666666;
      background-color: #f5f7fa;
    }

    // 单元格样式
    .sy-table-cell {
      font-size: 24rpx;
      color: #333333;

      // 商品名称单元格
      &:first-child {
        padding-left: 16rpx;
        text-align: left;
      }

      // 金额单元格
      &:last-child {
        color: #f33429;
      }
    }
  }
}
.guide-btn-box {
  // display: flex;
  padding-right: 24rpx;
  // justify-content: flex-end;
  text-align: right;
}
.track-popup-tips-esc {
  position: relative;
  bottom: 78rpx;
}
.track-popup-container {
  padding-bottom: 24rpx;
  .track-popup-tips {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80rpx;
  }
}
.sy-order-card-remark-color {
  background: #f5f6f7;
}
.name-width {
  width: 480rpx;
  margin-right: 16rpx;
}
.price-box {
  // width: 158rpx;
  flex: 1;
  justify-content: space-between;
  .nums-box {
    width: 36rpx;
  }
}
