# SyOrderCard 订单卡片组件

## 功能描述

订单卡片组件，用于显示订单详细信息，支持多种订单状态和操作。

## 新增功能：拨打电话限制 ✨

### 功能概述

为了保护客户隐私，现已添加拨打电话限制功能：

1. **订单已取消时不可拨打电话**
2. **订单完成1小时后不可拨打电话**
3. **受限时按钮置灰显示**
4. **点击受限按钮显示提示信息**

### 限制规则

#### 1. 订单取消限制

- 当 `orderData.orderStatus.code === 'CANCEL'` 时，禁止拨打电话
- 适用于客户电话和骑手电话

#### 2. 订单完成时间限制

- 当订单状态为已完成（`DELIVERED`, `FINISHED`, `COMPLETED`, `FINISH`）且完成时间超过1小时时，禁止拨打电话
- 完成时间获取优先级：
  1. `orderData.times.deliveryTime`
  2. `orderData.deliveryRecord.arriveStoreTime`

### 视觉效果

#### 正常状态

- 背景色：`#F1F1F1`
- 透明度：`1`
- 图标透明度：`1`

#### 受限状态

- 背景色：`#E0E0E0`
- 透明度：`0.5`
- 图标透明度：`0.6`

### 交互反馈

当用户点击受限的拨打电话按钮时，显示Toast提示：

```
为保护客户隐私，电话已不可拨打
```

## 配送信息隐藏逻辑

组件会自动检测以下条件，满足时隐藏配送信息：

1. **订单状态**: 必须是 `CONFIRM`（已完成）
2. **配送状态**: 满足以下任一条件
   - `status` 或 `statusName` 为 "待发起配送"
   - `status` 或 `statusName` 为 "待分配骑手"
   - `status` 为 "WAIT_DISPATCH"（待发起配送状态码）
   - `status` 为 "WAIT_ASSIGN"（待分配骑手状态码）

### 示例数据

```typescript
// 这种情况下配送信息会被隐藏
const orderData = {
  orderStatus: { code: 'CONFIRM' },
  deliveryRecord: {
    status: 'WAIT_DISPATCH', // 或 'WAIT_ASSIGN'
    statusName: '待发起配送', // 或 '待分配骑手'
    // ... 其他配送信息
  },
}

// 这种情况下配送信息正常显示
const orderData = {
  orderStatus: { code: 'CONFIRM' },
  deliveryRecord: {
    status: 'DELIVERING',
    statusName: '配送中',
    courierName: '张师傅',
    // ... 其他配送信息
  },
}
```

## 属性 Props

| 属性名           | 类型      | 默认值 | 必填 | 说明             |
| ---------------- | --------- | ------ | ---- | ---------------- |
| orderData        | OrderData | -      | 是   | 订单数据对象     |
| showExpandButton | Boolean   | true   | 否   | 是否显示展开按钮 |
| expanded         | Boolean   | false  | 否   | 是否已展开       |
| isAfterSaleMode  | Boolean   | false  | 否   | 是否为售后模式   |
| id               | String    | ''     | 否   | 组件唯一标识     |

## 事件 Events

| 事件名             | 参数                    | 说明                 |
| ------------------ | ----------------------- | -------------------- |
| call               | phone: string           | 拨打电话事件         |
| address-click      | address: string         | 地址点击事件         |
| expand-click       | -                       | 展开点击事件         |
| accept-order       | orderId: string         | 接单按钮点击事件     |
| finish-order       | orderId: string         | 出餐完成按钮点击事件 |
| report-order       | orderId: string         | 上报出餐按钮点击事件 |
| refund-order       | orderId: string         | 申请退款按钮点击事件 |
| track-order        | orderId: string         | 订单跟踪点击事件     |
| operation-result   | result: OperationResult | 操作结果反馈         |
| after-sale-approve | tradeNo: string         | 售后同意             |
| after-sale-reject  | tradeNo: string         | 售后拒绝             |

## 暴露方法

### 新增方法

| 方法名                      | 返回类型               | 说明                      |
| --------------------------- | ---------------------- | ------------------------- |
| canMakePhoneCall            | ComputedRef\<boolean\> | 检查是否可以拨打电话      |
| isOrderCompletedOverOneHour | ComputedRef\<boolean\> | 检查订单是否完成超过1小时 |

## 使用示例

### 基础用法

```vue
<template>
  <sy-order-card :order-data="orderData" @call="handleCall" @accept-order="handleAcceptOrder" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { OrderData } from './card'

const orderData = ref<OrderData>({
  id: '123',
  orderNo: 'ORDER_001',
  customer: {
    name: '张三',
    phone: '138****1234',
    fullPhone: '13812341234',
  },
  orderStatus: {
    code: 'IN_PREPARE',
    text: '备餐中',
  },
  times: {
    createTime: '2023-12-25 10:00:00',
    acceptTime: '2023-12-25 10:05:00',
  },
  // ... 其他订单数据
})

const handleCall = (phone: string) => {
  console.log('拨打电话:', phone)
}

const handleAcceptOrder = (orderId: string) => {
  console.log('接单:', orderId)
}
</script>
```

### 受限场景示例

#### 已取消订单

```vue
<script setup lang="ts">
const canceledOrderData = ref<OrderData>({
  id: '123',
  orderStatus: {
    code: 'CANCEL', // 已取消状态
    text: '已取消',
  },
  customer: {
    phone: '138****1234',
  },
  // 此时拨打电话按钮将置灰且不可点击
})
</script>
```

#### 完成超过1小时的订单

```vue
<script setup lang="ts">
const completedOrderData = ref<OrderData>({
  id: '123',
  orderStatus: {
    code: 'DELIVERED', // 已完成状态
    text: '已送达',
  },
  times: {
    deliveryTime: '2023-12-25 08:00:00', // 超过1小时前完成
  },
  customer: {
    phone: '138****1234',
  },
  // 此时拨打电话按钮将置灰且不可点击
})
</script>
```

## 技术实现

### 核心逻辑

```typescript
// 检查是否可以拨打电话
const canMakePhoneCall = computed(() => {
  if (!props.orderData) return false

  // 订单已取消时不可拨打电话
  if (props.orderData.orderStatus?.code === 'CANCEL') {
    return false
  }

  // 检查订单是否完成超过1小时
  if (isOrderCompletedOverOneHour.value) {
    return false
  }

  return true
})

// 检查订单是否完成超过1小时
const isOrderCompletedOverOneHour = computed(() => {
  if (!props.orderData) return false

  const completedStatuses = ['DELIVERED', 'FINISHED', 'COMPLETED', 'FINISH']
  const orderStatus = props.orderData.orderStatus?.code

  if (!orderStatus || !completedStatuses.includes(orderStatus)) {
    return false
  }

  const completedTime =
    props.orderData.times?.deliveryTime || props.orderData.deliveryRecord?.arriveStoreTime

  if (!completedTime) return false

  try {
    const completedDate = new Date(completedTime)
    const now = new Date()
    const diffInHours = (now.getTime() - completedDate.getTime()) / (1000 * 60 * 60)

    return diffInHours > 1
  } catch (error) {
    console.error('时间解析失败:', error)
    return false
  }
})
```

### 样式实现

```vue
<template>
  <view
    class="sy-order-card-call-btn w-60rpx h-60rpx rounded-full flex items-center justify-center"
    :class="canMakePhoneCall ? 'bg-#F1F1F1' : 'bg-#E0E0E0'"
    :style="{ opacity: canMakePhoneCall ? 1 : 0.5 }"
    @click="onCallClick"
  >
    <image
      src="/static/images/img/phone.svg"
      class="w-32rpx h-32rpx"
      mode="aspectFit"
      :style="{ opacity: canMakePhoneCall ? 1 : 0.6 }"
    />
  </view>
</template>
```

## 注意事项

1. **时间格式兼容性**：组件会自动处理多种时间格式，如果解析失败会在控制台输出错误信息
2. **状态码扩展性**：完成状态码列表可根据实际业务需求调整
3. **向后兼容**：新功能不影响现有组件的使用方式
4. **性能优化**：使用computed属性确保限制条件的响应式更新
5. **配送信息隐藏**：仅在订单完成状态下且配送状态为待发起/待分配时隐藏

## 更新日志

### v2.1.0 (2024-01-XX)

- ✨ 新增拨打电话限制功能
- 🎨 优化拨打电话按钮的视觉反馈
- 🐛 修复客户手机号显示字段错误的问题
- 📚 完善组件文档和使用示例
