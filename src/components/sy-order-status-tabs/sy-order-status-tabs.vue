<template>
  <view class="sy-order-status-tabs">
    <wd-tabs
      v-model="activeIndex"
      class="sy-order-status-tabs-container"
      :sticky="false"
      :slidable-num="5"
      :map-num="6"
      @change="handleTabChange"
    >
      <block v-for="status in props.statusList" :key="status.id">
        <wd-tab :name="status.id" :title="getTabTitle(status)" />
      </block>
    </wd-tabs>
  </view>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { orderStatusTabsProps, type OrderStatusItem } from './tabs'

defineOptions({
  name: 'SyOrderStatusTabs',
})

const props = defineProps(orderStatusTabsProps)

const emit = defineEmits<{
  'status-change': [statusId: string]
  'expand-click': []
}>()
const activeIndex = ref('ALL')

// 监听外部activeStatusId变化，同步更新内部activeIndex
watch(
  () => props.activeStatusId,
  (newValue) => {
    if (newValue && newValue !== activeIndex.value) {
      activeIndex.value = newValue
      console.log('同步更新状态标签activeIndex:', newValue)
    }
  },
  { immediate: true },
)

// 处理tab切换
const handleTabChange = ({ index }: { index: number }) => {
  if (props.statusList[index]) {
    emit('status-change', props.statusList[index].id)
  }
}

// 获取tab标题（包含名称和数量）
const getTabTitle = (status: OrderStatusItem) => {
  if (status.id === 'all') {
    return status.name
  }
  return `${status.name}${status.count > 0 ? ` ${status.count}` : ''}`
}
</script>

<style lang="scss" scoped>
.sy-order-status-tabs {
  &-container {
    :deep(.wd-tabs__nav) {
      // padding: 0;
    }

    :deep(.wd-tabs__nav-wrap) {
      // background-color: transparent;
    }

    :deep(.wd-tabs__nav-item) {
      // display: flex;
      // flex-shrink: 0;
      // align-items: center;
      display: flex;
      flex: none !important; // 移除等分宽度
      align-items: center;
      justify-content: center;
      width: auto !important; // 宽度自适应
      height: 62rpx;
      padding: 0 24rpx; // 左右边距各24rpx
      // padding: 12rpx 24rpx 14rpx;
      // margin-right: 12rpx;
      // border-radius: 8rpx;
      // transition: all 0.3s ease;
    }

    // :deep(.wd-tabs__nav-item) {
    //   flex: 0.24;
    // }
    :deep(.wd-tabs__nav-item-title) {
      font-size: 26rpx;
      font-weight: 600;
      line-height: 36rpx;
      color: #222222;
      white-space: nowrap;
    }
  }
}
</style>
