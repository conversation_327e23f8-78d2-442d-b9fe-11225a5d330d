/**
 * 订单状态标签页组件属性定义
 */

export interface OrderStatusItem {
  id: string
  name: string
  count: number
  remark?: any
}

export const orderStatusTabsProps = {
  /** 状态列表 */
  statusList: {
    type: Array as () => OrderStatusItem[],
    default: () => [
      // { id: 'all', name: '全部', count: 0 },
      // { id: 'pending', name: '待接单', count: 0 },
      // { id: 'preparing', name: '待出餐', count: 0 },
      // { id: 'ready', name: '待发配送', count: 0 },
      // { id: 'delivering', name: '配送中', count: 0 },
      // { id: 'cancelled', name: '已取消', count: 0 },
    ],
  },
  /** 当前激活的状态 ID */
  activeStatusId: {
    type: String,
    default: 'ALL',
  },
  /** 是否显示展开按钮 */
  showExpandButton: {
    type: Boolean,
    default: false,
  },
}
