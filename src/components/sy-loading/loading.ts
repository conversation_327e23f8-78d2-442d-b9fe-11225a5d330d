import { PropType } from 'vue'

// uni-app图片模式类型
type ImageMode =
  | 'scaleToFill'
  | 'aspectFit'
  | 'aspectFill'
  | 'widthFix'
  | 'heightFix'
  | 'top'
  | 'bottom'
  | 'center'
  | 'left'
  | 'right'
  | 'top left'
  | 'top right'
  | 'bottom left'
  | 'bottom right'

/**
 * 加载组件属性定义
 */
export const loadingProps = {
  /** 是否显示加载组件 */
  show: {
    type: Boolean,
    default: true,
  },
  /** 加载图片路径 */
  src: {
    type: String,
    default: '/static/images/img/goods-loading.gif',
  },
  /** 加载提示文本 */
  toastText: {
    type: String,
    default: '数据加载中...',
  },
  /** 图片宽度，带单位 */
  imgWidth: {
    type: [String, Number],
    default: 200,
  },
  /** 图片高度，带单位 */
  imgHeight: {
    type: [String, Number],
    default: 200,
  },
  /** 是否显示背景蒙层 */
  showMask: {
    type: Boolean,
    default: false,
  },
  /** 是否使用fixed定位，false则使用absolute定位 */
  fixed: {
    type: Boolean,
    default: true,
  },
  /** 图片裁剪、缩放的模式 */
  mode: {
    type: String as PropType<ImageMode>,
    default: 'aspectFit',
  },
  /** 图片懒加载，仅支持网络图片 */
  lazyLoad: {
    type: Boolean,
    default: false,
  },
  /** 开启长按图片显示识别小程序码菜单 */
  showMenuByLongpress: {
    type: Boolean,
    default: false,
  },
  /** 图片失败时默认显示的图片 */
  errorImg: {
    type: String,
    default: '',
  },
  /** 图片加载中显示的图片 */
  loadingImg: {
    type: String,
    default: '',
  },
  /** 是否能拖动图片 */
  draggable: {
    type: Boolean,
    default: false,
  },
  /** 是否开启图片点击效果 */
  hoverClass: {
    type: String,
    default: 'none',
  },
  /** 自定义样式 */
  customStyle: {
    type: String,
    default: '',
  },
}
