<template>
  <view
    v-if="show"
    class="sy-loading"
    :class="{ 'with-mask': showMask, 'absolute-position': !fixed }"
  >
    <image
      class="sy-loading-img"
      :src="src"
      :mode="mode"
      :lazy-load="lazyLoad"
      :show-menu-by-longpress="showMenuByLongpress"
      :draggable="draggable"
      :hover-class="hoverClass"
      :style="`width: ${typeof imgWidth === 'number' ? imgWidth + 'rpx' : imgWidth}; height: ${typeof imgHeight === 'number' ? imgHeight + 'rpx' : imgHeight}; ${customStyle}`"
      :error="errorImg"
      :loading="loadingImg"
    ></image>
    <text class="sy-loading-text">{{ toastText }}</text>
  </view>
</template>

<script setup lang="ts">
import { loadingProps } from './loading'

// 定义组件属性
const props = defineProps(loadingProps)

// 添加默认导出
defineOptions({
  name: 'SyLoading',
})
</script>

<script lang="ts">
// 导出为默认组件
export default {
  name: 'SyLoading',
}
</script>

<style lang="scss">
.sy-loading {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;

  &.absolute-position {
    position: absolute;
  }

  &.with-mask {
    background-color: rgba(255, 255, 255, 0.9);
  }

  &-img {
    margin-bottom: 20rpx;
  }

  &-text {
    font-size: 28rpx;
    color: #666;
  }
}
</style>
