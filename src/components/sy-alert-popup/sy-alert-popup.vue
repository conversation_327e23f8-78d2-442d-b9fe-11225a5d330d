<template>
  <wd-popup v-model="show" custom-style="border-radius:24rpx;">
    <view class="sy-alert-popup">
      <view class="sy-alert-title">{{ title }}</view>
      <view class="sy-alert-content">
        <slot>
          <template v-if="Array.isArray(content)">
            <view v-for="(item, index) in content" :key="index" class="content-line">
              {{ item }}
            </view>
          </template>
          <template v-else>
            {{ content }}
          </template>
        </slot>
      </view>
      <view class="sy-alert-actions">
        <wd-button type="primary" block @click="onConfirm" class="confirm-btn">
          {{ confirmText }}
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { alertPopupProps } from './alert-popup'

// 定义组件属性
const props = defineProps(alertPopupProps)

// 定义事件
const emit = defineEmits(['update:modelValue', 'confirm'])

// 计算属性：控制弹窗显示
const show = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

// 点击确认按钮
const onConfirm = () => {
  show.value = false
  emit('confirm')
}

// 添加默认导出
defineOptions({
  name: 'SyAlertPopup',
})
</script>

<style lang="scss">
.sy-alert-popup {
  box-sizing: border-box;
  width: 626rpx;
  padding: 32rpx 24rpx 24rpx 24rpx;
  background: #ffffff;
  border-radius: 24rpx;

  .sy-alert-title {
    margin-bottom: 32rpx;
    font-size: 32rpx;
    font-weight: 600;
    line-height: 44rpx;
    color: #222222;
    text-align: center;
  }

  .sy-alert-content {
    max-height: 600rpx;
    margin-bottom: 48rpx;
    overflow-y: auto;
    font-size: 26rpx;
    font-weight: 400;
    line-height: 40rpx;
    color: #999999;
    text-align: center;
    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 4rpx;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2rpx;
    }

    &::-webkit-scrollbar-thumb {
      background: #ddd;
      border-radius: 2rpx;
    }

    .content-line {
      margin-bottom: 8rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .sy-alert-actions {
    .confirm-btn {
      width: 578rpx;
      height: 78rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #ffffff;
      background: #f33429;
      border: none;
      border-radius: 44rpx;

      &:active {
        opacity: 0.9;
      }
    }
  }
}
</style>
