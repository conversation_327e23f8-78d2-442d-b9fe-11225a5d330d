/**
 * tabbar 状态，增加 storageSync 保证刷新浏览器时在正确的 tabbar 页面
 * 使用reactive简单状态，而不是 pinia 全局状态
 */
import { reactive } from 'vue'
import { PERMISSION_CODES } from '@/config/permissions'

export const useTabbarStore = () => {
  const tabbarData = reactive({
    curIdx: 0,
  })

  const setCurIdx = (idx: number) => {
    tabbarData.curIdx = idx
  }

  const resetToFirst = () => {
    tabbarData.curIdx = 0
  }

  return {
    ...tabbarData,
    setCurIdx,
    resetToFirst,
  }
}

export const tabbarStore = useTabbarStore()

/**
 * 获取当前 tabbar 高度，用于内容区域计算
 */
export function getTabbarHeight(): number {
  // 默认 tabbar 高度 + 安全区域高度
  let height = 50
  // #ifdef APP-PLUS || H5
  const systemInfo = uni.getSystemInfoSync()
  // 获取安全区域高度
  const safeAreaInsets = systemInfo.safeAreaInsets || { bottom: 0 }
  height += safeAreaInsets.bottom || 0
  // #endif
  return height
}

/**
 * 带权限控制的 TabBar 配置列表
 * 注意：权限码请根据后端实际返回的权限码进行调整
 */
export const tabbarList = [
  {
    pagePath: '/pages/index/index',
    // text: '订单',
    icon: '/static/tabbar/order.png',
    iconSelected: '/static/tabbar/order-selected.png',
    iconType: 'local',
    permissionCode: PERMISSION_CODES.MENU_INDEX, // 使用统一配置
  },
  {
    pagePath: '/pages/dishManagement/index',
    // text: '菜品',
    icon: '/static/tabbar/dishes.png',
    iconSelected: '/static/tabbar/dishes-selected.png',
    iconType: 'local',
    permissionCode: PERMISSION_CODES.MENU_DISH_MANAGEMENT, // 使用统一配置
  },
  {
    pagePath: '/pages/review/index',
    // text: '评价',
    icon: '/static/tabbar/review.png',
    iconSelected: '/static/tabbar/review-selected.png',
    iconType: 'local',
    permissionCode: PERMISSION_CODES.MENU_REVIEW, // 使用统一配置
  },
  {
    pagePath: '/pages/storeDetail/index',
    // text: '门店',
    icon: '/static/tabbar/store.png',
    iconSelected: '/static/tabbar/store-selected.png',
    iconType: 'local',
    permissionCode: PERMISSION_CODES.MENU_STORE_DETAIL, // 使用统一配置
  },
]

/**
 * TabBar 切换函数
 */
export function switchTab(item: any, index: number) {
  tabbarStore.setCurIdx(index)
  uni.switchTab({
    url: item.pagePath,
  })
}
