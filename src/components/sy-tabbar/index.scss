.tabbar-icon-fade-slide-enter-active,
.tabbar-icon-fade-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tabbar-icon-fade-slide-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.tabbar-icon-fade-slide-enter-to {
  opacity: 1;
  transform: translateY(0);
}

.tabbar-icon-fade-slide-leave-from {
  opacity: 1;
  transform: translateY(0);
}

.tabbar-icon-fade-slide-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}
/* 修复滚动条问题 */
:deep(.wd-tabbar) {
  z-index: 999;
  box-sizing: border-box;
}

:deep(.wd-tabbar__placeholder) {
  box-sizing: border-box;
  height: auto !important;
}
.tabbar-box {
  height: 160rpx !important;
}
