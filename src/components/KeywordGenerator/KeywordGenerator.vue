<template>
  <view class="keyword-generator">
    <view class="keyword-header">
      <text class="keyword-title">门店关键词</text>
      <view class="keyword-action" @click="showAddKeywordPopup">
        <text class="add-text">添加关键词</text>
        <image src="/static/images/icons/add-circle.png" class="add-icon" mode="aspectFit" />
      </view>
    </view>

    <view class="keyword-list">
      <view v-for="(keyword, index) in keywords" :key="index" class="keyword-item">
        <text class="keyword-text">{{ keyword }}</text>
        <image
          src="/static/images/icons/close-circle.png"
          class="delete-icon"
          mode="aspectFit"
          @click="deleteKeyword(index)"
        />
      </view>

      <view v-if="keywords.length === 0" class="empty-tip">
        <text>暂无关键词，请点击添加</text>
      </view>
    </view>

    <!-- 添加关键词弹窗 -->
    <wd-popup v-model="showPopup" custom-style="border-radius:24rpx;">
      <view class="keyword-popup">
        <view class="popup-title">添加关键词</view>
        <view class="popup-content">
          <wd-input v-model="newKeyword" placeholder="请输入关键词" :maxlength="10" clearable />
          <text class="tip-text">关键词最多10个字符</text>
        </view>
        <view class="popup-actions">
          <wd-button @click="cancelAdd" class="cancel-btn">取消</wd-button>
          <wd-button type="primary" @click="confirmAdd" class="confirm-btn">确定</wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 关键词列表
const keywords = ref<string[]>(['口味好', '服务周到', '环境优雅', '性价比高', '出餐快'])

// 弹窗控制
const showPopup = ref(false)
const newKeyword = ref('')

// 显示添加关键词弹窗
const showAddKeywordPopup = () => {
  newKeyword.value = ''
  showPopup.value = true
}

// 取消添加
const cancelAdd = () => {
  showPopup.value = false
}

// 确认添加
const confirmAdd = () => {
  if (newKeyword.value.trim()) {
    keywords.value.push(newKeyword.value.trim())
    showPopup.value = false
  }
}

// 删除关键词
const deleteKeyword = (index: number) => {
  keywords.value.splice(index, 1)
}

// 添加默认导出
defineOptions({
  name: 'KeywordGenerator',
})
</script>

<style lang="scss">
.keyword-generator {
  padding: 32rpx;
  margin: 32rpx;
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.04);

  .keyword-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .keyword-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #222222;
    }

    .keyword-action {
      display: flex;
      align-items: center;

      .add-text {
        font-size: 28rpx;
        color: #f33429;
      }

      .add-icon {
        width: 32rpx;
        height: 32rpx;
        margin-left: 8rpx;
      }
    }
  }

  .keyword-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;

    .keyword-item {
      display: flex;
      align-items: center;
      padding: 12rpx 20rpx;
      background: #f5f6fa;
      border-radius: 100rpx;

      .keyword-text {
        font-size: 28rpx;
        color: #333333;
      }

      .delete-icon {
        width: 28rpx;
        height: 28rpx;
        margin-left: 12rpx;
      }
    }

    .empty-tip {
      width: 100%;
      padding: 32rpx 0;
      font-size: 28rpx;
      color: #999999;
      text-align: center;
    }
  }
}

.keyword-popup {
  width: 600rpx;
  padding: 32rpx;

  .popup-title {
    margin-bottom: 32rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #222222;
    text-align: center;
  }

  .popup-content {
    margin-bottom: 32rpx;

    .tip-text {
      margin-top: 8rpx;
      font-size: 24rpx;
      color: #999999;
    }
  }

  .popup-actions {
    display: flex;
    gap: 16rpx;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 80rpx;
      font-size: 28rpx;
    }

    .cancel-btn {
      color: #666666;
      background: #ffffff;
      border: 2rpx solid #e5e6eb;
    }

    .confirm-btn {
      color: #ffffff;
      background: #f33429;
    }
  }
}
</style>
