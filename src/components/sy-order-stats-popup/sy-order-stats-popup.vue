<template>
  <wd-popup v-model="show" position="bottom" :custom-style="customStyle">
    <view class="sy-order-stats-popup">
      <view class="sy-order-stats-popup-title">{{ title }}</view>
      <!-- 内容区域 -->
      <view class="sy-order-stats-popup-content">
        <!-- 今日共A单 -->
        <view class="sy-order-stats-popup-item">
          <view class="sy-order-stats-popup-item-title">今日共{{ totalCount }}单</view>
          <view class="sy-order-stats-popup-item-desc">
            表示【今日】顾客在本店下单并完成支付的订单数量共有{{ totalCount }}单
          </view>
        </view>
        <!-- 进行中B单 -->
        <!-- <view class="sy-order-stats-popup-item">
          <view class="sy-order-stats-popup-item-title">进行中{{ processingCount }}单</view>
          <view class="sy-order-stats-popup-item-desc">
            表示【今日】顾客在本店下单并完成支付的订单中，订单状态为"未完成"和"未取消"的订单数量共有{{
              processingCount
            }}单
          </view>
        </view> -->
        <!-- 已取消C单 -->
        <!-- <view class="sy-order-stats-popup-item">
          <view class="sy-order-stats-popup-item-title">已取消{{ cancelledCount }}单</view>
          <view class="sy-order-stats-popup-item-desc">
            表示【今日】顾客在本店下单并完成支付的订单中，订单状态为"已取消"的订单数量共有{{
              cancelledCount
            }}单
          </view>
        </view> -->
      </view>
      <!-- 按钮区域 -->
      <view class="sy-order-stats-popup-actions">
        <wd-button
          type="primary"
          block
          @click="onConfirmClick"
          class="sy-order-stats-popup-confirm"
        >
          {{ confirmText }}
        </wd-button>
      </view>

      <!-- 底部安全区域 -->
      <view class="sy-order-stats-popup-safe-area"></view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { statsPopupProps } from './stats-popup'

// 定义组件选项
defineOptions({
  name: 'SyOrderStatsPopup',
})

// 定义组件属性
const props = defineProps(statsPopupProps)

// 定义事件
const emit = defineEmits(['update:modelValue', 'confirm'])

// 自定义弹窗样式
const customStyle = 'border-radius:24rpx 24rpx 0 0;'

// 计算属性：控制弹窗显示
const show = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

// 点击确认按钮
const onConfirmClick = () => {
  emit('confirm')
  show.value = false
}

// 导出需要的属性和方法
defineExpose({
  show,
})
</script>

<style lang="scss" scoped>
.sy-order-stats-popup {
  padding: 40rpx 24rpx;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;

  &-title {
    margin-bottom: 24rpx;
    font-size: 30rpx;
    font-weight: 600;
    color: #222222;
    text-align: center;
  }

  &-content {
    padding: 0 12rpx;
  }

  &-item {
    margin-bottom: 24rpx;

    &-title {
      margin-bottom: 8rpx;
      font-size: 30rpx;
      font-weight: 500;
      color: #333333;
    }

    &-desc {
      font-size: 26rpx;
      line-height: 36rpx;
      color: #666666;
    }
  }

  &-actions {
    margin-top: 16rpx;
  }

  &-confirm {
    height: 78rpx !important;
    font-size: 30rpx !important;
    font-weight: 500 !important;
    color: #fff !important;
    background-color: #f33429 !important;
    border: none !important;
    border-radius: 39rpx !important;
  }

  &-safe-area {
    height: 80rpx; /* TabBar的高度 */
  }
}
</style>
