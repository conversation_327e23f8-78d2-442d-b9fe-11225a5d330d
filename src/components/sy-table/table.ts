/**
 * 表格组件属性定义
 */
import type { PropType, CSSProperties } from 'vue'

/**
 * 表格列配置
 */
export interface TableColumn {
  /** 列字段名 */
  prop: string
  /** 列标题 */
  label: string
  /** 列宽度，单位rpx */
  width?: number | string
  /** 单元格高度，单位rpx */
  height?: number | string
  /** 表头单元格高度，单位rpx */
  headerHeight?: number | string
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
  /** 垂直对齐方式 */
  verticalAlign?: 'top' | 'middle' | 'bottom'
  /** 是否固定列 */
  fixed?: boolean | 'left' | 'right'
  /** 自定义渲染函数 */
  render?: (row: any, index: number) => any
  /** 是否可排序 */
  sortable?: boolean
  /** 自定义类名 */
  className?: string
  /** 插槽名称，用于自定义渲染 */
  slotName?: string
  /** 是否自适应宽度 */
  resizable?: boolean
  /** 最小宽度，单位rpx */
  minWidth?: number | string
  /** 最大宽度，单位rpx */
  maxWidth?: number | string
  /** 列宽度权重，用于自适应宽度计算 */
  flex?: number
  /** 自定义单元格格式化函数 */
  formatter?: (row: TableData, column: TableColumn, cellValue: any, index: number) => string
  /** 自定义单元格渲染函数 */
  renderCell?: (row: TableData, column: TableColumn, cellValue: any, index: number) => any
  /** 单元格内容显示行数，超出显示省略号，默认为1 */
  ellipsis?: number | boolean
  /** 是否允许文本换行 */
  wrapText?: boolean
  /** 表头字体大小，单位rpx */
  headerFontSize?: number | string
  /** 表头字体粗细 */
  headerFontWeight?: string | number
  /** 表头文字颜色 */
  headerColor?: string
  /** 表头背景色 */
  headerBgColor?: string
  /** 单元格字体大小，单位rpx */
  cellFontSize?: number | string
  /** 单元格字体粗细 */
  cellFontWeight?: string | number
  /** 单元格文字颜色 */
  cellColor?: string
  /** 单元格背景色 */
  cellBgColor?: string
  /** 自定义单元格样式 */
  cellStyle?:
    | CSSProperties
    | ((row: TableData, column: TableColumn, rowIndex: number) => CSSProperties)
}

/**
 * 表格数据项
 */
export interface TableData {
  [key: string]: any
}

/**
 * 排序信息
 */
export interface SortInfo {
  prop: string
  order: 'asc' | 'desc' | null
}

/**
 * 表格组件属性
 */
export const tableProps = {
  /** 表格数据 */
  data: {
    type: Array as PropType<TableData[]>,
    default: () => [],
  },
  /** 表格列配置 */
  columns: {
    type: Array as PropType<TableColumn[]>,
    default: () => [],
  },
  /** 是否显示边框 */
  border: {
    type: Boolean,
    default: true,
  },
  /** 边框颜色 */
  borderColor: {
    type: String,
    default: '#e6e6e6',
  },
  /** 是否显示斑马纹 */
  stripe: {
    type: Boolean,
    default: false,
  },
  /** 表格高度，固定表头时使用 */
  height: {
    type: [String, Number],
    default: '',
  },
  /** 表格宽度，单位rpx或百分比 */
  width: {
    type: [String, Number],
    default: '100%',
  },
  /** 是否固定表头 */
  fixedHeader: {
    type: Boolean,
    default: false,
  },
  /** 固定表头模式：'height' - 通过高度控制，'rows' - 通过行数控制 */
  fixedHeaderMode: {
    type: String as PropType<'height' | 'rows'>,
    default: 'height',
  },
  /** 最大可见行数，超过该行数时启用固定表头竖向滚动 */
  maxVisibleRows: {
    type: Number,
    default: 0, // 0表示不限制
  },
  /** 是否列宽自适应 */
  columnResizable: {
    type: Boolean,
    default: false,
  },
  /** 是否显示表头 */
  showHeader: {
    type: Boolean,
    default: true,
  },
  /** 是否可选择行 */
  selectable: {
    type: Boolean,
    default: false,
  },
  /** 空数据提示文本 */
  emptyText: {
    type: String,
    default: '暂无数据',
  },
  /** 行唯一标识字段 */
  rowKey: {
    type: String,
    default: 'id',
  },
  /** 是否加载中 */
  loading: {
    type: Boolean,
    default: false,
  },
  /** 行高 */
  rowHeight: {
    type: [Number, String],
    default: '',
  },
  /** 是否支持横向滚动 */
  scrollable: {
    type: Boolean,
    default: false,
  },
  /** 全局设置单元格内容显示行数，超出显示省略号，默认为1 */
  ellipsis: {
    type: [Number, Boolean],
    default: false,
  },
  /** 全局设置是否允许文本换行 */
  wrapText: {
    type: Boolean,
    default: false,
  },
  /** 表头高度，单位rpx */
  headerHeight: {
    type: [Number, String],
    default: 50,
  },
  /** 表头最小高度，单位rpx */
  headerMinHeight: {
    type: [Number, String],
    default: '50rpx',
  },
  /** 表头字体大小，单位rpx */
  headerFontSize: {
    type: [Number, String],
    default: '24rpx',
  },
  /** 表头字体粗细 */
  headerFontWeight: {
    type: [Number, String],
    default: 600,
  },
  /** 表头文字颜色 */
  headerColor: {
    type: String,
    default: '#333',
  },
  /** 表头背景色 */
  headerBgColor: {
    type: String,
    default: 'transparent', // 默认背景色为透明
  },
  /** 单元格字体大小，单位rpx */
  cellFontSize: {
    type: [Number, String],
    default: '24rpx',
  },
  /** 单元格字体粗细 */
  cellFontWeight: {
    type: [Number, String],
    default: 400,
  },
  /** 单元格文字颜色 */
  cellColor: {
    type: String,
    default: '#333',
  },
  /** 单元格最小高度，单位rpx */
  cellMinHeight: {
    type: [Number, String],
    default: '50rpx', // 表头、单元格默认最小高度为50rpx
  },
  /** 默认水平对齐方式 */
  defaultAlign: {
    type: String as PropType<'left' | 'center' | 'right'>,
    default: 'center', // 默认水平居中
  },
  /** 默认垂直对齐方式 */
  defaultVerticalAlign: {
    type: String as PropType<'top' | 'middle' | 'bottom'>,
    default: 'middle',
  },
}

/**
 * 表格组件实例方法
 */
export interface TableInstance {
  /** 清除所有选择 */
  clearSelection: () => void
  /** 设置选中行 */
  setSelection: (rows: TableData[]) => void
}

/**
 * 表格排序信息
 */
export interface TableSortInfo {
  prop: string
  order: 'asc' | 'desc' | null
}

/**
 * 表格事件
 */
export const tableEmits = [
  'row-click',
  'cell-click',
  'selection-change',
  'sort-change',
  'scroll',
  'update:columns',
]
