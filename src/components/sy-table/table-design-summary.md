# SyTable 组件设计与优化总结

## 组件结构

SyTable 组件采用了三层嵌套结构设计，解决了横向滚动和固定表头同时使用时的冲突问题：

1. **外层容器** `.sy-table`：负责整体布局和样式控制
2. **中间容器** `.sy-table-outer-container`：处理布局方向和溢出控制
3. **内层容器** `.sy-table-inner-container`：处理内容的具体呈现

表头和内容分别使用独立的滚动区域：

- **表头区域** `.sy-table-header-scroll`：横向滚动，但不纵向滚动
- **内容区域** `.sy-table-body`：可同时横向和纵向滚动

## 修复的问题

### 1. 竖向滚动失效问题

**问题描述**：设置了 `fixedHeader` 或 `height` 后，表格的竖向滚动不生效。

**解决方案**：

- 将容器结构调整为嵌套的 flex 布局
- 确保 `.sy-table-body` 设置了 `flex: 1` 和正确的 `overflow` 属性
- 调整了 `box-sizing` 属性以保证尺寸计算准确

### 2. 横向滚动与固定表头冲突

**问题描述**：同时启用横向滚动和固定表头时，会导致布局错乱。

**解决方案**：

- 分离表头和内容的滚动区域
- 将表头和内容的横向滚动位置同步
- 使用 `handleHorizontalScroll` 和 `handleBothScroll` 方法处理滚动事件

### 3. 无宽度列平分问题

**问题描述**：没有指定 `width` 或 `minWidth` 的列无法均匀平分整个视图宽度。

**解决方案**：

- 添加 `sy-table--auto-width` 条件类，当检测到存在未设置宽度的列时应用

  ```ts
  'sy-table--auto-width': !scrollable && columns.some(col => !col.width && !col.minWidth)
  ```

- 为无宽度列设置特殊样式，使其能够平分剩余空间：

  ```scss
  &--auto-width {
    .sy-table-inner-container {
      table-layout: fixed;
      width: 100%;
    }

    .sy-table-cell,
    .sy-table-header__cell {
      &:not([style*='width:']) {
        flex: 1 1 0;
        width: 0;
        min-width: 0;
        box-sizing: border-box;
      }
    }
  }
  ```

- 改进 `getColumnStyle` 方法，针对无宽度列添加特殊处理：

  ```ts
  // 如果没有设置任何宽度相关属性，则平分剩余空间
  else {
    style.flex = '1 1 0' // 使用flex:1让列平分剩余空间

    // 当没有设置任何宽度时，确保表格内容能够平分视图宽度
    if (!props.scrollable) {
      style.width = '0' // 设置宽度为0，让flex属性控制实际宽度
      style.minWidth = '0' // 设置最小宽度为0，防止内容撑开
    }
  }
  ```

- 在表格内容样式计算中添加 `tableLayout: fixed` 属性，确保列宽平均分配：
  ```ts
  if (!props.scrollable) {
    style.width = '100%'
    style.tableLayout = 'fixed' // 使用固定表格布局以确保列平分
    return style
  }
  ```

### 4. 边框对齐问题

**问题描述**：横向滚动+固定表头场景下，边框显示不对齐。

**解决方案**：

- 添加 `.sy-table--border-fix` 类处理边框问题
- 统一边框颜色为 `#e6e6e6`（从原来的 `#ccc` 改变）
- 处理滚动条导致的边框错位
- 控制 `overflow` 属性防止出现不必要的滚动条

### 5. 滚动事件统一处理

**问题描述**：横向和纵向滚动事件处理分散，导致同步问题。

**解决方案**：

- 添加 `handleBothScroll` 方法同时处理横向和纵向滚动
- 保持 `scrollLeft` 值的同步，确保表头和内容区域滚动一致

## 样式优化

1. **统一盒模型**：所有关键容器使用 `box-sizing: border-box`
2. **滚动条美化**：自定义滚动条样式，提升用户体验
3. **边框处理**：统一边框样式和颜色，解决边框重叠问题
4. **样式变量**：使用 CSS 变量实现主题定制

## 性能考虑

1. **减少重排重绘**：合理使用 `z-index` 和 `position` 属性
2. **滚动优化**：使用 `enhanced` 和 `scroll-with-animation` 提升滚动体验
3. **条件渲染**：根据配置属性有条件地渲染组件部分，减少不必要的 DOM 节点

## 扩展性设计

1. **灵活的列配置**：支持 `width`、`minWidth` 和 `flex` 多种列宽设置方式
2. **自定义渲染**：支持 `renderCell` 和 `formatter` 实现自定义内容渲染
3. **丰富的事件**：提供完整的事件系统，支持行点击、单元格点击等
4. **主题定制**：通过 CSS 变量支持表格外观定制

## 兼容性考虑

1. **跨平台适配**：考虑小程序、H5、App 的不同环境
2. **滚动兼容**：使用通用的滚动属性和事件处理
3. **样式兼容**：使用标准 CSS 属性，同时提供浏览器前缀
