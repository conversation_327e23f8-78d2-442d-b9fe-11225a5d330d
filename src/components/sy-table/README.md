# SyTable 表格组件

## 功能说明

SyTable 是一个功能完整的表格组件，支持以下特性：

- 固定表头（纵向滚动）
- 横向滚动
- 表格边框
- 斑马纹
- 排序功能
- 行选择
- 自定义单元格渲染
- 文本省略和换行
- 列宽调整
- 空数据和加载状态

## 最近修复的问题

1. **竖向滚动失效问题**：通过调整CSS样式和容器结构，修复了表格竖向滚动不生效的问题。

2. **横向滚动与固定表头兼容**：解决了横向滚动和固定表头同时使用时的冲突问题，确保两种滚动方式可以同时工作。

3. **无宽度列平分问题**：优化了没有指定width的列的处理，使它们能够平均分配剩余空间。

4. **边框对齐问题**：修复了横向滚动+固定表头场景下边框显示不对齐的问题，通过添加`.sy-table--border-fix`类并设置相应的CSS规则。

5. **统一边框样式**：统一了边框颜色从`#ccc`改为`#e6e6e6`，确保边框粗细和颜色一致。

## 基本使用

```vue
<template>
  <sy-table
    :data="tableData"
    :columns="columns"
    :border="true"
    :stripe="true"
    @row-click="handleRowClick"
  />
</template>

<script setup>
import { ref } from 'vue'
import SyTable from '@/components/sy-table/SyTable.vue'

// 表格数据
const tableData = ref([
  { id: '1', name: '张三', age: 25, address: '北京市朝阳区' },
  { id: '2', name: '李四', age: 30, address: '上海市浦东新区' },
  { id: '3', name: '王五', age: 28, address: '广州市天河区' },
])

// 列配置
const columns = ref([
  { prop: 'name', label: '姓名', width: 120 },
  { prop: 'age', label: '年龄', width: 80, sortable: true },
  { prop: 'address', label: '地址', minWidth: 200 },
])

// 行点击事件
const handleRowClick = (row, index) => {
  console.log('点击行:', row, index)
}
</script>
```

## 横向滚动 + 固定表头示例

```vue
<template>
  <sy-table
    :data="tableData"
    :columns="wideColumns"
    :border="true"
    :fixed-header="true"
    :height="300"
    :scrollable="true"
  />
</template>

<script setup>
// 较多的列配置，需要横向滚动
const wideColumns = ref([
  { prop: 'name', label: '姓名', width: 120 },
  { prop: 'age', label: '年龄', width: 80 },
  { prop: 'address', label: '地址', width: 300 },
  { prop: 'phone', label: '电话', width: 180 },
  { prop: 'email', label: '邮箱', width: 250 },
  { prop: 'department', label: '部门', width: 150 },
  { prop: 'position', label: '职位', width: 150 },
  { prop: 'createTime', label: '创建时间', width: 180 },
])
</script>
```

## Props 属性

| 属性名               | 类型           | 默认值     | 说明                                                |
| -------------------- | -------------- | ---------- | --------------------------------------------------- |
| data                 | Array          | []         | 表格数据                                            |
| columns              | Array          | []         | 表格列配置                                          |
| rowKey               | String         | 'id'       | 行数据的唯一标识字段                                |
| height               | String/Number  | -          | 表格高度，可以是数字（rpx）或字符串                 |
| width                | String/Number  | '100%'     | 表格宽度，可以是数字（rpx）或字符串                 |
| border               | Boolean        | true       | 是否显示边框                                        |
| borderColor          | String         | '#e6e6e6'  | 边框颜色                                            |
| stripe               | Boolean        | false      | 是否显示斑马纹                                      |
| selectable           | Boolean        | false      | 是否可选择行                                        |
| fixedHeader          | Boolean        | false      | 是否固定表头                                        |
| scrollable           | Boolean        | false      | 是否允许横向滚动                                    |
| showHeader           | Boolean        | true       | 是否显示表头                                        |
| emptyText            | String         | '暂无数据' | 空数据提示文本                                      |
| loading              | Boolean        | false      | 是否显示加载状态                                    |
| headerBgColor        | String         | '#f8f8f8'  | 表头背景色                                          |
| defaultAlign         | String         | 'left'     | 默认对齐方式，可选值：'left'、'center'、'right'     |
| defaultVerticalAlign | String         | 'middle'   | 默认垂直对齐方式，可选值：'top'、'middle'、'bottom' |
| columnResizable      | Boolean        | false      | 是否允许调整列宽                                    |
| ellipsis             | Boolean/Number | false      | 文本超出是否显示省略号，可以是布尔值或数字（行数）  |
| wrapText             | Boolean        | false      | 是否允许文本换行                                    |

## Events 事件

| 事件名           | 说明           | 参数                                         |
| ---------------- | -------------- | -------------------------------------------- |
| row-click        | 行点击事件     | (row, index)                                 |
| cell-click       | 单元格点击事件 | (row, column, cellValue, rowIndex, colIndex) |
| selection-change | 选择项变化事件 | (selectedRows)                               |
| sort-change      | 排序变化事件   | ({ prop, order })                            |
| scroll           | 滚动事件       | (event)                                      |

## 列配置项

每个列配置对象（column）支持以下属性：

| 属性名        | 类型           | 默认值 | 说明                                    |
| ------------- | -------------- | ------ | --------------------------------------- |
| prop          | String         | -      | 字段名称（必填）                        |
| label         | String         | -      | 列标题（必填）                          |
| width         | Number/String  | -      | 列宽度（rpx）                           |
| minWidth      | Number/String  | -      | 最小列宽度（rpx）                       |
| align         | String         | -      | 对齐方式：'left'、'center'、'right'     |
| verticalAlign | String         | -      | 垂直对齐方式：'top'、'middle'、'bottom' |
| className     | String         | -      | 自定义类名                              |
| sortable      | Boolean        | false  | 是否可排序                              |
| resizable     | Boolean        | false  | 列是否可调整宽度                        |
| renderCell    | Function       | -      | 自定义单元格渲染函数                    |
| formatter     | Function       | -      | 单元格数据格式化函数                    |
| ellipsis      | Boolean/Number | -      | 文本超出是否显示省略号                  |
| wrapText      | Boolean        | -      | 是否允许文本换行                        |
| flex          | Number         | -      | 弹性布局比例，用于自适应列宽            |

## 方法

可以通过 ref 获取表格实例，调用以下方法：

| 方法名         | 说明           | 参数   |
| -------------- | -------------- | ------ |
| clearSelection | 清除所有选择行 | -      |
| setSelection   | 设置选中行     | (rows) |

```vue
<template>
  <sy-table ref="tableRef" :data="tableData" :columns="columns" :selectable="true" />
  <button @click="clearTableSelection">清除选择</button>
</template>

<script setup>
import { ref } from 'vue'

const tableRef = ref(null)

const clearTableSelection = () => {
  tableRef.value.clearSelection()
}
</script>
```

## 注意事项

1. 同时使用横向滚动和固定表头时，建议设置固定的高度和列宽，以获得最佳效果。
2. 列配置中的`width`属性为固定宽度，如果需要自适应，可以使用`minWidth`或`flex`属性。
3. 边框对齐问题已经在组件内部处理，无需额外设置。
4. 如需对表格进行自定义样式，可以通过CSS变量覆盖默认样式。
