<template>
  <view
    class="sy-table"
    :class="{
      'sy-table--border': border,
      'sy-table--stripe': stripe,
      'sy-table--fixed-header': needFixedHeader,
      'sy-table--scrollable': scrollable,
      'sy-table--border-fix': scrollable && needFixedHeader && border,
      'sy-table--auto-width': !scrollable && columns.some((col) => !col.width && !col.minWidth),
    }"
    :style="tableStyle"
  >
    <!-- 表格内容容器 -->
    <view class="sy-table-outer-container">
      <!-- 表头部分 -->
      <view
        v-if="showHeader"
        class="sy-table-header-wrapper"
        :class="{ 'is-fixed': needFixedHeader }"
        :style="headerWrapperStyle"
        ref="tableHeaderRef"
      >
        <scroll-view
          :scroll-x="scrollable"
          :scroll-left="scrollLeft"
          @scroll="handleHorizontalScroll"
          :enhanced="true"
          :show-scrollbar="false"
          class="sy-table-header-scroll"
        >
          <view class="sy-table-header">
            <view class="sy-table-header__row">
              <!-- 选择列 -->
              <view
                v-if="selectable"
                class="sy-table-header__cell sy-table-header__cell--selection"
                :class="{ 'sy-table-header__cell--border': border }"
              >
                <wd-checkbox v-model="isAllSelected" @change="handleSelectAll" />
              </view>

              <!-- 数据列 -->
              <view
                v-for="(column, index) in columns"
                :key="index"
                class="sy-table-header__cell"
                :class="[
                  column.className,
                  {
                    'sy-table-header__cell--border': border,
                    [`sy-table-header__cell--align-${column.align || props.defaultAlign}`]: true,
                    [`sy-table-header__cell--valign-${column.verticalAlign || props.defaultVerticalAlign}`]: true,
                    'sy-table-header__cell--sortable': column.sortable,
                    'sy-table-header__cell--resizable': column.resizable || columnResizable,
                    'sy-table-header__cell--flex': !!column.flex,
                  },
                ]"
                :style="[getColumnStyle(column), getHeaderStyle(column)]"
                @click="handleHeaderClick(column)"
              >
                <view class="sy-table-header__content">
                  {{ column.label }}

                  <!-- 排序图标 -->
                  <view v-if="column.sortable" class="sy-table-header__sort">
                    <view
                      class="sy-table-header__sort-icon sy-table-header__sort-icon--up"
                      :class="{
                        'is-active': sortInfo.prop === column.prop && sortInfo.order === 'asc',
                      }"
                    >
                      <view class="iconfont icon-xiangshang"></view>
                    </view>
                    <view
                      class="sy-table-header__sort-icon sy-table-header__sort-icon--down"
                      :class="{
                        'is-active': sortInfo.prop === column.prop && sortInfo.order === 'desc',
                      }"
                    >
                      <view class="iconfont icon-xiangxia"></view>
                    </view>
                  </view>
                </view>

                <!-- 列宽调整手柄 -->
                <view
                  v-if="column.resizable || columnResizable"
                  class="sy-table-header__resize-handle"
                  @touchstart.stop="handleResizeStart(column, index, $event)"
                  @touchmove.stop="handleResizeMove($event)"
                  @touchend.stop="handleResizeEnd"
                ></view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 表格主体 -->
      <scroll-view
        :scroll-x="scrollable"
        :scroll-y="true"
        :style="bodyStyle"
        :class="['sy-table-body', { 'rows-mode': props.fixedHeaderMode === 'rows' }]"
        @scroll="handleBothScroll"
        :enhanced="true"
        :show-scrollbar="true"
        :scroll-with-animation="true"
        ref="tableBodyRef"
      >
        <view class="sy-table-inner-container" :style="tableContentStyle">
          <!-- 数据行 -->
          <view class="sy-table-body-inner">
            <view
              v-for="(row, rowIndex) in data"
              :key="rowIndex"
              class="sy-table-row"
              :style="getRowStyle(row, rowIndex)"
              @click="handleRowClick(row, rowIndex)"
            >
              <!-- 选择列 -->
              <view
                v-if="selectable"
                class="sy-table-cell sy-table-cell--selection"
                :class="{ 'sy-table-cell--border': border }"
                @click.stop
              >
                <wd-checkbox
                  :value="isRowSelected(row)"
                  @change="(val) => handleSelectRow(row, val)"
                />
              </view>

              <!-- 数据列 -->
              <view
                v-for="(column, colIndex) in columns"
                :key="colIndex"
                class="sy-table-cell"
                :class="[
                  column.className,
                  {
                    'sy-table-cell--border': border,
                    [`sy-table-cell--align-${column.align || props.defaultAlign}`]: true,
                    [`sy-table-cell--valign-${column.verticalAlign || props.defaultVerticalAlign}`]: true,
                    'sy-table-cell--flex': !!column.flex,
                  },
                ]"
                :style="[getColumnStyle(column), getCellStyle(column)]"
                @click.stop="handleCellClick(row, column, row[column.prop], rowIndex, colIndex)"
              >
                <!-- 自定义渲染 -->
                <template v-if="column.renderCell">
                  <component :is="column.renderCell(row, column, row[column.prop], rowIndex)" />
                </template>

                <!-- 默认渲染 -->
                <template v-else>
                  <view
                    class="sy-table-cell__content"
                    :class="{
                      'sy-table-cell__content--ellipsis': getEllipsisConfig(column),
                      'sy-table-cell__content--wrap': getWrapTextConfig(column),
                      [`sy-table-cell__content--ellipsis-${getEllipsisLines(column)}`]:
                        getEllipsisLines(column) > 1,
                    }"
                  >
                    {{ formatCellValue(row, column, rowIndex) }}
                  </view>
                </template>
              </view>
            </view>

            <!-- 空状态 -->
            <view v-if="!data.length && !loading" class="sy-table-empty">
              <view class="sy-table-empty__icon">
                <image
                  src="/static/images/img/no-result.png"
                  mode="aspectFit"
                  class="empty-image"
                />
              </view>
              <view class="sy-table-empty__text">{{ emptyText }}</view>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 加载状态 -->
      <view v-if="loading" class="sy-table-loading">
        <wd-loading color="#F33429" />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import {
  tableProps,
  tableEmits,
  type TableData,
  type SortInfo,
  type TableInstance,
  type TableColumn,
} from './table'
import { extractNumericValue, ensureUnit } from '@/utils/transform'

// 定义组件选项
defineOptions({
  name: 'SyTable',
})

// 定义组件属性
const props = defineProps(tableProps)

// 定义组件事件
const emit = defineEmits(tableEmits)

// 响应式数据
const sortInfo = ref<SortInfo>({
  prop: '',
  order: null,
})

const selectedRows = ref<TableData[]>([])
const scrollLeft = ref(0)

// 列宽调整相关状态
const resizingColumn = ref<TableColumn | null>(null)
const resizingColumnIndex = ref(-1)
const startX = ref(0)
const startWidth = ref(0)
const columnWidths = ref<Record<string, number | string>>({})

// 表格高度相关
const tableBodyRef = ref<any>(null)
const tableHeaderRef = ref<any>(null)
const actualTableHeight = ref(0)
const shouldFixHeader = ref(false)

// 计算属性
// 表格主体样式
const bodyStyle = computed(() => {
  const style: Record<string, string> = {}

  // 根据不同的固定表头模式设置不同的高度
  if (
    props.fixedHeaderMode === 'rows' &&
    props.maxVisibleRows > 0 &&
    props.data.length > props.maxVisibleRows
  ) {
    // 行数模式：根据maxVisibleRows计算高度
    // 计算每行高度（假设行高一致）
    const rowHeight = extractNumericValue(props.rowHeight || props.cellMinHeight)

    // 计算表格高度 = 行数 * 行高 (确保最小高度为200rpx，防止计算错误)
    const calculatedHeight = Math.max(props.maxVisibleRows * rowHeight, 200)
    style.height = `${calculatedHeight}rpx`
    // 确保设置了明确的overflow-y属性
    style.overflowY = 'auto'
  } else if (props.height) {
    // 高度模式：直接使用设置的高度
    style.height = typeof props.height === 'number' ? `${props.height}rpx` : props.height
  } else if (needFixedHeader.value) {
    // 其他情况下需要固定表头，使用默认高度
    style.height = '500rpx'
  }

  // 确保在 flex 布局中占据剩余空间
  style.flexGrow = '1'

  return style
})

// 表格样式
const tableStyle = computed(() => {
  const style: Record<string, string> = {}

  // 设置表格宽度
  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}rpx` : props.width
  } else {
    // 如果没有设置宽度，默认占满视图宽度
    style.width = '100%'
  }

  // 设置表格高度
  if (props.height) {
    style.height = typeof props.height === 'number' ? `${props.height}rpx` : props.height
  }
  // 根据不同的固定表头模式设置不同的高度
  if (
    props.fixedHeaderMode === 'rows' &&
    props.maxVisibleRows > 0 &&
    props.data.length > props.maxVisibleRows
  ) {
    // 行数模式：根据maxVisibleRows计算高度
    // 计算每行高度（假设行高一致）
    const rowHeight = extractNumericValue(props.rowHeight || props.cellMinHeight)

    // 计算表格高度 = 行数 * 行高
    style.height = `${props.maxVisibleRows * rowHeight}rpx`
  }

  // 设置边框颜色
  if (props.border && props.borderColor) {
    style['--table-border-color'] = props.borderColor
  }

  return style
})

// 表头包装器样式
const headerWrapperStyle = computed(() => {
  const style: Record<string, string> = {}

  // 设置表头背景色
  if (props.headerBgColor && props.headerBgColor !== 'transparent') {
    style.backgroundColor = props.headerBgColor
  }

  return style
})

// 表格内容样式
const tableContentStyle = computed(() => {
  const style: Record<string, string> = {}

  // 如果没有可滚动属性，不需要设置宽度，让表格自然占满容器
  if (!props.scrollable) {
    style.width = '100%'
    style.tableLayout = 'fixed' // 使用固定表格布局以确保列平分
    return style
  }

  // 计算所有列的宽度总和
  let totalWidth = 0
  let hasFlexColumns = false
  let fixedWidthColumns = 0
  let flexColumns = 0

  props.columns.forEach((column) => {
    if (column.width) {
      const width = extractNumericValue(column.width)
      totalWidth += width
      fixedWidthColumns++
    } else if (column.minWidth) {
      const minWidth = extractNumericValue(column.minWidth)
      totalWidth += minWidth
      hasFlexColumns = true
      flexColumns++
    } else if (column.flex) {
      // 如果使用flex，给一个基础宽度
      totalWidth += 100
      hasFlexColumns = true
      flexColumns++
    } else {
      // 没有设置宽度的列，标记为自适应列
      hasFlexColumns = true
      flexColumns++
      // 给无宽度列一个基础宽度，确保表格能够正确计算
      totalWidth += 100
    }
  })

  if (props.selectable) {
    totalWidth += 80 // 如果有选择列，加上选择列的宽度
    fixedWidthColumns++
  }

  // 如果所有列都有固定宽度，并且没有自适应列，则使用固定宽度
  if (fixedWidthColumns === props.columns.length + (props.selectable ? 1 : 0) && !hasFlexColumns) {
    style.width = `${totalWidth}rpx`
    style.minWidth = `100%` // 确保至少占满容器
  } else if (props.scrollable) {
    // 如果有自适应列且允许横向滚动，设置最小宽度为计算的总宽度
    style.minWidth = `${totalWidth}rpx`
    // 同时也设置 width: max-content 确保能够正确显示所有内容
    style.width = 'max-content'
  } else {
    // 如果有自适应列但不允许横向滚动，则设置宽度为100%
    style.width = '100%'
    style.tableLayout = 'fixed' // 使用固定表格布局以确保列平分
  }

  return style
})

// 是否全选
const isAllSelected = computed(() => {
  return props.data.length > 0 && selectedRows.value.length === props.data.length
})

// 是否需要固定表头（根据配置和实际情况计算）
const needFixedHeader = computed(() => {
  // 如果明确设置了fixedHeader为true，则固定表头
  if (props.fixedHeader) {
    return true
  }

  // 根据不同的固定表头模式判断
  if (props.fixedHeaderMode === 'rows') {
    // 行数模式：如果设置了maxVisibleRows且数据行数超过了maxVisibleRows，则固定表头
    return props.maxVisibleRows > 0 && props.data.length > props.maxVisibleRows
  } else {
    // 高度模式：如果设置了height且实际表格高度超过了设置的height，则固定表头
    if (props.height && actualTableHeight.value > 0) {
      const heightValue = extractNumericValue(props.height)
      return actualTableHeight.value > heightValue
    }
  }

  // 其他情况不固定表头
  return false
})

// 方法
// 检查行是否被选中
const isRowSelected = (row: TableData) => {
  return selectedRows.value.some((selectedRow) => selectedRow[props.rowKey] === row[props.rowKey])
}

// 处理表头点击事件
const handleHeaderClick = (column: { prop: string; sortable?: boolean }) => {
  if (column.sortable) {
    if (sortInfo.value.prop === column.prop) {
      // 切换排序方向
      if (sortInfo.value.order === 'asc') {
        sortInfo.value.order = 'desc'
      } else if (sortInfo.value.order === 'desc') {
        sortInfo.value.order = null
      } else {
        sortInfo.value.order = 'asc'
      }
    } else {
      sortInfo.value.prop = column.prop
      sortInfo.value.order = 'asc'
    }

    emit('sort-change', sortInfo.value)
  }
}

// 处理行点击事件
const handleRowClick = (row: TableData, index: number) => {
  emit('row-click', row, index)
}

// 处理行选择事件
const handleSelectRow = (row: TableData, selected: boolean) => {
  if (selected) {
    selectedRows.value.push(row)
  } else {
    const index = selectedRows.value.findIndex(
      (selectedRow) => selectedRow[props.rowKey] === row[props.rowKey],
    )
    if (index !== -1) {
      selectedRows.value.splice(index, 1)
    }
  }

  emit('selection-change', selectedRows.value)
}

// 处理全选事件
const handleSelectAll = (selected: boolean) => {
  selectedRows.value = selected ? [...props.data] : []
  emit('selection-change', selectedRows.value)
}

// 处理单元格点击事件
const handleCellClick = (
  row: TableData,
  column: TableColumn,
  cellValue: any,
  rowIndex: number,
  columnIndex: number,
) => {
  emit('cell-click', row, column, cellValue, rowIndex, columnIndex)
}

// 格式化单元格内容
const formatCellValue = (row: TableData, column: TableColumn, index: number) => {
  const value = row[column.prop]

  if (column.formatter) {
    return column.formatter(row, column, value, index)
  }

  return value
}

// 获取列样式
const getColumnStyle = (column: TableColumn) => {
  const style: Record<string, string> = {}

  // 优先使用调整后的宽度
  if (column.prop && columnWidths.value[column.prop]) {
    style.width =
      typeof columnWidths.value[column.prop] === 'number'
        ? ensureUnit(columnWidths.value[column.prop])
        : (columnWidths.value[column.prop] as string)
    style.flexShrink = '0'
  }
  // 其次使用配置的固定宽度
  else if (column.width) {
    style.width = typeof column.width === 'number' ? ensureUnit(column.width) : column.width
    style.flexShrink = '0'
  }
  // 再次使用最小宽度
  else if (column.minWidth) {
    style.minWidth =
      typeof column.minWidth === 'number' ? ensureUnit(column.minWidth) : column.minWidth
    style.flex = '1 1 auto' // 添加flex:1使其自适应
  }
  // 最后使用flex比例
  else if (column.flex) {
    style.flex = `${column.flex} 1 auto`
  }
  // 如果没有设置任何宽度相关属性，则平分剩余空间
  else {
    style.flex = '1 1 0' // 使用flex:1让列平分剩余空间
    // 当没有设置任何宽度时，确保表格内容能够平分视图宽度
    if (!props.scrollable) {
      style.width = '0' // 设置宽度为0，让flex属性控制实际宽度
      style.minWidth = '0' // 设置最小宽度为0，防止内容撑开
    }
  }

  return style
}

// 获取行样式
const getRowStyle = (row: TableData, index: number) => {
  const style: Record<string, string> = {}

  if (props.rowHeight) {
    style.height =
      typeof props.rowHeight === 'number' ? ensureUnit(props.rowHeight) : props.rowHeight
  }

  return style
}

// 开始调整列宽
const handleResizeStart = (column: TableColumn, index: number, event: TouchEvent) => {
  if (!column.resizable && !props.columnResizable) return

  resizingColumn.value = column
  resizingColumnIndex.value = index
  startX.value = event.touches[0].clientX

  // 获取当前列宽
  const currentWidth = column.width ? extractNumericValue(column.width) : 100 // 默认宽度

  startWidth.value = currentWidth
}

// 调整列宽
const handleResizeMove = (event: TouchEvent) => {
  if (!resizingColumn.value) return

  const deltaX = event.touches[0].clientX - startX.value
  const newWidth = Math.max(
    startWidth.value + deltaX,
    resizingColumn.value.minWidth ? extractNumericValue(resizingColumn.value.minWidth) : 50,
  ) // 最小宽度默认 50rpx

  // 更新列宽
  if (resizingColumn.value) {
    const columnsData = [...props.columns]
    columnsData[resizingColumnIndex.value] = {
      ...columnsData[resizingColumnIndex.value],
      width: newWidth,
    }
    // 由于 props 是只读的，这里我们通过事件通知父组件更新列配置
    emit('update:columns', columnsData)
  }
}

// 结束调整列宽
const handleResizeEnd = () => {
  resizingColumn.value = null
  resizingColumnIndex.value = -1
}

// 处理横向滚动
const handleHorizontalScroll = (e: any) => {
  scrollLeft.value = e.detail.scrollLeft
}

// 处理垂直滚动
const handleVerticalScroll = (e: any) => {
  // 如果需要处理垂直滚动事件
  emit('scroll', e)
}

// 处理同时处理横向和垂直滚动
const handleBothScroll = (e: any) => {
  // 更新横向滚动位置
  scrollLeft.value = e.detail.scrollLeft
  // 触发滚动事件
  emit('scroll', e)
}

// 清除所有选择
const clearSelection = () => {
  selectedRows.value = []
}

// 设置选中行
const setSelection = (rows: TableData[]) => {
  selectedRows.value = rows
}

// 计算表格实际高度
const calculateTableHeight = () => {
  nextTick(() => {
    // 使用uni.createSelectorQuery获取表格实际高度
    const query = uni.createSelectorQuery().in(this)
    query
      .select('.sy-table-body-inner')
      .boundingClientRect((data: any) => {
        if (data && typeof data.height === 'number') {
          actualTableHeight.value = data.height

          // 根据不同的固定表头模式判断是否需要固定表头
          if (props.fixedHeaderMode === 'rows') {
            // 行数模式：如果设置了maxVisibleRows且数据行数超过了maxVisibleRows，则固定表头
            shouldFixHeader.value =
              props.maxVisibleRows > 0 && props.data.length > props.maxVisibleRows
          } else if (props.height) {
            // 高度模式：根据实际高度和设置的高度判断是否需要固定表头
            const heightValue = extractNumericValue(props.height)
            shouldFixHeader.value = actualTableHeight.value > heightValue
          }
        }
      })
      .exec()
  })
}

// 暴露组件方法
defineExpose<TableInstance>({
  clearSelection,
  setSelection,
})

// 监听数据变化，重置选中状态和重新计算表格高度
watch(
  () => props.data,
  () => {
    // 当数据变化时，保留已选中且仍在数据中的行
    selectedRows.value = selectedRows.value.filter((selectedRow) =>
      props.data.some((row) => row[props.rowKey] === selectedRow[props.rowKey]),
    )

    // 延迟一下再重新计算表格高度，确保DOM已经更新
    nextTick(() => {
      setTimeout(() => {
        calculateTableHeight()
      }, 50)
    })
  },
)

// 监听 fixedHeaderMode 和 maxVisibleRows 变化
watch([() => props.fixedHeaderMode, () => props.maxVisibleRows], () => {
  // 当模式或行数变化时，重新计算表格高度
  nextTick(() => {
    setTimeout(() => {
      calculateTableHeight()
    }, 50)
  })
})

// 获取列的省略号配置
const getEllipsisConfig = (column: TableColumn) => {
  // 优先使用列配置，如果列未配置则使用全局配置
  return column.ellipsis !== undefined ? !!column.ellipsis : !!props.ellipsis
}

// 获取列的文本换行配置
const getWrapTextConfig = (column: TableColumn) => {
  // 优先使用列配置，如果列未配置则使用全局配置
  return column.wrapText !== undefined ? column.wrapText : props.wrapText
}

// 获取省略号显示的行数
const getEllipsisLines = (column: TableColumn) => {
  if (column.ellipsis === true || (column.ellipsis === undefined && props.ellipsis === true)) {
    return 1 // 布尔值true表示单行省略
  }

  if (typeof column.ellipsis === 'number') {
    return column.ellipsis // 数字表示指定行数
  }

  if (typeof props.ellipsis === 'number') {
    return props.ellipsis // 全局配置的行数
  }

  return 1 // 默认单行
}

// 添加获取表头样式的方法
const getHeaderStyle = (column: TableColumn) => {
  const style: Record<string, string> = {}

  // 设置表头最小高度
  const headerMinHeight = props.headerMinHeight
  if (headerMinHeight) {
    style.minHeight = ensureUnit(headerMinHeight)
  }

  // 设置表头字体大小
  const headerFontSize = column.headerFontSize || props.headerFontSize
  if (headerFontSize) {
    style.fontSize =
      typeof headerFontSize === 'number' ? ensureUnit(headerFontSize) : headerFontSize
  }

  // 设置表头字体粗细
  const headerFontWeight = column.headerFontWeight || props.headerFontWeight
  if (headerFontWeight) {
    style.fontWeight = headerFontWeight.toString()
  }

  // 设置表头文字颜色
  const headerColor = column.headerColor || props.headerColor
  if (headerColor) {
    style.color = headerColor
  }

  // 设置表头背景色（列级别）
  const columnHeaderBgColor = column.headerBgColor
  if (columnHeaderBgColor && columnHeaderBgColor !== 'transparent') {
    style.backgroundColor = columnHeaderBgColor
  }

  return style
}

// 添加获取单元格样式的方法
const getCellStyle = (column: TableColumn) => {
  const style: Record<string, string> = {}

  // 设置单元格最小高度
  style.minHeight = ensureUnit(props.cellMinHeight)

  // 设置单元格字体大小
  const cellFontSize = column.cellFontSize || props.cellFontSize
  if (cellFontSize) {
    style.fontSize = typeof cellFontSize === 'number' ? ensureUnit(cellFontSize) : cellFontSize
  }

  // 设置单元格字体粗细
  const cellFontWeight = column.cellFontWeight || props.cellFontWeight
  if (cellFontWeight) {
    style.fontWeight = cellFontWeight.toString()
  }

  // 设置单元格文字颜色
  const cellColor = column.cellColor || props.cellColor
  if (cellColor) {
    style.color = cellColor
  }

  // 设置单元格背景色
  const cellBgColor = column.cellBgColor
  if (cellBgColor) {
    style.backgroundColor = cellBgColor
  }

  return style
}

// 组件挂载后初始化
onMounted(() => {
  // 延迟一下再计算表格实际高度，确保DOM已经渲染完成
  setTimeout(() => {
    calculateTableHeight()
  }, 50)
})
</script>

<style lang="scss" scoped>
.sy-table {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  font-size: var(--table-font-size, 24rpx);
  background-color: var(--table-bg-color, transparent);

  // 表格外部容器
  .sy-table-outer-container {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  // 表格头部区域
  .sy-table-header-wrapper {
    z-index: 2;
    flex-shrink: 0;
    width: 100%;
    background-color: var(--table-header-bg-color, transparent);
    border-bottom: 1px solid var(--table-border-color, #e6e6e6);

    &.is-fixed {
      position: sticky;
      top: 0;
      z-index: 10;
    }
  }

  // 表头滚动区域
  .sy-table-header-scroll {
    box-sizing: border-box;
    width: 100%;
    overflow-x: auto;
    white-space: nowrap;
  }

  // 表格内容部分
  .sy-table-body {
    position: relative;
    box-sizing: border-box;
    flex: 1;
    width: 100%;
    height: 100%;
    overflow: auto;

    // 确保在 rows 模式下能正确显示滚动条
    &.rows-mode {
      overflow-y: auto !important;
    }
  }

  // 表格内部容器
  .sy-table-inner-container {
    box-sizing: border-box;
    min-width: 100%;
  }

  // 表格头部
  .sy-table-header {
    box-sizing: border-box;
    width: 100%;
  }

  // 表格头部行
  .sy-table-header__row {
    box-sizing: border-box;
    display: flex;
    width: 100%;
    border-bottom: none;
  }

  // 表格头部单元格
  .sy-table-header__cell {
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    justify-content: center; /* 垂直居中 */
    min-height: var(--table-cell-min-height, 50rpx); /* 使用变量控制最小高度 */
    padding: var(--table-cell-padding, 0 20rpx);
    overflow: hidden;
    font-weight: var(--table-header-font-weight, 600);
    color: var(--table-header-text-color, #333);
    text-overflow: ellipsis;
    white-space: nowrap;

    // 对齐方式
    &--align-left {
      text-align: left;
    }

    &--align-center {
      text-align: center;
    }

    &--align-right {
      text-align: right;
    }

    // 垂直对齐
    &--valign-top {
      vertical-align: top;
    }

    &--valign-middle {
      vertical-align: middle;
    }

    &--valign-bottom {
      vertical-align: bottom;
    }

    // 边框样式
    &--border {
      border-right: 1px solid var(--table-border-color, #e6e6e6);
    }

    // 可排序
    &--sortable {
      cursor: pointer;
    }

    // 可调整宽度
    &--resizable {
      position: relative;
    }

    // 弹性布局
    &--flex {
      flex: 1 1 0;
    }

    // 内容区域
    &__content {
      display: flex;
      align-items: center;
    }
  }

  // 表格选择列
  .sy-table-header__cell--selection {
    display: flex;
    flex: 0 0 80rpx;
    align-items: center;
    justify-content: center;
    width: 80rpx;
  }

  // 排序图标
  .sy-table-header__sort {
    display: flex;
    flex-direction: column;
    margin-left: 10rpx;
  }

  // 排序图标
  .sy-table-header__sort-icon {
    font-size: 24rpx;
    color: #bbb;

    &.is-active {
      color: var(--table-primary-color, #f33429);
    }

    &--up {
      margin-bottom: -8rpx;
    }

    &--down {
      margin-top: -8rpx;
    }
  }

  // 宽度调整手柄
  .sy-table-header__resize-handle {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    width: 20rpx;
    height: 100%;
    cursor: col-resize;
  }

  // 表格主体内部
  .sy-table-body-inner {
    width: 100%;
  }

  // 表格行
  .sy-table-row {
    box-sizing: border-box;
    display: flex;
    width: 100%;
    border-bottom: none;

    // 最后一行单元格没有底部边框
    &:last-child {
      .sy-table-cell {
        border-bottom-width: 0;
      }
    }
  }

  // 表格单元格
  .sy-table-cell {
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    justify-content: center; /* 垂直居中 */
    min-height: var(--table-cell-min-height, 50rpx);
    padding: var(--table-cell-padding, 0 20rpx);
    word-break: break-all;
    white-space: nowrap;
    border-width: 0;
    border-right-color: var(--table-border-color, #e6e6e6);
    border-right-style: solid;
    border-right-width: 0;
    border-bottom-color: var(--table-border-color, #e6e6e6);
    border-bottom-style: solid;
    border-bottom-width: 1px;

    // 对齐方式
    &--align-left {
      text-align: left;
    }

    &--align-center {
      text-align: center;
    }

    &--align-right {
      text-align: right;
    }

    // 垂直对齐
    &--valign-top {
      vertical-align: top;
    }

    &--valign-middle {
      vertical-align: middle;
    }

    &--valign-bottom {
      vertical-align: bottom;
    }

    // 边框样式
    &--border {
      border-right-width: 1px;
    }

    // 弹性布局
    &--flex {
      flex: 1 1 0;
    }

    // 单元格内容
    &__content {
      box-sizing: border-box;
      display: inline-block;
      align-self: center; /* 水平居中 */
      width: 100%;
      overflow: hidden;

      // 省略号
      &--ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      // 文本换行
      &--wrap {
        word-break: break-all;
        white-space: normal;
      }

      // 多行省略
      &--ellipsis-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        white-space: normal;
      }

      &--ellipsis-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        white-space: normal;
      }
    }
  }

  // 表格选择列
  .sy-table-cell--selection {
    display: flex;
    flex: 0 0 80rpx;
    align-items: center;
    justify-content: center;
    width: 80rpx;
  }

  // 空状态
  .sy-table-empty {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 60rpx 0;

    &__icon {
      margin-bottom: 20rpx;

      .empty-image {
        width: 220rpx;
        height: 220rpx;
      }
    }

    &__text {
      font-size: 24rpx;
      color: #999;
    }
  }

  // 加载状态
  .sy-table-loading {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.7);
  }

  // 边框样式
  &--border {
    border: 1px solid var(--table-border-color, #e6e6e6);
    border-right: none;
    .sy-table-body-inner {
      border-right: 0;
    }
  }

  // 斑马线样式
  &--stripe {
    .sy-table-row:nth-child(even) {
      background-color: var(--table-stripe-bg-color, #fafafa);
    }
  }

  // 固定表头和横向滚动同时存在时的边框修复
  &--border-fix {
    .sy-table-header-scroll,
    .sy-table-body {
      // 统一边框宽度以保证对齐
      border-right: 1px solid transparent;

      .sy-table-header__cell,
      .sy-table-cell {
        &:last-child {
          border-right-width: 0;
        }
      }
    }

    // 修复边框渲染问题
    .sy-table-header-wrapper {
      box-sizing: border-box;
      border-bottom: 1px solid var(--table-border-color, #e6e6e6);

      .sy-table-header__row {
        border-bottom: none;
      }
    }

    // 固定在视口顶部时保持边框一致
    &.sy-table--fixed-header {
      .sy-table-header-wrapper {
        z-index: 10;
      }
    }

    // 确保头部和主体边框对齐
    .sy-table-header-scroll {
      box-sizing: border-box;
      margin-right: 0;
      overflow-y: hidden; // 防止出现垂直滚动条
    }

    // 处理滚动条宽度导致的边框错位
    .sy-table-body {
      scrollbar-width: thin;
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #c0c0c0;
        border-radius: 4px;
      }
      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
      }
    }
  }

  // 自动宽度样式 - 处理没有设置width的列平分整个视图宽度
  &--auto-width {
    .sy-table-inner-container {
      width: 100%;
      table-layout: fixed;
    }

    .sy-table-cell:not([style*='width:']),
    .sy-table-header__cell:not([style*='width:']) {
      box-sizing: border-box;
      flex: 1 1 0;
      width: 0;
      min-width: 0;
    }
  }
}
</style>
