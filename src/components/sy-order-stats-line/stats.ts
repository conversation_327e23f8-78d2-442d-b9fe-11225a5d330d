import type { PropType } from 'vue'

/**
 * 统计数据接口
 */
export interface OrderStats {
  totalCount: number
  processingCount: number
  cancelledCount: number
}

/**
 * 订单统计信息行组件属性定义
 */
export const componentProps = {
  /** 统计数据对象 */
  stats: {
    type: Object as PropType<OrderStats>,
    default: () => ({
      totalCount: 0,
      processingCount: 0,
      cancelledCount: 0,
    }),
  },
  /** 总订单数 */
  totalCount: {
    type: Number,
    default: 0,
  },
  /** 进行中订单数 */
  processingCount: {
    type: Number,
    default: 0,
  },
  /** 已取消订单数 */
  cancelledCount: {
    type: Number,
    default: 0,
  },
  /** 是否展开 */
  isExpanded: {
    type: Boolean,
    default: false,
  },
  /** 是否显示视图切换按钮 */
  showViewToggle: {
    type: Boolean,
    default: false,
  },
  /** 是否为小卡片视图 */
  isMiniCard: {
    type: Boolean,
    default: true,
  },
}
