<template>
  <view>
    <view class="sy-order-stats-line">
      <view class="sy-order-stats-line-left">
        <text class="sy-order-stats-line-text">
          共{{ stats?.totalCount || totalCount }}单
          <!-- (进行中{{
            stats?.processingCount || processingCount
          }}单, 已取消{{ stats?.cancelledCount || cancelledCount }}单) -->
        </text>
        <view class="sy-order-stats-line-help" @click="showStatsPopup = true">
          <wd-icon name="help-circle" color="#f33429" size="20rpx"></wd-icon>
        </view>
      </view>
      <view class="sy-order-stats-line-right">
        <view class="sy-order-stats-line-action" @click="onExpandClick">
          <wd-icon name="menu-unfold" color="#999999" size="24rpx"></wd-icon>
          <text class="sy-order-stats-line-expand">{{ isExpanded ? '收起' : '展开' }}</text>
        </view>
        <view class="sy-order-stats-line-divider" v-if="showViewToggle"></view>
        <view class="sy-order-stats-line-action" v-if="showViewToggle" @click="onToggleViewClick">
          <wd-icon name="swap" color="#999999" size="24rpx"></wd-icon>
          <text class="sy-order-stats-line-toggle">{{ isMiniCard ? '切换' : '切换' }}</text>
        </view>
      </view>
    </view>

    <!-- 订单统计说明弹窗 -->
    <sy-order-stats-popup
      v-model="showStatsPopup"
      :total-count="stats?.totalCount || totalCount"
      :processing-count="stats?.processingCount || processingCount"
      :cancelled-count="stats?.cancelledCount || cancelledCount"
    />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { componentProps } from './stats'
import SyOrderStatsPopup from '../sy-order-stats-popup'

// 定义组件选项
defineOptions({
  name: 'SyOrderStatsLine',
})

// 定义组件属性
const props = defineProps(componentProps)

// 定义事件
const emit = defineEmits(['expand', 'toggle-view'])

// 弹窗显示状态
const showStatsPopup = ref(false)

// 展开/收起点击事件
const onExpandClick = () => {
  emit('expand', !props.isExpanded)
}

// 切换视图点击事件
const onToggleViewClick = () => {
  emit('toggle-view', !props.isMiniCard)
}
</script>

<style lang="scss" scoped>
.sy-order-stats-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background-color: #f1f1f1;

  &-left {
    display: flex;
    gap: 8rpx;
    align-items: center;
  }

  &-text {
    font-size: 22rpx;
    color: #666666;
  }

  &-help {
    display: flex;
    align-items: center;
    padding: 4rpx;
    cursor: pointer;
  }

  &-right {
    display: flex;
    gap: 16rpx;
    align-items: center;

    &--full {
      justify-content: flex-end;
      width: 100%;
    }
  }

  &-action {
    display: flex;
    gap: 8rpx;
    align-items: center;
    cursor: pointer;
  }

  &-expand,
  &-toggle {
    font-size: 22rpx;
    color: #666666;
  }

  &-divider {
    width: 1px;
    height: 24rpx;
    margin: 0 4rpx;
    background-color: #dddddd;
  }
}
</style>
