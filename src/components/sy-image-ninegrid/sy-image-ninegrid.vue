<template>
  <view class="image-grid">
    <!-- 九宫格图片项 -->
    <view v-for="(item, index) in imageList" :key="index" class="image-item">
      <!-- 图片容器 -->
      <view class="image-wrapper">
        <image
          v-if="!(imageList.length > 9 && index === 8)"
          class="image"
          mode="aspectFill"
          :src="item"
          @click="handlePrePick ? handlePrePick(index) : toPreLookPic(index)"
        />
        <!-- 超过9张时的「+N」图标 -->
        <!-- <view v-if="imageList.length > 9 && index === 8" class="more-icon">
          <text>+{{ imageList.length - 9 }}</text>
        </view> -->
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 定义组件选项
defineOptions({
  name: 'SyImageNineGrid',
})
// 组件属性
const props = defineProps({
  handlePrePick: {
    // 点击图片事件处理函数
    type: Function,
    default: (index: number) => {},
  },
  imageList: {
    // 图片列表（数组，元素包含url）
    type: Array<string>,
    default: () => [],
  },
  gap: {
    // 图片间距（rpx）
    type: Number,
    default: 20,
  },
  maxSize: {
    // 单图最大显示尺寸（rpx）
    type: Number,
    default: 160,
  },
})

const toPreLookPic = (index: number) => {
  uni.previewImage({
    urls: props.imageList,
    current: props.imageList[index], // 当前显示图片的链接
  })
}
</script>

<style scoped lang="scss" src="./index.scss"></style>
