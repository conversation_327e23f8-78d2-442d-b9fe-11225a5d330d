.image-grid {
  display: flex;
  flex-wrap: wrap;
  margin: calc(-1 * var(--gap) / 2); // 负间距抵消内边距
  .image-item {
    position: relative;
    width: calc((100% - 2 * var(--gap)) / 3 - var(--gap)); // 三列布局计算
    // height: calc((100% - 2 * var(--gap)) / 3 - var(--gap));
    margin: calc(var(--gap) / 2);
    overflow: hidden;
    border-radius: 8rpx;

    &:nth-child(3n + 1) {
      margin-left: calc(var(--gap) / 2);
    } // 首列特殊处理
    &:nth-child(3n) {
      margin-right: calc(var(--gap) / 2);
    } // 尾列特殊处理

    .image-wrapper {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;

      .more-icon {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        font-size: 32rpx;
        color: #1c1c1c;
        background: rgba(145, 144, 144, 0.8);

        text {
          font-weight: bold;
        }
      }
    }
  }
  .image {
    width: 202rpx;
    height: 202rpx;
    margin-right: 10rpx;
    margin-bottom: 8rpx;
    border-radius: 24rpx;
  }
}
