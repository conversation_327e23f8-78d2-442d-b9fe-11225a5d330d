<template>
  <wd-popup v-model="showPopup" custom-style="border-radius:24rpx;">
    <view class="rating-rule-popup">
      <view class="rating-rule-title">提示</view>
      <view class="rating-rule-content">评分规则请至平台APP查看，路径：门店原寨评价-计算详情</view>
      <view class="rating-rule-actions">
        <wd-button type="primary" block @click="onConfirm" class="confirm-btn">确定</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'confirm'])

// 计算属性：控制弹窗显示
const showPopup = ref(false)

// 监听props变化
watch(
  () => props.modelValue,
  (val) => {
    showPopup.value = val
  },
  { immediate: true },
)

// 监听内部状态变化
watch(
  () => showPopup.value,
  (val) => {
    emit('update:modelValue', val)
  },
)

// 点击确认按钮
const onConfirm = () => {
  showPopup.value = false
  emit('confirm')
}

// 导出需要的属性和方法
defineExpose({
  showPopup,
})

// 添加默认导出
defineOptions({
  name: 'SyStoreRatingRule',
})
</script>

<style lang="scss">
.rating-rule-popup {
  box-sizing: border-box;
  width: 540rpx;
  padding: 48rpx 32rpx;
  background: #ffffff;
  border-radius: 24rpx;

  .rating-rule-title {
    margin-bottom: 32rpx;
    font-size: 32rpx;
    font-weight: 600;
    line-height: 44rpx;
    color: #222222;
    text-align: center;
  }

  .rating-rule-content {
    margin-bottom: 48rpx;
    font-size: 28rpx;
    line-height: 40rpx;
    color: #666666;
    text-align: center;
  }

  .rating-rule-actions {
    .confirm-btn {
      height: 88rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #ffffff;
      background: #f33429;
      border: none;
      border-radius: 44rpx;

      &:active {
        opacity: 0.9;
      }
    }
  }
}
</style>
