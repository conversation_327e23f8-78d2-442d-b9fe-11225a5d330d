/**
 * 订单类型选择器组件类型定义
 */

import type { PropType } from 'vue'

/**
 * 订单类型选项接口
 */
export interface OrderTypeItem {
  /** 订单类型代码 */
  code: string
  /** 订单类型名称 */
  name: string
  /** 订单类型图标 */
  icon?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 其他可能的属性 */
  [key: string]: any
}

/**
 * 订单类型选择器组件属性
 */
export const componentProps = {
  /** 订单类型选项列表 */
  orderTypes: {
    type: Array as PropType<OrderTypeItem[]>,
    default: () => [],
    required: true,
  },
  /** 当前选中的订单类型代码数组 */
  modelValue: {
    type: Array as PropType<string[]>,
    default: () => [],
    required: true,
  },
  /** 是否支持多选 */
  multiple: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
  /** 最大列数（用于网格布局） */
  maxColumns: {
    type: Number as PropType<number>,
    default: 1,
  },
  /** 是否显示图标 */
  showIcon: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
  /** 顶部偏移量，用于调整弹出位置 */
  topOffset: {
    type: Number as PropType<number>,
    default: 0,
  },
  /** 左侧偏移量，用于与左边文字对齐 */
  leftOffset: {
    type: Number as PropType<number>,
    default: 300,
  },
  /** "全部"选项的值 */
  allOptionValue: {
    type: String as PropType<string>,
    default: '',
  },
  /** "全部"选项的文本 */
  allOptionText: {
    type: String as PropType<string>,
    default: '全部订单',
  },
  /** 是否显示选择器 */
  visible: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
  /** 是否显示"全部"选项 */
  showAllOption: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
}

/**
 * 订单类型选择器组件事件
 */
export interface OrderTypeSelectorEmits {
  /** 更新选中值 */
  (e: 'update:modelValue', value: string[]): void
  /** 选中值变化 */
  (e: 'change', selectedCodes: string[], selectedItems: OrderTypeItem[]): void
  /** 关闭选择器 */
  (e: 'close'): void
  /** 确认选择 */
  (e: 'confirm', selectedCodes: string[], selectedItems: OrderTypeItem[]): void
  /** 取消选择 */
  (e: 'cancel'): void
  /** 更新可见状态 */
  (e: 'update:visible', visible: boolean): void
}
