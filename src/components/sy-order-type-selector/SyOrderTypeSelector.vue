<template>
  <view
    v-if="popupVisible"
    :style="{
      top: props.topOffset + 'rpx',
      left: props.leftOffset + 'rpx',
      position: 'absolute',
    }"
    class="sy-order-type-selector"
  >
    <!-- 添加遮罩层 -->
    <view class="order-type-selector-overlay" @click="handleOverlayClick"></view>

    <view class="order-type-selector-popup">
      <!-- 弹窗内容 -->
      <view class="order-type-selector-content">
        <!-- 无数据提示 -->
        <view
          v-if="!props.orderTypes || props.orderTypes.length === 0"
          class="order-type-selector-empty"
        >
          <text class="order-type-selector-empty-text">暂无订单类型数据</text>
        </view>

        <wd-checkbox-group
          v-else
          shape="square"
          checked-color="#F33429"
          v-model="tempSelectedValues"
          @change="handleCheckboxChange"
        >
          <!-- 全部选项 -->
          <view v-if="props.showAllOption" class="order-type-selector-item">
            <wd-checkbox
              :model-value="isAllChecked ? props.allOptionValue : ''"
              @click="toggleAllOrderTypes"
            >
              {{ props.allOptionText }}
            </wd-checkbox>
          </view>

          <!-- 订单类型选项列表 -->
          <view v-for="item in props.orderTypes" :key="item.code" class="order-type-selector-item">
            <wd-checkbox :model-value="item.code" :disabled="item.disabled">
              <view class="order-type-selector-item-content">
                <view class="order-type-selector-item-main">
                  <view v-if="props.showIcon && item.icon" class="order-type-selector-item-icon">
                    <image :src="item.icon" class="order-type-icon" mode="aspectFit" />
                  </view>
                  <text class="order-type-selector-item-text">{{ item.name }}</text>
                </view>
              </view>
            </wd-checkbox>
          </view>
        </wd-checkbox-group>
      </view>

      <!-- 底部按钮区域 -->
      <view class="order-type-selector-footer">
        <view class="order-type-selector-btn-group">
          <view
            class="order-type-selector-btn order-type-selector-btn-cancel color-#333333"
            @click="handleCancel"
          >
            取消
          </view>
          <wd-divider vertical style="align-self: center" />
          <view
            class="order-type-selector-btn order-type-selector-btn-confirm color-#F33429"
            @click="handleConfirm"
          >
            确认
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue'
import { componentProps, type OrderTypeSelectorEmits, type OrderTypeItem } from './types'

// 定义组件名称
defineOptions({ name: 'SyOrderTypeSelector' })

// 定义组件属性
const props = defineProps(componentProps)

// 定义组件事件
const emit = defineEmits<OrderTypeSelectorEmits>()

// 弹窗显示状态
const popupVisible = ref(false)

// 当前实际选中值数组（最终提交的值）
const selectedValues = ref<string[]>([])

// 临时选中值数组（用于在弹窗中操作）
const tempSelectedValues = ref<string[]>([])

// 获取所有订单类型代码数组（不包含全选值）
const allOrderTypeCodes = computed((): string[] => {
  return props.orderTypes
    .map((option) => option.code)
    .filter((code) => code !== props.allOptionValue)
})

// 计算全选状态
const isAllChecked = computed((): boolean => {
  // 当没有订单类型时，全选状态为false
  if (allOrderTypeCodes.value.length === 0) return false

  // 检查是否所有订单类型都被选中
  return allOrderTypeCodes.value.every((code) => tempSelectedValues.value.includes(code))
})

// 组件挂载时
onMounted(() => {
  console.log('SyOrderTypeSelector mounted, props:', props)

  // 初始化时检查是否需要同步全选状态
  syncAllOptionState()
})

// 监听外部visible变化
watch(
  () => props.visible,
  (val) => {
    popupVisible.value = val
    if (val) {
      // 打开弹窗时，初始化临时选中值为当前选中值
      tempSelectedValues.value = Array.isArray(selectedValues.value)
        ? [...selectedValues.value]
        : []

      // 打开弹窗时同步全选状态，确保全选状态正确
      setTimeout(() => {
        syncAllOptionState()
      }, 0)
    }
  },
  { immediate: true },
)

// 监听内部弹窗状态变化
watch(
  () => popupVisible.value,
  (newVal) => {
    console.log('SyOrderTypeSelector popupVisible changed:', newVal)
    emit('update:visible', newVal)
  },
)

// 监听外部modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    console.log('SyOrderTypeSelector modelValue changed:', newVal)
    // 确保是新数组，防止引用问题
    selectedValues.value = Array.isArray(newVal) ? [...newVal] : []

    // 外部value变化时，同步临时选中值
    tempSelectedValues.value = Array.isArray(newVal) ? [...newVal] : []

    // 外部value变化时，同步全选状态
    syncAllOptionState()
  },
  { immediate: true },
)

// 同步全选选项状态
const syncAllOptionState = (): void => {
  // 同步临时选中值的全选状态
  tempSelectedValues.value = syncAllSelectionState(
    tempSelectedValues.value,
    allOrderTypeCodes.value,
    props.allOptionValue,
  )
}

// 同步全选状态
function syncAllSelectionState(
  selectedValues: string[],
  allTypeCodes: string[],
  allOptionValue: string,
): string[] {
  // 创建新数组避免引用问题
  const newSelectedValues = [...selectedValues]

  // 检查是否所有订单类型都被选中
  const isAllSelected =
    allTypeCodes.length > 0 && allTypeCodes.every((code) => newSelectedValues.includes(code))

  // 检查是否包含全选选项
  const hasAllOption = newSelectedValues.includes(allOptionValue)

  // 同步全选状态
  if (isAllSelected && !hasAllOption) {
    // 所有选项都被选中，但没有全选标记，添加全选标记
    newSelectedValues.push(allOptionValue)
  } else if (!isAllSelected && hasAllOption) {
    // 不是所有选项都被选中，但有全选标记，移除全选标记
    return newSelectedValues.filter((code) => code !== allOptionValue)
  }

  return newSelectedValues
}

// 切换全选状态
const toggleAllOrderTypes = (): void => {
  let newValues: string[] = []

  if (!isAllChecked.value) {
    // 当前非全选状态，变为全选状态
    newValues = [props.allOptionValue, ...allOrderTypeCodes.value]
  } else {
    // 当前全选状态，变为非全选状态
    newValues = []
  }

  // 更新临时选中值
  tempSelectedValues.value = newValues
}

// 获取有效的选中值数组
function getValidValues(value: unknown[]): string[] {
  return (value || [])
    .map((v) => String(v))
    .filter((v) => v !== 'false' && v !== 'undefined' && v !== 'null')
}

// 获取选中项的数据
const getSelectedItems = (values: string[]): OrderTypeItem[] => {
  const selectedItems: OrderTypeItem[] = []

  values.forEach((code) => {
    // 跳过全选选项的值
    if (code === props.allOptionValue) return

    const option = props.orderTypes.find((item) => item.code === code)
    if (option) {
      selectedItems.push(option)
    }
  })

  return selectedItems
}

// 处理复选框组变化
const handleCheckboxChange = ({ value }: { value: string[] }) => {
  console.log('SyOrderTypeSelector handleCheckboxChange:', value)

  // 获取有效值
  const validValues = getValidValues(value)

  // 检查是否全选状态被改变
  const wasAllSelected = tempSelectedValues.value.includes(props.allOptionValue)
  const isNowAllSelected = validValues.includes(props.allOptionValue)

  // 如果全选状态发生变化
  if (isNowAllSelected !== wasAllSelected) {
    // 用户点击了全选选项
    toggleAllOrderTypes()
    return
  }

  // 处理常规选项选中/取消选中
  const newValues = [...validValues]

  // 同步全选状态
  tempSelectedValues.value = syncAllSelectionState(
    newValues,
    allOrderTypeCodes.value,
    props.allOptionValue,
  )
}

// 处理确认按钮点击
const handleConfirm = () => {
  console.log('SyOrderTypeSelector handleConfirm, 选中值:', tempSelectedValues.value)

  // 先同步一次全选状态，确保全选标记正确
  syncAllOptionState()

  // 将临时选中值应用到实际选中值（创建新数组避免引用问题）
  selectedValues.value = [...tempSelectedValues.value]

  // 获取选中的订单类型项
  const selectedItems = getSelectedItems(selectedValues.value)

  // 触发更新事件
  emit('update:modelValue', selectedValues.value)
  emit('change', selectedValues.value, selectedItems)
  emit('confirm', selectedValues.value, selectedItems)

  // 关闭弹窗
  // popupVisible.value = false
  // emit('close')
}

// 处理取消按钮点击
const handleCancel = () => {
  console.log('SyOrderTypeSelector handleCancel')

  // 恢复临时选中值为实际选中值
  tempSelectedValues.value = [...selectedValues.value]

  // 触发取消事件
  emit('cancel')

  // 关闭弹窗
  popupVisible.value = false
  emit('close')
}

// 处理遮罩层点击
const handleOverlayClick = () => {
  handleCancel()
}

// 暴露方法
defineExpose({
  handleCheckboxChange,
  handleCancel,
  handleConfirm,
  toggleAllOrderTypes,
  syncAllOptionState,
})
</script>

<style lang="scss" scoped>
// 颜色变量定义
$primary-color: #f33429;
$text-color: #333333;
$text-light-color: #999999;
$border-color: #edecee;
$border-light-color: #f5f5f5;
$bg-color: #ffffff;
$shadow-color: rgba(0, 0, 0, 0.08);

.sy-order-type-selector {
  z-index: 999;
  width: 280rpx;

  .order-type-selector-overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9998;
    background-color: transparent;
  }

  // 弹窗样式
  .order-type-selector-popup {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
    width: 29vw;
    width: 20vh;
    height: max-content;
    // display: flex;
    // flex-direction: column;
    // max-height: 90vh;

    overflow: hidden;
    background-color: $bg-color;
    box-shadow: 0 4rpx 12rpx $shadow-color;
  }

  // 弹窗内容
  .order-type-selector-content {
    flex: 1;
    max-height: 60vh;
    overflow-y: auto;
  }

  // 订单类型选项
  .order-type-selector-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 80rpx;
    padding: 0rpx 24rpx;
    border-bottom: 1px solid $border-light-color;

    :deep() {
      .wd-checkbox {
        flex: 1;
        margin-bottom: 0rpx;

        .wd-checkbox__label {
          flex: 1;
          overflow: hidden;
          color: $text-color;
        }

        .wd-checkbox__shape {
          // 确保勾选框显示
          border-radius: 0;
          &.is-checked {
            background-color: $primary-color;
            border-color: $primary-color;
          }
        }
      }
    }
  }

  .order-type-selector-item-content {
    display: flex;
    flex: 1;
    flex-direction: column;
  }

  .order-type-selector-item-main {
    display: flex;
    align-items: center;
  }

  .order-type-selector-item-text {
    font-size: 28rpx;
    line-height: 1.2;
    color: $text-color;
  }

  .order-type-selector-item-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 16rpx;
    overflow: hidden;
    border-radius: 50%;

    .order-type-icon {
      width: 100%;
      height: 100%;
    }
  }

  // 空状态
  .order-type-selector-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200rpx;

    &-text {
      font-size: 28rpx;
      color: $text-light-color;
    }
  }

  // 底部按钮区域
  .order-type-selector-footer {
    border-top: 1px solid $border-color;
  }

  .order-type-selector-btn-group {
    display: flex;
    height: 88rpx;
  }

  .order-type-selector-btn {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 28rpx;

    &-cancel {
      font-size: 26rpx;
      font-weight: normal;
      line-height: normal;
      color: #333333;
      text-align: center;
      letter-spacing: normal;
    }
    &-confirm {
      font-size: 26rpx;
      font-weight: 500;
      line-height: normal;
      color: #f33429;
      text-align: center;
      letter-spacing: normal;
    }
  }
}
</style>
