import type { PropType } from 'vue'

/**
 * 单条退款记录接口
 */
export interface RefundRecord {
  /** 售后商品数据（可选） */
  afterSalesItems?: Array<{
    /** 商品名称 */
    name?: string
    /** 商品数量 */
    quantity?: number
    /** 商品价格 */
    price?: number
    /** 商品图片 */
    image?: string
    [key: string]: any
  }>
  /** 创建时间 */
  createTime?: string
  /** 理由 */
  reason?: string
  /** 退款状态码 */
  refundStatus?: string
  /** 退款状态名称 */
  refundStatusName?: string
  /** 标题 */
  title?: string
  /** 交易号 */
  tradeNo?: string
  /** 链接（可选） */
  url?: string
  /** 申诉记录类型 */
  type?: string
}

/**
 * 订单数据接口
 */
export interface OrderData {
  /** 订单ID */
  id: string
  /** 订单号 */
  orderNo?: string
  /** 渠道单号 */
  channelOrderNo?: string
  /** 第三方流水号 */
  takeNo?: string
  /** 配送时间 */
  deliveryTime?: string
  /** 下单时间 */
  placeTime?: string
  /** 订单状态 */
  status?: string
  /** 订单类型 */
  type?: string
  /** 订单类型 */
  bizType?: string
  /** 第三方渠道/外卖平台 */
  channel?: string
  /** 商家确认时间 */
  merchantConfirmTime?: string
  /** 取餐号 */
  pickupNumber?: string
  /** 应用名称，对应后端appName字段 */
  appName?: string
  saleChannel: string
  /** 商家信息 */
  merchant?: {
    /** 商家头像 */
    avatar?: string
    /** 商家名称 */
    name?: string
    /** 商家别名 */
    alias?: string
    /** 商家ID */
    id?: string
  }
  /** 顾客信息 */
  customer?: {
    /** 顾客姓名 */
    name?: string
    /** 手机号 */
    phone?: string
    /** 完整手机号 */
    fullPhone?: string
  }
  /** 地址信息 */
  address?: {
    /** 地址详情 */
    detail?: string
    /** 距离 */
    distance?: string
  }
  /** 警告信息 */
  warning?: {
    /** 警告文本 */
    text: string
    /** 倒计时（秒） */
    countdown: number
  }
  /** 订单状态信息 */
  orderStatus?: {
    /** 状态文本 */
    text?: string
    /** 状态时间 */
    time?: string
    /** 状态描述 */
    description?: string
    /** 状态颜色 */
    color?: string
    /** 已用时间 */
    usedTime?: string
    /** 订单状态码 */
    code?: string
    /** 订单交易状态 */
    tradeStatus?: string
    /** 是否显示接单按钮 */
    showAcceptButton?: boolean
    /** 是否显示出餐完成按钮 */
    showFinishButton?: boolean
    /** 是否显示上报出餐按钮 */
    showReportButton?: boolean
    /** 出餐用时（上报出餐完成时长） */
    mealReadyDuration?: string
  }
  /** 配送信息 */
  delivery?: {
    /** 配送方式 */
    method?: string
    /** 配送时间 */
    time?: string
    /** 配送平台 */
    platform?: string
    /** 是否需要客户自提 */
    needCustomerDelivery?: boolean
    /** 配送费 */
    fee?: number
  }
  /** 商品信息 */
  goods?: {
    /** 商品数量 */
    count?: number
    /** 商品摘要 */
    summary?: string
    /** 商品列表 */
    items?: Array<{
      /** 商品名称 */
      name: string
      /** 商品规格 */
      skuName?: string
      /** 商品价格 */
      price: number
      /** 商品数量 */
      count: number
      /** 是否为赠品 */
      isGift?: boolean
      /** 加价 */
      addPrice?: number
      /** 套餐子项 */
      combos?: Array<{
        name: string
        skuName?: string
        price: number
        count: number
        addPrice?: number
      }>
    }>
  }
  /** 费用信息 */
  fees?: {
    /** 商家收入 */
    merchantIncome?: number
    /** 配送费 */
    deliveryFee?: number
    /** 包装费/餐盒费 */
    packagingFee?: number
    /** 总金额 */
    totalAmount?: number
    /** 实付金额 */
    payAmount?: number
    /** 优惠金额 */
    discountAmount?: number
    /** 佣金 */
    commission?: number
    /** 服务费 */
    serviceFee?: number
    /** 补贴 */
    subsidy?: number
  }
  /** 时间信息 */
  times?: {
    /** 创建时间 */
    createTime?: string
    /** 接单时间 */
    acceptTime?: string
    /** 配送时间 */
    deliveryTime?: string
    /** 下单时间 */
    placeTime?: string
  }
  /** 用户备注 */
  userNote?: string
  /** 买家备注 */
  buyerRemark?: string
  /** 原始API数据 */
  apiData?: any
  /** 是否为预约单 */
  isBook?: number
  /** 渠道颜色 */
  channelColor?: {
    /** 背景色 */
    bg?: string
    /** 文字颜色 */
    text?: string
    /** 渐变背景 */
    gradient?: string
    /** 按钮背景色 */
    btnBg?: string
    /** 按钮文字颜色 */
    btnText?: string
    /** 水印图标颜色 */
    watermarkColor?: string
    /** 水印图标类名 */
    watermarkIconClass?: string
  }
  /** 配送记录信息 */
  deliveryRecord?: {
    /** 骑手名称 */
    courierName?: string
    /** 骑手电话 */
    courierMobile?: string
    /** 骑手配送费 */
    deliveryFee?: string | number
    /** 物流单号 */
    deliveryNo?: string
    /** 第三方物流单号 */
    thirdDeliveryNo?: string
    /** 到店时间 */
    arriveStoreTime?: string
    /** 配送方式 */
    channelName?: string
    /** 配送状态名称 */
    statusName?: string
    /** 配送状态图标 */
    statusIcon?: string
    /** 接单时间 */
    acceptTime?: string
    /** 配送日志详情列表 */
    deliveryLogDetailList?: Array<{
      /** 骑手名称 */
      name: string
      /** 配送状态 */
      status: string
      /** 状态时间 */
      time: string
      /** 骑手手机号 */
      mobile?: string
    }>
  }
  /** 顾客地址 */
  deliveryAddress?: string
  /** 订单跟踪信息 */
  trackSteps?: Array<{
    /** 步骤标题 */
    title: string
    /** 步骤时间 */
    time: string
    /** 步骤状态 */
    status?: string
    /** 描述信息 */
    description?: string
  }>
  /** 租户ID */
  tenantId?: string
  /** 实例ID */
  instanceId?: string
  /** 售后信息 - 更新为数组结构 */
  afterSale?: RefundRecord[]
  /** 兼容旧版本的售后信息（单个对象） */
  legacyAfterSale?: {
    /** 售后编号 */
    refundNo?: string
    /** 售后类型 */
    refundType?: 'REFUND_ONLY' | 'RETURN_BASE'
    /** 售后类型名称 */
    refundTypeName?: string
    /** 退款状态 */
    refundStatus?: string
    /** 退款状态名称 */
    refundStatusName?: string
    /** 申请原因 */
    refundReason?: string
    /** 申请时间 */
    applyTime?: string
    /** 退款金额 */
    refundAmount?: number
    /** 申请图片 */
    refundImages?: string[]
    /** 处理流程阶段 */
    processStage?: string
    /** 是否显示操作按钮 */
    showActions?: boolean
  }
}

/**
 * 订单信息接口
 */
export interface OrderInfo {
  id: string
  saleChannel: string // 销售渠道
  saleChannelName: string // 销售渠道名称
  expanded?: boolean // 是否展开
  channelColor?: {
    bg?: string
    btnBg?: string
    btnText?: string
    gradient?: string
    watermarkColor?: string
  }
  merchant?: {
    id?: string
    name?: string
    alias?: string
    avatar?: string
  }
  customer?: {
    name?: string
    phone?: string
    fullPhone?: string // 添加完整电话号码字段
  }
  items?: Array<{
    name: string
    skuName?: string
    price: number
    count: number
    addPrice?: number // 加价
  }>
  // ... 其他字段
  [key: string]: any
}

/**
 * 组件属性定义
 */
export const componentProps = {
  /** 订单数据 */
  orderData: {
    type: Object as PropType<OrderData>,
    required: true,
  },
  /** 是否显示展开按钮 */
  showExpandButton: {
    type: Boolean,
    default: true,
  },
  /** 是否已展开 */
  expanded: {
    type: Boolean,
    default: false,
  },
  /** 是否是售后模式 */
  isAfterSaleMode: {
    type: Boolean,
    default: false,
  },
  /** 组件ID，用于唯一标识 */
  id: {
    type: String,
    default: '',
  },
}

/**
 * 组件事件类型
 */
export interface OrderCardEmits {
  /** 发起申诉事件 */
  (e: 'handleToAppeal', index: number): void
}

/**
 * 订单跟踪信息
 */
export interface OrderTrackStep {
  /** 步骤标题 */
  title: string
  /** 步骤时间 */
  time: string
  /** 步骤状态 */
  status?: 'finished' | 'process' | 'error'
  /** 描述信息 */
  description?: string
}
