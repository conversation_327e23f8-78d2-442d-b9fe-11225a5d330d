<template>
  <view class="sy-order-card-container bg-white rounded-16rpx mx-24rpx mb-24rpx overflow-hidden">
    <!-- 卡片上半部分渐变背景区域 -->
    <view
      class="sy-order-card-gradient-bg absolute top-0 left-0 right-0 h-378rpx"
      :style="{
        opacity: 0.2,
        background: orderData?.channelColor?.gradient || '#fff',
      }"
    ></view>

    <!-- 水印区域 -->
    <view class="sy-order-card-watermark absolute top-80rpx right-0 z-1">
      <view
        :class="['iconfont', orderData?.channelColor?.watermarkIconClass || 'icon-xiafancai']"
        :style="{
          fontSize: '210rpx',
          color: orderData?.channelColor?.watermarkColor || '#F0F0F0',
          opacity: 0.5,
        }"
      ></view>
    </view>

    <!-- 订单头部信息 -->
    <view
      class="sy-order-card-header flex items-center justify-between px-24rpx pt-24rpx relative z-1"
    >
      <view class="flex items-center">
        <image
          class="sy-order-card-channel-icon w-48rpx h-48rpx mr-16rpx"
          :src="orderData?.merchant?.avatar || ''"
          mode="aspectFill"
        />
        <text class="text-30rpx font-medium max-w-480rpx">
          {{ orderData?.merchant?.alias || orderData?.merchant?.name || '' }}
        </text>
      </view>
      <!-- 渠道类型标签 -->
      <view class="sy-order-card-type-tag px-12rpx py-4rpx rounded-4rpx">
        <text
          class="text-24rpx font-medium"
          :style="{
            color: '#F33429',
          }"
        >
          {{ orderData?.type || '' }}
        </text>
      </view>
    </view>

    <!-- 虚线分割线 -->
    <view class="sy-order-card-divider mx-24rpx mt-16rpx relative z-1"></view>

    <!-- 第二行信息：租户标签 -->
    <view class="flex items-center px-24rpx mt-16rpx relative z-1">
      <!-- 添加租户名称标签 -->
      <view
        class="sy-order-card-tenant-tag rounded-4rpx h-32rpx flex items-center justify-center pl-8rpx pr-8rpx"
        :style="{
          background:
            orderData?.channelColor?.bg || getChannelButtonStyle(orderData?.saleChannel).bg,
        }"
      >
        <image
          src="/static/images/icons/tent_tag.svg"
          class="w-24rpx h-24rpx mr-4rpx"
          mode="aspectFit"
          :style="{
            filter: `${orderData?.channelColor?.watermarkColor ? `drop-shadow(0 0 0 ${orderData.channelColor.watermarkColor})` : ''}`,
          }"
        />
        <text class="text-20rpx text-white font-medium">
          {{ orderData?.appName || '外卖' }}
        </text>
      </view>
    </view>

    <!-- 第三行信息：订单流水号、取餐号、送达时间、第三方流水号和状态 -->
    <view class="flex items-baseline justify-between px-24rpx mt-16rpx relative z-1">
      <view class="flex items-baseline flex-1" v-if="orderData?.bizType != '20'">
        <!-- 取餐号 -->
        <view class="mr-16rpx flex items-baseline">
          <text class="sy-order-card-serial-prefix text-30rpx font-semibold text-#222222 mr-4rpx">
            #
          </text>
          <text class="sy-order-card-serial-number text-64rpx font-semibold text-#222222">
            {{ orderData?.takeNo || '' }}
          </text>
          <view class="sy-order-card-type-tag-yu rounded-6rpx" v-if="orderData?.isBook == 2">
            <text class="text-22rpx font-medium">预</text>
          </view>
        </view>
        <view v-if="orderData?.pickupNumber" class="mr-16rpx">
          <text class="text-26rpx opacity-80">取餐号：</text>
          <text class="text-30rpx font-medium">{{ orderData?.pickupNumber }}</text>
        </view>
        <text class="text-22rpx">
          {{ orderData?.deliveryTime }}
          <text>前送达</text>
        </text>
      </view>
      <view class="items-baseline flex-1" v-if="orderData?.bizType == '20'">
        <!-- 取餐号 -->
        <view class="mr-16rpx">
          <text class="text-34rpx opacity-80">取餐码</text>
          <text class="text-34rpx font-medium text-#F33429">{{ orderData?.takeNo }}</text>
        </view>

        <view class="text-22rpx">
          {{ orderData?.deliveryTime ? orderData?.deliveryTime + '前取餐' : '' }}
        </view>
      </view>

      <!-- 订单状态 -->
      <view class="flex items-center ml-16rpx">
        <text class="text-26rpx mr-16rpx">{{ orderData?.status || '' }}</text>
      </view>
    </view>

    <!-- 虚线分割线 -->
    <view class="sy-order-card-divider mx-24rpx mt-32rpx relative z-1"></view>

    <!-- 售后模块 (仅在售后模式下显示) -->
    <view
      v-if="isAfterSaleMode && hasAfterSaleData"
      class="sy-order-card-after-sale bg-#F5F6F7 px-24rpx pt-16rpx pb-0rpx relative z-1"
    >
      <view class="appeal-step-header">
        <text class="title">售后</text>
        <view class="step-status">
          <text>1.退款处理··</text>
          <text>2.餐损审核··</text>
          <text>3.按责赔付</text>
        </view>
      </view>
      <!-- 2. 主要信息区域 - 显示所有售后记录 -->
      <view class="after-sale-main-wrapper">
        <view
          v-for="(record, index) in sortedAfterSaleRecords"
          :key="index"
          class="after-sale-item-view"
        >
          <view
            class="after-sale-item"
            :class="{
              'after-sale-item-line': index < sortedAfterSaleRecords.length - 1,
            }"
          >
            <view class="after-sale-record-item flex mb-32rpx">
              <!-- 左侧手写步骤条 -->
              <view
                class="custom-step-wrapper flex flex-col items-center"
                v-if="sortedAfterSaleRecords.length > 1"
              >
                <!-- 步骤图标 -->
                <image
                  v-if="
                    index === 0 && record.refundStatus && ['30', '50'].includes(record.refundStatus)
                  "
                  :src="'/static/images/icons/cg_icon.png'"
                  class="w-24rpx h-24rpx status-img"
                  mode="aspectFit"
                />
                <image
                  v-if="
                    index === 0 &&
                    record.refundStatus &&
                    !['30', '50'].includes(record.refundStatus)
                  "
                  :src="'/static/images/icons/dq_icon.png'"
                  class="w-24rpx h-24rpx status-img"
                  mode="aspectFit"
                />
                <image
                  v-if="index !== 0"
                  :src="'/static/images/icons/zj_cion.png'"
                  class="w-8rpx h-8rpx zj-img"
                  mode="aspectFit"
                />
              </view>

              <!-- 右侧内容区域 -->
              <view class="step-content-area w-100% h-100% flex justify-between">
                <view class="w-60%">
                  <!-- 标题信息 -->
                  <view v-if="record.title" class="title-info mb-4rpx pl-16rpx">
                    <text class="title-text text-28rpx text-#222222">
                      {{ record.title }}
                    </text>
                  </view>
                  <!-- 理由说明 -->
                  <view v-if="record.reason" class="reason-info mb-4rpx pl-16rpx">
                    <text class="reason-text text-24rpx text-#3D3D3D">
                      理由：{{ record.reason }}
                      <text
                        class="apeeal-rule"
                        v-if="record.type === 'appealPass' || record.type === 'appealRefuse'"
                        @click="onGoToAppealRule"
                      >
                        <text class="text-24rpx text-#3D3D3D">可申诉标准详见：</text>
                        <text>申诉规则></text>
                      </text>
                    </text>
                  </view>
                  <wd-button
                    v-if="index == 0"
                    style="width: 200rpx; height: 58rpx"
                    type="error"
                    @click="handleToAppeal(index)"
                  >
                    发起申诉
                  </wd-button>
                </view>
                <view>
                  <!-- 状态信息行 -->
                  <view class="status-row flex items-center mb-4rpx">
                    <view class="status-dot w-12rpx h-12rpx rounded-full mr-16rpx"></view>
                    <text class="status-text text-28rpx font-500">
                      {{
                        record.refundStatusName == '审核失败'
                          ? '拒绝退款'
                          : record.refundStatusName || '状态未知'
                      }}
                    </text>
                  </view>
                  <!-- 时间信息 -->
                  <view class="time-info pl-28rpx">
                    <text class="time-text text-24rpx text-#999999"></text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <!-- 申诉上传的图片 -->
          <view>
            <view
              class="flex flex-wrap mb-40rpx"
              :class="{
                'gap-8rpx pl-38rpx': sortedAfterSaleRecords.length > 1,
                'gap-20rpx': sortedAfterSaleRecords.length <= 1,
              }"
            >
              <image
                v-for="image in record.url"
                :key="image"
                :src="image"
                class="w-136rpx h-136rpx rounded-16rpx"
                mode="aspectFit"
                @click="previewAfterSaleImage(image, [record.url])"
              />
            </view>
          </view>
        </view>

        <!-- 底部信息 -->
        <view
          v-if="showExpandButton"
          class="sy-order-card-footer flex items-center justify-between pl-52rpx pb-20rpx"
        >
          <!-- 右侧展开/收起按钮 -->
          <view class="flex items-center" @click="toggleExpand">
            <text class="text-26rpx text-#666666 mr-8rpx">
              {{ isExpanded ? '收起' : '展开' }}完整信息>
            </text>

            <text
              :class="[
                'text-20rpx text-#999999',
                isExpanded ? 'sy-order-card-collapse-arrow' : 'sy-order-card-expand-arrow',
              ]"
            ></text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed } from 'vue'
import { componentProps, type OrderCardEmits, type OrderData, type OrderTrackStep } from './appeal'
import { getChannelColor, channelColorMap } from '@/utils/transform'

// 定义组件属性
const props = defineProps(componentProps)

// 定义组件事件
const emit = defineEmits<OrderCardEmits>()

/**
 * 订单展开状态（完全由组件内部管理，默认 false）
 */
const isExpanded = ref(false)
/**
 * 拨打隐私号客户电话弹窗显示状态
 */
const showCallCustomerPopup = ref(false)

/**
 * 步骤条当前激活步骤
 */
const activeStep = ref(0)

/**
 * 订单跟踪步骤数据
 */
const orderTrackSteps = ref<OrderTrackStep[]>([])

/**
 * 检测当前页面是否为 tabbar 页面
 */
const isTabbarPage = ref(false)

/**
 * 获取当前页面路径并判断是否为 tabbar 页面
 */
const checkTabbarPage = () => {
  try {
    const pages = getCurrentPages()
    if (pages && pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage.route || ''

      // 定义 tabbar 页面路径列表（根据实际项目的 tabbar 配置）
      const tabbarPages = [
        'pages/index/index',
        'pages/about/about',
        'pages/dishManagement/index',
        'pages/my/index',
        'pages/storeDetail/index',
      ]

      isTabbarPage.value = tabbarPages.some((path) => currentRoute.includes(path))
    }
  } catch (error) {
    console.error('检测 tabbar 页面失败:', error)
    isTabbarPage.value = false
  }
}

// 用于强制更新的计数器
const timerCount = ref(0)
// 定时器引用
let timer: number | null = null

// 在组件挂载时启动定时器
onMounted(() => {
  // 检测当前页面是否为 tabbar 页面
  checkTabbarPage()
  console.log('申诉数据:', props.orderData)
  // 每秒更新一次计数器，触发重新渲染
  timer = setInterval(() => {
    timerCount.value++
  }, 1000) as unknown as number

  if (!hasAfterSaleData.value) return []
  const records = [...props.orderData!.afterSale!]
  if (records.length >= 2) {
    sortedAfterSaleRecords.value = records.slice(0, 2)
  }
})

// 在组件卸载时清除定时器
onUnmounted(() => {
  if (timer !== null) {
    clearInterval(timer)
    timer = null
  }
})

// 监听外部传入的expanded属性变化，但不触发事件
watch(
  () => props.expanded,
  (newValue) => {
    if (typeof newValue === 'boolean') {
      isExpanded.value = newValue
    }
  },
)

// 是否显示收缩按钮
const showExpandButton = computed(() => {
  return sortedAfterSaleRecords.value.length > 2
})

// 跳转申诉规则页面
const onGoToAppealRule = () => {
  uni.navigateTo({
    url: '/pages/damageAppeal/appealRule/index',
  })
}

/**
 * 获取渠道按钮样式
 */
const getChannelButtonStyle = (channel: string) => {
  return channelColorMap[channel] || channelColorMap.default
}

/**
 * 格式化金额显示
 */
const displayNum = (num: number): string => {
  // 参考index.js中的displayNum函数
  return typeof num === 'number' ? num.toFixed(2) : '0.00'
}

/**
 * 计算订单应付金额
 */
const calculatePayAmount = () => {
  if (!props.orderData || !props.orderData.fees) {
    return 0
  }
  return props.orderData.fees.payAmount || 0
}

/**
 * 切换展开状态
 */
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

// 发起申诉实践
const handleToAppeal = (index: number) => {
  // 触发点击事件
  emit('handleToAppeal', index)
}

/**
 * 格式化手机号，只显示尾号4位
 */
const formatPhoneNumber = (phone: string): string => {
  if (!phone || phone.length < 4) return phone || ''
  return phone.slice(-4)
}

/**
 * 格式化订单时间
 */
const formatOrderTime = (time: string): string => {
  if (!time) return time

  try {
    const date = new Date(time)
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')

    return `${month}-${day} ${hours}:${minutes}`
  } catch (error) {
    console.error('格式化时间失败:', error)

    // 如果时间包含T，按ISO格式处理
    if (time.includes('T')) {
      const [datePart, timePart] = time.split('T')
      if (datePart && timePart) {
        const [year, month, day] = datePart.split('-')
        const timeValue = timePart.substring(0, 5)
        if (month && day && timeValue) {
          return `${month}-${day} ${timeValue}`
        }
      }
    }

    // 如果是标准格式，提取月日和时间
    if (time.includes('-') && time.includes(':')) {
      const parts = time.split(' ')
      if (parts.length === 2) {
        const dateParts = parts[0].split('-')
        if (dateParts.length >= 3) {
          const month = dateParts[1]
          const day = dateParts[2]
          const timeValue = parts[1].substring(0, 5)
          return `${month}-${day} ${timeValue}`
        }
      }
    }

    return time
  }
}

/**
 * 格式化骑手手机号显示
 * @param mobile 原始手机号，可能包含分机号如 "13045975938-411"
 * @returns 格式化后的显示文本如 "13045975938转411"
 */
const formatRiderPhone = (mobile: string): string => {
  if (!mobile) return ''

  // 检查是否包含分机号
  if (mobile.includes('-')) {
    const [phone, ext] = mobile.split('-')
    return `${phone}转${ext}`
  }

  return mobile
}

/**
 * 格式化隐私号显示
 * @param phoneNumber 隐私号如 "13148112418_0778"
 * @returns 格式化后的显示文本如 "13148112418转0778"
 */
const formatCustomerPrivacyPhone = (phoneNumber: string): string => {
  if (!phoneNumber || !phoneNumber.includes('_')) return phoneNumber

  const [phone, ext] = phoneNumber.split('_')
  return `${phone}转${ext}`
}

/**
 * 获取隐私号的分机号
 * @param phoneNumber 隐私号如 "13148112418_0778"
 * @returns 分机号如 "0778"
 */
const getCustomerPhoneExtension = (phoneNumber: string): string => {
  if (!phoneNumber || !phoneNumber.includes('_')) return ''
  return phoneNumber.split('_')[1] || ''
}

/**
 * 从弹窗中拨打隐私号客户电话
 */
// const onCallCustomerFromPopup = () => {
//   if (!props.orderData || !props.orderData.customer) return

//   const phoneNumber = props.orderData.customer.fullPhone || props.orderData.customer.phone || ''
//   if (!phoneNumber || !phoneNumber.includes('_')) return

//   // 提取主号码部分
//   const mainPhone = phoneNumber.split('_')[0]
//   if (!mainPhone) return

//   // 关闭弹窗
//   showCallCustomerPopup.value = false

//   // 使用 uni.makePhoneCall 拨打电话
//   uni.makePhoneCall({
//     phoneNumber: mainPhone,
//     success: () => {
//       console.log('拨打隐私号客户电话成功')
//     },
//     fail: (err) => {
//       console.error('拨打隐私号客户电话失败:', err)
//       // 拨打失败时，仍然触发 call 事件，让父组件可以处理
//       emit('call', mainPhone)
//     },
//   })
// }

/**
 * 判断是否隐藏订单状态信息
 * 自营渠道（微信、支付宝、抖音随心团）需要隐藏订单状态信息
 */
const shouldHideOrderStatus = computed(() => {
  const channel = props.orderData?.saleChannel || ''
  return ['TikTokMiniProgram', 'AlipayMiniProgram', 'WeChatMiniProgram'].includes(channel)
})

/**
 * 格式化售后申请时间
 * @param timeString 时间字符串
 */
const formatRefundApplyTime = (timeString?: string): string => {
  if (!timeString) return ''
  return formatOrderTime(timeString)
}

/**
 * 预览图片
 * @param current 当前图片URL
 * @param urls 所有图片URL数组
 */
const previewImage = (current: string, urls: string[]) => {
  uni.previewImage({
    current,
    urls,
    fail: (err) => {
      console.error('预览图片失败:', err)
      uni.showToast({
        title: '预览图片失败',
        icon: 'none',
      })
    },
  })
}

/**
 * 预览售后图片
 * @param current 当前点击的图片URL
 * @param urls 所有图片URL数组
 */
const previewAfterSaleImage = (current: string, urls: string[]) => {
  if (!current || !urls || urls.length === 0) return

  // 使用 Wot Design Uni 的图片预览
  uni.previewImage({
    current,
    urls,
    fail: (err) => {
      console.error('预览售后图片失败:', err)
      uni.showToast({
        title: '预览图片失败',
        icon: 'none',
      })
    },
  })
}

/**
 * 检查是否有售后数据
 */
const hasAfterSaleData = computed(() => {
  console.log('查看申诉数据：', props.orderData)
  return Boolean(
    props.orderData?.afterSale &&
      Array.isArray(props.orderData.afterSale) &&
      props.orderData.afterSale.length > 0,
  )
})

/**
 * 当前售后记录（最新的一条）
 */
const currentAfterSaleRecord = computed(() => {
  if (!hasAfterSaleData.value) return null
  const records = props.orderData!.afterSale!
  // 返回最新的记录（数组最后一个）
  return records[records.length - 1]
})

/**
 * 所有售后记录
 */
const allAfterSaleRecords = computed(() => {
  if (!hasAfterSaleData.value) return []
  console.log('申诉单数据：', props.orderData)
  return props.orderData!.afterSale!
})

const sortedAfterSaleRecords = ref<any[]>([])

// 使用watch监听排序后的数据变化，并更新缓存
watch(isExpanded, () => {
  if (!hasAfterSaleData.value) return []
  const records = [...props.orderData!.afterSale!]
  if (isExpanded.value) {
    sortedAfterSaleRecords.value = records
  } else {
    if (records.length >= 2) {
      sortedAfterSaleRecords.value = records.slice(0, 2)
    }
  }
})

/**
 * 售后步骤条数据
 */
const afterSaleSteps = computed(() => {
  if (!hasAfterSaleData.value) return []

  return allAfterSaleRecords.value.map((record, index) => ({
    title: record.refundStatusName || record.title || `步骤${index + 1}`,
    time: formatRefundApplyTime(record.createTime),
    status: index === allAfterSaleRecords.value.length - 1 ? 'process' : 'finished',
    description: record.reason || record.refundStatusName || '',
  }))
})

/**
 * 获取步骤描述
 */
const getStepDescription = (record: any, index: number): string => {
  // 返回状态名称作为步骤描述
  return record.refundStatusName || `步骤${index + 1}`
}

// 暴露属性和方法
defineExpose({
  formatPhoneNumber,
  formatOrderTime,
  isExpanded,
  toggleExpand,
  getChannelButtonStyle,
  formatRiderPhone,
  // 隐私号客户电话相关方法
  showCallCustomerPopup,
  formatCustomerPrivacyPhone,
  getCustomerPhoneExtension,
  // 安全区相关方法和状态
  isTabbarPage,
  checkTabbarPage,
  // 售后相关方法
  formatRefundApplyTime,
  previewImage,
  previewAfterSaleImage,
  // 订单状态显示相关
  shouldHideOrderStatus,
})
</script>

<style lang="scss" src="./index.scss" scoped></style>
