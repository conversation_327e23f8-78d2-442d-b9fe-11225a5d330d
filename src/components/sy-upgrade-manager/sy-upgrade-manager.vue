<template>
  <view style="display: none">
    <!-- 升级管理器组件 - 不渲染任何UI -->
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { checkAndHandleUpdate } from '@/uni_modules/uni-upgrade-center-app/utils/app-updater'

const props = defineProps({
  /** 是否使用自定义升级弹窗 */
  useCustomDialog: {
    type: Boolean,
    default: true,
  },
  /** 是否在应用启动时自动检查更新 */
  autoCheck: {
    type: Boolean,
    default: true,
  },
  /** 自动检查更新的延迟时间（毫秒） */
  checkDelay: {
    type: Number,
    default: 2000,
  },
})

const emit = defineEmits<{
  'update-start': []
  'update-success': []
  'update-fail': [error: any]
  'no-update': []
}>()

// 全局升级状态管理
const GLOBAL_UPDATE_KEY = '__global_update_status__'
const APP_LAUNCH_KEY = '__app_launch_time__'

// 获取全局升级状态
const getGlobalUpdateStatus = () => {
  return (
    uni.getStorageSync(GLOBAL_UPDATE_KEY) || {
      isChecking: false,
      isUpdating: false,
      lastCheckTime: 0,
      managerCount: 0,
      appLaunchTime: 0,
    }
  )
}

// 设置全局升级状态
const setGlobalUpdateStatus = (status: any) => {
  uni.setStorageSync(GLOBAL_UPDATE_KEY, status)
}

// 获取当前应用启动时间
const getCurrentAppLaunchTime = () => {
  return uni.getStorageSync(APP_LAUNCH_KEY) || 0
}

// 设置应用启动时间
const setAppLaunchTime = (time: number) => {
  uni.setStorageSync(APP_LAUNCH_KEY, time)
}

// 升级状态
const isChecking = ref(false)
const isUpdating = ref(false)
const managerId = ref(`manager_${Date.now()}_${Math.random()}`)

/**
 * 检查应用更新
 */
const checkForUpdate = async (): Promise<void> => {
  const globalStatus = getGlobalUpdateStatus()
  const currentAppLaunchTime = getCurrentAppLaunchTime()

  // 如果全局正在检查或更新，跳过
  // if (globalStatus.isChecking || globalStatus.isUpdating) {
  //   console.log(`🔄 升级管理器 ${managerId.value}：全局正在检查或更新中，跳过此次检查`)
  //   return
  // }

  // 检查是否是同一个应用启动周期
  const now = Date.now()
  if (
    globalStatus.appLaunchTime === currentAppLaunchTime &&
    now - globalStatus.lastCheckTime < 30000
  ) {
    console.log(`🔄 升级管理器 ${managerId.value}：同一启动周期内已检查过，跳过此次检查`)
    return
  }

  try {
    // 设置全局检查状态
    setGlobalUpdateStatus({
      ...globalStatus,
      isChecking: true,
      lastCheckTime: now,
      appLaunchTime: currentAppLaunchTime,
    })

    isChecking.value = true
    console.log(`🔄 升级管理器 ${managerId.value}：开始检查更新`)

    await checkAndHandleUpdate(false, props.useCustomDialog)

    console.log(`✅ 升级管理器 ${managerId.value}：更新检查完成`)
    emit('update-success')
  } catch (error) {
    console.error(`❌ 升级管理器 ${managerId.value}：更新检查失败:`, error)

    if (error?.message?.includes('已是最新版本')) {
      emit('no-update')
    } else {
      emit('update-fail', error)
    }
  } finally {
    // 清除全局检查状态
    const currentStatus = getGlobalUpdateStatus()
    setGlobalUpdateStatus({
      ...currentStatus,
      isChecking: false,
    })

    isChecking.value = false
  }
}

/**
 * 手动触发更新检查
 */
const manualCheck = (): Promise<void> => {
  console.log(`📱 升级管理器 ${managerId.value}：手动触发更新检查`)
  // 手动检查时，清除上次检查时间，强制检查
  const globalStatus = getGlobalUpdateStatus()
  setGlobalUpdateStatus({
    ...globalStatus,
    lastCheckTime: 0,
  })
  return checkForUpdate()
}

// 定时器引用
let checkTimer: number | null = null

/**
 * 启动自动检查
 */
const startAutoCheck = (): void => {
  if (!props.autoCheck) return

  checkTimer = setTimeout(() => {
    checkForUpdate()
  }, props.checkDelay)
}

/**
 * 停止自动检查
 */
const stopAutoCheck = (): void => {
  if (checkTimer) {
    clearTimeout(checkTimer)
    checkTimer = null
  }
}

// 监听全局更新检查事件
const onGlobalUpdateCheck = () => {
  console.log(`📡 升级管理器 ${managerId.value}：收到全局更新检查事件`)
  manualCheck()
}

// 生命周期
onMounted(() => {
  console.log(`🚀 升级管理器 ${managerId.value}：组件已挂载`)

  // 设置当前应用启动时间（如果还没有设置）
  const currentAppLaunchTime = getCurrentAppLaunchTime()
  if (!currentAppLaunchTime) {
    const now = Date.now()
    setAppLaunchTime(now)
    console.log(`📱 设置应用启动时间: ${now}`)
  }

  // 更新管理器计数
  const globalStatus = getGlobalUpdateStatus()
  setGlobalUpdateStatus({
    ...globalStatus,
    managerCount: globalStatus.managerCount + 1,
  })

  // 注册全局事件监听
  uni.$on('check-app-update', onGlobalUpdateCheck)

  // 启动自动检查
  startAutoCheck()
})

onUnmounted(() => {
  console.log(`💥 升级管理器 ${managerId.value}：组件即将卸载`)

  // 更新管理器计数
  const globalStatus = getGlobalUpdateStatus()
  setGlobalUpdateStatus({
    ...globalStatus,
    managerCount: Math.max(0, globalStatus.managerCount - 1),
  })

  // 移除全局事件监听
  uni.$off('check-app-update', onGlobalUpdateCheck)

  // 清理定时器
  stopAutoCheck()
})

// 暴露方法给父组件
defineExpose({
  checkForUpdate: manualCheck,
  isChecking: isChecking.value,
  isUpdating: isUpdating.value,
})
</script>

<style lang="scss" scoped>
/* 升级管理器组件不需要样式，因为它不渲染任何UI */
</style>
