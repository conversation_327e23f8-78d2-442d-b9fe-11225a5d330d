/**
 * 升级管理器组件
 * 用于在layout中管理应用更新，不渲染UI但提供更新功能
 */

export { default } from './sy-upgrade-manager.vue'

export interface UpgradeManagerProps {
  /** 是否使用自定义升级弹窗 */
  useCustomDialog?: boolean
  /** 是否在应用启动时自动检查更新 */
  autoCheck?: boolean
  /** 自动检查更新的延迟时间（毫秒） */
  checkDelay?: number
}

export interface UpgradeManagerEvents {
  /** 更新开始 */
  'update-start': () => void
  /** 更新成功 */
  'update-success': () => void
  /** 更新失败 */
  'update-fail': (error: any) => void
  /** 没有更新 */
  'no-update': () => void
}

export interface UpgradeManagerExpose {
  /** 手动检查更新 */
  checkForUpdate: () => Promise<void>
  /** 是否正在检查 */
  isChecking: boolean
  /** 是否正在更新 */
  isUpdating: boolean
}
