.position {
  position: relative;
  width: 100%;
  // margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  box-shadow: 0px 8rpx 10rpx rgba(246, 246, 246, 1);
  /* 毛玻璃效果 */

  .tab-icon {
    position: absolute;
    top: -4rpx;
    right: 0rpx;
    z-index: 1;
    background: white;
    image {
      width: 72rpx;
      height: 84rpx;
    }
  }
  .store-tab-nav {
    position: relative;
    // display: flex;
    // flex-direction: row;
    align-items: center;
    height: 76rpx;
    // padding-top: 10rpx;
    // padding-right: 48rpx;
    // padding-top: 14rpx;
    // padding: 0 32rpx;
    // padding: ;
    padding-left: 24rpx;
    margin-right: 40rpx;
    // margin-right: 96rpx;
    overflow: hidden;
    overflow-x: auto;
    background: #fff;
    // border-bottom: 1rpx solid #f0f0f0;

    .tab-item {
      position: relative;
      display: inline-block;
      height: 40rpx;
      // padding: 24rpx 0 16rpx 0;
      padding-right: 20rpx;
      padding-bottom: rpx;
      // margin-right: 20rpx;
      font-size: 26rpx;
      font-weight: 400;
      color: #666666;
      white-space: nowrap;
      cursor: pointer;
      background: transparent;

      &.active {
        // font-weight: bold;
        font-weight: 600;
        color: #1e1e1e;
        &::after {
          position: absolute;
          bottom: -18rpx;
          left: 50%;
          display: block;
          width: 38rpx;
          height: 8rpx;
          content: '';
          background: #e60112;
          border-radius: 4rpx;
          transform: translateX(-75%);
          // border-radius: ;
        }
      }
      &:last-child {
        padding-right: 60rpx;
      }
    }
  }
  .store-second-tab-nav {
    padding-left: 24rpx;
    margin-top: 0rpx;
    .secon-tab-item {
      display: flex;
      display: inline-block;
      align-items: center;
      height: 42rpx;
      padding: 0 32rpx;
      margin-right: 24rpx;
      font-size: 24rpx;
      font-weight: 400;
      line-height: 42rpx;
      color: #666666;
      background: #f3f3f3;
      border-radius: 200rpx;
    }
  }
  .seconActive {
    color: rgba(243, 52, 41, 1);
    background: rgba(243, 52, 41, 0.1);
  }
  /* 底部毛玻璃效果层 */
  .frosted-glass-layer {
    // position: absolute;
    // bottom: -0rpx;
    // left: 0;
    // z-index: 1;
    // width: 100%;
    // height: 16rpx;
    // background-color: rgba(255, 255, 255, 0.6);
    // -webkit-backdrop-filter: blur(10px);
    // backdrop-filter: blur(10px);
    // border-bottom-right-radius: 20rpx;
    // border-bottom-left-radius: 20rpx;
    // box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  }
}
/* 隐藏滚动条样式 */
.hide-scrollbar {
  /* Chrome, Safari, Opera */
  &::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
  }
}
/* 毛玻璃效果 */
.frosted-glass {
  background-color: rgba(255, 255, 255, 0.6);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
/* 深色毛玻璃效果 */
.frosted-glass-dark {
  background-color: rgba(0, 0, 0, 0.35);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
/* 浅色毛玻璃效果 */
.frosted-glass-light {
  background-color: rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
}
.tab-item-second {
  display: inline-block;
  // line-height: 42rpx;
  height: 42rpx;
  // height: 42rpx;
  // height: 42rpx;
  padding: 0 16rpx;
  margin-right: 32rpx;
  font-size: 24rpx;
  font-weight: 400;
  line-height: 42rpx;
  color: #666666;
  background: #f3f3f3;
  border-radius: 200rpx;
}

.store-tab-nav-second {
  position: relative;
  // display: flex;
  // flex-direction: row;
  align-items: center;
  height: 50rpx;
  padding-top: 0rpx;
  // padding-top: 10rpx;
  // padding-right: 48rpx;
  // padding-top: 14rpx;
  // padding: 0 32rpx;
  // padding: ;
  padding-left: 24rpx;
  margin-right: 40rpx;
  margin-left: 12rpx;
  // margin-right: 96rpx;
  overflow: hidden;
  overflow-x: auto;
  background: #fff;
}
.activeSecond {
  color: rgba(243, 52, 41, 1);
  background: rgba(243, 52, 41, 0.1);
}
/* 绝对定位的更多图标 */
.more-icon {
  position: absolute;
  top: -18rpx;
  right: 6rpx;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
  height: 84rpx;
  // padding: 8px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 1) 60%);
  // transform: translateY(-50%);
  .more-icon-img {
    width: 26rpx;
    height: 24rpx;
  }
}
.channel-popup-header {
  position: relative;
  .icon-danchuangguanbi {
    position: absolute;
    right: 32rpx;
    font-size: 18rpx;
    font-weight: 800;
  }
}
.channel-popup-container {
  position: relative;
  top: -20rpx !important;
}
