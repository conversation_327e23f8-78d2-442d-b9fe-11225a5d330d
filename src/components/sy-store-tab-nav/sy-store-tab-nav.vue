<template>
  <view class="position">
    <scroll-view
      class="store-tab-nav whitespace-nowrap hide-scrollbar"
      scroll-x
      :show-scrollbar="false"
      enhanced
      :scroll-with-animation="true"
      :scroll-into-view="secondScrollIntoViewId"
    >
      <view
        v-for="item in seconTab"
        :key="item.key"
        class="tab-item"
        :class="{ active: secondModelValue === item.key }"
        :id="`second-tab-${item.key}`"
        @click="handleSecondTabClick(item)"
      >
        {{ item.label }}
      </view>
    </scroll-view>
    <scroll-view
      class="store-tab-nav-second whitespace-nowrap hide-scrollbar"
      scroll-x
      :show-scrollbar="false"
      enhanced
      :scroll-into-view="scrollIntoViewId"
      :scroll-with-animation="true"
    >
      <view
        v-for="item in filterFirstTab"
        :key="item.key"
        class="tab-item-second"
        :class="{ activeSecond: modelValue === item.key }"
        :id="`tab-${item.key}`"
        @click="handleFirstTabClick(item)"
      >
        {{ item.label }}
      </view>
    </scroll-view>
    <view class="more-icon">
      <image
        class="more-icon-img"
        src="/static/images/img/more.png"
        mode="aspectFit"
        @click.stop="openChannelPopup"
      />
    </view>

    <!-- 渠道选择弹出层 - 图2 -->
    <view v-if="isChannelPopupVisible">
      <view class="channel-popup-overlay" @click="closeChannelPopup"></view>
      <view class="channel-popup-container">
        <view class="channel-popup-content">
          <view class="channel-grid">
            <view
              class="channel-item"
              v-for="item in filterFirstTab"
              :key="item.key"
              :class="{ 'channel-item-active': modelValue === item.key }"
              @click="handleFirstTabClick(item)"
            >
              <text class="channel-item-text">{{ item.label }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class=""></view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import type { StoreTab } from '@/types/store'
import { onLoad, onUnload, onHide, onShow } from '@dcloudio/uni-app'
import { useShopStore } from '@/store/shop'

// 添加空值请求计数器
const emptyRequestCount = ref(0)

const props = defineProps<{
  modelValue: string
  secondModelValue: string
  firstTab: StoreTab[]
  seconTab: StoreTab[]
  filterChannleList?: string[] | null
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'update:secondModelValue', value: string): void
  (e: 'select-channel', channel: StoreTab): void
}>()

// 渠道选择弹出层控制
const isChannelPopupVisible = ref(false)

// 用于scroll-into-view的ID
const scrollIntoViewId = ref('')
const secondScrollIntoViewId = ref('')
const filterFirstTab = ref()
const firstLoad = ref(false)
// 监听modelValue变化，更新scrollIntoViewId
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      // 添加延时，确保DOM已经更新
      // findCurrentShop()
      setTimeout(() => {
        scrollIntoViewId.value = `tab-${newVal}`
      }, 100)
    }
  },
)

// 监听secondModelValue变化，更新secondScrollIntoViewId
watch(
  () => props.secondModelValue,
  (newVal) => {
    if (newVal) {
      // 添加延时，确保DOM已经更新
      setTimeout(() => {
        secondScrollIntoViewId.value = `second-tab-${newVal}`
        findCurrentShop(true)
      }, 100)
    }
  },
)
watch(
  () => filterFirstTab.value,
  (newVal) => {
    console.log('filterFirstTab====>空值', newVal.length)
    if (newVal?.length === 0) {
      // 添加计数器判断，只有在计数小于等于3次时才发送请求
      if (emptyRequestCount.value <= 3) {
        // 计数器+1
        emptyRequestCount.value++
        console.log(`重试次数: ${emptyRequestCount.value}/4`)

        setTimeout(() => {
          findCurrentShop(true)
        }, 500)
      } else {
        console.log('已达到最大重试次数(4次)，停止请求')
      }
    }
  },
)
const findCurrentShop = (refresh = false) => {
  const filterFirstTabTemp = []
  const findItem = props.seconTab.find((item) => item.key === props.secondModelValue)
  // console.log('过滤类型', props.firstTab, findItem, props.seconTab, props.secondModelValue)

  // 检查 findItem 和 channelList 是否存在
  if (findItem && (findItem as any).channelList) {
    props.firstTab.forEach((item) => {
      ;(findItem as any).channelList.forEach((sonItem: string) => {
        if (item.key === sonItem) {
          filterFirstTabTemp.push(item)
        }
      })
    })
  }

  filterFirstTab.value = filterFirstTabTemp
  if ((filterFirstTab.value?.length > 0 && !firstLoad.value) || refresh) {
    handleFirstTabClick(filterFirstTab.value[0])
    firstLoad.value = true
  }
}
onLoad(() => {
  // findCurrentShop()
  setTimeout(() => {
    findCurrentShop()
  }, 50)
})
onShow(() => {
  const shopStore = useShopStore()
  uni.$on('refreshStoreTabNav', function (data) {
    console.log('监听到事件来自 update ，携带参数 msg 为：')
    setTimeout(() => {
      findCurrentShop()
    }, 500)
  })
})

onHide(() => {
  // 重置计数器
  emptyRequestCount.value = 0
  firstLoad.value = false
})
onUnload(() => {
  // 重置计数器
  emptyRequestCount.value = 0
})
// 打开渠道选择弹出层
const openChannelPopup = () => {
  isChannelPopupVisible.value = true
}

// 关闭渠道选择弹出层
const closeChannelPopup = () => {
  isChannelPopupVisible.value = false
}

// 点击处理函数 - 一级标签
const handleFirstTabClick = (item: StoreTab) => {
  console.log('item====>', item)
  if (!item?.key) {
    return
  }
  emit('update:modelValue', item?.key)
  emit('select-channel', item)

  // 设置scroll-into-view的ID
  scrollIntoViewId.value = `tab-${item.key}`

  closeChannelPopup() // 点击后自动关闭弹出层
}

// 点击处理函数 - 二级标签
const handleSecondTabClick = (item: StoreTab) => {
  emit('update:secondModelValue', item.key)
  // 设置scroll-into-view的ID
  secondScrollIntoViewId.value = `second-tab-${item.key}`
}

// 添加默认导出
defineOptions({
  name: 'SyStoreTabNav',
})
</script>

<style lang="scss">
@import './sy-store-tab-nav';
/* 渠道选择弹出层样式 - 图2 */
.channel-popup-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 5;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
}

.channel-popup-container {
  position: absolute;
  top: 0rpx;
  left: 0;
  z-index: 10;
  width: 100%;
  background-color: #ffffff;
  border-bottom-right-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.channel-popup-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
}

.channel-popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
}

.channel-popup-close {
  position: absolute;
  top: 20rpx;
  right: 32rpx;
  width: 32rpx;
  height: 32rpx;
}

.channel-popup-content {
  padding: 36rpx 60rpx 48rpx 60rpx;
}

.channel-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.channel-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 196rpx;
  height: 96rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}
/* 修复 no-descending-specificity 错误，将这个选择器移到后面 */
.channel-item .channel-item-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #000000;
}
.channel-item-active {
  background: rgba(252, 229, 231, 1);
  .channel-item-text {
    font-weight: 600;
    color: #e60112;
  }
}
</style>
