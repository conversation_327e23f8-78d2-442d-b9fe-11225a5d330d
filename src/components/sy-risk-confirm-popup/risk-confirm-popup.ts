/**
 * 高危风险确认弹窗组件属性定义
 */
export const riskConfirmPopupProps = {
  /**
   * 是否显示
   */
  visible: {
    type: Boolean,
    default: false,
  },
  /**
   * 弹窗标题
   */
  title: {
    type: String,
    default: '来单不响高危风险',
  },
  /**
   * 弹窗内容
   */
  content: {
    type: String,
    default: '您有1项设置影响接单，请按要求设置需开启新消息通知',
  },
}

/**
 * 高危风险确认弹窗组件实例类型
 */
export interface RiskConfirmPopupInstance {
  /** 显示弹窗 */
  show: () => void
  /** 隐藏弹窗 */
  hide: () => void
}
