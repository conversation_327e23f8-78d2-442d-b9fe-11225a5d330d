<template>
  <wd-popup v-model="show" custom-style="background-color: transparent;" @close="handleIgnore">
    <view class="sy-risk-confirm-popup">
      <view class="popup-content">
        <text class="title">{{ title }}</text>
        <text class="content">{{ content }}</text>
        <view class="action-buttons">
          <button class="action-btn ignore-btn" @click="handleIgnore">忽略</button>
          <button class="action-btn setting-btn" @click="handleGoSetting">去设置</button>
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { riskConfirmPopupProps } from './risk-confirm-popup'

const props = defineProps(riskConfirmPopupProps)

const emit = defineEmits<{
  'update:visible': [value: boolean]
  goSetting: []
  ignore: []
}>()

const show = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const handleIgnore = () => {
  emit('ignore')
  show.value = false
}

const handleGoSetting = () => {
  emit('goSetting')
  show.value = false
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
  },
}
</script>

<style lang="scss" scoped>
.sy-risk-confirm-popup {
  width: 626rpx; /* 313px * 2 */
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 24rpx; /* 12px * 2 */
}

.popup-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx; /* 16px * 2 */
}

.title {
  margin-bottom: 24rpx; /* 12px * 2 */
  font-size: 30rpx; /* 15px * 2 */
  font-weight: 600;
  color: #333333;
}

.content {
  margin-bottom: 48rpx; /* 24px * 2 */
  font-size: 26rpx; /* 13px * 2 */
  line-height: 1.5;
  color: #999999;
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 24rpx; /* 12px * 2 */
  width: 100%;
}

.action-btn {
  flex: 1;
  height: 78rpx; /* 39px * 2 */
  margin: 0;
  font-size: 26rpx; /* 13px * 2 */
  line-height: 78rpx;
  border-radius: 39rpx; /* 19.5px * 2 */

  &::after {
    border: none;
  }
}

.ignore-btn {
  color: #333333;
  background-color: #ffffff;
  border: 1rpx dashed #9a9a9a;
}

.setting-btn {
  color: #ffffff;
  background-color: #f33429;
}
</style>
