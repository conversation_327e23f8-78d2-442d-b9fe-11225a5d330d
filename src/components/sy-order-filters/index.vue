import { ref, defineEmits, defineProps, watch, computed } from 'vue' // 导入属性定义 const props =
defineProps({ // 当前筛选条件 currentFilters: { type: Object, default: () => ({}), }, // 渠道选项
channelOptions: { type: Array, default: () => [], }, // 订单类型选项 orderTypeOptions: { type:
Array, default: () => [], }, // 日期选项 dateOptions: { type: Array, default: () => [], }, //
是否正在加载数据 loading: { type: <PERSON>olean, default: false, }, }) // 渠道选项是否有效 const
hasValidChannelOptions = computed(() => { return props.channelOptions && props.channelOptions.length
> 0; }); // 订单类型选项是否有效 const hasValidOrderTypeOptions = computed(() => { return
props.orderTypeOptions && props.orderTypeOptions.length > 0; });
