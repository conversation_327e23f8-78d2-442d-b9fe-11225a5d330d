/**
 * 订单筛选栏组件属性定义
 */

export interface FilterTexts {
  channelText: string
  storeText: string
  orderTypeText: string
  dateText: string
  channelCodes?: string[] // 添加渠道代码数组
  shopIds?: string[] // 添加门店ID数组
  _startPlaceTime?: string // 日期开始时间
  _endPlaceTime?: string // 日期结束时间
}

export interface FilterBooleans {
  needChannel: boolean
  needStore: boolean
  needOrderType: boolean
  needDate: boolean
  needDateFilter: boolean
  // 是否渠道单选模式
  isMultipleChannelMode: boolean
}

export interface FilterOption {
  code: string
  name: string
  [key: string]: any
}

export const orderFiltersProps = {
  /** 当前各筛选条件显示文本 */
  currentFilters: {
    type: Object as () => FilterTexts,
    default: () => ({
      channelText: '渠道',
      storeText: '门店列表',
      orderTypeText: '订单类型',
      dateText: '今日',
      channelCodes: [],
      shopIds: [],
    }),
  },
  // 当前需要显示的筛选项
  isNeedFilters: {
    type: Object as () => FilterBooleans,
    default: () => ({
      // 是否需要筛选框
      needChannel: true,
      needStore: true,
      needOrderType: true,
      needDate: true,
      needDateFilter: true,
      // 是否渠道单选
      isMultipleChannelMode: true,
    }),
  },
  /** 渠道选项列表 */
  channelOptions: {
    type: Array as () => FilterOption[],
    default: () => [],
  },
  /** 订单类型选项列表 */
  orderTypeOptions: {
    type: Array as () => FilterOption[],
    default: () => [],
  },
  /** 订单状态选项列表 */
  orderStatusOptions: {
    type: Array as () => FilterOption[],
    default: () => [],
  },
  /** 日期选项列表 */
  dateOptions: {
    type: Array as () => { label: string; value: string }[],
    default: () => [
      { label: '今日', value: 'today' },
      { label: '近7天', value: 'last7days' },
      { label: '近30天', value: 'last30days' },
      { label: '自定义', value: 'custom' },
    ],
  },
}

// 定义事件类型
export const orderFiltersEmits = {
  'filter-item-click': (type: 'channel' | 'store' | 'orderType' | 'date' | 'dateFilter') => true,
  'filter-change': (
    type: 'channel' | 'store' | 'orderType' | 'date' | 'dateFilter',
    value: string | string[],
    label: string | string[],
    extraData?: any,
  ) => true,
  'filter-error': (errorMessage: string) => true,
}
