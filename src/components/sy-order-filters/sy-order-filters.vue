<template>
  <view class="sy-order-filters">
    <view class="sy-order-filters-item" @click="onFilterItemClick('channel')">
      <text class="sy-order-filters-text">渠道</text>
      <view class="sy-order-filters-arrow">
        <wd-icon name="arrow-down" size="16rpx" />
      </view>
    </view>

    <view class="sy-order-filters-item" @click="onFilterItemClick('store')">
      <text class="sy-order-filters-text">门店列表</text>
      <view class="sy-order-filters-arrow">
        <wd-icon name="arrow-down" size="16rpx" />
      </view>
    </view>

    <view
      v-if="props.isNeedFilters.needOrderType"
      class="sy-order-filters-item"
      @click="onFilterItemClick('orderType')"
    >
      <text class="sy-order-filters-text">订单类型</text>
      <view class="sy-order-filters-arrow">
        <wd-icon name="arrow-down" size="16rpx" />
      </view>
    </view>

    <view
      v-if="props.isNeedFilters.needDate"
      class="sy-order-filters-item"
      @click="onFilterItemClick('date')"
    >
      <text class="sy-order-filters-text">{{ props.currentFilters.dateText || '今日' }}</text>
      <view class="sy-order-filters-arrow">
        <wd-icon name="arrow-down" size="16rpx" />
      </view>
    </view>

    <!-- 渠道筛选弹窗 -->
    <sy-channel-selector
      v-model:visible="popups.channel"
      v-model:value="selectedValues.channel"
      :options="processedChannelOptions"
      :top-offset="channelSelectorTop"
      :multiple="props.isNeedFilters.isMultipleChannelMode"
      title="选择渠道"
      @confirm="handleChannelConfirm"
    />

    <!-- 门店筛选弹窗 -->
    <sy-store-selector
      v-model:visible="popups.store"
      v-model:value="selectedValues.store"
      :options="storeOptions"
      :top-offset="channelSelectorTop"
      :left-offset="120"
      title="门店列表"
      @confirm="handleStoreConfirm"
    />

    <!-- 订单类型选择器 -->
    <SyOrderTypeSelector
      v-model:visible="popups.orderType"
      v-model="selectedOrderTypeValues"
      :order-types="orderTypeOptions"
      :multiple="true"
      :max-columns="1"
      :show-icon="true"
      :top-offset="channelSelectorTop"
      :left-offset="310"
      :show-all-option="true"
      :all-option-value="''"
      :all-option-text="'全部'"
      @confirm="handleOrderTypeConfirm"
      @cancel="popups.orderType = false"
      @close="popups.orderType = false"
    />

    <!-- 日期筛选弹窗 -->
    <SyDateSelector
      v-model:visible="popups.date"
      v-model="selectedValues.date"
      :top-offset="channelSelectorTop"
      :left-offset="370"
      @confirm="handleDateConfirm"
      @cancel="popups.date = false"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import {
  orderFiltersProps,
  orderFiltersEmits,
  type FilterTexts,
  type FilterBooleans,
  type FilterOption,
} from './filters'
import SyChannelSelector from '@/components/sy-channel-selector'
import SyStoreSelector from '@/components/sy-store-selector'
import SyOrderTypeSelector from '@/components/sy-order-type-selector'
import SyDateSelector from '@/components/sy-date-selector'
import { channelIconMap, getChannelIcon } from '@/components/sy-channel-selector/utils'
import { useShopStore } from '@/store/shop'
import { type DateSelectionPayload } from '@/components/sy-date-selector'
import { isNeedFilters } from '@/pages/index'

// 定义组件名称
defineOptions({ name: 'SyOrderFilters' })

// 定义组件属性
const props = defineProps(orderFiltersProps)

// 定义事件
const emit = defineEmits(orderFiltersEmits)

// 弹窗显示状态
const popups = reactive({
  channel: false,
  store: false,
  orderType: false,
  date: false,
})

// 定义更详细的类型
interface SelectedFilterValues {
  channel: string[]
  store: string[]
  orderType: string
  date: string
  singleChannel: string[]
  multipleChannel: string[]
}

// 选中的值
const selectedValues = reactive<SelectedFilterValues>({
  channel: [] as string[],
  store: [] as string[],
  orderType: '',
  date: 'today',
  singleChannel: [] as string[],
  multipleChannel: [] as string[],
})

// 订单类型选择器使用的值（适配新组件接口）
const selectedOrderTypeValues = ref<string[]>([])

// 初始化订单类型选择器的值
if (props.currentFilters.orderTypeText === '全部') {
  // 如果当前显示为"全部"，则设置为空字符串（全选值）
  selectedOrderTypeValues.value = ['10', '20']
} else if (
  props.currentFilters.orderTypeText &&
  props.currentFilters.orderTypeText !== '订单类型'
) {
  // 如果有选中值，则转换为数组
  const orderTypeValue = props.currentFilters.orderTypeText || ''
  if (orderTypeValue.includes('已选')) {
    // 如果是多选（"已选N个类型"格式），则需要解析出实际的选中值
    const orderTypeCodes = selectedValues.orderType.split(',')
    selectedOrderTypeValues.value = orderTypeCodes
  } else {
    // 单选情况
    selectedOrderTypeValues.value = [selectedValues.orderType]
  }
}

const channelFilter = ref(null)
const channelSelectorTop = ref(0)

// 方法：获取“渠道”文本容器的位置
const getChannelPosition = () => {
  const query = uni.createSelectorQuery()
  query
    .select('.sy-order-filters-item')
    .boundingClientRect((res) => {
      if (res) {
        // res.top 是 px 值，将其转换为 rpx
        const systemInfo = uni.getSystemInfoSync()
        const rpx = (res.top * systemInfo.windowHeight) / systemInfo.windowWidth
        channelSelectorTop.value = rpx - systemInfo.statusBarHeight || 0
      }
    })
    .exec()
}

watch(
  () => props.orderTypeOptions,
  (newVal) => {
    console.log('监听到订单类型：', newVal, selectedValues.orderType)
    if (!selectedValues.orderType && newVal.length > 0) {
      const codes = newVal.map((item) => item.code)
      selectedValues.orderType = codes.join(',')
      selectedOrderTypeValues.value = codes
    }
  },
  { immediate: true },
)

// 监听订单类型值变化
watch(
  () => selectedValues.orderType,
  (newVal) => {
    console.log('OrderFilters - orderType选中值变化:', newVal)

    // 如果有值且包含逗号，说明是多选
    if (newVal && newVal.includes(',')) {
      // 分割为数组
      selectedOrderTypeValues.value = newVal.split(',')
    } else if (newVal) {
      // 单选情况
      selectedOrderTypeValues.value = [newVal]
    } else {
      // 未选择任何值，默认为为空
      selectedOrderTypeValues.value = ['']
    }
  },
  { immediate: true },
)

// 跟踪selectedValues.store变化
watch(
  () => selectedValues.store,
  (newVal) => {
    console.log('OrderFilters - store选中值变化:', newVal)
  },
  { deep: true },
)

watch(
  () => props.channelOptions,
  (newVal) => {
    // 如果没有选中的渠道，则初始化默认全选
    if (selectedValues.channel.length === 0) {
      selectedValues.channel = [...props.channelOptions.map((item) => item.code)]
    }
  },
  { deep: true },
)

watch(
  () => props.isNeedFilters.isMultipleChannelMode,
  (newVal) => {
    // 如果没有选中的渠道，则初始化默认全选
    if (newVal) {
      console.log('选择模式 isMultipleChannelMode', newVal, selectedValues.multipleChannel)
      selectedValues.channel = [...selectedValues.multipleChannel]
    } else {
      console.log('单渠道模式:', selectedValues.singleChannel)
      if (selectedValues.singleChannel.length > 0) {
        selectedValues.channel = [...selectedValues.singleChannel]
      } else {
        // 如果没有选择则默认选第一个渠道
        selectedValues.channel = [props.channelOptions[0].code]
        selectedValues.singleChannel = [props.channelOptions[0].code]
      }
    }
  },
  { deep: true },
)

watch(
  () => props.currentFilters.channelCodes,
  (newVal) => {
    // 如果没有选中的渠道，则初始化默认全选
    if (selectedValues.channel.length > 0) {
      selectedValues.channel = [...newVal]
    }
  },
  { deep: true },
)

// 初始化选中的门店值
if (props.currentFilters.shopIds && props.currentFilters.shopIds.length > 0) {
  selectedValues.store = [...props.currentFilters.shopIds]
}

// 从店铺Store获取门店数据
const shopStore = useShopStore()

// 处理门店选项数据
const storeOptions = computed(() => {
  // 从shopStore获取门店数据
  const shopStore = useShopStore()

  // 检查数据结构
  const currentShop = shopStore.currentShop?.raw as any
  if (currentShop?.shopDtoList) {
    const shopDtoList = currentShop.shopDtoList || []
    return shopDtoList.map((shop: any) => ({
      code: shop.code,
      id: shop.id || shop.code, // 添加id字段，如果没有则使用code
      // 优先使用name字段，如果没有则使用alias
      name: shop.name || shop.alias,
    }))
  }

  // 如果还没有数据，返回空数组
  return []
})

// 处理渠道选项，添加图标和描述，并添加防护措施
const processedChannelOptions = computed(() => {
  // 添加防护措施，确保在数据未加载时不会报错
  if (!props.channelOptions || props.channelOptions.length === 0) {
    console.log('渠道数据为空，可能还未加载完成')
    return []
  }

  try {
    return processChannelData(props.channelOptions)
  } catch (error) {
    console.error('处理渠道数据出错:', error)
    return []
  }
})

// 处理渠道数据，添加图标
function processChannelData(channels: any[]): any[] {
  if (!channels || !Array.isArray(channels)) return []

  return channels.map((channel) => {
    // 使用智能匹配获取图标
    return {
      ...channel,
      icon: getChannelIcon(channel),
    }
  })
}

// 处理订单类型选项数据
const orderTypeOptions = computed(() => {
  // 确保在数据未加载时不会报错
  if (!props.orderTypeOptions || props.orderTypeOptions.length === 0) {
    return []
  }

  // 只返回原始选项数组，不再添加全部选项
  return [...props.orderTypeOptions]
})

// 处理订单类型确认
const handleOrderTypeConfirm = (codes: string[], items: any[]) => {
  console.log('订单类型确认:', codes, items)

  if (codes.length === 0) {
    uni.showToast({
      title: '至少要勾选一种订单类型',
      icon: 'none',
    })
    return
  }
  // 获取所有有效的订单类型代码
  const allTypeCodes = props.orderTypeOptions.map((item) => item.code)

  // 检查是否选中了全部选项（空字符串表示全部选项）
  const hasAllOption = codes.includes('')

  if (hasAllOption || (codes.length > 0 && codes.length === allTypeCodes.length)) {
    // 如果选择了"全部"选项或者选择了所有可用选项，显示"全部"，并发送所有订单类型代码
    selectFilterItem('orderType', {
      code: allTypeCodes.join(','),
      name: '全部',
    })
  } else if (codes.length === 0) {
    // 如果没有选中任何项，默认显示"全部"，但发送空字符串表示未选择
    selectFilterItem('orderType', {
      code: '',
      name: '全部',
    })
  } else if (codes.length === 1) {
    // 单个选择
    const code = codes[0]
    const selectedItem = items.find((item) => item.code === code)
    const name = selectedItem ? selectedItem.name : code
    selectFilterItem('orderType', { code, name })
  } else {
    // 多选情况下，显示"已选择N个类型"
    selectFilterItem('orderType', {
      code: codes.join(','),
      name: `已选${codes.length}个类型`,
    })
  }

  // 关闭弹窗
  popups.orderType = false
}

// 显示筛选弹窗
const showFilterPopup = (type: 'channel' | 'store' | 'orderType' | 'date') => {
  popups[type] = true
  emit('filter-item-click', type)
}

// 点击筛选项处理函数
const onFilterItemClick = (type: 'channel' | 'store' | 'orderType' | 'date') => {
  try {
    getChannelPosition()
    // 检查渠道数据是否已加载
    if (type === 'channel' && (!props.channelOptions || props.channelOptions.length === 0)) {
      console.log('渠道数据未加载完成，不打开选择器')
      emit('filter-error', '渠道数据加载中...')
      return
    }

    // 检查门店数据是否已加载
    if (type === 'store' && storeOptions.value.length === 0) {
      console.log('门店数据未加载完成，不打开选择器')
      emit('filter-error', '门店数据加载中...')
      return
    }

    // 关闭其他弹窗
    Object.keys(popups).forEach((key) => {
      if (key !== type) {
        popups[key as keyof typeof popups] = false
      }
    })

    // 打开选择的弹窗
    popups[type] = true

    // 通知父组件
    emit('filter-item-click', type)
  } catch (error) {
    console.error('筛选项点击处理出错:', error)
    emit('filter-error', '操作失败，请重试')
  }
}

// 处理日期选择确认
const handleDateConfirm = (payload: DateSelectionPayload) => {
  console.log('日期选择确认:', payload)

  // 如果label中包含"今日"，则只显示"今日"文本
  const dateLabel = payload.label?.includes('今日') ? '今日' : payload.label

  // 使用selectFilterItem函数来处理日期选择
  selectFilterItem('date', {
    value: payload.value,
    label: dateLabel,
    // 存储开始和结束时间，以便在fetchOrders时使用
    _startPlaceTime: payload.startPlaceTime,
    _endPlaceTime: payload.endPlaceTime,
  })
}

// 选择筛选项
const selectFilterItem = (type: 'channel' | 'store' | 'orderType' | 'date', item: any) => {
  try {
    // 只有日期筛选选择后关闭弹窗，其他筛选项保持打开状态
    if (type === 'date') {
      popups[type] = false
    }

    // 更新选中值（注意渠道和门店是数组）
    if (type === 'channel') {
      // 渠道选择器已经处理好了数组，但确保是新数组且值有效
      let channelCodes = Array.isArray(item.code) ? [...item.code] : [item.code]

      // 打印原始数据，帮助调试
      console.log('渠道筛选接收到的原始值:', channelCodes)

      // 清理可能的无效值
      channelCodes = channelCodes
        .map((code) => String(code))
        .filter(
          (code) => code !== 'false' && code !== 'undefined' && code !== 'null' && code !== '',
        )

      console.log('渠道筛选处理后的值:', channelCodes, props.isNeedFilters.isMultipleChannelMode)

      // 更新选中值
      if (props.isNeedFilters.isMultipleChannelMode) {
        selectedValues.multipleChannel = channelCodes
        selectedValues.channel = [...selectedValues.multipleChannel]
      } else {
        selectedValues.singleChannel = [channelCodes[0]]
        selectedValues.channel = [...selectedValues.singleChannel]
      }
      console.log('渠道筛选最终值:', selectedValues.channel)

      // 特别确保更新到父组件的是正确的值
      emit('filter-change', type, channelCodes, item.name)
    } else if (type === 'store') {
      // 门店选择器已经处理好了数组，确保是新数组
      selectedValues.store = Array.isArray(item.code) ? [...item.code] : [item.code]
      console.log('门店筛选值更新为:', selectedValues.store)

      emit('filter-change', type, selectedValues.store, '门店列表')
    } else if (type === 'orderType') {
      // 处理订单类型选择
      const value = item.code
      // 使用类型断言解决类型问题
      selectedValues.orderType = value as string
      emit('filter-change', type, value, '订单类型')
    } else if (type === 'date') {
      const value = item.value
      // 使用类型断言解决类型问题
      selectedValues.date = value as string

      // 检查是否有额外的时间范围信息
      const extraData =
        item._startPlaceTime && item._endPlaceTime
          ? {
              startPlaceTime: item._startPlaceTime,
              endPlaceTime: item._endPlaceTime,
            }
          : undefined

      // 使用传入的label，而不是写死为"今日"
      emit('filter-change', type, value, item.label || '今日', extraData)
    } else {
      // 这段代码应该不会执行，但保留以防万一
      const value = type === 'date' ? item.value : item.code
      // 使用类型断言解决类型问题
      if (type === 'date') {
        selectedValues.date = value as string
      } else if (type === 'orderType') {
        selectedValues.orderType = value as string
      }
      emit('filter-change', type, value, type === 'date' ? '今日' : '全部')
    }
  } catch (error) {
    console.error('selectFilterItem 执行出错:', error)
    // 使用父组件的事件机制来处理错误
    emit('filter-error', '筛选处理出错，请重试')
  }
}

// 处理渠道确认
const handleChannelConfirm = (value: string[], labels: string[]) => {
  try {
    console.log('渠道确认:', value, labels)

    // 确保 value 是数组类型
    const channelCodes = Array.isArray(value) ? value : [value]

    // 过滤无效值
    const validCodes = channelCodes
      .map((v) => String(v))
      .filter((v) => v !== 'false' && v !== 'undefined' && v !== 'null' && v !== '')

    // 更新筛选项
    selectFilterItem('channel', {
      code: channelCodes,
      name: labels,
    })
  } catch (error) {
    console.error('处理渠道筛选确认事件出错:', error)
    // 使用父组件的事件机制来处理错误
    emit('filter-error', '处理渠道筛选出错，请重试')
  }
}

// 处理门店确认
const handleStoreConfirm = (value: string[], labels: string[]) => {
  try {
    console.log('门店确认事件触发:', { value, labels, type: typeof value })

    // 1. 数据验证与安全处理
    if (!value || (!Array.isArray(value) && typeof value !== 'string')) {
      console.warn('门店确认：无效的值参数', value)
      emit('filter-error', '选择的门店数据无效')
      return
    }

    // 2. 规范化为数组格式
    const rawShopIds = Array.isArray(value) ? [...value] : [String(value)]
    console.log('规范化后的原始ID数组:', rawShopIds)

    // 3. 检查是否包含全选标识 'ALL'
    const hasAllOption = rawShopIds.includes('ALL') || rawShopIds.includes('all')

    // 4. 过滤处理后的有效ID（用于内部状态）
    const validShopIds = rawShopIds
      .filter((id) => id != null && id !== undefined && id !== '')
      .map((id) => String(id))
      .filter((id) => id !== 'ALL' && id !== 'all')

    console.log('过滤后的有效门店ID:', validShopIds)

    // 5. 处理显示标签
    let displayLabel = '门店列表'

    if (hasAllOption) {
      displayLabel = '全部门店'
    } else if (Array.isArray(labels) && labels.length > 0) {
      // 过滤掉可能的"全部"标签
      const validLabels = labels.filter(
        (label) => label && label !== 'ALL' && label !== 'all' && !label.includes('全部门店'),
      )

      if (validLabels.length === 1) {
        displayLabel = validLabels[0]
      } else if (validLabels.length > 1) {
        displayLabel = `已选${validLabels.length}个门店`
      }
    } else if (typeof labels === 'string' && labels !== 'ALL') {
      displayLabel = labels
    }

    console.log('最终显示标签:', displayLabel)

    // 6. 更新内部状态（始终使用过滤后的有效ID）
    selectedValues.store = validShopIds
    console.log('更新selectedValues.store为:', selectedValues.store)

    // 7. 向父组件发送事件
    // 如果选择了全部，传递空数组表示不限制门店
    // const emitValue = hasAllOption ? [] : validShopIds
    emit('filter-change', 'store', validShopIds, displayLabel)

    console.log('向父组件发送门店筛选事件:', {
      type: 'store',
      value: validShopIds,
      label: displayLabel,
    })

    // 8. 关闭弹窗
    popups.store = false
  } catch (error) {
    console.error('门店确认处理失败:', error)

    // 确保错误不会阻止弹窗关闭
    popups.store = false

    // 向父组件发送错误事件
    emit('filter-error', '门店选择处理失败，请重试')
  }
}
</script>

<style lang="scss" scoped>
.sy-order-filters {
  display: flex;
  align-items: center;
  height: 90rpx; // 45px * 2
  padding: 0 24rpx; // 12px * 2
  overflow-x: auto; // 允许水平滚动
  white-space: nowrap; // 防止文本换行
  background-color: #ffffff;

  &-item {
    display: flex;
    flex-shrink: 0; // 防止项目被挤压
    align-items: center;
    margin-right: 48rpx; // 24px * 2
    cursor: pointer;

    &:last-child {
      margin-right: 0;
    }
  }

  &-text {
    max-width: 180rpx; // 限制最大宽度
    margin-right: 4rpx; // 2px * 2
    overflow: hidden;
    font-size: 28rpx; // 调小字体尺寸
    font-weight: 600; // Medium/Semibold
    line-height: 40rpx;
    color: #222222;
    text-overflow: ellipsis; // 文本过长时显示省略号
    white-space: nowrap;
  }

  &-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    // transform: rotate(90deg);
  }
}

// 筛选弹窗样式
.filter-popup {
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    padding: 0 24rpx;
    border-bottom: 1px solid #f2f2f4;
  }

  &-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #222222;
  }

  &-close {
    padding: 10rpx;
  }

  &-content {
    max-height: 60vh;
    padding: 0 24rpx;
    overflow-y: auto;
  }

  &-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    border-bottom: 1px solid #f2f2f4;

    &:last-child {
      border-bottom: none;
    }

    &-left {
      display: flex;
      align-items: center;
    }

    &-checkbox {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28rpx;
      height: 28rpx;
      margin-right: 20rpx;
      border: 1px solid #cccccc;
      border-radius: 4rpx;

      &.is-checked {
        background-color: #0064ff;
        border-color: #0064ff;
      }
    }

    &-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;
      overflow: hidden;
      border-radius: 50%;

      .channel-icon {
        width: 100%;
        height: 100%;
      }
    }

    &-text {
      font-size: 30rpx;
      color: #222222;
    }

    &-active {
      .filter-popup-item-text {
        color: #0064ff;
      }
    }
  }
}

.sy-selector-container {
  position: relative;
  z-index: 200;
}
</style>
