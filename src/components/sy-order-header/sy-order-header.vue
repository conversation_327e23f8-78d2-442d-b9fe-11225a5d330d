<template>
  <view class="sy-order-header">
    <view class="sy-order-header-tabs">
      <!-- 使用 v-if 和 useAuth 钩子控制标签显示 -->
      <view
        v-if="hasPermission(PERMISSION_CODES.ORDER_TAB)"
        class="sy-order-header-tab-item"
        :class="{ active: props.activeTab === 'order' }"
        @click="emit('tab-change', 'order')"
      >
        <text class="sy-order-header-tab-text">订单</text>
        <view v-if="props.activeTab === 'order'" class="sy-order-header-tab-indicator" />
      </view>

      <view
        v-if="hasPermission(PERMISSION_CODES.AFTER_SALE_TAB)"
        class="sy-order-header-tab-item"
        :class="{ active: props.activeTab === 'after-sale' }"
        @click="emit('tab-change', 'after-sale')"
      >
        <text class="sy-order-header-tab-text">售后</text>
        <view v-if="props.activeTab === 'after-sale'" class="sy-order-header-tab-indicator" />
      </view>

      <view
        v-if="hasPermission(PERMISSION_CODES.DAMAGE_APPEAL_TAB)"
        class="sy-order-header-tab-item"
        :class="{ active: props.activeTab === 'damage-appeal' }"
        @click="emit('tab-change', 'damage-appeal')"
      >
        <text class="sy-order-header-tab-text">申诉</text>
        <view v-if="props.activeTab === 'damage-appeal'" class="sy-order-header-tab-indicator" />
      </view>
    </view>

    <view class="sy-order-header-actions">
      <view
        v-if="props.showPreOrderButton"
        class="sy-order-header-pre-order-btn"
        @click="emit('pre-order-click')"
      >
        <text class="sy-order-header-pre-order-text">预订单</text>
        <wd-icon name="arrow-right" size="20rpx" color="#222222" />
      </view>
      <view class="sy-order-header-search-btn" @click="emit('search-click')">
        <wd-icon name="search" size="40rpx" color="#222222" />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { orderHeaderProps, type ActiveTabType } from './order-header'
import { useAuth } from '@/hooks/useAuth'
import { PERMISSION_CODES } from '@/config/permissions'

const props = defineProps(orderHeaderProps)

const emit = defineEmits<{
  (e: 'tab-change', tab: ActiveTabType): void
  (e: 'pre-order-click'): void
  (e: 'search-click'): void
}>()

// 权限控制
const { hasPermission } = useAuth()
</script>

<style lang="scss" scoped>
.sy-order-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 94rpx;
  padding: 0 24rpx;
  background-color: #ffffff;
  box-shadow: inset 0px -2rpx 0px 0px #f2f2f4;

  &-tabs {
    display: flex;
    gap: 36rpx;
    align-items: center;
  }

  &-tab-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 12rpx;
    cursor: pointer;

    .sy-order-header-tab-text {
      font-size: 40rpx;
      font-weight: normal;
      line-height: 56rpx;
      color: #666666;
    }

    &.active {
      .sy-order-header-tab-text {
        font-weight: 600;
        color: #222222;
      }
      .sy-order-header-tab-indicator {
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 38rpx;
        height: 10rpx;
        background-color: #f33429;
        border-radius: 8rpx;
        transform: translateX(-50%);
      }
    }
  }

  &-actions {
    display: flex;
    gap: 24rpx;
    align-items: center;
  }

  &-pre-order-btn {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48rpx;
    padding: 0 20rpx;
    cursor: pointer;
    background-color: #ffffff;
    border: 2rpx solid #222222;
    border-radius: 24rpx;

    .sy-order-header-pre-order-text {
      margin-right: 4rpx;
      font-size: 24rpx;
      font-weight: 600;
      line-height: 34rpx;
      color: #222222;
    }
  }

  &-search-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48rpx;
    height: 48rpx;
    cursor: pointer;
  }
}
</style>
