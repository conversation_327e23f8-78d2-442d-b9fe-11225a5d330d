<template>
  <view class="mqtt-status" v-if="showStatus">
    <view class="mqtt-status-header" @click="toggleExpanded">
      <view class="status-indicator" :class="statusClass"></view>
      <text class="status-text">{{ statusText }}</text>
      <wd-icon :name="isExpanded ? 'arrow-up' : 'arrow-down'" size="24rpx" />
    </view>

    <view v-if="isExpanded" class="mqtt-status-details">
      <view class="detail-item">
        <text class="label">连接状态:</text>
        <text class="value" :class="isConnected ? 'success' : 'error'">
          {{ isConnected ? '已连接' : '未连接' }}
        </text>
      </view>

      <view class="detail-item">
        <text class="label">连接中:</text>
        <text class="value">{{ isConnecting ? '是' : '否' }}</text>
      </view>

      <view class="detail-item">
        <text class="label">重连次数:</text>
        <text class="value">{{ reconnectAttempts }}/{{ maxReconnectAttempts }}</text>
      </view>

      <view class="actions">
        <wd-button size="small" type="primary" @click="handleReconnect" :disabled="isConnecting">
          {{ isConnecting ? '连接中...' : '重连' }}
        </wd-button>

        <wd-button size="small" type="info" @click="handleGetInfo">获取信息</wd-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  isConnected?: boolean
  isConnecting?: boolean
  reconnectAttempts?: number
  maxReconnectAttempts?: number
  showStatus?: boolean
}

interface Emits {
  (e: 'reconnect'): void
  (e: 'get-info'): void
}

const props = withDefaults(defineProps<Props>(), {
  isConnected: false,
  isConnecting: false,
  reconnectAttempts: 0,
  maxReconnectAttempts: 5,
  showStatus: true,
})

const emit = defineEmits<Emits>()

const isExpanded = ref(false)

const statusClass = computed(() => {
  if (props.isConnecting) return 'connecting'
  if (props.isConnected) return 'connected'
  return 'disconnected'
})

const statusText = computed(() => {
  if (props.isConnecting) return 'MQTT 连接中...'
  if (props.isConnected) return 'MQTT 已连接'
  return 'MQTT 未连接'
})

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

const handleReconnect = () => {
  emit('reconnect')
}

const handleGetInfo = () => {
  emit('get-info')
}
</script>

<style lang="scss" scoped>
.mqtt-status {
  position: fixed;
  top: 200rpx;
  right: 20rpx;
  z-index: 9999;
  min-width: 200rpx;
  padding: 16rpx;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 12rpx;

  .mqtt-status-header {
    display: flex;
    gap: 8rpx;
    align-items: center;
    cursor: pointer;

    .status-indicator {
      width: 16rpx;
      height: 16rpx;
      border-radius: 50%;

      &.connected {
        background-color: #52c41a;
      }

      &.connecting {
        background-color: #faad14;
        animation: pulse 1s infinite;
      }

      &.disconnected {
        background-color: #ff4d4f;
      }
    }

    .status-text {
      flex: 1;
      font-size: 24rpx;
      color: white;
    }
  }

  .mqtt-status-details {
    padding-top: 16rpx;
    margin-top: 16rpx;
    border-top: 1px solid rgba(255, 255, 255, 0.2);

    .detail-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8rpx;

      .label {
        font-size: 22rpx;
        color: rgba(255, 255, 255, 0.7);
      }

      .value {
        font-size: 22rpx;
        color: white;

        &.success {
          color: #52c41a;
        }

        &.error {
          color: #ff4d4f;
        }
      }
    }

    .actions {
      display: flex;
      gap: 8rpx;
      margin-top: 16rpx;
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
