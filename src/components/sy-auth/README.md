# SyAuth 权限控制组件

## 功能描述

`SyAuth` 组件用于控制页面元素的权限显示和交互。支持两种模式：

- `hidden` 模式：无权限时隐藏元素
- `toast` 模式：无权限时显示但禁用，点击弹 toast 提示

## 属性 Props

| 属性名           | 类型                | 默认值  | 必填 | 说明                                                     |
| ---------------- | ------------------- | ------- | ---- | -------------------------------------------------------- |
| code             | string \| string[]  | -       | 是   | 所需的权限码或权限码数组                                 |
| mode             | 'all' \| 'any'      | 'all'   | 否   | 权限检查模式：'all' 需要所有权限，'any' 拥有任意一个即可 |
| showPlaceholder  | boolean             | false   | 否   | 当没有权限时是否显示占位内容                             |
| showOnLoading    | boolean             | false   | 否   | 权限未加载时是否显示内容                                 |
| toastMode        | 'hidden' \| 'toast' | 'toast' | 否   | 权限渲染模式                                             |
| autoDisableStyle | boolean             | true    | 否   | 是否自动应用禁用样式（grayscale + opacity）              |

## 插槽 Slots

### 默认插槽

在 `toast` 模式下，插槽会接收以下参数：

| 参数名              | 类型     | 说明                   |
| ------------------- | -------- | ---------------------- |
| isAllowed           | boolean  | 是否有权限             |
| onNoPermissionClick | function | 无权限时的点击处理函数 |
| disableStyle        | object   | 自动计算的禁用样式对象 |
| disableClass        | object   | 自动计算的禁用类名对象 |

## 使用示例

### 基础用法

```vue
<template>
  <!-- 简单按钮权限控制 -->
  <SyAuth code="user_edit" toast-mode="toast" v-slot="{ isAllowed, onNoPermissionClick }">
    <button @click="isAllowed ? handleEdit() : onNoPermissionClick()">编辑用户</button>
  </SyAuth>
</template>
```

### 自动禁用样式（推荐）

```vue
<template>
  <!-- 使用自动禁用样式，无需手动设置 grayscale 和 opacity -->
  <SyAuth
    code="jidanbao_menu_index_order_acceptbtn"
    toast-mode="toast"
    v-slot="{ isAllowed, onNoPermissionClick, disableStyle, disableClass }"
  >
    <view
      class="action-btn"
      :class="disableClass"
      :style="disableStyle"
      @click="isAllowed ? handleAccept() : onNoPermissionClick()"
    >
      立即接单
    </view>
  </SyAuth>
</template>
```

### 自定义禁用样式

```vue
<template>
  <!-- 关闭自动禁用样式，自定义样式 -->
  <SyAuth
    code="user_delete"
    toast-mode="toast"
    :auto-disable-style="false"
    v-slot="{ isAllowed, onNoPermissionClick }"
  >
    <button
      :class="{ 'custom-disabled': !isAllowed }"
      :style="{
        filter: !isAllowed ? 'blur(1px)' : 'none',
        opacity: !isAllowed ? 0.3 : 1,
      }"
      @click="isAllowed ? handleDelete() : onNoPermissionClick()"
    >
      删除用户
    </button>
  </SyAuth>
</template>
```

### 隐藏模式

```vue
<template>
  <!-- 无权限时完全隐藏 -->
  <SyAuth code="admin_panel" toast-mode="hidden">
    <div class="admin-panel">管理员面板</div>
  </SyAuth>
</template>
```

### 多权限检查

```vue
<template>
  <!-- 需要拥有所有权限 -->
  <SyAuth
    code="['user_read', 'user_write']"
    mode="all"
    toast-mode="toast"
    v-slot="{ isAllowed, onNoPermissionClick, disableStyle, disableClass }"
  >
    <button
      :class="disableClass"
      :style="disableStyle"
      @click="isAllowed ? handleAction() : onNoPermissionClick()"
    >
      用户管理
    </button>
  </SyAuth>

  <!-- 拥有任意一个权限即可 -->
  <SyAuth
    code="['user_read', 'user_write']"
    mode="any"
    toast-mode="toast"
    v-slot="{ isAllowed, onNoPermissionClick, disableStyle, disableClass }"
  >
    <button
      :class="disableClass"
      :style="disableStyle"
      @click="isAllowed ? handleAction() : onNoPermissionClick()"
    >
      用户操作
    </button>
  </SyAuth>
</template>
```

## 样式说明

### 自动禁用样式

当 `autoDisableStyle` 为 `true`（默认）时，组件会自动应用以下样式：

```css
.sy-auth-no-permission {
  pointer-events: none !important;
  cursor: not-allowed !important;
  user-select: none;
  filter: grayscale(100%);
  opacity: 0.7;
}
```

### 自定义样式

可以通过 CSS 覆盖默认样式：

```scss
.sy-auth-no-permission {
  // 自定义禁用样式
  filter: blur(2px);
  opacity: 0.5;
  background-color: #f5f5f5;
}
```

## 最佳实践

1. **推荐使用自动禁用样式**：设置 `autoDisableStyle="true"`，使用 `disableStyle` 和 `disableClass`
2. **统一权限码命名**：使用有意义的权限码，如 `jidanbao_menu_index_order_acceptbtn`
3. **合理使用模式**：根据业务需求选择 `hidden` 或 `toast` 模式
4. **错误处理**：在点击处理函数中添加适当的错误处理逻辑

## 注意事项

1. 确保权限系统已正确初始化
2. 权限码必须与后端权限系统保持一致
3. 在组件销毁时会自动清理权限检查
4. 支持动态权限更新，权限变化时会自动重新渲染
