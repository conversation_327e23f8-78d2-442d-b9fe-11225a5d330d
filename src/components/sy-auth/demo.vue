<template>
  <view class="sy-auth-demo">
    <text class="demo-title">SyAuth 组件使用示例</text>

    <!-- 基础用法 -->
    <view class="demo-section">
      <text class="section-title">基础用法</text>
      <SyAuth code="user_edit" toast-mode="toast" v-slot="{ isAllowed, onNoPermissionClick }">
        <button @click="isAllowed ? handleEdit() : onNoPermissionClick()">编辑用户</button>
      </SyAuth>
    </view>

    <!-- 自动禁用样式（推荐） -->
    <view class="demo-section">
      <text class="section-title">自动禁用样式（推荐）</text>
      <SyAuth
        code="jidanbao_menu_index_order_acceptbtn"
        toast-mode="toast"
        v-slot="{ isAllowed, onNoPermissionClick, disableStyle, disableClass }"
      >
        <view
          class="action-btn"
          :class="disableClass"
          :style="disableStyle"
          @click="isAllowed ? handleAccept() : onNoPermissionClick()"
        >
          立即接单
        </view>
      </SyAuth>
    </view>

    <!-- 自定义禁用样式 -->
    <view class="demo-section">
      <text class="section-title">自定义禁用样式</text>
      <SyAuth
        code="user_delete"
        toast-mode="toast"
        :auto-disable-style="false"
        v-slot="{ isAllowed, onNoPermissionClick }"
      >
        <button
          :class="{ 'custom-disabled': !isAllowed }"
          :style="{
            filter: !isAllowed ? 'blur(1px)' : 'none',
            opacity: !isAllowed ? 0.3 : 1,
          }"
          @click="isAllowed ? handleDelete() : onNoPermissionClick()"
        >
          删除用户
        </button>
      </SyAuth>
    </view>

    <!-- 隐藏模式 -->
    <view class="demo-section">
      <text class="section-title">隐藏模式</text>
      <SyAuth code="admin_panel" toast-mode="hidden">
        <div class="admin-panel">管理员面板</div>
      </SyAuth>
    </view>

    <!-- 多权限检查 -->
    <view class="demo-section">
      <text class="section-title">多权限检查</text>
      <SyAuth
        code="['user_read', 'user_write']"
        mode="any"
        toast-mode="toast"
        v-slot="{ isAllowed, onNoPermissionClick, disableStyle, disableClass }"
      >
        <button
          :class="disableClass"
          :style="disableStyle"
          @click="isAllowed ? handleAction() : onNoPermissionClick()"
        >
          用户操作
        </button>
      </SyAuth>
    </view>
  </view>
</template>

<script setup lang="ts">
import SyAuth from './SyAuth.vue'

const handleEdit = () => {
  console.log('编辑用户')
}

const handleAccept = () => {
  console.log('立即接单')
}

const handleDelete = () => {
  console.log('删除用户')
}

const handleAction = () => {
  console.log('用户操作')
}
</script>

<style lang="scss" scoped>
.sy-auth-demo {
  padding: 32rpx;
}

.demo-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 32rpx;
}margin-bottom: 32rpx;


.demo-section {
  padding: 24rpx;
  border: 2rpx solid #eee;
  border-radius: 16rpx;
}pddg2
mrg-bottom8
.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #666;
  margin-bottom: 24rpx;
  display: block;
  margin-bottom: 24rpx;
  display: block;
}

  color: white;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;

fntsze2
bucolor: white;
  tton {
  backgraucd: #1890ff;
  bordergradousd:#52c41a;
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  poddine: 16rpx 32rpx;
  f:nt-sizene8rpx
  font-size: 28rpx;
}bcknd#52c41a
.admin-panel {
  bardergradousd:#f0f0f0;
  padding: 24rpx;
  border-radius: 8rpx;
  text-align: center;

clo#666
.custom-disabled {
  babkgraund: #f0f0f0;
  bkgder-radiusro8rpx #d9d9d9 !important;
  color: #999 !important;
}
</style>
olobakgrundddd
