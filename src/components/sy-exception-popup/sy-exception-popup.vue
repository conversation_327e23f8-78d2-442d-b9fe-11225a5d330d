<template>
  <wd-popup
    v-model="show"
    position="bottom"
    custom-style="border-radius: 24rpx 24rpx 0 0; background-color: #f1f1f1;"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <view class="sy-exception-popup">
      <!-- 头部 -->
      <view class="popup-header">
        <text class="popup-title">异常提醒</text>
        <view class="close-icon" @click="handleClose">
          <wd-icon name="arrow-down" size="32rpx" color="#999999" />
        </view>
      </view>

      <!-- 内容区域 -->
      <view class="popup-content">
        <view class="content-left">
          <wd-icon name="warning-bold" size="36rpx" color="#F33429" />
          <view class="text-group">
            <text class="main-text">来单不响异常</text>
            <text class="sub-text">未开启新消息通知，请开启</text>
          </view>
        </view>
        <view class="content-right" @click="handleGoSetting">
          <text class="action-text ignore-text" @click.stop="handleIgnore">忽略</text>
          <view class="divider" />
          <text class="action-text setting-text">去设置</text>
          <wd-icon name="arrow-right" size="24rpx" color="#F33429" />
        </view>
      </view>

      <!-- 底部安全区 -->
      <view class="safe-area-bottom" :style="{ height: isTabbarPage ? '80rpx' : '0' }"></view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { exceptionPopupProps } from './exception-popup'

const props = defineProps(exceptionPopupProps)

const emit = defineEmits<{
  'update:visible': [value: boolean]
  ignore: []
  goSetting: []
}>()

const show = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const handleClose = () => {
  show.value = false
  emit('ignore')
}

const handleIgnore = () => {
  emit('ignore')
  handleClose()
}

const handleGoSetting = () => {
  emit('goSetting')
  handleClose()
}

// 检测当前页面是否为 tabbar 页面
const isTabbarPage = ref(false)

const checkTabbarPage = () => {
  try {
    const pages = getCurrentPages()
    if (pages && pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage.route || ''

      const tabbarPages = [
        'pages/index/index',
        'pages/about/about',
        'pages/dishManagement/index',
        'pages/my/index',
        'pages/storeDetail/index',
      ]

      isTabbarPage.value = tabbarPages.some((path) => currentRoute.includes(path))
    }
  } catch (error) {
    console.error('检测 tabbar 页面失败:', error)
    isTabbarPage.value = false
  }
}

onMounted(() => {
  checkTabbarPage()
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
  },
}
</script>

<style lang="scss" scoped>
.sy-exception-popup {
  width: 100%;
  overflow: hidden;
  background-color: #f1f1f1;
  border-radius: 24rpx 24rpx 0 0;
}

.popup-header {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 92rpx; /* 46px * 2 */
  padding: 24rpx 32rpx;
  background-color: #fafafa;

  .popup-title {
    font-size: 32rpx; /* 16px * 2 */
    font-weight: 600;
    color: #222222;
  }

  .close-icon {
    padding: 10rpx;
  }
}

.popup-content {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 164rpx; /* 82px * 2 */
  padding: 0 32rpx;
  background-color: #ffffff;

  .content-left {
    display: flex;
    align-items: center;
  }

  .text-group {
    display: flex;
    flex-direction: column;
    margin-left: 16rpx;
  }

  .main-text {
    font-size: 32rpx; /* 16px * 2 */
    font-weight: 400;
    color: #222222;
  }

  .sub-text {
    margin-top: 4rpx;
    font-size: 26rpx; /* 13px * 2 */
    color: #999999;
  }

  .content-right {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .action-text {
    font-size: 26rpx; /* 13px * 2 */
  }

  .ignore-text {
    color: #999999;
  }

  .divider {
    width: 2rpx;
    height: 20rpx;
    margin: 0 20rpx;
    background-color: #d8d8d8;
  }

  .setting-text {
    margin-right: 8rpx;
    color: #f33429;
  }
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #f1f1f1;
}
</style>
