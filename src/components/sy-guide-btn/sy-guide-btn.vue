<template>
  <view class="position">
    <!-- {{ orderStatus }} -->
    <!-- 美团 -->
    <view v-if="props.channle === 'MeiTuanTakeOutBrand'">
      <!-- 待分配骑手 -->
      <!-- 商家已发起配送状态 -->
      <view
        class="btn"
        @tap="jumpGuidePage('cancelDelivery')"
        v-if="
          [100, 800, 801, 802].includes(Number(props.status)) &&
          !['CANCEL', 'CONFIRM'].includes(orderStatus)
        "
      >
        取消配送指引
        <image class="icon-box" src="@/static/images/img/guide-icon.png" mode="widthFix"></image>
      </view>
      <view
        class="btn"
        @tap="jumpGuidePage('addConsumption')"
        v-if="
          [100, 800, 801, 802].includes(Number(props.status)) &&
          !['CANCEL', 'CONFIRM'].includes(orderStatus)
        "
      >
        加小费指引
        <image class="icon-box" src="@/static/images/img/guide-icon.png" mode="widthFix"></image>
      </view>
      <!-- 待骑手取餐状态 -->
      <view
        class="btn"
        @tap="jumpGuidePage('changeRider')"
        v-if="
          [200, 250].includes(Number(props.status)) && !['CANCEL', 'CONFIRM'].includes(orderStatus)
        "
      >
        换骑手指引
        <image class="icon-box" src="@/static/images/img/guide-icon.png" mode="widthFix"></image>
      </view>
      <view
        class="btn"
        @tap="jumpGuidePage('urgePickup')"
        v-if="
          [200, 250].includes(Number(props.status)) && !['CANCEL', 'CONFIRM'].includes(orderStatus)
        "
      >
        催取餐指引
        <image class="icon-box" src="@/static/images/img/guide-icon.png" mode="widthFix"></image>
      </view>
      <!-- 骑手已取餐 -->
      <view
        class="btn"
        @tap="jumpGuidePage('reissue')"
        v-if="[300].includes(Number(props.status)) && props.orderStatus != 'CANCEL'"
      >
        补发指引
        <image class="icon-box" src="@/static/images/img/guide-icon.png" mode="widthFix"></image>
      </view>
    </view>
    <!-- 饿了么 -->
    <view v-if="props.channle === 'ELeMeTakeOutBrand' && props.orderStatus != 'CANCEL'">
      <!-- 商家已发起配送状态 -->

      <view
        class="btn"
        @tap="jumpGuidePage('addConsumption')"
        v-if="
          [100, 800, 801, 802].includes(Number(props.status)) &&
          !['CANCEL', 'CONFIRM'].includes(orderStatus)
        "
      >
        加小费指引
        <image class="icon-box" src="@/static/images/img/guide-icon.png" mode="widthFix"></image>
      </view>
    </view>
    <!-- 京东 -->
    <view v-if="props.channle === 'JingDongTakeOutBrand' && props.orderStatus != 'CANCEL'">
      <!-- 骑手已接单待取餐状态 -->

      <!-- <view
        class="btn"
        @tap="jumpGuidePage('logo')"
        v-if="[200, 250].includes(Number(props.status))"
      >
        拉黑骑手指引
        <image class="icon-box" src="@/static/images/img/guide-icon.png" mode="widthFix"></image>
      </view> -->
      <view
        class="btn"
        @tap="jumpGuidePage('tellRider')"
        v-if="[200, 250].includes(Number(props.status))"
      >
        投诉骑手指引
        <image class="icon-box" src="@/static/images/img/guide-icon.png" mode="widthFix"></image>
      </view>
    </view>
    <!-- {{ props.channle }}{{ props.type }}{{ props.status }} -->
  </view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { StoreTab } from '@/types/store'
import { onLoad } from '@dcloudio/uni-app'
import { useShopStore } from '@/store/shop'
const props = defineProps<{
  status: string | number
  type: string
  channle: string | number
  orderStatus: string
}>()
const jumpGuidePage = (str: string) => {
  console.log('str===>', str)

  const { status, type, channle } = props

  uni.navigateTo({
    url: `/pages/operationGuide/index?type=${type}&channle=${props.channle}&status=${str}`,
  })
}
</script>

<style lang="scss">
@import './sy-guide-btn';
/* 渠道选择弹出层样式 - 图2 */
</style>
