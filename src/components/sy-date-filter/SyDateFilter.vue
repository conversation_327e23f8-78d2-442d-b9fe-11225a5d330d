<template>
  <view
    v-if="props.visible"
    class="sy-date-filter bg-white"
    :style="{
      top: props.topOffset + 'rpx',
      left: props.leftOffset + 'rpx',
      position: 'absolute',
      zIndex: 200,
    }"
  >
    <!-- 添加遮罩层 -->
    <view class="date-selector-overlay" @click="handleOverlayClick"></view>
    <view class="sy-date-filter-body">
      <wd-calendar
        ref="calendar"
        type="daterange"
        v-model="initDateRangeValue"
        @confirm="handleConfirmDate"
      >
        <view class="date-input">
          <view class="date-input-label">
            <text>{{ selectedDateRange.length > 0 ? selectedDateRange[0] : '开始日期' }}</text>
            -
            <text>{{ selectedDateRange.length > 0 ? selectedDateRange[1] : '结束日期' }}</text>
          </view>
          <wd-icon name="calendar" size="22px"></wd-icon>
        </view>
      </wd-calendar>
      <view class="handle-view">
        <wd-button type="text" @click="handleOverlayClick">取消</wd-button>
        <wd-divider vertical />
        <wd-button type="text" @click="confirmFilterDateClick">筛选</wd-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { dateFilterProps, dateFilterEmits } from './types'
// import type { CalendarInstance } from 'wot-design-uni/components/wd-calendar/types'
import { getCurrentDate, timestampToDate } from '../../utils/datetime'

// 定义组件名称
defineOptions({ name: 'SyDateFilter' })

// 定义组件属性
const props = defineProps(dateFilterProps)

// 定义事件
const emit = defineEmits(dateFilterEmits)

// 内部状态
const selectedDate = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const formatter = (type, value) => {
  switch (type) {
    case 'year':
      return value + '年'
    case 'month':
      return value + '月'
    case 'date':
      return value + '日'
  }
}

// 初始化显示日期选择范围
const initDateRangeValue = ref<number[]>([])
// 处理遮罩层点击
const handleOverlayClick = () => {
  // 关闭弹窗
  emit('update:visible', false)
}

// 确认筛选按钮事件
const confirmFilterDateClick = () => {
  emit('confirmFilterDate', selectedDateRange.value)
  // 关闭弹窗
  emit('update:visible', false)
}

// 选择的日期范围
const selectedDateRange = ref<string[]>([getCurrentDate(), getCurrentDate()])
// 时间范围选择框确认时间选择
const handleConfirmDate = ({ value }) => {
  selectedDateRange.value = [timestampToDate(value[0]), timestampToDate(value[1])]
}

onMounted(() => {})
</script>

<style lang="scss" scoped>
.sy-date-filter {
  width: 564rpx; // 调整宽度使其能容纳提示文字
  height: 184rpx;
  overflow-y: auto;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  border-radius: 10rpx;

  .date-selector-overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9998;
    background-color: transparent;
  }

  .sy-date-filter-body {
    width: calc(100% - 48rpx);
    position: absolute;
    z-index: 9999;

    .date-input {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin: 16rpx 24rpx;
      border-radius: 8rpx;
      padding: 10rpx 24rpx 10rpx 16rpx;
      gap: 16rpx;
      background: #ffffff;
      box-sizing: border-box;
      border: 1px solid #d0d3d6;
    }

    .date-input-label {
      flex: 1;
      display: flex;
      justify-content: space-around;
      font-size: 26rpx;
      font-weight: normal;
      line-height: 44rpx;
      letter-spacing: 0px;
    }

    .handle-view {
      display: flex;
      flex-direction: row;
      justify-content: space-evenly;
      align-items: center;
    }
  }
}
</style>
