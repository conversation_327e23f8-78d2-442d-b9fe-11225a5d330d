.store-rating-box {
  box-sizing: border-box;
  // box-sizing: border-box;
  display: flex;
  align-items: start;
  width: 702rpx;
  padding: 24rpx 32rpx;
  margin-bottom: 30rpx;
  margin-left: 24rpx;
  background: #f8f8f8;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  .text-primary {
    color: #f33429;
  }

  image {
    vertical-align: middle;
  }

  // 星星样式
  .star-container {
    display: flex;
    align-items: center;
  }

  // 评分详情区域
  .rating-details {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .rating-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 0 16rpx;

      .label {
        font-size: 24rpx;
        color: #333;
      }

      .value {
        margin-top: 4rpx;
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
      }
    }
  }
  .items-center-left {
    flex: 1;
  }
  .items-center-right {
    position: relative;
    flex: 1;
  }
  .displayRating-text {
    position: relative;
    top: -22rpx;
    width: 96rpx;
    // margin-right: 8rpx;
    font-size: 64rpx;
    font-weight: 600;
    color: #333333;
    text-align: center;
  }
  .items-center {
    display: flex;
    align-items: center;
    // width: 50%;
    .items-center-item {
      display: flex;
      // flex: 1;
      justify-content: center;
      width: 33.33%;
      text-align: center;
    }
    .items-center-item-esc {
      // width: 240rpx;
    }
  }
  .items-center-left {
    width: 60%;
  }
  .items-top {
    align-items: start;
    margin-top: 20rpx;
  }
}
.rate-box {
  position: relative;
  left: 26rpx;
}
