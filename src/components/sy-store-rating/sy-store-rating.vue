<template>
  <view class="store-rating-box" v-if="hideRateing">
    <!-- 左侧评分区域 -->
    <view class="flex flex-row items-center items-center-left">
      <view class="flex flex-col">
        <view class="flex flex-row items-center">
          <view class="title-xs text-gray-800 mr-2" v-if="hideRateing">总评分</view>
          <view class="flex flex-row items-center" @click="onClickRule">
            <view class="title-xs text-primary">计算详情</view>
            <image src="/static/images/img/wenhao-shop.png" class="w-3 h-3 ml-1" mode="aspectFit" />
          </view>
        </view>
        <view class="flex flex-row items-top mt-1">
          <text class="min-w-[96rpx] displayRating-text" v-if="hideRateing">
            {{ displayRating }}
          </text>
          <view class="flex flex-row items-center rate-box">
            <wd-rate v-model="localRating" size="15px" readonly allow-half />
          </view>
        </view>
      </view>
    </view>

    <!-- 右侧评分详情 -->
    <view class="flex flex-row items-center items-center-right">
      <!-- 口味 -->
      <view class="flex flex-col items-center-item">
        <view class="title-xs text-gray-800">口味</view>
        <view class="text-lg font-bold text-gray-800 mt-1">{{ displayTaste }}</view>
      </view>

      <!-- 包装 -->
      <view class="flex flex-col items-center-item">
        <view class="title-xs text-gray-800">包装</view>
        <view class="text-lg font-bold text-gray-800 mt-1">{{ displayPackaging }}</view>
      </view>

      <!-- 配送满意度 -->
      <view class="flex flex-col items-center-item items-center-item-esc">
        <view class="title-xs text-gray-800">配送</view>
        <view class="text-lg font-bold text-gray-800 mt-1">{{ displayDeliveryRate }}</view>
      </view>
    </view>

    <!-- 评分规则弹窗 -->
    <!-- <sy-store-rating-rule v-model="showRatingRulePopup" /> -->
  </view>
</template>
<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'

const props = defineProps<{
  rating: number
  taste: number
  packaging: number
  deliveryRate: string
  hideRateing: boolean
}>()

// 定义事件
const emit = defineEmits<{
  'click-rule': []
}>()

// 创建本地响应式变量
const localRating = ref(0)
const showRatingRulePopup = ref(false)

// 计算属性：处理空值展示
const displayRating = computed(() => {
  return props.rating ? formatDot(props.rating) : '--'
})

const displayTaste = computed(() => {
  return props.taste ? formatDot(props.taste) : '--'
})

const displayPackaging = computed(() => {
  return props.packaging ? formatDot(props.packaging) : '--'
})

const displayDeliveryRate = computed(() => {
  return props.deliveryRate ? formatDot(props.deliveryRate) : '--'
})
const formatDot = (value: number | string) => {
  console.log('value===>', value, typeof value)

  if (typeof value === 'string') {
    return Number(value).toFixed(1)
  }

  return value.toFixed(1)
}
// 计算属性：将百分比字符串转换为数字
const deliveryRateNumber = computed(() => {
  // 如果是百分比格式（如"98.2%"），移除百分号并转换为数字
  if (props.deliveryRate && typeof props.deliveryRate === 'string') {
    return parseFloat(props.deliveryRate.replace('%', ''))
  }
  // 如果已经是数字，则直接返回
  return parseFloat(props.deliveryRate || '0')
})

// 初始化和监听变化
onMounted(() => {
  localRating.value = props.rating ? props.rating : 0
})

// 监听props变化，更新本地变量
watch(
  () => props.rating,
  (newVal) => {
    localRating.value = newVal || 0
  },
)

// 点击计算详情
const onClickRule = () => {
  console.log('点击计算详情')
  emit('click-rule')
}

// 显示评分规则弹窗
const showRatingRule = () => {
  showRatingRulePopup.value = true
}

// 添加默认导出
defineOptions({
  name: 'SyStoreRating',
})
</script>
<style lang="scss" src="./sy-store-rating.scss"></style>
