/**
 * 日期选择器组件属性定义
 */

// 日期选择结果载荷
export interface DateSelectionPayload {
  /** 选中的日期，格式: YYYY-MM-DD */
  value: string
  /** 选中的日期显示文本，格式: M月d日 (今日) */
  label: string
  /** 选中日期的开始时间，格式: YYYY-MM-DD 00:00:00 */
  startPlaceTime: string
  /** 选中日期的结束时间，格式: YYYY-MM-DD 23:59:59 */
  endPlaceTime: string
}

// 日期项数据结构
export interface DateItem {
  /** JS Date 对象，便于计算 */
  dateObject: Date
  /** 日期值，格式: YYYY-MM-DD */
  value: string
  /** 日期显示文本，格式: M月d日 */
  displayLabel: string
  /** 是否为今天 */
  isToday: boolean
  /** 完整显示文本，格式: M月d日 (今日) 或 M月d日 */
  fullLabel: string
  /** 日期开始时间，格式: YYYY-MM-DD 00:00:00 */
  startPlaceTime: string
  /** 日期结束时间，格式: YYYY-MM-DD 23:59:59 */
  endPlaceTime: string
}

/**
 * 日期选择器组件属性
 */
export const dateSelectorProps = {
  /** 当前选中的日期，格式为 YYYY-MM-DD */
  modelValue: {
    type: String,
    default: '',
  },
  /** 控制弹窗的显示与隐藏 */
  visible: {
    type: Boolean,
    default: false,
  },
  /** 弹窗标题 */
  title: {
    type: String,
    default: '选择日期',
  },
  /** 弹窗内容区域距离顶部的偏移量 */
  topOffset: {
    type: Number,
    default: 0,
  },
  /** 弹窗内容区域距离左侧的偏移量 */
  leftOffset: {
    type: Number,
    default: 0,
  },
}

/**
 * 日期选择器组件事件
 */
export const dateSelectorEmits = {
  'update:modelValue': (value: string) => true,
  'update:visible': (value: boolean) => true,
  confirm: (payload: DateSelectionPayload) => true,
  cancel: () => true,
}
