<template>
  <view
    v-if="props.visible"
    :style="{
      top: props.topOffset + 'rpx',
      left: props.leftOffset + 'rpx',
      position: 'absolute',
      zIndex: 200,
    }"
    class="sy-date-selector bg-white"
  >
    <!-- 添加遮罩层 -->
    <view class="date-selector-overlay" @click="handleOverlayClick"></view>
    <view class="date-selector-wrapper">
      <!-- 提示信息 -->
      <view class="sy-date-selector__tip px-[20rpx] border-b border-b-[#f5f5f5]">
        <text class="text-[24rpx] text-[#999999]">只可选择7天以内日期</text>
      </view>

      <!-- 日期列表 -->
      <view class="sy-date-selector__list">
        <view
          v-for="dateItem in dateList"
          :key="dateItem.value"
          class="sy-date-selector__item min-h-[80rpx] px-[20rpx] border-b border-b-[#f5f5f5] flex items-center"
          :class="{ 'sy-date-selector__item-active': dateItem.value === selectedDate }"
          @click="selectDate(dateItem)"
        >
          <view class="flex items-center px-[50rpx]">
            <text class="text-[28rpx] leading-[1.2] text-[#333333]">
              {{ dateItem.displayLabel }}
            </text>
            <!-- 今日标签 改为带边框的红色背景 -->
            <view v-if="dateItem.isToday" class="today-tag ml-[8rpx]">
              <text>今日</text>
            </view>
          </view>

          <!-- 选中项显示对勾图标 -->
          <wd-icon
            v-if="dateItem.value === selectedDate"
            name="check"
            color="#F33429"
            size="28rpx"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import {
  dateSelectorProps,
  dateSelectorEmits,
  type DateItem,
  type DateSelectionPayload,
} from './types'

// 定义组件名称
defineOptions({ name: 'SyDateSelector' })

// 定义组件属性
const props = defineProps(dateSelectorProps)

// 定义事件
const emit = defineEmits(dateSelectorEmits)

// 内部状态
const selectedDate = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 生成最近7天的日期列表
const dateList = ref<DateItem[]>([])

// 初始化日期列表
onMounted(() => {
  generateDateList()
})

// 生成日期列表 - 每次调用都重新计算最新的7天
const generateDateList = () => {
  const today = new Date()
  const result: DateItem[] = []

  for (let i = 0; i < 7; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() - i)

    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()

    const isToday = i === 0
    const value = formatDate(date, 'YYYY-MM-DD')
    const displayLabel = `${month}月${day}日`
    const fullLabel = isToday ? `${displayLabel} (今日)` : displayLabel

    // 开始时间为当天 00:00:00
    const startPlaceTime = `${value} 00:00:00`
    // 结束时间为当天 23:59:59
    const endPlaceTime = `${value} 23:59:59`

    result.push({
      dateObject: date,
      value,
      displayLabel,
      isToday,
      fullLabel,
      startPlaceTime,
      endPlaceTime,
    })
  }

  dateList.value = result
}

// 格式化日期
const formatDate = (date: Date, format: string): string => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()

  return format
    .replace('YYYY', year.toString())
    .replace('MM', month.toString().padStart(2, '0'))
    .replace('DD', day.toString().padStart(2, '0'))
}

// 选择日期
const selectDate = (dateItem: DateItem) => {
  selectedDate.value = dateItem.value

  const payload: DateSelectionPayload = {
    value: dateItem.value,
    label: dateItem.fullLabel,
    startPlaceTime: dateItem.startPlaceTime,
    endPlaceTime: dateItem.endPlaceTime,
  }

  // 先发送confirm事件，将选择的日期数据传给父组件
  emit('confirm', payload)

  // 再关闭弹窗
  emit('update:visible', false)
}

// 处理遮罩层点击
const handleOverlayClick = () => {
  // 关闭弹窗
  emit('update:visible', false)
}

// 监听visible变化 - 每次显示时都重新生成最新日期
watch(
  () => props.visible,
  (newValue) => {
    if (newValue) {
      // 弹窗显示时，总是重新生成最新的7天日期
      generateDateList()
    }
  },
  { immediate: true }, // 立即执行一次，确保初始化时也能正确生成
)
</script>

<style lang="scss" scoped>
.sy-date-selector {
  width: 47vw; // 调整宽度使其能容纳提示文字
  height: 658rpx;
  overflow-y: auto;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

  .date-selector-overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9998;
    background-color: transparent;
  }

  .date-selector-wrapper {
    width: 100%;
    position: absolute;
    z-index: 9999;
  }
  &__tip {
    background-color: #f8f8f8;
  }

  &__list {
    display: flex;
    flex-direction: column;
  }

  &__item {
    &:last-child {
      border-bottom: none;
    }

    &-active {
      background-color: #f6f6f6;
    }
  }

  .sy-date-selector__tip {
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 26rpx;
  }
  .sy-date-selector__item {
    border-top: 1px solid #f5f5f5;
  }

  // 今日标签样式
  .today-tag {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32rpx;
    padding: 0 8rpx;
    border: 1px solid #f33429;
    border-radius: 4rpx;

    text {
      font-size: 22rpx;
      line-height: 1;
      color: #f33429;
    }
  }
}
</style>
