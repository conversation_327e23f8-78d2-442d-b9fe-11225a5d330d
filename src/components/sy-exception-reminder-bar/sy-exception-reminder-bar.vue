<template>
  <view
    v-if="visible"
    class="sy-exception-reminder-bar-wrapper"
    :style="{
      bottom: isTabbarPage ? '100rpx' : '0',
    }"
    @click="handleClick"
  >
    <view class="sy-exception-reminder-bar">
      <view class="left-content">
        <wd-icon custom-class="exception-icon" name="warning-bold" size="24rpx" />
        <text class="reminder-text">异常提醒：来单不响异常</text>
      </view>
      <wd-icon custom-class="arrow-icon" name="arrow-up" size="24rpx" />
    </view>
    <view class="safe-area-bottom"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { exceptionReminderBarProps } from './exception-reminder-bar'

const props = defineProps(exceptionReminderBarProps)
const emit = defineEmits(['click'])

const handleClick = () => {
  emit('click')
}

// 检测当前页面是否为 tabbar 页面
const isTabbarPage = ref(false)

const checkTabbarPage = () => {
  try {
    const pages = getCurrentPages()
    if (pages && pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage.route || ''

      // 定义 tabbar 页面路径列表（根据实际项目的 tabbar 配置）
      const tabbarPages = [
        'pages/index/index',
        'pages/about/about',
        'pages/dishManagement/index',
        'pages/my/index',
        'pages/storeDetail/index',
      ]

      isTabbarPage.value = tabbarPages.some((path) => currentRoute.includes(path))
    }
  } catch (error) {
    console.error('检测 tabbar 页面失败:', error)
    isTabbarPage.value = false
  }
}

onMounted(() => {
  checkTabbarPage()
})
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
  },
}
</script>

<style lang="scss" scoped>
.sy-exception-reminder-bar-wrapper {
  position: fixed;
  left: 0;
  z-index: 100;
  width: 100%;
  background-color: #fff2f1; // 根据设计稿的 #F33429 调整的浅色背景
}

.sy-exception-reminder-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 78rpx; /* 39px * 2 */
  padding: 0 24rpx;
}

.left-content {
  display: flex;
  align-items: center;
}

.reminder-text {
  margin-left: 8rpx;
  font-family: 'PingFang SC', sans-serif;
  font-size: 24rpx; /* 12px * 2 */
  line-height: 40rpx; /* 20px * 2 */
  color: #f33429;
}

:deep(.exception-icon) {
  color: #f33429 !important;
}

:deep(.arrow-icon) {
  color: #f33429 !important;
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #fff2f1;
}
</style>
