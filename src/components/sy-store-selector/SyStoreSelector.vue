<template>
  <view
    v-if="props.visible"
    class="sy-store-selector"
    :style="{
      top: props.topOffset + 'rpx',
      left: props.leftOffset + 'rpx',
      position: 'absolute',
      zIndex: 200,
    }"
  >
    <!-- 添加遮罩层 -->
    <view class="store-selector-overlay" @click="handleOverlayClick"></view>
    <view class="store-selector-popup">
      <!-- 搜索框 -->
      <wd-search
        v-model="searchText"
        placeholder-left
        placeholder="请输入门店名称"
        @clear="onClearInput"
        hide-cancel
        :maxlength="16"
      />

      <!-- 门店列表 -->
      <view class="store-selector-content">
        <!-- 无数据提示 -->
        <view v-if="!props.options || props.options.length === 0" class="store-selector-empty">
          <text class="store-selector-empty-text">暂无门店数据</text>
        </view>

        <wd-checkbox-group
          v-else
          shape="square"
          checked-color="#F33429"
          v-model="tempSelectedValues"
          @change="handleCheckboxChange"
        >
          <!-- 全部门店选项 -->
          <view class="store-selector-item">
            <wd-checkbox
              model-value="ALL"
              v-if="props.multiple"
              :disabled="props.options && props.options.length === 1"
              @click="toggleAllStores"
            >
              全部门店
            </wd-checkbox>
          </view>

          <!-- 门店列表项 -->
          <view
            v-for="store in filteredStores"
            :key="store.id || store.code"
            class="store-selector-item"
          >
            <wd-checkbox :model-value="store.id || store.code">
              {{ store.name }}
              <!-- <text v-if="store.alias && store.alias !== store.name" class="store-selector-alias">
                ({{ store.alias }})
              </text> -->
            </wd-checkbox>
          </view>
        </wd-checkbox-group>

        <!-- 无搜索结果 -->
        <view
          v-if="
            filteredStores.length === 0 &&
            searchText.trim() !== '' &&
            props.options &&
            props.options.length > 0
          "
          class="store-selector-empty"
        >
          <text class="store-selector-empty-text">无匹配门店</text>
        </view>
      </view>

      <!-- 底部按钮区域 -->
      <view class="store-selector-footer">
        <view class="store-selector-btn store-selector-btn-cancel" @click="handleCancel">取消</view>
        <wd-divider vertical style="align-self: center" />
        <view class="store-selector-btn store-selector-btn-confirm" @click="handleConfirm">
          确认
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { storeSelectorProps, storeSelectorEmits, type StoreOption } from './SyStoreSelector'

// 定义组件名称
defineOptions({
  name: 'SyStoreSelector',
})

// 定义组件属性
const props = defineProps(storeSelectorProps)

// 定义组件事件
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'update:value': [value: string[]]
  change: [value: string[], labels: string[]]
  confirm: [value: string[], labels: string[]]
  cancel: []
}>()

// 搜索文本
const searchText = ref('')

// 实际选中的值（最终提交的值）
const selectedValues = ref<string[]>([])

// 临时选中值（弹窗操作中使用）
const tempSelectedValues = ref<string[]>([])

// 监听tempSelectedValues变化，帮助调试
watch(
  () => tempSelectedValues.value,
  (newVal) => {
    console.log('SyStoreSelector tempSelectedValues changed:', newVal)
  },
)

// 监听props.value变化，更新内部selectedValues
watch(
  () => props.value,
  (newValue) => {
    console.log('SyStoreSelector props.value changed:', newValue)
    // 确保是新数组，防止引用问题
    selectedValues.value = Array.isArray(newValue) ? [...newValue] : []

    // 当弹窗打开时，同步到临时选中值
    if (props.visible) {
      tempSelectedValues.value = Array.isArray(newValue) ? [...newValue] : []
    }
  },
  { immediate: true },
)

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 打开弹窗时，初始化临时值为当前选中值
      tempSelectedValues.value = [...selectedValues.value]

      // 同步全选状态
      syncAllOptionState()
    }
  },
)

// 获取所有门店ID数组（不包含全选值）
const allStoreIds = computed(() => {
  return props.options
    .map((store) => store.id || store.code)
    .filter((id) => id !== props.allOptionValue)
})

// 计算全选状态
const isAllChecked = computed(() => {
  if (allStoreIds.value.length === 0) return false
  return allStoreIds.value.every((id) => tempSelectedValues.value.includes(id))
})

// 过滤后的门店列表
const filteredStores = computed(() => {
  // 如果没有options或options为空数组，直接返回空数组
  if (!props.options || props.options.length === 0) {
    return []
  }

  if (!searchText.value.trim()) {
    // 打印每个门店的ID和代码，帮助调试
    console.log(
      '门店列表详情:',
      props.options.map((store) => ({
        id: store.id || '',
        code: store.code,
        name: store.name,
      })),
    )
    return props.options
  }

  const lowerSearchText = searchText.value.toLowerCase()
  return props.options.filter((store) =>
    (store.name?.toLowerCase() || '').includes(lowerSearchText),
  )
})

// 切换全选状态
const toggleAllStores = () => {
  let newValues: string[] = []

  if (!isAllChecked.value) {
    // 当前非全选状态，变为全选状态
    newValues = [props.allOptionValue, ...allStoreIds.value]
  } else {
    // 当前全选状态，变为非全选状态
    newValues = []
  }

  // 更新临时选中值
  tempSelectedValues.value = newValues
}

// 同步全选选项状态
const syncAllOptionState = () => {
  // 如果只有一个门店，强制选中
  if (props.options && props.options.length === 1) {
    const storeId = props.options[0].id || props.options[0].code
    tempSelectedValues.value = [storeId, props.allOptionValue]
    return
  }

  // 获取所有非"全部"选项的门店ID
  const storeIds = allStoreIds.value

  // 检查是否所有门店都被选中
  const allSelected =
    storeIds.length > 0 && storeIds.every((id) => tempSelectedValues.value.includes(id))

  // 处理"全部"选项
  if (allSelected && !tempSelectedValues.value.includes(props.allOptionValue)) {
    // 如果所有门店都被选中，自动添加"全部"选项
    tempSelectedValues.value.push(props.allOptionValue)
  } else if (!allSelected && tempSelectedValues.value.includes(props.allOptionValue)) {
    // 如果不是所有门店都被选中，但包含"全部"选项，则移除"全部"选项
    const index = tempSelectedValues.value.indexOf(props.allOptionValue)
    if (index !== -1) {
      tempSelectedValues.value.splice(index, 1)
    }
  }
}

// 选择门店
const handleCheckboxChange = ({ value }: { value: string[] }) => {
  console.log('SyStoreSelector handleCheckboxChange - 原始值:', value)

  // 确保所有值都是字符串并过滤无效值
  const validValues = (value || [])
    .map((v) => String(v))
    .filter((v) => v !== 'false' && v !== 'undefined' && v !== 'null')

  console.log('SyStoreSelector handleCheckboxChange - 处理后的值:', validValues)

  // 只有一个门店时，强制保持选中状态，并同时选中"全部"选项
  if (props.options && props.options.length === 1) {
    const storeId = props.options[0].id || props.options[0].code
    // 确保门店ID和"全部"都被选中
    tempSelectedValues.value = [storeId, props.allOptionValue]
    return
  }

  // 检查是否点击了全选选项
  const wasAllSelected = tempSelectedValues.value.includes(props.allOptionValue)
  const isNowAllSelected = validValues.includes(props.allOptionValue)

  if (wasAllSelected !== isNowAllSelected) {
    // 用户点击了全选选项
    toggleAllStores()
    return
  }

  // 更新临时选中值
  tempSelectedValues.value = [...validValues]
  // 同步全选状态
  syncAllOptionState()
}

// 获取选中的门店名称列表
const getSelectedLabels = (values: string[]): string[] => {
  const selectedLabels: string[] = []

  values.forEach((id) => {
    // 特殊处理全选选项
    if (id === props.allOptionValue) {
      selectedLabels.push(props.allOptionText)
      return
    }

    const option = props.options.find((item) => (item.id || item.code) === id)
    if (option) {
      selectedLabels.push(option.name)
    }
  })

  return selectedLabels
}

// 处理确认按钮点击
const handleConfirm = () => {
  console.log('SyStoreSelector handleConfirm, 选中值:', tempSelectedValues.value)

  if (tempSelectedValues.value.length === 0) {
    uni.showToast({ title: '至少选择一个门店', icon: 'none' })
    return
  }
  // 先同步一次全选状态，确保全选标记正确
  syncAllOptionState()

  // 将临时选中值应用到实际选中值
  selectedValues.value = [...tempSelectedValues.value]

  // 获取选中门店的名称
  const selectedLabels = getSelectedLabels(selectedValues.value)

  // 触发事件
  emit('update:value', selectedValues.value)
  emit('change', selectedValues.value, selectedLabels)
  emit('confirm', selectedValues.value, selectedLabels)

  // 关闭弹窗
  emit('update:visible', false)

  // 清空搜索文本
  searchText.value = ''
}

// 处理取消按钮点击
const handleCancel = () => {
  console.log('SyStoreSelector handleCancel')

  // 恢复临时选中值为实际选中值
  tempSelectedValues.value = [...selectedValues.value]

  // 触发取消事件
  emit('cancel')

  // 关闭弹窗
  emit('update:visible', false)

  // 清空搜索文本
  searchText.value = ''
}

// 处理遮罩层点击
const handleOverlayClick = () => {
  if (tempSelectedValues.value.length === 0) {
    uni.showToast({ title: '至少选择一个门店', icon: 'none' })
    return
  }
  handleCancel()
}

const onClearInput = () => {}

// 添加默认导出
defineExpose({
  handleCheckboxChange,
  handleCancel,
  handleConfirm,
  toggleAllStores,
  handleOverlayClick,
})
</script>

<style lang="scss" scoped>
.sy-store-selector {
  position: absolute;
  left: 20rpx;
  z-index: 999;

  // 弹窗样式
  .store-selector-popup {
    position: fixed;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    min-width: 72vw;
    max-height: 90vh;
    overflow: hidden;
    background-color: #ffffff;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  }

  // 遮罩层样式
  .store-selector-overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9997;
    background-color: transparent;
  }
}

// 搜索框
.store-selector-search {
  padding: 20rpx;
}

// 搜索输入框
.store-selector-search-input {
  :deep() {
    .wd-input__inner {
      height: 72rpx;
      padding-left: 20rpx;
      font-size: 28rpx;
      background-color: #f5f5f5;
      border: none;
      border-radius: 36rpx;
      &::after {
        content: none;
      }
    }
  }
}

// 弹窗内容区
.store-selector-content {
  flex: 1;
  max-height: 600rpx;
  overflow-y: auto;
}

// 门店列表项
.store-selector-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80rpx;
  padding: 0rpx 20rpx;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  :deep() {
    .wd-checkbox {
      flex: 1;
      margin-bottom: 0rpx;
      overflow: hidden;

      .wd-checkbox__label {
        flex: 1;
        overflow: hidden;
        font-size: 28rpx;
        color: #333333;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .wd-checkbox__shape {
        flex-shrink: 0;
      }
    }
  }
}

// 门店数量
.store-selector-count {
  margin-left: auto;
  font-size: 24rpx;
  color: #999999;
}

// 门店别名
.store-selector-alias {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #999999;
}

// 空状态
.store-selector-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200rpx;
}

// 空状态文本
.store-selector-empty-text {
  font-size: 28rpx;
  color: #999999;
}

// 底部按钮区域
.store-selector-footer {
  display: flex;
  height: 88rpx;
  border-top: 1px solid #f5f5f5;
}

// 按钮样式
.store-selector-btn {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 28rpx;

  &-cancel {
    font-size: 26rpx;
    font-weight: normal;
    line-height: normal;
    color: #333333;
    text-align: center;
    letter-spacing: normal;
  }

  &-confirm {
    font-size: 26rpx;
    font-weight: 500;
    line-height: normal;
    color: #f33429;
    text-align: center;
    letter-spacing: normal;
  }
}
</style>
