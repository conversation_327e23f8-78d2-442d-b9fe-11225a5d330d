/**
 * 门店选择器组件属性定义
 */
import { PropType } from 'vue'

/**
 * 门店选项接口
 */
export interface StoreOption {
  /** 门店唯一编码 */
  code: string
  /** 门店ID */
  id?: string
  /** 门店显示名称 */
  name: string
  /** 门店别名 */
  alias?: string
  /** 其他可能的字段 */
  [key: string]: any
}

/**
 * 组件属性定义
 */
export const storeSelectorProps = {
  /** 是否显示弹窗 */
  visible: {
    type: Boolean,
    default: false,
  },
  /** 当前选中的门店code数组 */
  value: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  /** 弹窗相对于触发元素的顶部偏移量(rpx) */
  topOffset: {
    type: Number,
    default: 0,
  },
  /** 弹窗相对于触发元素的左侧偏移量(rpx) */
  leftOffset: {
    type: Number,
    default: 0,
  },
  /** 门店列表选项 */
  options: {
    type: Array as PropType<StoreOption[]>,
    default: () => [],
  },
  /** 是否支持多选 */
  multiple: {
    type: Boolean,
    default: true,
  },
  /** 参数名称，用于传递给后端的参数名 */
  paramName: {
    type: String,
    default: 'shopIds',
  },
  /** "全部"选项的值 */
  allOptionValue: {
    type: String,
    default: 'ALL',
  },
  /** "全部"选项的文本 */
  allOptionText: {
    type: String,
    default: '全部门店',
  },
}

/**
 * 组件事件定义
 */
export const storeSelectorEmits = ['update:visible', 'update:value', 'change', 'cancel', 'confirm']
