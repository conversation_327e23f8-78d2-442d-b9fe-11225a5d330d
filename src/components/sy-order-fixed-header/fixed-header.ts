/**
 * 订单固定头部组件属性
 */
import { PropType } from 'vue'

// 类型定义
export interface Store {
  id: string
  name: string
  address?: string
  avatar?: string
  subShopIds?: string[]
}

// 组件属性定义
export const fixedHeaderProps = {
  /** 当前门店 */
  currentStore: {
    type: Object as PropType<Store>,
    default: () => ({
      id: '',
      name: '',
      address: '',
      avatar: '',
    }),
  },
  /** 是否展开门店信息 */
  isStoreExpanded: {
    type: Boolean,
    default: false,
  },
}

// 组件事件定义
export const fixedHeaderEmits = ['store-select', 'toggle-store-expand']
