<template>
  <view class="fixed-header">
    <!-- 门店头部 -->

    <view class="store-header">
      <!-- 当为展开状态时，点击左边的门店部分就跳转页面；当为收起状态时，点击就展开门店 -->
      <view
        class="store-name-container"
        :class="{ 'store-name-centered': !isStoreExpanded }"
        @click="isStoreExpanded ? onStoreSelect() : onToggleStoreExpand(true)"
      >
        <text class="store-name">{{ isStoreExpanded ? currentStore.name : '展开门店' }}</text>
        <view class="store-arrow" v-if="!isStoreExpanded">
          <wd-icon name="fill-arrow-down" size="20rpx" />
        </view>
        <view class="store-arrow" v-else>
          <wd-icon name="arrow-right" size="20rpx" />
        </view>
      </view>
      <!-- 当为展开状态时，右侧显示隐藏按钮，点击收起 -->
      <!-- <view class="hide-button" v-if="isStoreExpanded" @click.stop="onToggleStoreExpand(false)">
        <text class="hide-text">隐藏</text>
        <wd-icon name="arrow-up" size="14rpx" />
      </view> -->
    </view>
  </view>
</template>

<script setup lang="ts">
import { fixedHeaderProps, fixedHeaderEmits } from './fixed-header'

// 定义组件属性
const props = defineProps(fixedHeaderProps)

// 定义组件事件
const emit = defineEmits(fixedHeaderEmits)

// 门店选择处理
const onStoreSelect = () => {
  uni.navigateTo({
    url: '/pages/shop/list/index',
  })
}

// 切换门店展开状态
const onToggleStoreExpand = (expanded: boolean) => {
  emit('toggle-store-expand', expanded)
}
</script>

<style lang="scss" scoped>
.fixed-header {
  position: sticky;
  top: 0;
  right: 0;
  left: 0;
  z-index: 10;
  background-color: #fff;
}

// 门店头部
.store-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 24rpx;
  background-color: #fff;

  .store-name-container {
    display: flex;
    align-items: center;
    cursor: pointer;

    &.store-name-centered {
      flex: 1;
      justify-content: center;
    }
  }

  .store-name {
    font-size: 30rpx;
    font-weight: 600;
    line-height: 42rpx;
    color: #222222;
  }

  .store-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28rpx;
    height: 28rpx;
    margin-left: 8rpx;
  }

  .hide-button {
    display: flex;
    align-items: center;
    padding: 4rpx 12rpx;
    cursor: pointer;
    border-radius: 4rpx;

    .hide-text {
      margin-right: 4rpx;
      font-size: 24rpx;
      color: #666666;
    }
  }
}

.store-name-centered {
  margin: 0 auto;
}
</style>
