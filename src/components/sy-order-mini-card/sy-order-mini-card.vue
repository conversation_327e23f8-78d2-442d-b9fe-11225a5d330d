<template>
  <view class="sy-order-mini-card bg-white rounded-16rpx mx-12rpx mb-24rpx overflow-hidden">
    <!-- 卡片渐变背景 -->
    <view
      class="sy-order-mini-card-gradient-bg absolute top-0 left-0 right-0 h-378rpx"
      :style="{
        opacity: 0.2,
        background: orderData?.channelColor?.gradient || getChannelGradient(orderData?.channel),
      }"
    ></view>

    <!-- 水印区域 -->
    <view class="sy-order-mini-card-watermark absolute top-60rpx right-8rpx z-1">
      <i
        :class="['iconfont', orderData?.channelColor?.watermarkIconClass || 'icon-xiafancai']"
        :style="{
          fontSize: '200rpx',
          color: orderData?.channelColor?.watermarkColor || '#F0F0F0',
          opacity: 0.3,
        }"
      ></i>
    </view>

    <!-- 卡片头部信息 -->
    <view
      class="sy-order-mini-card-header flex items-center justify-between px-16rpx pt-16rpx relative z-1"
    >
      <view class="flex items-center">
        <image
          class="sy-order-mini-card-channel-icon w-32rpx h-32rpx rounded-full"
          :src="orderData?.merchant?.avatar || ''"
          mode="aspectFill"
        />
        <view class="border-line"></view>
        <view
          class="sy-order-card-tenant-tag rounded-4rpx h-32rpx flex items-center justify-center pl-8rpx pr-8rpx"
          :style="{
            background:
              orderData?.channelColor?.bg || getChannelButtonStyle(orderData?.saleChannel).bg,
          }"
        >
          <image
            src="/static/images/icons/tent_tag.svg"
            class="w-24rpx h-24rpx mr-4rpx"
            mode="aspectFit"
            :style="{
              filter: `${orderData?.channelColor?.watermarkColor ? `drop-shadow(0 0 0 ${orderData.channelColor.watermarkColor})` : ''}`,
            }"
          />
          <text class="text-20rpx text-white font-medium">
            {{ orderData?.appName || '外卖' }}
          </text>
        </view>
      </view>
      <!-- 右上角展开图标 -->
      <view class="expand-icon" @click="showOrderDetail">
        <image src="/static/images/img/detail-btn.png" class="img-box"></image>
      </view>
    </view>

    <!-- 虚线分割线 -->
    <view class="sy-order-mini-card-divider mx-16rpx mt-20rpx relative z-1"></view>

    <!-- 订单内容区域 -->
    <view class="sy-order-mini-card-content px-16rpx relative z-1">
      <!-- 订单号和标识 -->
      <view class="flex items-center mb-12rpx">
        <!-- 取餐号 -->
        <view class="flex items-baseline mb-12rpx mr-16rpx">
          <view class="flex items-baseline">
            <text class="sy-order-mini-card-serial-prefix text-40rpx text-#222222">#</text>
            <text class="sy-order-mini-card-serial-number text-80rpx text-#222222">
              {{ orderData?.takeNo || '3' }}
            </text>
          </view>
        </view>
        <!-- 渠道类型标签 -->
        <view class="sy-order-card-type-tag px-12rpx py-4rpx rounded-4rpx">
          <text class="text-24rpx font-medium text-#F33429">
            {{ orderData?.type || '' }}
          </text>
        </view>
        <view class="sy-order-card-type-tag-yu rounded-6rpx" v-if="orderData?.isBook == 2">
          <text class="text-22rpx font-medium">预</text>
        </view>
      </view>

      <!-- 订单状态和用时 -->
      <view class="flex items-center mb-12rpx">
        <!-- 参考 sy-order-card.vue 修复用时显示逻辑 -->
        <template
          v-if="
            (orderData.isBook !== 2 && orderData?.orderStatus?.code === 'IN_PREPARE') ||
            (orderData.isBook === 2 && isNearlyOrder)
          "
        >
          <text class="text-24rpx text-#222222">
            备餐中，已用时{{
              orderData?.isBook !== 2
                ? formatUsedTime(orderData?.merchantConfirmTime || orderData?.times?.acceptTime)
                : startTime(orderData?.sendTime)
            }}
          </text>
        </template>
        <!-- 其他状态 -->
        <template v-else>
          <text class="text-24rpx text-#222222">
            {{
              orderData?.isBook !== 2
                ? '未上报出餐完成，无出餐用时'
                : !isNearlyOrder
                  ? '备餐提醒后开始计时'
                  : ''
            }}
          </text>
        </template>
      </view>

      <!-- 出餐状态 -->
      <view class="flex items-center mb-16rpx">
        <text class="text-20rpx text-#999999">
          {{ orderData?.orderStatus?.text || '备餐中' }}
        </text>
      </view>
    </view>

    <!-- 底部按钮区域 -->
    <view class="sy-order-mini-card-footer flex justify-center px-16rpx pb-16rpx relative z-1">
      <SyAuth
        code="jidanbao_menu_index_order_reportMealsbtn"
        toast-mode="toast"
        v-slot="{ isAllowed, onNoPermissionClick }"
      >
        <view
          v-if="showReportButton"
          class="sy-order-mini-card-action-btn rounded-26rpx flex items-center justify-center w-full"
          :class="{ 'sy-auth-no-permission': !isAllowed }"
          :style="{
            backgroundColor:
              canReportMeals && isAllowed
                ? orderData?.channelColor?.btnBg ||
                  orderData?.channelColor?.bg ||
                  getChannelButtonStyle(orderData?.saleChannel).bg
                : '#CCCCCC',
            height: '60rpx',
            opacity: canReportMeals && isAllowed ? 1 : 0.7,
            filter: !isAllowed ? 'grayscale(100%)' : 'none',
          }"
          @click="canReportMeals && isAllowed ? onReportOrderClick() : onNoPermissionClick()"
        >
          <text
            class="text-24rpx"
            :style="{
              color:
                canReportMeals && isAllowed
                  ? orderData?.channelColor?.btnText ||
                    getChannelButtonStyle(orderData?.saleChannel).text
                  : '#fff',
            }"
          >
            上报出餐完成
          </text>
        </view>
      </SyAuth>
    </view>

    <!-- 订单详情弹窗 -->
    <wd-popup
      v-model="showPopup"
      position="bottom"
      :close-on-click-modal="false"
      class="order-detail-popup"
      custom-style="border-radius:24rpx 24rpx 0 0;"
    >
      <view class="popup-container">
        <view class="popup-header flex justify-center items-center px-24rpx py-20rpx">
          <text class="text-30rpx font-semibold text-#222222">订单详情</text>
          <view class="popup-close" @click="showPopup = false">
            <wd-icon name="close" size="36rpx" color="#333333"></wd-icon>
          </view>
        </view>
        <scroll-view
          :scroll-y="true"
          class="popup-content"
          :scroll-top="scrollTop"
          @scroll="onScroll"
        >
          <SyOrderCard
            :order-data="orderData"
            :expanded="true"
            :show-expand-button="true"
            @call="onCallCustomer"
            @address-click="onAddressClick"
            @accept-order="onAcceptOrder"
            @finish-order="onFinishOrder"
            @report-order="onReportOrder"
          />
        </scroll-view>

        <!-- 底部安全区域 -->
        <view class="popup-safe-area"></view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { componentProps } from './card'
import type { OrderMiniCardEmits } from './card'
import { computed, ref, onMounted, onUnmounted } from 'vue'
import SyOrderCard from '../sy-order-card'
import { isOutOneHour } from '@/utils/datetime'
import SyAuth from '@/components/sy-auth'

// 定义组件属性
const props = defineProps(componentProps)

// 定义事件
const emit = defineEmits<OrderMiniCardEmits>()

// 是否显示顶部提示
const showTopTip = computed(() => {
  return (
    props.orderData?.orderStatus?.code === 'IN_PREPARE' ||
    props.orderData?.apiData?.orderStatus === 'IN_PREPARE'
  )
})

// 用于强制更新的计数器
const timerCount = ref(0)
// 定时器引用
let timer: number | null = null

// 弹窗显示状态
const showPopup = ref(false)

// 滚动位置
const scrollTop = ref(0)

// 自定义最小备餐时间（分钟）- 可以根据需求调整
const minPreparationMinutes = 1

// (预订单)是否即将到时
const isNearlyOrder = computed(() => {
  const isNear = isOutOneHour(props.orderData?.sendTime)
  if (props.orderData?.orderStatus?.code === 'IN_PREPARE' && isNear) {
    return true
  }
  return false
})

/**
 * 判断是否显示上报出餐按钮
 */
const showReportButton = computed(() => {
  // 待出餐状态
  return (
    props.orderData?.status === '待出餐' ||
    props.orderData?.orderStatus?.text === '待出餐' ||
    props.orderData?.orderStatus?.code === 'WAIT_REPORT_MEALS' ||
    props.orderData?.apiData?.orderStatus === 'WAIT_REPORT_MEALS' ||
    isNearlyOrder.value
  )
})

/**
 * 判断上报出餐按钮是否可点击
 * 条件：非预定单 或者 (是预定单 且 已经备餐超过N分钟)
 */
const canReportMeals = computed(() => {
  // 使用timerCount依赖，确保每秒都重新计算一次
  const _ = timerCount.value

  // 如果是预订单，并且距离当前时间大于一个小时则不能点击上报出餐
  if (props.orderData?.isBook === 2) {
    const ifOutOneHour = isOutOneHour(props.orderData?.sendTime)
    if (ifOutOneHour) {
      return true
    }
    return false
  }

  // 是预订单，需要检查备餐时间
  const confirmTime = props.orderData?.merchantConfirmTime || props.orderData?.times?.acceptTime
  if (!confirmTime) {
    return false
  }

  try {
    // 计算备餐已用时间（毫秒）
    const confirmDate = new Date(confirmTime)
    const now = new Date()
    const diffMs = now.getTime() - confirmDate.getTime()

    // 转换为分钟
    const diffMinutes = diffMs / (1000 * 60)

    // // 自定义最小备餐时间（分钟）- 可以根据需求调整
    // const minPreparationMinutes = 1

    // 如果备餐时间小于最小要求，按钮不可点击
    return diffMinutes >= minPreparationMinutes
  } catch (error) {
    console.error('计算备餐时间失败:', error)
    return false
  }
})

/**
 * 处理滚动事件
 */
function onScroll(e) {
  // 记录滚动位置
  console.log('滚动位置:', e.detail.scrollTop)
}

// 在组件挂载时启动定时器
onMounted(() => {
  // 每秒更新一次计数器，触发重新渲染
  timer = setInterval(() => {
    timerCount.value++
  }, 1000) as unknown as number
})

// 在组件卸载时清除定时器
onUnmounted(() => {
  if (timer !== null) {
    clearInterval(timer)
    timer = null
  }
})

/**
 * 格式化已用时间
 * @param confirmTime 商家接单/确认时间
 */
function formatUsedTime(confirmTime?: string): string {
  if (!confirmTime) return '00:00:00'

  try {
    const confirmDate = new Date(confirmTime)
    const now = new Date()

    // 使用timerCount强制依赖更新，但不直接使用它的值
    const _ = timerCount.value // 使用变量捕获值，避免ESLint错误

    // 计算时间差（毫秒）
    const diffMs = now.getTime() - confirmDate.getTime()

    // 转换为时分秒格式
    const hours = Math.floor(diffMs / (1000 * 60 * 60))
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((diffMs % (1000 * 60)) / 1000)

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  } catch (error) {
    console.error('格式化时间失败:', error)
    return '00:00:00'
  }
}

function getPreviousHour(dateTime): Date {
  const result = new Date(dateTime)
  result.setHours(result.getHours() - 1) // 减去1小时
  return result
}

// 获取当前时间开始即时计时并格式化
const startTime = (sendTime?: string) => {
  if (!sendTime) return '00:00:00'

  try {
    // 计算备餐开始时间（送达时间前60分钟）
    const preparationStartTime = new Date(getPreviousHour(sendTime))
    const deliveryTime = new Date(sendTime)
    const now = new Date()

    // 使用timerCount强制依赖更新，但不直接使用它的值
    const _ = timerCount.value // 使用变量捕获值，避免ESLint错误

    // 正在备餐中，计算从备餐开始到现在的正向时间
    const elapsedMs = now.getTime() - preparationStartTime.getTime()
    const totalSeconds = Math.floor(elapsedMs / 1000)
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  } catch (error) {
    console.error('格式化时间失败:', error)
    return '00:00:00'
  }
}

/**
 * 获取渠道按钮样式
 * @param channel 渠道
 */
function getChannelButtonStyle(channel?: string) {
  // 默认样式
  const defaultStyle = {
    bg: '#F33429',
    text: '#FFFFFF',
  }

  // 根据渠道返回对应样式
  switch (channel) {
    case 'mt':
      return {
        bg: '#FFD100',
        text: '#333333',
      }
    case 'elm':
      return {
        bg: '#0097FF',
        text: '#FFFFFF',
      }
    case 'dy':
      return {
        bg: '#121212',
        text: '#FFFFFF',
      }
    default:
      return defaultStyle
  }
}

/**
 * 获取渠道渐变背景
 * @param channel 渠道
 */
function getChannelGradient(channel?: string) {
  // 默认渐变
  const defaultGradient = 'linear-gradient(180deg, #FFECEC 0%, #FFFFFF 100%)'

  // 根据渠道返回对应渐变
  switch (channel) {
    case 'mt':
      return 'linear-gradient(180deg, #FFF8D9 0%, #FFFFFF 100%)'
    case 'elm':
      return 'linear-gradient(180deg, #E5F5FF 0%, #FFFFFF 100%)'
    case 'dy':
      return 'linear-gradient(180deg, #E5E5E5 0%, #FFFFFF 100%)'
    default:
      return defaultGradient
  }
}

/**
 * 上报出餐按钮点击事件
 */
function onReportOrderClick() {
  // 检查按钮是否可点击（预订单且备餐时间不足）
  if (!canReportMeals.value) {
    return
  }
  if (props.orderData?.id) {
    emit('report-order', props.orderData)
  }
}

/**
 * 显示订单详情弹窗
 */
function showOrderDetail() {
  showPopup.value = true
  // 重置滚动位置
  scrollTop.value = 0
}

/**
 * 处理电话按钮点击事件
 */
function onCallCustomer(phone: string) {
  uni.makePhoneCall({
    phoneNumber: phone,
    fail: (err) => {
      console.error('拨打电话失败:', err)
    },
  })
}

/**
 * 处理地址点击事件
 */
function onAddressClick(address: string) {
  console.log('地址点击:', address)
}

/**
 * 处理接单按钮点击
 */
function onAcceptOrder(orderId: string) {
  console.log('接单:', orderId)
}

/**
 * 处理出餐完成按钮点击
 */
function onFinishOrder(orderId: string) {
  console.log('出餐完成:', orderId)
}

/**
 * 处理上报出餐按钮点击
 */
function onReportOrder(orderId: string) {
  if (props.orderData?.id) {
    emit('report-order', props.orderData)
  }
}
</script>

<style lang="scss">
.sy-order-mini-card {
  position: relative;
  width: 340rpx;
  height: 378rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

  &-gradient-bg {
    z-index: 0;
    pointer-events: none;
  }

  &-watermark {
    pointer-events: none;
  }

  &-tip {
    border-bottom: 1rpx solid #ffcfcf;
  }

  &-divider {
    height: 0;
    border-top: 2rpx dashed #bebebe;
  }

  &-action-btn {
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  &-serial-prefix {
    font-weight: 600;
  }

  &-serial-number {
    font-weight: 600;
  }

  .sy-order-card-type-tag {
    box-sizing: border-box;
    /* 自动布局 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 36rpx;
    /* 自动布局 */
    font-family: PingFang SC;
    font-size: 22rpx;
    font-weight: normal;
    line-height: 28rpx;
    color: #f33429;
    letter-spacing: normal;
    border: 1px solid #f33429;
    border-radius: 4rpx;
  }

  .sy-order-card-type-tag-yu {
    box-sizing: border-box;
    /* 自动布局 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 36rpx;
    height: 36rpx;
    margin-left: 8rpx;
    /* 自动布局 */
    font-family: PingFang SC;
    font-size: 22rpx;
    font-weight: normal;
    color: #fff;
    letter-spacing: normal;
    background-color: #f33429;
    border: 1px solid #f33429;
    border-radius: 6rpx;
  }

  .expand-icon {
    position: absolute;
    top: 16rpx;
    right: 16rpx;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32rpx;
    height: 32rpx;
    padding: 4rpx;
    cursor: pointer;
    background: #666666;
    border-radius: 50%;
  }
}

.order-detail-popup {
  :deep(.wd-popup__transition) {
    overflow: hidden;
  }

  .popup-container {
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;
  }

  .popup-header {
    position: relative;
    flex-shrink: 0; /* 防止被压缩 */
    padding: 24rpx 0;
    border-bottom: 1rpx solid #eeeeee;
  }

  .popup-close {
    position: absolute;
    top: 50%;
    right: 24rpx;
    padding: 10rpx;
    transform: translateY(-50%);
  }

  .popup-content {
    height: 75vh;
    min-height: 0; /* 允许flex子项收缩 */
    padding: 16rpx 0;
    padding-bottom: 80rpx;
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
  }

  .popup-safe-area {
    flex-shrink: 0; /* 防止被压缩 */
    height: constant(safe-area-inset-bottom, 40rpx);
    height: env(safe-area-inset-bottom, 40rpx);
    background-color: #fff;
  }
}
.border-line {
  width: 1rpx;
  height: 32rpx;
  margin: 0 12rpx 0 8rpx;
  background: rgba(102, 102, 102, 0.5);
}
.img-box {
  position: relative;
  right: 0;
  width: 32rpx;
  height: 32rpx;
}
</style>
