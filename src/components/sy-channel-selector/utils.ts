/**
 * 渠道选择器组件工具函数
 */

/**
 * 渠道图标映射
 */
export const channelIconMap: Record<string, string> = {
  // 美团外卖
  MeiTuanTakeOutBrand: '/static/images/icons/mt.png',
  MT: '/static/images/icons/mt.png',
  MEITUAN: '/static/images/icons/mt.png',
  '美团外卖(品牌商)': '/static/images/icons/mt.png',
  美团外卖: '/static/images/icons/mt.png',

  // 饿了么
  ELeMeTakeOutBrand: '/static/images/icons/elm.png',
  ELM: '/static/images/icons/elm.png',
  ELEME: '/static/images/icons/elm.png',
  '饿了么(品牌商)': '/static/images/icons/elm.png',
  饿了么: '/static/images/icons/elm.png',

  // 京东秒送
  JingDongTakeOutBrand: '/static/images/icons/jd.png',
  JingDongMiaoSong: '/static/images/icons/jd.png',
  JD: '/static/images/icons/jd.png',
  JINGDONG: '/static/images/icons/jd.png',
  京东秒送: '/static/images/icons/jd.png',

  // 抖音小时达
  DouYinXiaoshiDa: '/static/images/icons/dy.png',
  DY: '/static/images/icons/dy.png',
  DOUYIN: '/static/images/icons/dy.png',
  抖音小时达: '/static/images/icons/dy.png',

  // 小程序
  SYZY: '/static/images/icons/mp.png',
  MP: '/static/images/icons/mp.png',
  MINIPROGRAM: '/static/images/icons/mp.png',
  小程序: '/static/images/icons/mp.png',
  '小程序(微信&支付宝)': '/static/images/icons/mp.png',
  '小程序（微信、支付宝、抖音）': '/static/images/icons/mp.png',
}

/**
 * 获取渠道图标
 * @param channel 渠道数据
 * @returns 渠道图标URL
 */
export function getChannelIcon(channel: { code: string; name: string }): string {
  // 首先尝试直接通过代码匹配
  if (channelIconMap[channel.code]) {
    return channelIconMap[channel.code]
  }

  // 如果代码不匹配，尝试通过名称匹配
  if (channelIconMap[channel.name]) {
    return channelIconMap[channel.name]
  }

  // 如果都不匹配，尝试通过关键词智能匹配
  const code = channel.code.toLowerCase()
  const name = channel.name.toLowerCase()

  if (code.includes('meituan') || name.includes('美团')) {
    return '/static/images/icons/mt.png'
  }

  if (code.includes('eleme') || name.includes('饿了么')) {
    return '/static/images/icons/elm.png'
  }

  if (code.includes('jingdong') || code.includes('jd') || name.includes('京东')) {
    return '/static/images/icons/jd.png'
  }

  if (
    code.includes('TikTokMiniProgram') ||
    name.includes('抖音随心团') ||
    name.includes('抖音小程序')
  ) {
    return '/static/images/icons/sxt.png'
  }
  if (code.includes('WeChatMiniProgram') || name.includes('微信')) {
    return '/static/images/icons/wx.png'
  }
  if (code.includes('AlipayMiniProgram') || name.includes('支付宝')) {
    return '/static/images/icons/zfb.png'
  }
  if (code.includes('DouYinXiaoshiDa') || name.includes('抖音小时达')) {
    return '/static/images/icons/xsd.png'
  }

  // 如果都没匹配到，返回自营图标
  return '/static/images/icons/ziying.png'
}

/**
 * 处理渠道数据，添加图标
 * @param channels 原始渠道数据
 * @returns 处理后的渠道数据
 */
export function processChannelData(channels: any[]): any[] {
  if (!channels || !Array.isArray(channels)) return []

  console.log('处理渠道数据，原始数据:', JSON.stringify(channels))

  return channels.map((channel) => {
    // 使用智能匹配获取图标
    const icon = getChannelIcon(channel)
    console.log(`渠道 ${channel.name} (${channel.code}) 的图标:`, icon)

    return {
      ...channel,
      key: channel.code,
      label: channel.name,
      icon,
    }
  })
}

/**
 * 获取有效的选中值数组
 * @param value 原始值数组
 * @returns 处理后的有效值数组
 */
export function getValidValues(value: unknown[]): string[] {
  return (value || [])
    .map((v) => String(v))
    .filter((v) => v !== 'false' && v !== 'undefined' && v !== 'null')
}

/**
 * 判断是否所有渠道都被选中
 * @param selectedValues 当前选中值数组
 * @param channelCodes 所有渠道代码数组
 * @param allOptionValue 全选选项的值
 * @returns 是否所有渠道都被选中
 */
export function isAllSelected(
  selectedValues: string[],
  channelCodes: string[],
  allOptionValue: string,
): boolean {
  // 排除全选选项本身
  const channelCodesWithoutAll = channelCodes.filter((code) => code !== allOptionValue)

  // 检查是否所有渠道都被选中
  return (
    channelCodesWithoutAll.length > 0 &&
    channelCodesWithoutAll.every((code) => selectedValues.includes(code))
  )
}

/**
 * 同步全选状态
 * @param selectedValues 当前选中值数组
 * @param allChannelCodes 所有渠道代码数组
 * @param allOptionValue 全选选项的值
 * @returns 同步后的选中值数组
 */
export function syncAllSelectionState(
  selectedValues: string[],
  allChannelCodes: string[],
  allOptionValue: string,
): string[] {
  // 创建新数组避免引用问题
  const newSelectedValues = [...selectedValues]

  // 检查是否所有渠道都被选中
  const isAllSelected =
    allChannelCodes.length > 0 && allChannelCodes.every((code) => newSelectedValues.includes(code))

  // 检查是否包含全选选项
  const hasAllOption = newSelectedValues.includes(allOptionValue)

  // 同步全选状态
  if (isAllSelected && !hasAllOption) {
    // 所有选项都被选中，但没有全选标记，添加全选标记
    newSelectedValues.push(allOptionValue)
  } else if (!isAllSelected && hasAllOption) {
    // 不是所有选项都被选中，但有全选标记，移除全选标记
    return newSelectedValues.filter((code) => code !== allOptionValue)
  }

  return newSelectedValues
}
