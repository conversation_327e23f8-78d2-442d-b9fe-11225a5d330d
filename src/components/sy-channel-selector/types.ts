/**
 * 渠道选择器组件类型定义
 */

import type { PropType } from 'vue'

/**
 * 渠道选项类型
 */
export interface ChannelOption {
  /** 渠道代码 */
  code: string
  /** 渠道名称 */
  name: string
  /** 渠道图标 */
  icon?: string
  /** 渠道备注 */
  remark?: string | null
}

/**
 * 渠道选择器组件属性
 */
export const channelSelectorProps = {
  /** 渠道选项列表 */
  options: {
    type: Array as PropType<ChannelOption[]>,
    default: () => [],
  },
  /** 当前选中的渠道代码数组 */
  value: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  /** 是否显示选择器弹窗 */
  visible: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
  /** 弹窗标题 */
  title: {
    type: String as PropType<string>,
    default: '选择渠道',
  },
  /** 是否显示"全部"选项 */
  showAllOption: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
  /** "全部"选项的文本 */
  allOptionText: {
    type: String as PropType<string>,
    default: '全部',
  },
  /** "全部"选项的值 */
  allOptionValue: {
    type: String as PropType<string>,
    default: '',
  },
  /** 顶部偏移量，用于调整弹出位置 */
  topOffset: {
    type: Number as PropType<number>,
    default: 0,
  },
  /** 左侧偏移量，用于与左边文字对齐 */
  leftOffset: {
    type: Number as PropType<number>,
    default: 0,
  },
  /** 是否支持多选 */
  multiple: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
  /** 参数名称，用于传递给后端的参数名 */
  paramName: {
    type: String as PropType<string>,
    default: 'channelCodes',
  },
  /** 是否在选中/取消选中时自动关闭弹窗 */
  closeOnSelect: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
}

/**
 * 渠道选择器组件事件类型
 */
export interface ChannelSelectorEmits {
  'update:visible': [value: boolean]
  'update:value': [value: string[]]
  change: [value: string[], labels: string[]]
  close: []
  confirm: [value: string[], labels: string[]]
  cancel: []
}
