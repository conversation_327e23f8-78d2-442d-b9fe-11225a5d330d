<template>
  <view class="sy-channel-selector">
    <!-- 弹出层 - 不使用遮罩层 -->
    <view
      v-if="popupVisible"
      class="channel-selector-wrapper"
      :style="{
        top: props.topOffset + 'rpx',
      }"
    >
      <!-- 添加遮罩层 -->
      <view class="channel-selector-overlay" @click="handleOverlayClick"></view>
      <view class="channel-selector-popup">
        <!-- 弹窗内容 -->
        <view class="channel-selector-content">
          <!-- 无数据提示 -->
          <view v-if="!props.options || props.options.length === 0" class="channel-selector-empty">
            <text class="channel-selector-empty-text">暂无渠道数据</text>
          </view>

          <wd-checkbox-group
            v-else
            shape="square"
            checked-color="#F33429"
            v-model="tempSelectedValues"
            @change="handleCheckboxChange"
          >
            <!-- 全部选项 -->
            <view v-if="props.showAllOption" class="channel-selector-item">
              <wd-checkbox
                :model-value="isAllChecked ? props.allOptionValue : ''"
                @click="toggleAllChannels"
                :disabled="!props.multiple"
              >
                {{ props.allOptionText }}
              </wd-checkbox>
            </view>

            <!-- 渠道选项列表 -->
            <view
              v-for="item in props.options"
              :key="item.code"
              class="channel-selector-item"
              :class="{ 'channel-selector-item-view': !!item.icon }"
            >
              <wd-checkbox :model-value="item.code">
                <view class="channel-selector-item-content">
                  <view class="channel-selector-item-main">
                    <view v-if="item.icon" class="channel-selector-item-icon">
                      <image :src="item.icon" class="channel-icon" mode="aspectFit" />
                    </view>
                    <text class="channel-selector-item-text">{{ item.name }}</text>
                  </view>
                  <text v-if="item.remark" class="channel-selector-item-desc">
                    {{ item.remark }}
                  </text>
                </view>
              </wd-checkbox>
            </view>
          </wd-checkbox-group>
        </view>

        <!-- 底部按钮区域 -->
        <view class="channel-selector-footer">
          <view
            class="channel-selector-btn channel-selector-btn-cancel color-#333333"
            @click="handleCancel"
          >
            取消
          </view>
          <wd-divider vertical style="align-self: center" />
          <view
            class="channel-selector-btn channel-selector-btn-confirm color-#F33429"
            @click="handleConfirm"
          >
            确认
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed } from 'vue'
import { channelSelectorProps } from './types'
import { getValidValues, isAllSelected, syncAllSelectionState } from './utils'

// 定义组件名称
defineOptions({ name: 'SyChannelSelector' })

// 定义组件属性
const props = defineProps(channelSelectorProps)

// 定义组件事件
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'update:value': [value: string[]]
  change: [value: string[], labels: string[]]
  close: []
  confirm: [value: string[], labels: string[]]
  cancel: []
}>()

// 弹窗显示状态
const popupVisible = ref(false)

// 当前实际选中值数组（最终提交的值）
const selectedValues = ref<string[]>([])

// 临时选中值数组（用于在弹窗中操作）
const tempSelectedValues = ref<string[]>([])

// 获取所有渠道代码数组（不包含全选值）
const allChannelCodes = computed((): string[] => {
  return props.options.map((option) => option.code).filter((code) => code !== props.allOptionValue)
})

// 计算全选状态
const isAllChecked = computed((): boolean => {
  // 当没有渠道时，全选状态为false
  if (allChannelCodes.value.length === 0) return false

  // 检查是否所有渠道都被选中
  return allChannelCodes.value.every((code) => tempSelectedValues.value.includes(code))
})

// 打印调试信息
onMounted(() => {
  console.log('SyChannelSelector mounted, props:', props)
  // 添加点击事件监听

  // 初始化时检查是否需要同步全选状态
  syncAllOptionState()
})

// 在组件卸载时移除事件监听

// 监听外部visible变化
watch(
  () => props.visible,
  (val) => {
    popupVisible.value = val
    if (val) {
      // 打开弹窗时，初始化临时选中值为当前选中值
      tempSelectedValues.value = Array.isArray(selectedValues.value)
        ? [...selectedValues.value]
        : []

      // 打开弹窗时同步全选状态，确保全选状态正确
      setTimeout(() => {
        syncAllOptionState()
      }, 0)
    }
  },
  { immediate: true },
)

// 监听内部弹窗状态变化
watch(
  () => popupVisible.value,
  (newVal) => {
    console.log('SyChannelSelector popupVisible changed:', newVal)
    emit('update:visible', newVal)
  },
)

// 监听外部value变化
watch(
  () => props.value,
  (newVal) => {
    console.log('SyChannelSelector value changed:', newVal)
    // 确保是新数组，防止引用问题
    selectedValues.value = Array.isArray(newVal) ? [...newVal] : []

    // 外部value变化时，同步临时选中值
    tempSelectedValues.value = Array.isArray(newVal) ? [...newVal] : []

    // 外部value变化时，同步全选状态
    syncAllOptionState()
  },
  { immediate: true },
)

// 同步全选选项状态
const syncAllOptionState = (): void => {
  // 使用辅助函数同步临时选中值的全选状态
  tempSelectedValues.value = syncAllSelectionState(
    tempSelectedValues.value,
    allChannelCodes.value,
    props.allOptionValue,
  )
}

// 切换全选状态
const toggleAllChannels = (): void => {
  if (!props.multiple) {
    return
  }
  let newValues: string[] = []

  if (!isAllChecked.value) {
    // 当前非全选状态，变为全选状态
    newValues = [props.allOptionValue, ...allChannelCodes.value]
  } else {
    // 当前全选状态，变为非全选状态
    newValues = []
  }

  // 更新临时选中值
  tempSelectedValues.value = newValues
}

// 获取选中项的标签数组
const getSelectedLabels = (values: string[]): string[] => {
  const selectedLabels: string[] = []

  values.forEach((code) => {
    // 特殊处理全选选项
    if (code === props.allOptionValue) {
      selectedLabels.push(props.allOptionText)
      return
    }

    const option = props.options.find((item) => item.code === code)
    if (option) {
      selectedLabels.push(option.name)
    }
  })

  return selectedLabels
}

// 发送值变更事件
const emitValueChange = (newValue: string[]): void => {
  // 获取选中的渠道名称列表
  const selectedLabels = getSelectedLabels(newValue)

  // 触发事件
  emit('update:value', newValue)
  emit('change', newValue, selectedLabels)
}

// 上一个临时选中值
const lastTempChannelCode = ref('')
// 处理复选框组变化
const handleCheckboxChange = ({ value }: { value: string[] }) => {
  console.log('SyChannelSelector handleCheckboxChange:', tempSelectedValues.value, value)

  // 获取有效值
  const validValues = getValidValues(value)
  console.log('处理后的有效值:', validValues)

  // 检查是否全选状态被改变
  const wasAllSelected = tempSelectedValues.value.includes(props.allOptionValue)
  const isNowAllSelected = validValues.includes(props.allOptionValue)

  // 如果全选状态发生变化
  if (isNowAllSelected !== wasAllSelected) {
    // 用户点击了全选选项
    toggleAllChannels()
    return
  }

  // 处理常规选项选中/取消选中
  if (!props.multiple) {
    if (validValues.length > 1) {
      lastTempChannelCode.value = validValues[1]
    }
    console.log('xwj监听渠道选择：', selectedValues.value, lastTempChannelCode.value)
    if (selectedValues.value.length > 0 && validValues.length === 0) {
      validValues.push(
        lastTempChannelCode.value ? lastTempChannelCode.value : selectedValues.value[0],
      )
    } else {
      validValues.shift()
    }
  }
  const newValues = [...validValues]

  // 使用辅助函数同步全选状态
  tempSelectedValues.value = syncAllSelectionState(
    newValues,
    allChannelCodes.value,
    props.allOptionValue,
  )

  console.log('临时选中值更新为:', tempSelectedValues.value)
}

// 处理确认按钮点击
const handleConfirm = () => {
  console.log('SyChannelSelector handleConfirm, 选中值:', tempSelectedValues.value)

  // 先同步一次全选状态，确保全选标记正确
  syncAllOptionState()

  // 将临时选中值应用到实际选中值（创建新数组避免引用问题）
  selectedValues.value = [...tempSelectedValues.value]

  // 触发更新事件
  const selectedLabels = getSelectedLabels(selectedValues.value)
  emit('update:value', selectedValues.value)
  emit('change', selectedValues.value, selectedLabels)
  emit('confirm', selectedValues.value, selectedLabels)

  // 关闭弹窗
  popupVisible.value = false
  emit('close')
}

// 处理取消按钮点击
const handleCancel = () => {
  console.log('SyChannelSelector handleCancel')

  // 恢复临时选中值为实际选中值
  tempSelectedValues.value = [...selectedValues.value]

  // 触发取消事件
  emit('cancel')

  // 关闭弹窗
  popupVisible.value = false
  emit('close')
}

// 处理遮罩层点击
const handleOverlayClick = () => {
  handleCancel()
}

// 添加默认导出
defineExpose({
  handleCheckboxChange,
  handleCancel,
  handleConfirm,
  toggleAllChannels,
})
</script>

<style lang="scss" scoped>
// 颜色变量定义
$primary-color: #f33429;
$text-color: #333333;
$text-light-color: #999999;
$border-color: #edecee;
$border-light-color: #f5f5f5;
$bg-color: #ffffff;
$shadow-color: rgba(0, 0, 0, 0.08);

.sy-channel-selector {
  width: 100%;
  height: 0;
  overflow: visible;

  // 弹窗容器
  .channel-selector-wrapper {
    position: absolute;
    left: 20rpx;
    z-index: 999;

    .channel-selector-overlay {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 9998;
      background-color: transparent;
    }
    // 弹窗样式
    .channel-selector-popup {
      overflow: hidden;
      box-shadow: 0 4rpx 12rpx $shadow-color;
      background-color: $bg-color;
      height: max-content;
      width: 28vh;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 9999;
    }
  }

  .channel-selector-item-view {
    :deep(.wd-checkbox__txt) {
      line-height: 0 !important;
    }
  }

  // 弹窗内容
  .channel-selector-content {
    flex: 1;
    max-height: 60vh;
    overflow-y: auto;
  }

  // 弹窗底部
  .channel-selector-footer {
    display: flex;
    height: 88rpx;
    border-top: 1px solid $border-color;
  }

  // 底部按钮
  .channel-selector-btn {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    height: 100%;

    &-cancel {
      font-size: 26rpx;
      font-weight: normal;
      line-height: normal;
      text-align: center;
      letter-spacing: normal;
      color: #333333;
    }
  }

  // 渠道选项
  .channel-selector-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 80rpx;
    padding: 0rpx 24rpx;
    border: 1rpx solid #edecee;

    :deep() {
      .wd-checkbox {
        flex: 1;
        margin-bottom: 0rpx;

        .wd-checkbox__label {
          flex: 1;
          overflow: hidden;
          color: $text-color;
        }

        .wd-checkbox__shape {
          // 确保勾选框显示
          border-radius: 0;
          &.is-checked {
            background-color: $primary-color;
            border-color: $primary-color;
          }
        }
      }
    }
  }

  .channel-selector-item-content {
    display: flex;
    flex: 1;
    flex-direction: column;
  }

  .channel-selector-item-main {
    display: flex;
    align-items: center;
  }

  .channel-selector-item-text {
    font-size: 28rpx;
    line-height: 1.2;
    color: $text-color;
  }

  .channel-selector-item-desc {
    margin-top: 4rpx;
    font-size: 24rpx;
    color: $text-light-color;
  }

  .channel-selector-item-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 16rpx;
    overflow: hidden;
    border-radius: 50%;

    .channel-icon {
      width: 100%;
      height: 100%;
    }
  }

  // 空状态
  .channel-selector-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200rpx;

    &-text {
      font-size: 28rpx;
      color: $text-light-color;
    }
  }
}
</style>
