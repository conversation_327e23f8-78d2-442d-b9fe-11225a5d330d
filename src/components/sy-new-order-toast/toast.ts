/**
 * 新订单提示弹窗属性定义
 */
export const toastProps = {
  /** 是否显示弹窗 */
  modelValue: {
    type: Boolean,
    default: false,
  },
  /** 渠道文本，例如：美团、饿了么等 */
  channelText: {
    type: String,
    default: 'XXX',
  },
  /** 订单号 */
  orderNo: {
    type: String,
    default: '',
  },
  /** 自动关闭时间(毫秒)，0表示不自动关闭 */
  duration: {
    type: Number,
    default: 3000, // 固定3秒
  },
  /** 是否自动关闭，为false时忽略duration属性 */
  autoClose: {
    type: Boolean,
    default: true, // 改为默认自动关闭
  },
  /** 头部高度，用于动态调整横幅位置 */
  headerHeight: {
    type: Number,
    default: 0,
  },
}

/**
 * 新订单提示弹窗配置项
 */
export interface ToastOptions {
  /** 是否显示弹窗 */
  visible?: boolean
  /** 渠道文本，例如：美团、饿了么等 */
  channelText?: string
  /** 订单号 */
  orderNo?: string
  /** 自动关闭时间(毫秒)，0表示不自动关闭 */
  duration?: number
  /** 是否自动关闭，为false时忽略duration属性 */
  autoClose?: boolean
  /** 头部高度，用于动态调整横幅位置 */
  headerHeight?: number
  /** 关闭回调 */
  onClose?: (orderNo?: string) => void
}

/**
 * 默认配置
 */
export const defaultOptions: ToastOptions = {
  visible: false,
  channelText: 'XXX',
  orderNo: '',
  duration: 3000, // 固定3秒
  autoClose: true, // 改为默认自动关闭
  onClose: undefined,
}

/**
 * 合并配置
 * @param options 用户配置
 * @returns 合并后的配置
 */
export const mergeOptions = (options: ToastOptions): ToastOptions => {
  return {
    ...defaultOptions,
    ...options,
  }
}

/**
 * 创建弹窗实例
 * @param options 弹窗配置
 */
export const toast = (options: ToastOptions): void => {
  // 这个方法在实际组件中被调用，
  // 但在本项目中直接使用组件的方式引入，
  // 所以这里只是预留接口
  console.log('创建弹窗', options)
}

/**
 * 销毁弹窗
 */
export const destroyToast = (): void => {
  // 销毁逻辑预留
  console.log('销毁弹窗')
}
