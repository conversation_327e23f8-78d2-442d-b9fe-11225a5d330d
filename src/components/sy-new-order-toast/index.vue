<!-- 新订单提示弹窗 -->
<template>
  <view class="sy-new-order-toast" v-if="show" :style="{ top: toastTopPosition }">
    <view class="toast-container flex justify-between items-center" @click="handleContentClick">
      <text class="toast-text">您有新的{{ channelText }}订单，请及时处理</text>
      <view class="close-button" @click.stop="handleClose">
        <view class="close-icon"></view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, watch, onBeforeUnmount } from 'vue'
import { toastProps } from './toast'

// 定义组件属性
const props = defineProps(toastProps)

// 定义事件
const emit = defineEmits(['update:modelValue', 'close', 'content-click'])

// 计算属性：控制弹窗显示
const show = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

// 计算横幅顶部位置
const toastTopPosition = computed(() => {
  // 如果提供了头部高度且大于0，则使用头部高度 + 16rpx的间距
  // if (props.headerHeight > 0) {
  //   console.log('使用动态头部高度计算横幅位置:', props.headerHeight)
  //   return `${props.headerHeight + 16}rpx`
  // }

  // 否则根据不同平台使用不同的默认值
  let defaultTop = '287rpx'

  // #ifdef APP-PLUS
  defaultTop = '328rpx' // APP端通常头部更高
  // #endif

  // #ifdef MP
  defaultTop = '295rpx' // 小程序端
  // #endif

  // #ifdef H5
  defaultTop = '275rpx' // H5端
  // #endif

  console.log('使用默认横幅位置:', defaultTop)
  return defaultTop
})

// 自动关闭定时器
let closeTimer: number | null = null

// 创建定时器
const createTimer = () => {
  // 清除现有定时器
  if (closeTimer) {
    clearTimeout(closeTimer)
    closeTimer = null
  }

  // 设置3秒自动关闭定时器
  console.log('设置3秒自动关闭定时器')
  closeTimer = setTimeout(() => {
    console.log('3秒定时器触发，自动关闭弹窗')
    handleClose()
  }, 3000) as unknown as number
}

// 监听显示状态变化，每次显示时重新计时
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      // 显示时创建新的3秒定时器
      createTimer()
    } else {
      // 隐藏时清除定时器
      if (closeTimer) {
        clearTimeout(closeTimer)
        closeTimer = null
      }
    }
  },
  { immediate: true },
)

// 监听内容变化，当有新订单时重新计时
watch(
  () => [props.channelText, props.orderNo],
  () => {
    if (props.modelValue) {
      // 内容变化且正在显示时，重新计时
      console.log('订单内容变化，重新计时3秒')
      createTimer()
    }
  },
)

// 内容点击处理
const handleContentClick = (event: Event) => {
  // 防止事件冒泡
  event.stopPropagation()
  // 触发内容点击事件
  emit('content-click', props.orderNo)
}

// 关闭处理
const handleClose = () => {
  emit('close', props.orderNo)
  show.value = false

  // 清除定时器
  if (closeTimer) {
    clearTimeout(closeTimer)
    closeTimer = null
  }
}

// 组件销毁前清理定时器
onBeforeUnmount(() => {
  if (closeTimer) {
    clearTimeout(closeTimer)
    closeTimer = null
  }
})

// 暴露方法
defineExpose({
  show,
  close: handleClose,
})

// 添加默认导出
defineOptions({
  name: 'SyNewOrderToast',
})
</script>

<style lang="scss">
.sy-new-order-toast {
  position: fixed;
  /* top值现在通过内联样式动态设置 */
  top: 352rpx;
  right: 0;
  left: 0;
  z-index: 97;
  display: flex;
  justify-content: center;
  padding: 0 24rpx;

  .toast-container {
    box-sizing: border-box;
    width: 100%;
    height: 72rpx;
    padding: 16rpx 40rpx;
    cursor: pointer;
    background-color: #f33429;
    border-radius: 4rpx;
  }

  .toast-text {
    font-size: 28rpx;
    font-weight: 500;
    line-height: 40rpx;
    color: #ffffff;
  }

  .close-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40rpx;
    height: 40rpx;

    .close-icon {
      position: relative;
      width: 20rpx;
      height: 20rpx;

      &::before,
      &::after {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 24rpx;
        height: 2rpx;
        content: '';
        background-color: #ffffff;
      }

      &::before {
        transform: translate(-50%, -50%) rotate(45deg);
      }

      &::after {
        transform: translate(-50%, -50%) rotate(-45deg);
      }
    }
  }
}
</style>
