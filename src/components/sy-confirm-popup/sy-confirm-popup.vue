<template>
  <wd-popup v-model="show" position="center" close-on-click-modal :custom-style="customStyle">
    <view class="sy-confirm-popup">
      <!-- 标题部分 -->
      <view class="sy-confirm-title" v-if="title">{{ title }}</view>

      <!-- 内容部分 -->
      <view class="sy-confirm-content">
        <slot>
          <template v-if="Array.isArray(content)">
            <view v-for="(item, index) in content" :key="index" class="content-line">
              {{ item }}
            </view>
          </template>
          <template v-else>
            {{ content }}
          </template>
        </slot>
      </view>

      <!-- 底部按钮区域 -->
      <view class="sy-confirm-actions" v-if="showFooter">
        <!-- 左侧按钮（通常为取消或拒绝） -->
        <view v-if="showCancel" class="left-btn" :style="cancelButtonStyle" @click="onCancel">
          <text class="left-btn-text">{{ cancelText }}</text>
        </view>

        <!-- 右侧按钮（通常为确认） -->
        <view class="right-btn" :style="confirmButtonStyle" @click="onConfirm">
          <text class="right-btn-text">{{ confirmText }}</text>
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

// 定义组件属性
const props = defineProps({
  /** 是否显示弹窗 */
  modelValue: {
    type: Boolean,
    default: false,
  },
  /** 弹窗标题 */
  title: {
    type: String,
    default: '提示',
  },
  /** 弹窗内容 */
  content: {
    type: [String, Array],
    default: '',
  },
  /** 确认按钮文本 */
  confirmText: {
    type: String,
    default: '确定',
  },
  /** 取消按钮文本 */
  cancelText: {
    type: String,
    default: '取消',
  },
  /** 是否显示取消按钮 */
  showCancel: {
    type: Boolean,
    default: true,
  },
  /** 确认按钮类型 */
  confirmType: {
    type: String,
    default: 'primary',
  },
  /** 取消按钮类型 */
  cancelType: {
    type: String,
    default: 'default',
  },
  /** 是否显示底部按钮区域 */
  showFooter: {
    type: Boolean,
    default: true,
  },
  /** 自定义确认按钮样式 */
  confirmButtonStyle: {
    type: String,
    default: '',
  },
  /** 自定义取消按钮样式 */
  cancelButtonStyle: {
    type: String,
    default: '',
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'confirm', 'cancel'])

// 计算属性：控制弹窗显示
const show = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

// 自定义弹窗样式
const customStyle = computed(() => {
  return 'border-radius: 24rpx; overflow: hidden;'
})

// 点击确认按钮
const onConfirm = () => {
  show.value = false
  emit('confirm')
}

// 点击取消按钮
const onCancel = () => {
  show.value = false
  emit('cancel')
}

// 添加默认导出
defineOptions({
  name: 'SyConfirmPopup',
})
</script>

<script lang="ts">
// 为了支持默认导出
export default {}
</script>

<style lang="scss">
.sy-confirm-popup {
  box-sizing: border-box;
  width: 626rpx;
  overflow: hidden;
  background: #ffffff;
  border-radius: 24rpx;

  .sy-confirm-title {
    padding: 32rpx 24rpx 16rpx;
    font-size: 30rpx;
    font-weight: 600;
    line-height: 42rpx;
    color: #333333;
    text-align: center;
  }

  .sy-confirm-content {
    max-height: 108rpx;
    padding: 0 32rpx 47rpx;
    overflow-y: auto;
    font-size: 26rpx;
    font-weight: 400;
    line-height: 36rpx;
    color: #999999;
    text-align: center;
    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 4rpx;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2rpx;
    }

    &::-webkit-scrollbar-thumb {
      background: #ddd;
      border-radius: 2rpx;
    }

    .content-line {
      margin-bottom: 8rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .sy-confirm-actions {
    display: flex;
    gap: 24rpx;
    justify-content: space-between;
    padding: 0 24rpx 24rpx;

    .left-btn {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      height: 78rpx;
      background-color: #ffffff;
      border: 2rpx solid #9a9a9a;
      border-radius: 39rpx;

      &:active {
        opacity: 0.8;
      }

      .left-btn-text {
        font-size: 26rpx;
        font-weight: 500;
        color: #333333;
      }
    }

    .right-btn {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      height: 78rpx;
      background-color: #f33429; // 海底捞-红色
      border-radius: 39rpx;

      &:active {
        opacity: 0.8;
      }

      .right-btn-text {
        font-size: 26rpx;
        font-weight: 500;
        color: #ffffff;
      }
    }
  }
}
</style>
