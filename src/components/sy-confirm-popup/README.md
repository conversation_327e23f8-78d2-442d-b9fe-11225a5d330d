# SyConfirmPopup 确认弹窗组件

基于设计稿实现的确认弹窗组件，支持自定义标题、内容、按钮文本和样式。

## 功能特点

- 支持自定义标题和内容
- 支持自定义确认和取消按钮文本
- 支持显示/隐藏取消按钮
- 支持自定义按钮样式

## 使用示例

```vue
<template>
  <view>
    <wd-button @click="showPopup = true">显示确认弹窗</wd-button>

    <sy-confirm-popup
      v-model="showPopup"
      title="建议您与顾客沟通协商后处理"
      content="当您未与顾客协商一致，直接拒绝退款用户有权向客服申诉，如被平台审核为商家原因导致订单取消，将影响您的排名。"
      cancel-text="已沟通，拒绝退款"
      confirm-text="取消"
      @cancel="handleReject"
      @confirm="handleCancel"
    />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SyConfirmPopup from '@/components/sy-confirm-popup/sy-confirm-popup.vue'

const showPopup = ref(false)

const handleReject = () => {
  console.log('已选择拒绝退款')
  // 执行拒绝退款逻辑
}

const handleCancel = () => {
  console.log('已取消弹窗')
  // 执行取消操作
}
</script>
```

## 属性说明

| 属性名             | 类型         | 默认值    | 说明                               |
| ------------------ | ------------ | --------- | ---------------------------------- |
| modelValue         | Boolean      | false     | 是否显示弹窗，支持 v-model 绑定    |
| title              | String       | '提示'    | 弹窗标题                           |
| content            | String/Array | ''        | 弹窗内容，可以是字符串或字符串数组 |
| confirmText        | String       | '确定'    | 确认按钮文本                       |
| cancelText         | String       | '取消'    | 取消按钮文本                       |
| showCancel         | Boolean      | true      | 是否显示取消按钮                   |
| confirmType        | String       | 'primary' | 确认按钮类型                       |
| cancelType         | String       | 'default' | 取消按钮类型                       |
| showFooter         | Boolean      | true      | 是否显示底部按钮区域               |
| confirmButtonStyle | String       | ''        | 自定义确认按钮样式                 |
| cancelButtonStyle  | String       | ''        | 自定义取消按钮样式                 |

## 事件说明

| 事件名            | 说明                     | 回调参数         |
| ----------------- | ------------------------ | ---------------- |
| update:modelValue | 当弹窗显示状态变化时触发 | (value: boolean) |
| confirm           | 点击确认按钮时触发       | -                |
| cancel            | 点击取消按钮时触发       | -                |

## 插槽

| 插槽名  | 说明                            |
| ------- | ------------------------------- |
| default | 弹窗内容区域，替代 content 属性 |

</rewritten_file>
