<route lang="json5">
{
  style: {
    navigationBarTitleText: '确认弹窗示例',
  },
}
</route>

<template>
  <view class="demo-container p-32rpx">
    <view class="title mb-24rpx">SyConfirmPopup 确认弹窗示例</view>

    <!-- 基础使用示例 -->
    <view class="section mb-48rpx">
      <view class="section-title mb-24rpx">基础用法</view>
      <wd-button block @click="showBasicPopup = true">显示基础弹窗</wd-button>

      <sy-confirm-popup
        v-model="showBasicPopup"
        title="提示"
        content="这是一个基础的确认弹窗示例"
        confirm-text="确定"
        cancel-text="取消"
        @confirm="onConfirm('基础弹窗')"
        @cancel="onCancel('基础弹窗')"
      />
    </view>

    <!-- 拒绝退款场景示例 -->
    <view class="section mb-48rpx">
      <view class="section-title mb-24rpx">拒绝退款场景</view>
      <wd-button block @click="showRejectPopup = true">显示拒绝退款弹窗</wd-button>

      <sy-confirm-popup
        v-model="showRejectPopup"
        title="建议您与顾客沟通协商后处理"
        content="当您未与顾客协商一致，直接拒绝退款用户有权向客服申诉，如被平台审核为商家原因导致订单取消，将影响您的排名。"
        cancel-text="已沟通，拒绝退款"
        confirm-text="取消"
        @cancel="onReject"
        @confirm="onCancelReject"
      />
    </view>

    <!-- 自定义场景示例 -->
    <view class="section mb-48rpx">
      <view class="section-title mb-24rpx">自定义样式</view>
      <wd-button block @click="showCustomPopup = true">显示自定义弹窗</wd-button>

      <sy-confirm-popup
        v-model="showCustomPopup"
        title="自定义样式示例"
        content="您可以自定义按钮样式和文本"
        confirm-text="主操作"
        cancel-text="次要操作"
        confirm-button-style="background-color: #07c160; border-radius: 8rpx;"
        cancel-button-style="background-color: #f2f2f2; color: #333; border-radius: 8rpx;"
        @confirm="onConfirm('自定义弹窗')"
        @cancel="onCancel('自定义弹窗')"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 控制弹窗显示
const showBasicPopup = ref(false)
const showRejectPopup = ref(false)
const showCustomPopup = ref(false)

// 基础弹窗事件处理
const onConfirm = (type: string) => {
  uni.showToast({
    title: `点击了${type}的确认按钮`,
    icon: 'none',
  })
}

const onCancel = (type: string) => {
  uni.showToast({
    title: `点击了${type}的取消按钮`,
    icon: 'none',
  })
}

// 拒绝退款场景事件处理
const onReject = () => {
  uni.showToast({
    title: '已选择拒绝退款',
    icon: 'none',
  })
  // 这里可以添加拒绝退款的业务逻辑
}

const onCancelReject = () => {
  uni.showToast({
    title: '已取消拒绝退款操作',
    icon: 'none',
  })
}
</script>

<style lang="scss">
.demo-container {
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }

  .section {
    .section-title {
      font-size: 28rpx;
      font-weight: 500;
      color: #666;
    }
  }
}
</style>
