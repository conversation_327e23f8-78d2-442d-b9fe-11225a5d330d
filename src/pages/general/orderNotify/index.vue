<route lang="json5">
{
  style: {
    navigationBarTitleText: '订单提醒',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>

<template>
  <view
    class="message-setting-page bg-#FFFFFF"
    :style="{ '--status-bar-height': statusBarHeight + 'px' }"
  >
    <wd-navbar title="订单提醒" left-text="" left-arrow @click-left="handleClickBack" />
    <!-- 开启消息推送 -->
    <view class="notify-setting">
      <!-- 铃声试听 -->
      <view class="reminder-item">
        <wd-cell title="铃声试听" is-link to="../ringPreview/index" />
        <view class="item-description">
          美团商家端运行时有新订单、异常订单待处理等消息时，当前手机会收到铃声提醒
        </view>
      </view>

      <!-- 预订单快到时间(60/30分钟) -->
      <view class="reminder-item">
        <view class="item-title"><text>预订单提醒</text></view>
        <wd-cell title="预订单快到时间(60/30分钟)" value="1次" to="" replace></wd-cell>
      </view>

      <!-- 接单中催单提醒 -->
      <view class="reminder-item">
        <view class="item-title"><text>接单与催单提醒</text></view>
        <wd-cell title="新订单" value="3次" to="" replace></wd-cell>
        <wd-cell style="margin-top: 2rpx" title="催单" value="1次" to="" replace></wd-cell>
      </view>

      <!-- 取消与退款提醒 -->
      <view class="reminder-item">
        <view class="item-title"><text>取消与退款提醒</text></view>
        <wd-cell title="订单被用户取消" value="3次" to="" replace></wd-cell>
        <wd-cell style="margin-top: 2rpx" title="用户申请退款" value="3次"></wd-cell>
        <wd-cell style="margin-top: 2rpx" title="顾客催促审核退款" value="1次"></wd-cell>
      </view>

      <!-- 配送提醒 -->
      <view class="reminder-item">
        <view class="item-title"><text>配送异常</text></view>

        <wd-cell title="配送异常" value="1次" :clickable="false" replace></wd-cell>
      </view>

      <!-- 提示信息 -->
      <view class="reminder-note">
        <text>以上消息和铃声设置仅对当前手机生效</text>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'

// 状态栏高度
const statusBarHeight = ref(0)

// 获取状态栏高度
const getStatusBarHeight = () => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  console.log(' systemInfo.statusBarHeight', systemInfo.statusBarHeight)
}

// 生命周期
onMounted(() => {
  getStatusBarHeight()
})

const handleClickBack = () => {
  uni.navigateBack()
}
</script>
<style lang="scss" scoped>
@import './index.scss';
</style>
