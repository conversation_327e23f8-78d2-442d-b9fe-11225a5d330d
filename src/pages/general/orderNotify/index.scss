.message-setting-page {
  padding-top: var(--status-bar-height);
  height: 100vh;

  .notify-setting {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f6f6f6;

    .item-description {
      margin: 16rpx 24rpx;
      font-size: 12px;
      font-weight: normal;
      line-height: normal;
      letter-spacing: normal;
      color: #999999;
    }

    .item-title {
      margin: 0rpx 0rpx 16rpx 30rpx;
      font-size: 26rpx;
      font-weight: normal;
      line-height: normal;
      letter-spacing: normal;
      color: #999999;
    }

    .reminder-item {
      margin-top: 40rpx;
      :deep(.wd-cell__left) {
        flex: content;
      }
    }

    .reminder-note {
      align-self: center;
      margin-top: 40rpx;
      font-size: 26rpx;
      font-weight: normal;
      line-height: normal;
      text-align: center;
      letter-spacing: normal;
      color: #999999;
    }
  }
}
