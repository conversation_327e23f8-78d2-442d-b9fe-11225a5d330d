.ring-setting-page {
  padding-top: var(--status-bar-height);

  .ring-container {
    background-color: #f6f6f6;
    height: 100vh;

    .reminder-item {
      .item-title {
        margin: 0rpx 0rpx 16rpx 24rpx;
        padding-top: 24rpx;
        font-size: 26rpx;
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #999999;
      }

      .item-ring {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        background-color: white;
        padding: 32rpx 24rpx;
        font-size: 30rpx;
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #000000;

        .play-icon {
          color: #f33429;
          display: flex;
          align-items: center;
          text-align: center;
        }
      }
    }
  }
}
