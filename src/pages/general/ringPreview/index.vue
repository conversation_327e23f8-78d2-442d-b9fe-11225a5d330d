<route lang="json5">
{
  style: {
    navigationBarTitleText: '订单提醒',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>
<template>
  <view class="ring-setting-page bg-#FFFFFF">
    <wd-navbar title="铃声试听" left-text="" left-arrow @click-left="handleClickBack" />

    <view class="ring-container">
      <!-- 预订单提醒 -->
      <view class="reminder-item">
        <view class="item-title"><text>预订单提醒</text></view>
        <view class="item-ring">
          <text>预订单快到时间</text>
          <view class="play-icon" @click="toPlayRingPreview(0)">
            <!--pause-circle 播放图标-->
            <wd-icon name="play-circle" size="32rpx" color="#F33429"></wd-icon>
            <span style="margin-left: 3rpx">试听</span>
          </view>
        </view>
      </view>

      <view class="reminder-item">
        <view class="item-title"><text>接单与催单提醒</text></view>
        <view class="item-ring">
          <text>新订单</text>
          <view class="play-icon" @click="toPlayRingPreview(1)">
            <wd-icon name="play-circle" size="32rpx" color="#F33429"></wd-icon>
            <span style="margin-left: 3rpx">试听</span>
          </view>
        </view>
        <view class="item-ring" style="margin-top: 2rpx">
          <text>催单</text>
          <view class="play-icon" @click="toPlayRingPreview(2)">
            <wd-icon name="play-circle" size="32rpx" color="#F33429"></wd-icon>
            <span style="margin-left: 3rpx">试听</span>
          </view>
        </view>
      </view>

      <view class="reminder-item">
        <view class="item-title"><text>取消与退款提醒</text></view>
        <view class="item-ring">
          <text>订单被用户取消</text>
          <view class="play-icon" @click="toPlayRingPreview(3)">
            <wd-icon name="play-circle" size="32rpx" color="#F33429"></wd-icon>
            <span style="margin-left: 3rpx">试听</span>
          </view>
        </view>
        <view class="item-ring" style="margin-top: 2rpx">
          <text>用户申请退款</text>
          <view class="play-icon" @click="toPlayRingPreview(4)">
            <wd-icon name="play-circle" size="32rpx" color="#F33429"></wd-icon>
            <span style="margin-left: 3rpx">试听</span>
          </view>
        </view>
        <view class="item-ring" style="margin-top: 2rpx">
          <text>顾客催促审核退款</text>
          <view class="play-icon" @click="toPlayRingPreview(5)">
            <wd-icon name="play-circle" size="32rpx" color="#F33429"></wd-icon>
            <span style="margin-left: 3rpx">试听</span>
          </view>
        </view>
      </view>

      <view class="reminder-item">
        <view class="item-title"><text>配送提醒</text></view>
        <view class="item-ring">
          <text>配送异常</text>
          <view class="play-icon" @click="toPlayRingPreview(6)">
            <wd-icon name="play-circle" size="32rpx" color="#F33429"></wd-icon>
            <span style="margin-left: 3rpx">试听</span>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { initTTS, playTTSAnnouncement, type TTSPayload } from '@/utils/tts'

const handleClickBack = () => {
  uni.navigateBack()
}

const toPlayRingPreview = (index: number) => {
  let playTTS: TTSPayload = { content: '', needMusic: false }
  switch (index) {
    case 0:
      playTTS = {
        content: '平台1号预订单快到时间了，请注意备货，若小票丢失请重新打印',
        needMusic: true,
      }
      break
    case 1:
      playTTS = {
        content: '你有新的平台订单请及时处理',
        needMusic: true,
      }
      break
    case 2:
      playTTS = {
        content: '平台1号订单用户向您催单，请您及时处理。',
        needMusic: true,
      }
      break
    case 3:
      playTTS = {
        content: '平台1号订单已被用户取消，为避免损失，请不要继续备货。',
        needMusic: true,
      }
      break
    case 4:
    case 5:
      playTTS = {
        content: '平台1号订单用户申请退款，请您及时处理。',
        needMusic: true,
      }
      break
    case 6:
      playTTS = {
        content: '平台1号订单配送异常，请重点关注。',
        needMusic: true,
      }
      break
  }

  playTTSAnnouncement(playTTS)
}
</script>
<style lang="scss" scoped>
@import './index.scss';
</style>
