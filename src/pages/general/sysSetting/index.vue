<route lang="json5">
{
  style: {
    navigationBarTitleText: '来电不响设置',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>

<template>
  <view
    class="sys-setting-page bg-#FFFFFF"
    :style="{ '--status-bar-height': statusBarHeight + 'px' }"
  >
    <wd-navbar title="来电不响设置" left-text="" left-arrow @click-left="handleClickBack" />
    <!-- 开启消息推送 -->
    <view class="notify-setting">
      <!-- 开启通知权限 -->
      <view class="reminder-item">
        <wd-cell title="需要开启通知权限" value="去设置" is-link @click="toSetSetting" />
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'

// 状态栏高度
const statusBarHeight = ref(0)

// 获取状态栏高度
const getStatusBarHeight = () => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  console.log(' systemInfo.statusBarHeight', systemInfo.statusBarHeight)
}

// 生命周期
onMounted(() => {
  getStatusBarHeight()
})

const handleClickBack = () => {
  uni.navigateBack()
}

const toSetSetting = () => {
  uni.openAppAuthorizeSetting()
}
</script>
<style lang="scss" scoped>
@import './index.scss';
</style>
