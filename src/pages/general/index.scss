.general-settings-page {
  padding-top: var(--status-bar-height);

  .general-setting {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f6f6f6;
  }
}

.page-content {
  padding: 10rpx;
  margin: 12rpx;
  border-radius: 8rpx;
}

.logout-button-container {
  position: fixed;
  bottom: 46rpx;
  display: flex;
  align-self: center;
  width: 660rpx;
  height: 100rpx;
  margin: auto;
  font-size: 32rpx;
  color: white;
  background: #f33429;
  border-radius: 50rpx;
}

// 设备底部安全区域处理
.pb-safe {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

// 版本号样式
.version-container {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
  margin-bottom: 20rpx;
}

.version-text {
  font-size: 26rpx;
  color: #999999;
}
