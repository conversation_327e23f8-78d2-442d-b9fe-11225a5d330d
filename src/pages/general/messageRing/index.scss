.message-setting-page {
  display: flex;
  flex-direction: column;
  padding-top: var(--status-bar-height);

  .setting-container {
    width: 100%;
    height: 100vh;
    background-color: #f5f6fa;

    .message-push-container {
      background-color: white;
      margin-top: 12rpx;
      padding: 30rpx 0rpx;

      .setting-notyfy {
        width: calc(100vw - 56rpx);
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin: 0rpx 28rpx;
      }
      .end-time {
        margin: 0rpx 32rpx;
        color: #999999;
        font-size: 28rpx;
      }
      .label-container {
        font-size: 26rpx;
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #999999;
      }
      :deep(.wd-cell__left) {
        flex: content;
      }
      :deep(.custom-value) {
        border: none;
      }
    }

    .setting-item {
      margin-top: 40rpx;
      .item-title {
        margin-left: 20rpx;
      }
      .ring-setting {
        margin-top: 20rpx;
        background-color: white;
      }
    }
  }
}
