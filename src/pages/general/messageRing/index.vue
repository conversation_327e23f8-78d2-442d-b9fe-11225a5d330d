<route lang="json5">
{
  style: {
    navigationBarTitleText: '消息和铃声设置',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>
<template>
  <view class="message-setting-page bg-#FFFFFF min-h-screen">
    <wd-navbar title="消息和铃声设置" left-text="" left-arrow @click-left="handleClickBack" />
    <!-- 开启消息推送 -->
    <view class="setting-container">
      <view class="message-push-container">
        <view class="setting-notyfy">
          <text>开启消息推送</text>
          <wd-button
            custom-class="custom-value"
            size="small"
            plain
            :disabled="pushNotification"
            @click="toSetSettingPage"
          >
            {{ pushNotification ? '已开启' : '去开启' }}
          </wd-button>
        </view>

        <view class="end-time">开启后，会收到消息的横幅，请务必保持开启，以免消息推送延迟</view>
      </view>

      <!-- 铃声提醒 -->
      <view class="setting-item">
        <text class="item-title">铃声提醒</text>
        <view class="ring-setting">
          <wd-cell title="震动提醒" value=""></wd-cell>
          <wd-cell title="铃声提醒音量" value="音量过小，可能影响接单" />
          <wd-slider
            v-model="volumeLevel"
            :step="1"
            style="margin: 0 40rpx"
            @dragend="onVolumeChange"
          />
        </view>
      </view>

      <!-- 场景设置 -->
      <view class="setting-item">
        <text class="item-title">场景设置</text>
        <wd-cell style="margin-top: 20rpx" title="订单提醒" is-link to="../orderNotify/index" />
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'

// 推送通知开启状态
const pushNotification = ref(false)
// 铃声音量大小
const volumeLevel = ref(0.0)

const handleClickBack = () => {
  uni.navigateBack()
}

onMounted(() => {
  // 获取通知权限状态
  const isOpen = judgeAppPermissionPush()
  console.log('是否开启了通知权限', isOpen)
  pushNotification.value = isOpen

  // 获取铃声音量大小
  if (uni.getSystemInfoSync().platform === 'android') {
    const ringtoneVolume = plus.device.getVolume()
    volumeLevel.value = Math.round(Number(ringtoneVolume.toFixed(2)) * 100)
    console.log('当前铃声音量：' + ringtoneVolume, volumeLevel.value)
  }
})

// 设置音量
const onVolumeChange = (valueObj: any) => {
  const volumeValue = Math.round(valueObj.value) // 确保是整数

  console.log('音量改变：', valueObj.value, volumeValue / 100)
  plus.device.setVolume(valueObj.value / 100.0)
}

function judgeAppPermissionPush(): boolean {
  const notificationAuthorized = uni.getAppAuthorizeSetting().notificationAuthorized
  return notificationAuthorized === 'authorized'
}

// 调用系统通知设置
const toSetSettingPage = () => {
  if (!pushNotification.value) {
    uni.reLaunch({
      url: '../sysSetting/index',
    })
  }
}
</script>
<style lang="scss" scoped>
@import './index.scss';
</style>
