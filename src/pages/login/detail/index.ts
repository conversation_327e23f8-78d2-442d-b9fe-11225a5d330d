import { ref, reactive } from 'vue'
import type { FormRules } from 'wot-design-uni/components/wd-form/types'
import {
  passwordLoginAPI,
  queryTenantsByPhoneAPI,
  sendVerifyCodeAPI,
  codeLoginAPI,
  getUserInfoByIdAPI,
} from '@/service/index/login'
import { setDeviceOnline } from '@/utils/device'
import { useUserStore } from '@/store'
import { useDeviceStore } from '@/store/device'
import { useAuthStore } from '@/store/auth'
import { rstr2b64, validPhone } from '@/utils/index'

// 防抖函数
function debounce<T extends (...args: any[]) => any>(fn: T, delay: number) {
  let timer: number | null = null
  return function (this: any, ...args: Parameters<T>) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
      timer = null
    }, delay)
  }
}

// 页面数据
export const pageData = reactive({
  activeTab: 'password' as 'password' | 'code',
  agree: false,
  showPrivacy: false,
  sendingCode: false,
  countdown: 60,
  canSendCode: false,
  tenants: [] as any[],
  form: {
    userName: '',
    password: '',
    phone: '',
    code: '',
  },
  checkCodeUniqueId: '',
  showLoginError: false,
  loginErrorMsg: '',
  loginErrorCode: '',
  userInfo: {} as any,
  passwordVisible: false,
})

export const passwordInputType = ref('password')
// 表单引用
export const formRef = ref()

// 倒计时定时器
let timer: any = null

// 校验函数
export const validateUserName = (val: string) => !!val
export const validatePassword = (val: string) => !!val
export const validatePhone = (val: string) => !!val
export const validatePhoneFormat = (val: string) => /^1[3-9]\d{9}$/.test(val)
export const validateCode = (val: string) => !!val

/**
 * 手机号输入处理
 */
export function onPhoneInput(event: any) {
  // 从事件对象中获取value
  const value = event && event.value !== undefined ? event.value : event

  // 立即反馈UI状态
  if (!value || !validatePhoneFormat(value)) {
    pageData.canSendCode = false
    return
  }

  // 使用防抖函数处理查询请求
  debouncedQueryTenants(value)
}

const password = computed({
  get: (): string => pageData.form.password,
  set: (newValue: string) => {
    pageData.form.password = newValue.replaceAll(' ', '')
  },
})

export function onPasswordInput(event: any) {
  if (event && validatePassword(event.value)) {
    pageData.form.password = event.value.replaceAll(' ', '')
  }
  console.log('onPasswordInput', pageData.form.password)
}

/**
 * 清空输入框时的回调，发送验证码按钮置为禁用
 */
export const onPhoneClear = () => {
  pageData.canSendCode = false
}

/**
 * 创建防抖版本的查询租户函数
 */
const debouncedQueryTenants = debounce((phone: string) => {
  // 只有当手机号格式完全正确时才查询
  if (validatePhone(phone) && validatePhoneFormat(phone)) {
    queryTenantsByPhone(phone)
  }
}, 0) // 300ms 的防抖延迟

/**
 * 查询租户
 */
function queryTenantsByPhone(phone: string) {
  // 获取用户store
  const userStore = useUserStore()

  // 如果格式不正确，设置不能发送验证码并返回
  if (!validatePhone(phone) || !validatePhoneFormat(phone)) {
    pageData.canSendCode = false
    return
  }

  // 查询租户
  queryTenantsByPhoneAPI(phone)
    .then((res) => {
      // 修改数据获取方式，根据实际后端返回结构
      // 如果返回的是完整用户信息，包含 tenants 数组
      if (res.data && res.data.tenants && res.data.tenants.length > 0) {
        pageData.tenants = res.data.tenants
        pageData.canSendCode = true

        // 提取租户ID列表并保存到store
        const tenantIds = res.data.tenants.map((item: any) => item.id).join(',')
        // 从租户列表中提取用户ID
        const userIds = res.data.tenants.map((item: any) => item.userId).join(',')

        // 更新用户信息中的租户ID和用户ID
        const currentUserInfo = userStore.userInfo
        userStore.setUserInfo({
          ...currentUserInfo,
          tenantIds,
          userIds,
        })
      }
      // 如果返回的是 data.data 结构（保留原有处理逻辑作为备选）
      else if (
        res.data &&
        res.data.data &&
        Array.isArray(res.data.data) &&
        res.data.data.length > 0
      ) {
        pageData.tenants = res.data.data
        pageData.canSendCode = true

        // 提取租户ID列表并保存到store
        const tenantIds = res.data.data.map((item: any) => item.id).join(',')
        // 从租户列表中提取用户ID
        const userIds = res.data.data.map((item: any) => item.userId).join(',')

        // 更新用户信息中的租户ID和用户ID
        const currentUserInfo = userStore.userInfo
        userStore.setUserInfo({
          ...currentUserInfo,
          tenantIds,
          userIds,
        })
      }
      // 没有租户信息
      else {
        pageData.tenants = []
        pageData.canSendCode = false
        pageData.loginErrorMsg = res.resultMsg || '未查询到租户'
        pageData.showLoginError = true
        pageData.loginErrorCode = ''
      }
    })
    .catch((error) => {
      console.error('查询租户失败', error)
      pageData.tenants = []
      pageData.canSendCode = false
      pageData.loginErrorMsg = error?.message || '查询租户失败'
      pageData.showLoginError = true
      pageData.loginErrorCode = ''
    })
}

/**
 * Tab切换处理
 */
export function onTabChange(name: string) {
  // 清空表单
  Object.assign(pageData.form, { userName: '', password: '', phone: '', code: '' })
  // 重置验证码状态
  pageData.canSendCode = false
  pageData.sendingCode = false
  // 清空验证码唯一标识
  pageData.checkCodeUniqueId = ''
  clearInterval(timer)
}

/**
 * 同意隐私政策
 */
export function onAgreePrivacy() {
  pageData.agree = true
  pageData.showPrivacy = false
  handleLogin()
}

/**
 * 清除手机号输入
 */
export function onClearPhone() {
  Object.assign(pageData.form, { phone: '' })
  pageData.canSendCode = false
  pageData.sendingCode = false
  clearInterval(timer)
}

export function onClearAccount() {
  Object.assign(pageData.form, { userName: '' })
}

export function onClearPassword() {
  Object.assign(pageData.form, { password: '' })
}

export function onClearCheckCode() {
  Object.assign(pageData.form, { code: '' })
}

// 切换密码是否可见
export function onTogglePasswordVisible() {
  // passwordInputType.value = pageData.passwordVisible ? 'text' : 'password'
  pageData.passwordVisible = !pageData.passwordVisible
}

/**
 * 发送验证码
 */
export function onSendCode() {
  if (!pageData.canSendCode || pageData.sendingCode) return
  if (!pageData.tenants.length) {
    // 发送验证码前再次确认是否有租户信息
    if (validatePhone(pageData.form.phone) && validatePhoneFormat(pageData.form.phone)) {
      // 如果没有租户信息但手机号格式正确，尝试再次查询
      queryTenantsByPhone(pageData.form.phone)
      // 如果查询后仍然没有租户，会在queryTenantsByPhone中显示错误
      if (!pageData.tenants.length) return
    } else {
      // 手机号格式不正确
      pageData.loginErrorMsg = '请输入正确的手机号'
      pageData.showLoginError = true
      pageData.loginErrorCode = ''
      return
    }
  }
  const { instanceId } = pageData.tenants[0] || {}
  const tenantId = pageData.tenants[0]?.id
  pageData.sendingCode = true
  sendVerifyCodeAPI(pageData.form.phone, instanceId, tenantId)
    .then((res) => {
      // 保存验证码唯一标识到本地变量中
      if (res.data && typeof res.data === 'string') {
        pageData.checkCodeUniqueId = res.data
      }
      uni.showToast({ title: '验证码已发送', icon: 'success' })
      pageData.countdown = 60
      timer = setInterval(() => {
        pageData.countdown--
        if (pageData.countdown <= 0) {
          clearInterval(timer)
          pageData.sendingCode = false
          pageData.countdown = 60
        }
      }, 1000)
    })
    .catch((error) => {
      pageData.sendingCode = false
      // 显示发送验证码失败的错误信息
      pageData.loginErrorMsg = error?.message || '验证码发送失败，请重试'
      pageData.showLoginError = true
      pageData.loginErrorCode = ''
    })
}

/**
 * 登录处理
 */
export function onLogin() {
  formRef.value?.validate().then(() => {
    if (!pageData.agree) {
      pageData.showPrivacy = true
      return
    }
    handleLogin()
  })
}

/**
 * 执行登录逻辑
 */
async function handleLogin() {
  try {
    let loginResult: any
    if (pageData.activeTab === 'password') {
      // 密码加密
      const currentPassword = pageData.form.password.replace(/\s/g, '')
      const encodedPassword = rstr2b64(currentPassword)
      loginResult = await passwordLoginAPI(pageData.form.userName, encodedPassword)

      // 密码登录成功后，先保存基本登录信息（包含token）

      if (loginResult) {
        // 检查后端返回的resultCode
        if (loginResult.resultCode !== '0' && loginResult.resultCode !== 0) {
          // resultCode不为0，显示后端返回的错误信息
          pageData.loginErrorMsg = loginResult.resultMsg || '登录失败'
          pageData.loginErrorCode = loginResult.resultCode
          pageData.showLoginError = true
          return
        }

        const userStore = useUserStore()
        if (loginResult.data) {
          const userData = loginResult.data || {}

          // 先保存基本用户信息和token，确保后续API调用有token
          userStore.setUserInfo({
            ...userData,
            token: userData.token || userData.accessToken,
            nickname: userData.nickname || userData.userName || '',
            avatar: userData.avatar || '',
          })
          pageData.userInfo = userData

          // 如果登录返回了ssoUserId或userId，调用获取用户信息接口
          const ssoUserId = userData.userId || userData.ssoUserId
          if (ssoUserId) {
            try {
              const userInfoResult = await getUserInfoByIdAPI(ssoUserId)
              console.log('获取用户信息结果', userInfoResult)

              // 检查后端返回的resultCode
              if (String(userInfoResult.resultCode) !== '0') {
                // resultCode不为0，显示后端返回的错误信息
                userStore.clearUserInfo()
                pageData.loginErrorMsg = userInfoResult.resultMsg || '获取用户信息失败'
                pageData.showLoginError = true
                pageData.loginErrorCode = ''
                loginResult.resultCode = userInfoResult.resultCode
                return // 不跳转页面
              }

              // 处理用户信息，提取租户ID和用户ID
              if (
                userInfoResult.data &&
                userInfoResult.data.tenants &&
                userInfoResult.data.tenants.length > 0
              ) {
                const tenantIds = userInfoResult.data.tenants.map((item: any) => item.id).join(',')
                const userIds = userInfoResult.data.tenants
                  .map((item: any) => item.userId)
                  .join(',')

                // 更新用户信息，添加租户信息
                const currentUserInfo = userStore.userInfo
                userStore.setUserInfo({
                  ...currentUserInfo,
                  phone: userInfoResult.data.phone,
                  tenantIds,
                  userIds,
                })
              }
              // 如果没有租户信息，保持之前保存的基本用户信息不变
            } catch (userInfoError: any) {
              console.error('获取用户信息失败', userInfoError)

              // 检查是否是401错误
              if (userInfoError?.statusCode === 401 || userInfoError?.status === 401) {
                // 401错误，清除用户信息并显示错误
                userStore.clearUserInfo()
                pageData.loginErrorMsg = '登录已过期，请重新登录'
                pageData.showLoginError = true
                pageData.loginErrorCode = userInfoError?.statusCode
                return // 不跳转页面
              }

              // 检查错误对象中是否包含resultCode
              if (
                userInfoError?.resultCode &&
                userInfoError.resultCode !== '0' &&
                userInfoError.resultCode !== 0
              ) {
                // resultCode不为0，显示后端返回的错误信息
                userStore.clearUserInfo()
                pageData.loginErrorMsg = userInfoError.resultMsg || '获取用户信息失败'
                pageData.showLoginError = true
                pageData.loginErrorCode = userInfoError.resultCode
                return // 不跳转页面
              }

              // 其他错误，显示警告但不影响登录流程
              console.warn('获取用户租户信息失败，但登录成功')
            }
          }
        }

        // 登录成功，跳转页面
        uni.showToast({ title: '登录成功', icon: 'success' })
        userStore.setIsAgainLogin(true)
        await handleLoginSuccess()
        return // 密码登录成功，直接返回
      }
    } else {
      if (!validPhone(pageData.form.phone)) {
        pageData.loginErrorMsg = '手机格式错误，请输入正确的手机号'
        pageData.showLoginError = true
        pageData.loginErrorCode = ''
        return
      }
      if (!pageData.tenants.length) {
        // 修改为使用弹窗显示错误
        pageData.loginErrorMsg = '请先输入手机号并查询租户'
        pageData.showLoginError = true
        pageData.loginErrorCode = ''
        return
      }
      const tenantId = pageData.tenants[0]?.id
      // 使用本地变量中的验证码唯一标识
      loginResult = await codeLoginAPI(
        pageData.form.phone,
        pageData.form.code,
        pageData.checkCodeUniqueId,
        tenantId,
      )
    }

    console.log('登录结果', loginResult)

    // 登录结果判断（验证码登录或密码登录失败的情况）
    if (loginResult) {
      // 检查后端返回的resultCode
      if (loginResult.resultCode !== '0' && loginResult.resultCode !== 0) {
        // resultCode不为0，显示后端返回的错误信息
        pageData.loginErrorMsg = loginResult.resultMsg || '登录失败'
        pageData.showLoginError = true
        pageData.loginErrorCode = loginResult.resultCode
        return
      }

      if (loginResult.data && loginResult.data.token) {
        const userData = loginResult.data || {}

        // 从登录结果中提取租户ID和用户ID（如果有）
        // 注意：这里假设登录接口返回的数据中可能包含tenantIds和userIds
        // 如果没有，我们保留之前可能已经设置的值（比如通过查询租户接口设置的）
        const userStore = useUserStore()
        const currentUserInfo = userStore.userInfo
        const tenantIds = userData.tenantIds || currentUserInfo.tenantIds || ''
        const userIds = userData.userIds || currentUserInfo.userIds || ''

        userStore.setUserInfo({
          ...userData,
          token: userData.token || userData.accessToken,
          nickname: userData.nickname || userData.userName || '',
          avatar: userData.avatar || '',
          tenantIds,
          userIds,
        })
        pageData.userInfo = userData

        uni.showToast({ title: '登录成功', icon: 'success' })
        userStore.setIsAgainLogin(true)

        await handleLoginSuccess()
      }
    } else {
      // 修改为使用弹窗显示错误，优先从resultMsg字段获取后端返回的错误信息
      // 根据接口返回格式可能有多种情况，尝试各种可能的路径获取错误信息
      if (loginResult?.resultMsg) {
        pageData.loginErrorMsg = loginResult.resultMsg
      } else if (loginResult?.data?.resultMsg) {
        pageData.loginErrorMsg = loginResult.data.resultMsg
      } else if (typeof loginResult === 'string') {
        pageData.loginErrorMsg = loginResult
      } else {
        pageData.loginErrorMsg = '登录失败，请重试'
      }
      pageData.showLoginError = true
      pageData.loginErrorCode = ''
    }
  } catch (error: any) {
    console.error('登录失败', error)
    // 修改为使用弹窗显示错误，尝试从错误对象中获取resultMsg
    // 错误对象可能有多种结构，尝试各种可能的路径
    if (error?.resultMsg) {
      pageData.loginErrorMsg = error.resultMsg
    } else if (error?.response?.data?.resultMsg) {
      pageData.loginErrorMsg = error.response.data.resultMsg
    } else if (error?.data?.resultMsg) {
      pageData.loginErrorMsg = error.data.resultMsg
    } else if (error?.message) {
      pageData.loginErrorMsg = error.message
    } else {
      pageData.loginErrorMsg = '登录失败，请检查账号密码'
    }
    pageData.showLoginError = true
    pageData.loginErrorCode = ''
  }
}

/**
 * 登录成功后的页面跳转处理
 */
async function handleLoginSuccess() {
  // 获取URL中的redirect参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // 访问options参数
  const options = (currentPage as any).options || {}
  const redirect = options.redirect
    ? decodeURIComponent(options.redirect)
    : '/pages/shop/list/index'

  // 新增：获取用户权限
  try {
    const authStore = useAuthStore()
    await authStore.fetchPermissions()
    console.log('[Login Success] 用户权限获取成功')
  } catch (error) {
    console.error('[Login Success] 获取用户权限失败:', error)
    // 权限获取失败不影响登录流程，继续后续操作
  }

  // 新增：调用 saveDeviceAPI 上报设备信息
  try {
    const deviceStore = useDeviceStore()
    const deviceId = deviceStore.deviceInfo.registerId
    console.log('deviceId', deviceId)
    console.log('pageData.form.phone', pageData.userInfo?.extFields.tenants[0])

    // 直接使用登录表单中的手机号
    const phone = pageData.userInfo?.extFields.tenants[0].phone || pageData.form.phone || ''

    if (deviceId) {
      console.log(`[Login Success] 准备调用 saveDeviceAPI，deviceId: ${deviceId}, phone: ${phone}`)
      setDeviceOnline(phone, '登录成功') // 设置设备在线状态
        .then((response) => {
          console.log('[Login Success] saveDeviceAPI 调用成功，响应:', response)
        })
        .catch((error) => {
          console.error('[Login Success] saveDeviceAPI 调用失败:', error)
        })
    } else {
      console.warn('[Login Success] 设备ID或手机号为空，未上报设备信息', { deviceId, phone })
    }
  } catch (error) {
    console.error('[Login Success] saveDeviceAPI 调用异常:', error)
    // 不影响后续跳转
  }

  setTimeout(() => {
    if (redirect.startsWith('/pages/')) {
      if (redirect.includes('/index/index') || redirect.includes('/about/about')) {
        uni.switchTab({ url: redirect })
      } else {
        uni.redirectTo({ url: redirect })
      }
    } else {
      uni.switchTab({ url: '/pages/shop/list/index' })
    }
  }, 1000)
}

/**
 * 忘记密码处理
 */
export function onForgot() {
  // 修改为使用弹窗显示信息
  pageData.loginErrorMsg = '请联系管理员找回账号或密码'
  pageData.showLoginError = true
  pageData.loginErrorCode = ''
}

/**
 * 判断表单是否有效，用于控制按钮禁用状态
 */
export function isFormValid(tabName: 'password' | 'code'): boolean {
  if (tabName === 'password') {
    return !!pageData.form.userName && !!pageData.form.password
  } else {
    return !!pageData.form.phone && !!pageData.form.code
  }
}

/**
 * 点击政策详情
 */
export function onShowPrivacyDetail() {
  // 跳转到新建的隐私协议页面
  uni.navigateTo({ url: '/settings/pages/privacy/index' })
}

export const handleConfirmTip = () => {
  pageData.showLoginError = false
  if (pageData.loginErrorCode === '4007') {
    const encodedPassword = rstr2b64(pageData.form.password)
    uni.navigateTo({
      url:
        '../fixPassword/index?loginName=' +
        pageData.form.userName +
        '&encodedPassword=' +
        encodedPassword,
    })
  }
}
