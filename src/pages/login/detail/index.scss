// 登录详情页面样式 - UnoCSS 风格
.login-detail {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100vw;
  background-color: #fff; // 设计稿主背景色

  :deep() {
    // tabs 样式，现在使用 custom-class
    .login-tabs {
      margin-bottom: 0;

      .wd-tabs__nav {
        position: relative;
        justify-content: center;
        width: calc(100% - 120rpx);
        margin-bottom: 8rpx;
        // border-bottom: 1px solid #f2f2f2;

        // 使用::after添加100%宽度的底部线条
        &::after {
          position: absolute;
          bottom: 0;
          left: 0;
          width: calc(100vw - 80rpx);
          height: 1px;
          content: '';
          background-color: #f2f2f2;
        }
      }

      .wd-tab {
        // padding: 0 24rpx;
        font-family: 'PingFang SC', sans-serif;
        font-size: 48rpx !important; // 24px = 48rpx
        font-weight: normal;
        line-height: normal;
        color: #999999;
        letter-spacing: normal;
      }
      .wd-tabs__nav-item-text {
        font-family: PingFang SC;
        font-size: 24px;
        font-weight: normal;
        line-height: normal;
        color: #999999;
        letter-spacing: normal;
      }
      // 第一个tab样式
      .wd-tabs__nav-item:first-child {
        // 添加垂直分隔线
        &::after {
          position: absolute;
          top: 50%;
          right: 10rpx;
          width: 1px;
          height: 22rpx;
          margin-left: 40rpx; // 距离密码登录右侧40rpx
          content: '';
          background-color: #999999;
          transform: translateY(-50%);
        }
      }
      .wd-tabs__nav-item.is-active .wd-tabs__nav-item-text {
        color: #222222 !important;
      }
      .wd-tabs__line {
        bottom: -2rpx;
        display: none;
        height: 4rpx;
        background: #f33429 !important;
        border-radius: 2rpx;
      }

      // tab内容区样式
      .wd-tabs__content {
        position: relative;
        min-height: 400rpx;
        padding-top: 32rpx;
        background: #fff;
        border-radius: 0 0 16rpx 16rpx;

        // 添加100%宽度的分割线
        &::before {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 1px;
          content: '';
          background-color: #f2f2f2;
        }
      }
    }

    // 登录按钮样式
    .login-btn {
      width: 100%;
      height: 88rpx;
      margin-top: 40rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: #fff;
      background: #f33429;
      border: none;
      border-radius: 4rpx;
      box-shadow:
        0 3px 1px -2px rgb(0 0 0 / 20%),
        0 2px 2px 0 rgb(0 0 0 / 14%),
        0 1px 5px 0 rgb(0 0 0 / 12%);
      transition: all 0.3s ease;

      &:active {
        box-shadow:
          0 5px 5px -3px rgb(0 0 0 / 20%),
          0 8px 10px 1px rgb(0 0 0 / 14%),
          0 3px 14px 2px rgb(0 0 0 / 12%);
        transform: translateY(-1px);
      }

      // 禁用状态样式
      &.is-disabled {
        color: #fff !important;
        background: #cbccd1 !important;
        box-shadow: none !important;
        transform: none !important;
      }
    }

    // Cell Group 样式
    .wd-cell-group {
      margin: 0;
      background: transparent;
      border: none;

      &::after {
        display: none;
      }
    }

    // 单元格样式
    .wd-cell {
      position: relative;
      display: flex;
      align-items: center;
      min-height: 88rpx;
      padding: 24rpx 0;
      background: transparent;

      &::after {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1rpx;
        content: '';
        background-color: #f2f2f2;
        transform: scaleY(0.5);
        transform-origin: 0 100%;
      }

      &:last-child::after {
        display: block;
      }
    }

    // 表单输入框样式
    .wd-input {
      align-items: center;
      // 标签样式 - 完全匹配截图中的红框部分
      &__label {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80rpx;
        min-width: 80rpx;
        height: 80rpx; // 添加固定高度，与输入框高度相同
        padding: 0;
        margin: 0;
        font-size: 28rpx;
        font-weight: bold;
        line-height: 1; // 调整行高为1，防止行高干扰垂直居中
        color: #000;
        text-align: center;
      }

      &__body {
        flex: 1;
        margin-left: 40rpx;
      }

      &__input {
        height: 80rpx;
        padding: 0;
        font-size: 32rpx;
        line-height: 80rpx;
        color: #222;
        background: transparent;
        border: none;
        border-radius: 0;
      }

      &__placeholder {
        font-size: 32rpx;
        color: #bfbfbf !important;
      }

      &__suffix {
        margin-left: 8rpx;
      }

      &:focus-within {
        .wd-cell::after {
          border-bottom: 2rpx solid #f33429;
        }
      }
    }

    // 错误消息样式
    .wd-input__error {
      margin-top: 2px;
      font-size: 14px;
      color: #f33429 !important;
    }

    // 验证码按钮样式
    .code-btn {
      min-width: 160rpx;
      height: 56rpx !important;
      padding: 0 12rpx !important;
      // margin-left: 30rpx;
      margin-right: 0;
      font-size: 24rpx !important;
      line-height: 56rpx !important;
      color: #fff !important;
      text-align: center;
      background: #f33429 !important;
      border: none !important;
      border-radius: 6rpx !important;
      transition: all 0.3s ease;

      &.is-disabled {
        color: #999 !important;
        background: #f5f5f5 !important;
        border: 1px solid #e0e0e0 !important;
        opacity: 0.8;
      }
    }

    // 手机号标签样式
    .phone-label {
      display: flex;
      align-items: center;
      height: 100%;
      font-size: 28rpx;
      font-weight: bold;
      color: #000;

      .area-code {
        margin-left: 8rpx;
        font-weight: normal;
        color: #666;
      }
    }

    // 验证码按钮样式优化
    .wd-button.code-btn {
      display: flex;
      align-items: center;
      min-width: 100rpx;
      height: 56rpx !important;
      padding: 0 20rpx !important;
      margin-right: 0;
      margin-left: 10rpx;
      font-size: 28rpx !important;
      line-height: 56rpx !important;
      color: #fff !important;
      text-align: center;
      border-radius: 6rpx !important;
      transition: all 0.3s ease;

      // 默认状态（禁用）
      &.is-disabled {
        color: #999 !important;
        background: #f5f5f5 !important;
        border: 1px solid #e0e0e0 !important;
        opacity: 0.8;
      }

      // 激活状态（可点击）
      &:not(.is-disabled) {
        background: #f33429 !important;
        border: none !important;
      }
    }
    // Checkbox样式
    .wd-checkbox__icon {
      border: 1.5px solid #cccccc !important;
      border-radius: 50% !important;
    }

    // 登录错误弹窗样式
    .sy-popup-desc {
      padding: 16rpx;
      font-size: 28rpx;
      line-height: 1.5;
      color: #333;
      word-break: break-all;

      // 错误信息高亮
      .error-highlight {
        font-weight: 500;
        color: #f33429;
      }
    }
  }
}

.password-input {
  .wd-input__password-icon {
    display: none !important;
  }
}

.form-container {
  box-sizing: border-box;
  width: 100%;
  padding: 0 32rpx 40rpx 32rpx;
  margin: 0 auto;
  margin-top: 32rpx;
  background: #fff;
  border-radius: 16rpx;
}

.form-title {
  margin: 48rpx 0 32rpx 0;
  font-family: 'PingFang SC', 'SFProDisplay-Medium', sans-serif;
  font-size: 48rpx;
  font-weight: 600;
  color: #222;
  text-align: center;
}

.input-tip {
  margin-bottom: 16rpx;
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #bfbfbf;
}

.forgot-link {
  display: inline-block;
  font-size: 26rpx;
  color: #bfbfbf;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
}

.flex {
  display: flex;
  align-items: center;
}

.text-haidilao-red {
  font-size: 28rpx;
  font-weight: 500;
  color: #f33429 !important;
}

.text-xs {
  font-size: 24rpx;
}

.text-gray-500 {
  color: #bfbfbf;
}

.popup-content {
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
}

.popup-title {
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
}

.popup-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 32rpx;
}

// 分割线
.divider {
  width: 100vw;
  height: 1px;
  margin: 24rpx 0;
  background: #bcbcbd;
}

// 隐私协议底部定位
.privacy-bottom {
  // position: fixed;
  // bottom: 48rpx;
  // left: 0;
  // z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  background: transparent;
}
.privacy-bottom .text-haidilao-red {
  font-size: 26rpx;
  font-weight: 500;
  color: #f33429 !important;
}
.privacy-bottom .text-gray-500 {
  font-size: 24rpx;
  color: #bfbfbf;
}

// 移除冗余样式代码，现在使用BasePopup组件
