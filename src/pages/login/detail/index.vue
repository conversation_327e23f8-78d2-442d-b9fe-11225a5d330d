<route lang="json5">
{
  style: {
    navigationBarTitleText: '登录',
    // navigationStyle: 'custom',
    // 'app-plus': {
    //   titleNView: false,
    // },
  },
}
</route>

<template>
  <view class="login-detail">
    <!-- 自定义头部 -->
    <view class="form-container">
      <wd-tabs v-model="pageData.activeTab" @change="onTabChange" custom-class="login-tabs mt-4">
        <wd-tab title="密码登录" name="password">
          <wd-form
            v-if="pageData.activeTab == 'password'"
            ref="formRef"
            :model="pageData.form"
            class="mt-6"
          >
            <wd-cell-group>
              <wd-input
                label="账号"
                v-model="pageData.form.userName"
                prop="userName"
                placeholder="请输入账号"
                :maxlength="20"
                :rules="[{ required: false, validator: validateUserName, message: '请输入账号' }]"
              >
                <template #suffix>
                  <div style="display: flex; align-items: center">
                    <wd-icon
                      v-if="pageData.form.userName.length > 0"
                      name="error-fill"
                      size="32rpx"
                      @click="onClearAccount"
                    />
                  </div>
                </template>
              </wd-input>
              <wd-input
                label="密码"
                class="password-input"
                v-model="pageData.form.password"
                prop="password"
                :password="false"
                placeholder="请输入密码"
                :maxlength="16"
                :type="pageData.passwordVisible ? 'text' : 'password'"
                :show-password-icon="false"
                :rules="[{ required: false, validator: validatePassword, message: '请输入密码' }]"
              >
                <template #suffix>
                  <div
                    style="display: flex; flex-direction: row; align-items: center; cursor: pointer"
                  >
                    <wd-icon
                      v-if="pageData.form.password.length > 0"
                      style="margin-right: 16rpx"
                      :name="pageData.passwordVisible ? 'view' : 'eye-close'"
                      size="32rpx"
                      @click="onTogglePasswordVisible"
                    />

                    <wd-icon
                      v-if="pageData.form.password.length > 0"
                      name="error-fill"
                      size="32rpx"
                      @click="onClearPassword"
                    />
                  </div>
                </template>
              </wd-input>
            </wd-cell-group>
            <wd-button
              custom-class="login-btn mt-6"
              type="primary"
              block
              :disabled="!isFormValid('password')"
              @click="onLogin"
            >
              登录
            </wd-button>
            <!-- 忘记账号和密码移至表单外部，两个tab共用 -->
          </wd-form>
        </wd-tab>
        <wd-tab title="验证码登录" name="code">
          <wd-form
            v-if="pageData.activeTab == 'code'"
            ref="formRef"
            :model="pageData.form"
            class="mt-6"
          >
            <wd-cell-group>
              <wd-input
                v-model="pageData.form.phone"
                prop="phone"
                placeholder="请输入手机号"
                :clearable="false"
                @input="onPhoneInput"
                @clear="onPhoneClear"
                type="text"
                label-width="25%"
                :maxlength="11"
                :rules="[
                  {
                    required: false,
                    validator: validatePhone,
                    message: '请输入手机号',
                  },
                  {
                    required: false,
                    validator: validatePhoneFormat,
                    message: '手机号格式不正确',
                  },
                ]"
              >
                <template #label>
                  <view class="phone-label">
                    <text>手机号</text>
                    <text class="area-code">+86</text>
                  </view>
                </template>
                <template #suffix>
                  <div style="display: flex; align-items: center">
                    <wd-icon
                      v-if="pageData.form.phone.length > 0"
                      name="error-fill"
                      size="32rpx"
                      @click="onClearPhone"
                    ></wd-icon>
                    <wd-button
                      size="small"
                      :type="pageData.canSendCode && !pageData.sendingCode ? 'primary' : 'info'"
                      custom-class="code-btn"
                      :disabled="!pageData.canSendCode || pageData.sendingCode"
                      @click="onSendCode"
                    >
                      {{ pageData.sendingCode ? `${pageData.countdown}s` : '获取验证码' }}
                    </wd-button>
                  </div>
                </template>
              </wd-input>
              <wd-input
                label="验证码&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"
                v-model="pageData.form.code"
                prop="code"
                placeholder="请输入验证码"
                type="number"
                label-width="25%"
                :maxlength="6"
                text-align="left"
                :readonly="!pageData.canSendCode"
                :rules="[{ required: false, validator: validateCode, message: '请输入验证码' }]"
              >
                <template #suffix>
                  <div style="display: flex; align-items: center">
                    <wd-icon
                      v-if="pageData.form.code.length > 0"
                      name="error-fill"
                      size="32rpx"
                      @click="onClearCheckCode"
                    />
                  </div>
                </template>
              </wd-input>
            </wd-cell-group>
            <wd-button
              custom-class="login-btn mt-6"
              type="primary"
              block
              :disabled="!isFormValid('code')"
              @click="onLogin"
            >
              登录
            </wd-button>
            <!-- 忘记账号和密码移至表单外部，两个tab共用 -->
          </wd-form>
        </wd-tab>
      </wd-tabs>
      <!-- 忘记账号和密码居中显示，两个tab共用 -->
      <view class="flex justify-center mt-4 mb-4" v-if="pageData.activeTab == 'password'">
        <text class="text-xs text-gray-500 forgot-link" @click="onForgot">忘记密码</text>
      </view>
    </view>
    <view class="privacy-bottom">
      <wd-checkbox v-model="pageData.agree" />
      <view class="ml-2 text-xs text-gray-500">
        登录即代表您已同意
        <text class="text-haidilao-red" @click="onShowPrivacyDetail">《海底捞全单汇隐私政策》</text>
      </view>
    </view>
    <SyPopup
      v-model="pageData.showPrivacy"
      title="用户隐私政策"
      confirm-text="同意"
      cancel-text="取消"
      @confirm="onAgreePrivacy"
      @cancel="pageData.showPrivacy = false"
    >
      使用APP前请阅读
      <text class="text-haidilao-red" @click="onShowPrivacyDetail">《用户隐私政策》</text>
    </SyPopup>

    <!-- 添加登录错误弹窗 -->
    <SyPopup
      v-model="pageData.showLoginError"
      title="错误提示"
      confirm-text="好的"
      :show-cancel="false"
      @confirm="handleConfirmTip"
    >
      {{ pageData.loginErrorMsg }}
    </SyPopup>
  </view>
</template>

<script setup lang="ts">
import SyPopup from '@/components/sy-popup'
import {
  pageData,
  formRef,
  validateUserName,
  validatePassword,
  validatePhone,
  validatePhoneFormat,
  validateCode,
  onPhoneInput,
  onPhoneClear,
  onTabChange,
  onAgreePrivacy,
  onSendCode,
  onClearPhone,
  onLogin,
  onForgot,
  isFormValid,
  onShowPrivacyDetail,
  onClearAccount,
  onClearPassword,
  onClearCheckCode,
  onTogglePasswordVisible,
  passwordInputType,
  handleConfirmTip,
  onPasswordInput,
} from './index'
</script>

<style src="./index.scss" scoped lang="scss"></style>
