// 登录页面样式 - UnoCSS 风格
.login-page {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100vw;
  height: 100vh;
  padding-top: 50%;
  background: #fff;
}

.custom-header {
  width: 100%;
}

.text-haidilao-red {
  color: #f33429;
}

.bg-haidilao-red {
  background-color: #f33429;
}

.logo-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.logo {
  width: 446rpx;
  height: 372rpx;
}

.title-container {
  margin-bottom: 40rpx;
  text-align: center;
}

.title-container {
  font-family: 'PingFang SC', sans-serif;
  font-size: 48rpx;
  font-weight: 500;
  line-height: normal;
  color: #333333;
  letter-spacing: 0.2em;
}

.subtitle {
  font-family: 'PingFang SC', sans-serif;
  font-size: 48rpx;
  font-weight: 500;
  line-height: normal;
  color: #333333;
  letter-spacing: 0.2em;
}

.login-btn-container {
  position: fixed;
  bottom: 194rpx;
  left: 0;
  display: flex;
  justify-content: center;
  width: 100%;
}

.login-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(100% - 40rpx);
  height: 80rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
  background: #f33429;
  border-radius: 12rpx;
}
