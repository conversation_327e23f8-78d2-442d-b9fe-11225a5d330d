import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store'

export function useLoginPage() {
  const safeAreaInsets = uni.getSystemInfoSync()?.safeAreaInsets || { top: 20 }
  const userStore = useUserStore()

  function handleLogin() {
    // 跳转到登录详情页面
    uni.redirectTo({
      url: '/pages/login/detail/index',
    })
  }

  // 检查登录状态，已登录则直接进入首页
  function checkLoginStatus() {
    if (userStore.isLogined) {
      console.log('Already logged in, redirecting to index page')
      setTimeout(() => {
        uni.switchTab({ url: '/pages/index/index' })
      }, 100)
    }
  }

  // 页面加载时检查登录状态
  onMounted(() => {
    checkLoginStatus()
  })

  return {
    safeAreaInsets,
    handleLogin,
  }
}
