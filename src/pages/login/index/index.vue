<route lang="json5">
{
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>

<template>
  <view class="login-page">
    <!-- 自定义头部 - 无标题 -->
    <view class="custom-header" :style="{ height: safeAreaInsets?.top + 'px' }"></view>

    <!-- Logo -->
    <view class="logo-container">
      <image src="/static/images/img/logo.png" mode="aspectFit" class="logo" />
    </view>

    <!-- 标题区域 -->
    <view class="title-container">
      <view class="subtitle">商家中心</view>
    </view>

    <!-- 登录按钮 -->
    <view class="login-btn-container">
      <button class="login-btn" @click="handleLogin">登录</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useLoginPage } from './indexMod'
const { safeAreaInsets, handleLogin } = useLoginPage()
</script>

<style src="./index.scss" lang="scss" scoped></style>
