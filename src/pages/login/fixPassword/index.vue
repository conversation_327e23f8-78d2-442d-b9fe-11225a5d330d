<route lang="json5">
{
  layout: 'default',
  needLogin: true,
  style: {
    navigationBarTitleText: '修改密码',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>
<template>
  <view class="fix-password-page" :style="{ '--status-bar-height': statusBarHeight + 'px' }">
    <wd-navbar title="修改密码" left-text="" left-arrow @click-left="handleClickBack"></wd-navbar>
    <wd-form class="form-body" ref="form" :model="formData" errorType="message">
      <wd-cell-group style="background-color: white; padding-bottom: 10rpx">
        <view>
          <wd-input
            label="输入新密码"
            label-width="100px"
            prop="newPassword"
            clearable
            :maxlength="16"
            v-model="formData.newPassword"
            placeholder="请输入"
          />
        </view>
        <view>
          <wd-input
            label="重复新密码"
            label-width="100px"
            prop="againPassword"
            clearable
            :maxlength="16"
            v-model="formData.againPassword"
            placeholder="请输入"
          />
          <p class="text-red-500 text-xs pt-1 ml-4">
            <text>密码要求: {{ passwordRuleStr ? passwordRuleStr : '' }}</text>
          </p>
        </view>
      </wd-cell-group>
      <view class="footer">
        <wd-button
          type="primary"
          :custom-class="disabledSumbmit ? 'footer-button-disable' : 'footer-button'"
          :disabled="disabledSumbmit"
          size="large"
          @click="handleSubmitPassword"
          block
          form-type="none"
        >
          确定
        </wd-button>
      </view>
    </wd-form>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { updatePasswordAPI, getLoginConfigAPI } from '@/service/index/login'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { rstr2b64, getStatusBarHeight, getEnvMode } from '@/utils/index'
import { saveDeviceAPI } from '@/service/user'
import { useUserStore } from '@/store'
import { useDeviceStore } from '@/store/device'

// 状态栏高度
const statusBarHeight = ref(0)

// 密码规则
const passwordLength = ref([])
const passwordType = ref([])

const form = ref()
const loginName = ref('')
const password = ref('')

// 初始化表单数据
const formData = reactive({
  newPassword: '',
  againPassword: '',
})

// 是否禁用提交按钮
const disabledSumbmit = computed(() => !formData.newPassword || !formData.againPassword)

function validatePassword(password: string): boolean {
  if (!password) return false

  let regexStr = '^'

  if (passwordType.value.includes(1)) {
    regexStr += '(?=.*\\d)'
  }

  if (passwordType.value.includes(2)) {
    regexStr += '(?=.*[A-Za-z])'
  }

  if (passwordType.value.includes(3)) {
    regexStr += '(?=.*[^A-Za-z0-9])'
  }

  const min = passwordLength.value[0] ?? 6
  const max = passwordLength.value[1] ?? 16

  // 构建字符范围
  let charPattern = ''
  if (passwordType.value.includes(1)) charPattern += '\\d'
  if (passwordType.value.includes(2)) charPattern += 'A-Za-z'
  if (passwordType.value.includes(3)) charPattern += '^A-Za-z0-9'

  regexStr += `[${charPattern}]{${min},${max}}$`

  try {
    console.log('正则表达式：', regexStr)
    const regex = new RegExp(regexStr)
    return regex.test(password)
  } catch (e) {
    console.error('正则表达式错误:', regexStr)
    return false
  }
}

const handleClickBack = () => {
  uni.navigateBack()
}

onLoad((option) => {
  statusBarHeight.value = getStatusBarHeight()
  if (option.loginName) {
    loginName.value = option.loginName
    password.value = option.encodedPassword

    const userStore = useUserStore()
    console.log('修改密码页面 - onLoad:', userStore.userInfo)
  }
})

onShow(() => {
  toGetLoginConfigAPI()
})

const passwordRuleStr = ref('')
const toGetLoginConfigAPI = () => {
  const envstr = getEnvMode()
  const tenantIds = envstr === 'production' ? '1354135729021061996' : '1351790872278738821'
  getLoginConfigAPI(tenantIds).then((res) => {
    console.log('查看返回数据：', res)
    if (res.resultMsg === 'success') {
      passwordLength.value = JSON.parse(res.data.passwordLength)
      let passwordRule = passwordLength.value[0] + '~' + passwordLength.value[1] + '位的'
      passwordType.value = JSON.parse(res.data.passwordType)
      if (passwordType.value.includes(1)) {
        passwordRule += '数字'
      }
      if (passwordType.value.includes(2)) {
        passwordRule += '+字母'
      }
      if (passwordType.value.includes(3)) {
        passwordRule += '+特殊字符'
      }
      passwordRuleStr.value = passwordRule
    }
  })
}

const handleSubmitPassword = () => {
  // TODO: 提交密码
  console.log('newPassword', formData)

  if (!validatePassword(formData.newPassword) || !validatePassword(formData.againPassword)) {
    uni.showToast({ title: '密码要求: 6~16位的数字+英文字母', icon: 'none' })
    return
  }

  if (formData.newPassword !== formData.againPassword) {
    uni.showToast({ title: '密码不一致', icon: 'none' })
    return
  }
  const newPassword = rstr2b64(formData.newPassword)
  const againPassword = rstr2b64(formData.againPassword)

  updatePasswordAPI(loginName.value, password.value, newPassword, againPassword, 4).then((res) => {
    if (res.resultMsg === 'success') {
      uni.showToast({
        title: '请用新密码重新登录',
        icon: 'none',
        duration: 1500,
      })
      setTimeout(() => {
        uni.redirectTo({ url: '/pages/login/detail/index' })
      }, 1000)
    } else {
      uni.showToast({
        title: res.resultMsg,
        icon: 'none',
        duration: 5000,
      })
    }
  })
}
</script>
<style lang="scss" scoped>
@import './index';
</style>
