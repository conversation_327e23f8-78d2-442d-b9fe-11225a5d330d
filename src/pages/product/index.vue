<route lang="json5">
{
  style: {
    navigationBarTitleText: '商品详情',
  },
}
</route>

<template>
  <view
    class="bg-gray-100 overflow-hidden min-h-screen"
    :style="{ paddingTop: safeAreaInsets?.top + 'px' }"
  >
    <!-- 商品图片轮播 -->
    <swiper class="product-swiper" indicator-dots autoplay circular>
      <swiper-item v-for="(item, index) in productImages" :key="index">
        <image
          :src="item"
          mode="aspectFill"
          class="w-full h-full"
          @error="handleImageError(index)"
        />
      </swiper-item>
    </swiper>

    <!-- 商品信息卡片 -->
    <view class="bg-white p-4 mt-2">
      <view class="flex items-center justify-between">
        <view class="text-red-500 text-xl font-bold">¥{{ productDetail.price }}</view>
        <view class="text-gray-500 text-sm">销量: {{ productDetail.sales }}</view>
      </view>
      <view class="text-lg font-medium mt-2">{{ productDetail.name }}</view>
      <view class="text-gray-600 text-sm mt-1">{{ productDetail.brief }}</view>
    </view>

    <!-- 商品规格 -->
    <view class="bg-white p-4 mt-2">
      <view class="flex justify-between items-center py-2" @click="openSpecsPopup">
        <text class="text-gray-700">规格</text>
        <view class="flex items-center">
          <text class="text-gray-500 mr-1">{{ selectedSpecs }}</text>
          <uni-icons type="right" size="16" color="#999"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 商品详情 -->
    <view class="bg-white p-4 mt-2">
      <view class="text-lg mb-2 font-medium">商品详情</view>
      <rich-text :nodes="productDetail.description"></rich-text>
    </view>

    <!-- 底部购买栏 -->
    <view
      class="fixed bottom-0 left-0 right-0 bg-white flex items-center justify-between p-2 border-t border-gray-200"
      :style="{ paddingBottom: safeAreaInsets?.bottom + 'px' }"
    >
      <view class="flex-1 flex">
        <view class="flex flex-col items-center justify-center px-4">
          <uni-icons type="star" size="24" color="#999"></uni-icons>
          <text class="text-xs text-gray-500">收藏</text>
        </view>
        <view class="flex flex-col items-center justify-center px-4">
          <uni-icons type="cart" size="24" color="#999"></uni-icons>
          <text class="text-xs text-gray-500">购物车</text>
        </view>
      </view>
      <view class="flex-1 flex">
        <button class="flex-1 py-2 bg-orange-400 text-white text-sm rounded-l-full">
          加入购物车
        </button>
        <button class="flex-1 py-2 bg-red-500 text-white text-sm rounded-r-full">立即购买</button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { onLoad } from '@dcloudio/uni-app'
import { ref, reactive } from 'vue'

defineOptions({
  name: 'ProductDetail',
})

// 获取屏幕安全区域
const { safeAreaInsets } = uni.getSystemInfoSync()

// 商品数据
const productDetail = reactive({
  id: '1',
  name: '高品质超柔纯棉T恤',
  price: 99.99,
  originalPrice: 199.99,
  sales: 1000,
  brief: '100%纯棉面料，舒适透气，多色可选',
  description:
    '<p>这是一款采用100%精梳棉制成的高品质T恤，面料柔软舒适，穿着透气。</p><p>多种颜色可选，适合各种场合穿着。</p>',
  specs: [
    { name: '颜色', options: ['白色', '黑色', '蓝色', '红色'] },
    { name: '尺码', options: ['S', 'M', 'L', 'XL', 'XXL'] },
  ],
})

// 商品图片
const productImages = ref([
  '/static/images/placeholder.jpg',
  '/static/images/placeholder.jpg',
  '/static/images/placeholder.jpg',
])

// 选中的规格
const selectedSpecs = ref('白色, M')

// 打开规格选择弹窗
function openSpecsPopup() {
  uni.showToast({
    title: '打开规格选择',
    icon: 'none',
  })
}

// 图片加载失败处理
function handleImageError(index: number) {
  console.log('图片加载失败:', index)
  // 可以在这里设置默认图片
}

// 页面加载
onLoad((options) => {
  const productId = options?.id || '1'
  console.log('加载商品ID:', productId)
  // 这里可以添加实际的商品数据加载逻辑
})
</script>

<style>
.product-swiper {
  width: 100%;
  height: 750rpx;
}
</style>
