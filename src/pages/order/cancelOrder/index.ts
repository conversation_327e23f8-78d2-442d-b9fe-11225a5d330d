import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getPartRefundProducts, queryRefundReasons, createRefund } from '@/service/order'

// 第三编码
export const thirdOrderCode = ref<string>('')

// 订单编号
export const tradeNo = ref<string>('')

// 实例ID和租户ID
export const instanceId = ref<string>('')
export const tenantId = ref<string>('')

// 商品项类型定义
export interface GoodsItem {
  id: string
  name: string
  skuName?: string
  refundPrice: number
  count: number
  maxCount: number
  selected: boolean
}

// 费用项类型定义
export interface FeeItem {
  name: string
  amount: number
}

// 检测当前页面是否为 tabbar 页面
export const isTabbarPage = ref(false)

// 确认弹窗配置
export const confirmPopupConfig = ref({
  visible: false,
  title: '提示',
  content: '是否确认退款',
  cancelText: '取消',
  confirmText: '确认',
  tradeNo: '',
})

// 获取当前页面路径并判断是否为 tabbar 页面
export const checkTabbarPage = () => {
  try {
    const pages = getCurrentPages()
    if (pages && pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      const currentRoute = currentPage.route || ''

      // 定义 tabbar 页面路径列表
      const tabbarPages = [
        'pages/index/index',
        'pages/about/about',
        'pages/dishManagement/index',
        'pages/my/index',
        'pages/storeDetail/index',
      ]

      isTabbarPage.value = tabbarPages.some((path) => currentRoute.includes(path))
    }
  } catch (error) {
    console.error('检测 tabbar 页面失败:', error)
    isTabbarPage.value = false
  }
}

// 重置页面状态
export const resetState = () => {
  // 重置基本信息
  thirdOrderCode.value = ''
  tradeNo.value = ''
  instanceId.value = ''
  tenantId.value = ''

  // 重置商品列表
  goodsList.value = []

  // 重置其他费用
  otherFees.value = [
    { name: '餐盒费', amount: 1.0 },
    { name: '配送费', amount: 0.0 },
  ]

  // 重置选择状态
  isAllSelected.value = false

  // 重置退款原因
  selectedReason.value = ''
  selectedReasonCode.value = ''
  showReasonPopup.value = false

  // 重置弹窗状态
  confirmPopupConfig.value = {
    visible: false,
    title: '提示',
    content: '是否确认退款',
    cancelText: '取消',
    confirmText: '确认',
    tradeNo: '',
  }
}

// 处理页面参数
export const handlePageParams = (options: Record<string, string>) => {
  // 重置页面状态
  resetState()

  console.log('接收到页面参数:', options)

  // 获取订单编号
  if (options.thirdOrderCode) {
    thirdOrderCode.value = options.thirdOrderCode
    console.log('订单编号:', thirdOrderCode.value)

    // TODO: 根据订单编号获取订单详情
    fetchOrderDetail(thirdOrderCode.value)
  }

  // 获取退款原因列表
  fetchRefundReasons()
}

// 获取订单详情
const fetchOrderDetail = async (orderCode: string) => {
  // 这里应该是调用获取订单详情的接口
  console.log('获取订单详情:', orderCode)

  try {
    // 调用查询可退商品接口
    const res = await getPartRefundProducts({ thirdOrderCode: orderCode })
    console.log('可退商品数据:', res)
    tradeNo.value = res?.data?.orderCode

    // 提取并保存 instanceId 和 tenantId
    instanceId.value = res?.data?.instanceId || ''
    tenantId.value = res?.data?.tenantId || ''
    // 根据接口返回数据更新商品列表
    // 注意：这里需要根据实际接口返回的数据结构进行调整
    if (res?.data?.refundItems) {
      // 将接口返回的数据转换为组件所需格式
      goodsList.value = res.data.refundItems.map((item: any) => ({
        ...item,
        count: item.num >= 1 ? 1 : 0,
        maxCount: item.num,
        selected: true, // 默认全部选中
      }))
    }

    // 更新全选状态 - 检查是否所有商品都被选中
    isAllSelected.value = goodsList.value.every((item) => item.selected)

    // 更新其他费用
    // 注意：这里需要根据实际接口返回的数据结构进行调整
    otherFees.value = [
      { name: '打包费', amount: res?.data?.totalMealFee },
      { name: '餐盒费', amount: res?.data?.totalBoxFee },
      { name: '配送费', amount: res?.data?.refundFreightAmount },
    ]
  } catch (error) {
    console.error('获取可退商品失败:', error)
    uni.showToast({
      title: '获取可退商品失败',
      icon: 'none',
    })
  }
}

// 获取退款原因列表
export const fetchRefundReasons = async () => {
  try {
    const res = await queryRefundReasons()
    console.log('退款原因数据:', res)

    // 如果接口返回成功且有数据
    if (res?.data) {
      // 更新退款原因列表
      reasonList.value = res.data?.storeRefundApply

      // 如果有数据，默认选择第一项
      if (reasonList.value && reasonList.value.length > 0) {
        // selectedReason.value = reasonList.value[0].reason
        // selectedReasonCode.value = reasonList.value[0].code
      }
    }
  } catch (error) {
    console.error('获取退款原因失败:', error)
    // 接口失败时保留默认数据
  }
}

// 商品列表数据
export const goodsList = ref<GoodsItem[]>([])

// 其他费用数据
export const otherFees = ref<FeeItem[]>([
  { name: '餐盒费', amount: 1.0 },
  { name: '配送费', amount: 0.0 },
])

// 退款原因列表
export const reasonList = ref<{ reason: string; code: string }[]>([
  {
    reason: '商品已经售罄',
    code: '5001',
  },
  {
    reason: '商品质量问题，原料变质或包装破损',
    code: '5002',
  },
  {
    reason: '顾客个人原因，计划有变，不想要了',
    code: '5003',
  },
  {
    reason: '其他',
    code: '5000',
  },
])

// 是否全选
export const isAllSelected = ref(false)

// 选中的退款原因
export const selectedReason = ref('')

// 选中的退款原因代码
export const selectedReasonCode = ref('')

// 是否显示退款原因选择器
export const showReasonPopup = ref(false)

// 计算总退款金额
export const totalRefundAmount = computed(() => {
  // 计算选中商品的总金额
  const goodsAmount = goodsList.value
    .filter((item) => item.selected)
    .reduce((total, item) => total + item.refundPrice * item.count, 0)

  // 计算其他费用总额
  // const feesAmount = otherFees.value.reduce((total, fee) => total + fee.amount, 0)
  // TODO: 暂不计算其他费用总额
  const feesAmount = 0

  return goodsAmount + feesAmount
})

// 是否可以确认退款
export const canConfirm = computed(() => {
  return goodsList.value.some((item) => item.selected)
})

// 切换全选状态
export const toggleSelectAll = () => {
  // 根据全选状态更新所有商品的选中状态
  // 注意：这里不需要再取反isAllSelected，因为v-model已经更新了它的值
  goodsList.value.forEach((item) => {
    item.selected = isAllSelected.value
  })
}

// 商品选择状态变化
export const onGoodsSelectionChange = () => {
  // 检查是否所有商品都被选中，更新全选状态
  const allSelected = goodsList.value.every((item) => item.selected)
  isAllSelected.value = allSelected

  // 如果没有选中任何商品，显示提示
  if (goodsList.value.length > 0 && !goodsList.value.some((item) => item.selected)) {
    // uni.showToast({
    //   title: '请至少选择一件商品',
    //   icon: 'none',
    // })
  }
}

// 减少商品数量
export const decreaseCount = (index: number) => {
  if (goodsList.value[index].count > 1) {
    goodsList.value[index].count--
  }
}

// 增加商品数量
export const increaseCount = (index: number) => {
  if (goodsList.value[index].count < goodsList.value[index].maxCount) {
    goodsList.value[index].count++
  }
}

// 显示退款原因选择器
export const showReasonSelector = () => {
  showReasonPopup.value = true
}

// 选择退款原因
export const selectReason = (reason: { reason: string; code: string }) => {
  selectedReason.value = reason.reason
  selectedReasonCode.value = reason.code
  showReasonPopup.value = false
}

// 显示确认弹窗
export const showConfirmPopup = () => {
  if (!canConfirm.value) return

  // 设置弹窗内容
  confirmPopupConfig.value = {
    visible: true,
    title: '提示',
    content: '是否确认退款',
    cancelText: '取消',
    confirmText: '确认',
    tradeNo: thirdOrderCode.value,
  }
}

// 确认退款
export const onConfirm = () => {
  if (!canConfirm.value) return
  if (!selectedReason.value) {
    uni.showToast({
      title: '请选择退款原因',
      icon: 'none',
    })
    return
  }

  // 显示确认弹窗
  showConfirmPopup()
}

// 确认弹窗确认按钮回调
export const handleConfirmPopup = async () => {
  // 获取选中的商品
  const selectedGoods = JSON.parse(JSON.stringify(goodsList.value))
    .filter((item) => item.selected)
    ?.map((item) => {
      const num = item.count
      delete item.selected
      delete item.num
      delete item.count
      delete item.maxCount
      return {
        ...item,
        refundNum: num,
        num,
      }
    })

  // 构建提交的数据
  const submitData = {
    handlerName: 'baseRefundAuditOperationHandler',
    tradeNo: tradeNo.value,
    refundItems: selectedGoods,
    refundBizType: 0,
    refundReason: selectedReason.value,
    refundReasonCode: selectedReasonCode.value,
    refundPrice: totalRefundAmount.value.toFixed(2),
    instanceId: instanceId.value,
    tenantId: tenantId.value,
  }

  console.log('提交退款申请:', submitData)

  try {
    // 调用退款接口
    const res = await createRefund(submitData)
    console.log('退款申请结果:', res)
    if (res?.resultCode === '0') {
      uni.showToast({
        title: '退款申请已提交',
        icon: 'success',
      })

      uni.setStorageSync('refresh_order_list', 'refresh')
      // 成功后返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      uni.showToast({
        title: res?.resultMsg || '退款申请提交失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('退款申请提交失败:', error)
    uni.showToast({
      title: '退款申请提交失败',
      icon: 'none',
    })
  }
}
