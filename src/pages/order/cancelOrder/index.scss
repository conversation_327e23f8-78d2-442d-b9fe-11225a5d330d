// 颜色变量定义
$primary-color: #f33429;
$text-color: #222222;
$text-light-color: #999999;
$text-medium-color: #666666;
$text-dark-color: #333333;
$border-color: #edecee;
$border-light-color: #f5f5f5;
$table-border-color: #cccccc;
$bg-color: #ffffff;
$bg-light-color: #f5f6fa;
$disabled-color: #cccccc;
$disabled-bg-color: #f2f3f5;
$divider-color: rgba(0, 0, 0, 0.1);
$gap-color: #f2f2f2;

.cancel-order-page {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding-bottom: 100rpx; // 为底部操作栏预留空间
  background-color: $bg-light-color;
  :deep(.wd-popup) {
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
  }
}

.cancel-order-content {
  flex: 1;
  padding-bottom: 100rpx; // 为底部操作栏预留空间
  overflow-y: auto;
}

// 公共样式 - 区块
.problem-goods-section,
.refund-reason-section,
.other-fees-section {
  padding: 24rpx;
  margin: 20rpx;
  background-color: $bg-color;
  border-radius: 12rpx;
}

// 公共样式 - 标题
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 24rpx;
  font-weight: 500;
  color: $text-color;
}

.section-subtitle {
  margin-left: 4rpx;
  font-size: 22rpx;
  color: $text-light-color;
}

// 全选行
.select-all-row {
  padding: 8rpx 0 16rpx;
  font-size: 24rpx;
  color: $text-color;
}

// 选择问题商品区域
.goods-list {
  margin-top: 12rpx;
}

.goods-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid $border-light-color;

  &:last-child {
    border-bottom: none;
  }
}

.goods-checkbox {
  // padding-top: 4rpx;
  // margin-right: 20rpx;
}

.goods-content {
  position: relative;
  display: flex;
  flex: 1;
  flex-wrap: wrap;
}

.goods-name {
  // width: 60%;
  // overflow: hidden;
  // font-size: 24rpx;
  color: $text-color;
  // text-overflow: ellipsis;
  // white-space: nowrap;
}

.goods-count {
  width: 10%;
  margin-right: 10rpx;
  font-size: 24rpx;
  color: $text-color;
}

.goods-counter {
  display: flex;
  // align-items: center;
}

.goods-price {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 60rpx;
  font-size: 24rpx;
  color: $text-color;
  text-align: right;
}

.goods-sub-row {
  width: 100%;
  // margin-top: 8rpx;
}

.goods-spec {
  font-size: 22rpx;
  color: $text-light-color;
}

.counter-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  font-size: 28rpx;
  background-color: $disabled-bg-color;
  border-radius: 4rpx;

  &.disabled {
    color: $disabled-color;
  }
}

.counter-num {
  width: 32rpx;
  margin: 0 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  text-align: center;
}

// 退款原因区域
.refund-reason-section {
  padding: 0;
  margin-top: 20rpx;
}

.section-item {
  position: relative;
  display: flex;
  align-items: center;
  height: 80rpx;
  // padding: 0 24rpx;
}

.item-label {
  position: absolute;
  left: 24rpx;
  width: 120rpx;
  font-size: 24rpx;
  color: $text-color;
}

.item-value {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.reason-content {
  font-size: 24rpx;
  color: $text-dark-color;
  text-align: center;
}

.right-arrow {
  position: absolute;
  right: 24rpx;
  width: 12rpx;
  height: 12rpx;
  border-top: 2rpx solid $text-light-color;
  border-right: 2rpx solid $text-light-color;
  transform: rotate(45deg);
}

// 其他费用区域
.other-fees-section {
  margin-top: 20rpx;
}

.fees-table {
  width: 100%;
  overflow: hidden;
  border: 0.5rpx solid $table-border-color;
  border-radius: 8rpx;
}

.fees-table-header,
.fees-row {
  display: flex;
  height: 50rpx;
  border-bottom: 0.5rpx solid $table-border-color;

  &:last-child {
    border-bottom: none;
  }
}

.fees-name-column,
.fees-amount-column {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
  font-size: 24rpx;
  color: $text-medium-color;
}

.fees-name-column {
  flex: 3;
  border-right: 0.5rpx solid $table-border-color;
}

.fees-amount-column {
  flex: 2;
}

.fees-table-header {
  .fees-name-column,
  .fees-amount-column {
    font-weight: 500;
    color: $text-color;
  }
}

// 底部操作栏
.bottom-action-bar {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 98rpx;
  padding: 0 24rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: $bg-color;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.refund-amount {
  display: flex;
  align-items: center;
}

.amount-label {
  margin-right: 8rpx;
  font-size: 24rpx;
  color: $text-color;
}

.amount-value {
  font-size: 36rpx;
  font-weight: 600;
  color: $text-color;
}

.confirm-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 354rpx;
  height: 78rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: #ffffff;
  background-color: $primary-color;
  border-radius: 39rpx;

  &.disabled {
    background-color: $disabled-color;
  }
}

// ActionSheet 样式
.reason-action-sheet {
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  overflow: hidden;
  background-color: $bg-color;
  border-radius: 24rpx 24rpx 0 0;
}

// 滚动区域
.reason-options-scroll {
  max-height: 60vh;
  overflow-y: auto;
}

.reason-options {
  overflow: hidden;
  background-color: $bg-color;
}

.reason-option-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 112rpx;
  border-bottom: 1rpx solid $divider-color;

  &.first-item {
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
  }

  &:first-child {
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
  }
}

.reason-option-text {
  font-size: 34rpx;
  color: $text-color;
  text-align: center;
}

.action-sheet-gap {
  height: 16rpx;
  background-color: $gap-color;
}

.reason-cancel-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 90rpx;
  background-color: $bg-color;
}

.cancel-text {
  font-size: 34rpx;
  color: $text-color;
}

.popup-safe-area {
  height: 34rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: $bg-color;
}

// 全选样式
:deep(.wd-checkbox__shape) {
  border-radius: 0;
  &.is-checked {
    background-color: $primary-color;
    border-color: $primary-color;
  }
}
