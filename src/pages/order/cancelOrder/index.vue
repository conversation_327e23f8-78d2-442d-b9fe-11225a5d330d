<route lang="json5">
{
  style: {
    navigationBarTitleText: '取消订单',
  },
}
</route>

<template>
  <view class="cancel-order-page">
    <!-- 页面内容部分 -->
    <scroll-view scroll-y class="cancel-order-content">
      <!-- 选择问题商品 -->
      <view class="problem-goods-section">
        <view class="section-header">
          <text class="section-title">选择问题商品</text>
        </view>

        <!-- 全选 -->
        <view class="select-all-row">
          <wd-checkbox
            v-model="isAllSelected"
            shape="square"
            checked-color="#F33429"
            @change="toggleSelectAll"
          >
            全选
          </wd-checkbox>
        </view>

        <!-- 商品列表 -->
        <view class="goods-list">
          <view v-for="(item, index) in goodsList" :key="index" class="goods-item">
            <view class="goods-checkbox">
              <wd-checkbox
                v-model="item.selected"
                shape="square"
                checked-color="#F33429"
                @change="onGoodsSelectionChange"
              ></wd-checkbox>
            </view>
            <view class="goods-content">
              <text class="goods-name w-288rpx text-24rpx line-clamp-2 mr-16rpx">
                {{ item.name }}
              </text>
              <text class="goods-count w-48rpx mr-16rpx">x{{ item.maxCount }}</text>
              <view class="goods-counter">
                <view
                  class="counter-btn counter-minus rounded-8rpx"
                  :class="{ disabled: item.count <= 1 }"
                  @click="decreaseCount(index)"
                >
                  -
                </view>
                <view class="counter-num flex items-center justify-center h-40rpx">
                  {{ item.count }}
                </view>
                <view
                  class="counter-btn counter-plus rounded-8rpx"
                  :class="{ disabled: item.count >= item.maxCount }"
                  @click="increaseCount(index)"
                >
                  +
                </view>
              </view>
              <view class="goods-price h-40rpx">¥{{ item.refundPrice.toFixed(2) }}</view>
              <view class="goods-sub-row" v-if="item.skuName">
                <text class="goods-spec">{{ item.skuName }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 退款原因 -->
      <view class="refund-reason-section" @click="showReasonSelector">
        <view class="section-item">
          <text class="item-label">退款原因</text>
          <view class="item-value">
            <text class="reason-content">{{ selectedReason || '' }}</text>
          </view>
          <view class="right-arrow"></view>
        </view>
      </view>

      <!-- 其他费用 -->
      <view class="other-fees-section" v-if="false">
        <view class="section-header">
          <text class="section-title">
            其它费用
            <text class="section-subtitle">本次预计退回</text>
          </text>
        </view>
        <view class="fees-table">
          <!-- 表头 -->
          <view class="fees-table-header">
            <view class="fees-name-column">费用名称</view>
            <view class="fees-amount-column">退款金额</view>
          </view>
          <!-- 表格内容 -->
          <view class="fees-row" v-for="(fee, index) in otherFees" :key="index">
            <view class="fees-name-column">{{ fee.name }}</view>
            <view class="fees-amount-column">¥{{ fee.amount.toFixed(2) }}</view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部固定操作栏 -->
    <view class="bottom-action-bar">
      <view class="refund-amount-row">
        <view class="refund-amount">
          <text class="amount-label">预计金额</text>
          <text class="amount-value">¥{{ totalRefundAmount.toFixed(2) }}</text>
        </view>
        <view class="refund-amount">
          <text class="text-22rpx text-#999">配送费仅支持整单退</text>
        </view>
      </view>
      <view class="confirm-btn" :class="{ disabled: !canConfirm }" @click="onConfirm">确认</view>
    </view>

    <!-- 退款原因选择器弹窗 -->
    <wd-popup v-model="showReasonPopup" position="bottom" :close-on-click-modal="true">
      <view class="reason-action-sheet">
        <!-- 选项列表容器 -->
        <scroll-view scroll-y class="reason-options-scroll">
          <view class="reason-options">
            <view
              v-for="(reason, index) in reasonList"
              :key="index"
              class="reason-option-item"
              :class="{ 'first-item': index === 0 }"
              @click="selectReason(reason)"
            >
              <text class="reason-option-text">{{ reason.reason }}</text>
            </view>
          </view>
        </scroll-view>

        <!-- 分隔条 -->
        <view class="action-sheet-gap"></view>

        <!-- 取消按钮 -->
        <view class="reason-cancel-btn" @click="showReasonPopup = false">
          <text class="cancel-text">取消</text>
        </view>

        <!-- 底部安全区域 -->
        <view class="popup-safe-area"></view>
      </view>
    </wd-popup>

    <!-- 确认退款弹窗 -->
    <sy-confirm-popup
      v-model="confirmPopupConfig.visible"
      :title="confirmPopupConfig.title"
      :content="confirmPopupConfig.content"
      :confirm-text="confirmPopupConfig.confirmText"
      :cancel-text="confirmPopupConfig.cancelText"
      @confirm="handleConfirmPopup"
    />
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import SyConfirmPopup from '@/components/sy-confirm-popup/sy-confirm-popup.vue'
import {
  isTabbarPage,
  checkTabbarPage,
  goodsList,
  otherFees,
  reasonList,
  isAllSelected,
  selectedReason,
  selectedReasonCode,
  showReasonPopup,
  totalRefundAmount,
  canConfirm,
  toggleSelectAll,
  onGoodsSelectionChange,
  decreaseCount,
  increaseCount,
  showReasonSelector,
  selectReason,
  onConfirm,
  handlePageParams,
  thirdOrderCode,
  confirmPopupConfig,
  handleConfirmPopup,
  resetState,
} from './index'

// 创建一个标志，表示是否需要重置页面
const needReset = ref(true)

// 页面加载时处理参数
onLoad((options) => {
  needReset.value = true
  handlePageParams(options)
})

// 页面显示时处理
onShow(() => {
  // 如果需要重置，并且有订单编号，则重新加载数据
  // if (needReset.value && thirdOrderCode.value) {
  //   handlePageParams({ thirdOrderCode: thirdOrderCode.value })
  // }

  // 每次页面显示后，都标记为需要重置
  // 这样下次再进入时就会重新加载数据
  needReset.value = true
})

// 组件挂载时执行
onMounted(() => {
  // 检测当前页面是否为 tabbar 页面
  checkTabbarPage()
})
</script>

<style lang="scss" src="./index.scss" scoped></style>
