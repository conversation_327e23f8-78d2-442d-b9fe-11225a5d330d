// 订单搜索页面样式
.order-search-page {
  min-height: 100vh;
  padding: 20rpx 0;
  background-color: #f1f1f1;
  // 固定头部区域
  .fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9;
    width: 100%;
    background-color: #fff;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  }

  // 自定义导航栏
  .custom-navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 24rpx;
    padding-top: var(--status-bar-height, 0);

    .back-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60rpx;
      height: 60rpx;
    }

    // 搜索组件包装器
    .search-wrapper {
      flex: 1;
      // padding: 0 16rpx;

      // 自定义wd-search组件样式
      :deep(.wd-search) {
        --search-background: #f7f7f7;
        --search-inner-padding: 0 24rpx;
        --search-input-height: 68rpx;
        --search-input-border-radius: 34rpx;
        --search-cancel-padding: 0 8rpx 0 16rpx;
        --search-placeholder-left: 8rpx;
        --search-input-font-size: 28rpx;
        padding: 0rpx 45rpx;
      }
    }
  }

  // 内容区域
  .content-area {
    padding-top: calc(var(--status-bar-height, 0) + 88rpx);
  }

  // 空状态
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 200rpx;

    .empty-image {
      width: 240rpx;
      height: 240rpx;
      margin-bottom: 40rpx;
    }

    .empty-text {
      font-size: 28rpx;
      color: #999999;
    }
  }

  // 无结果状态
  .no-result-state {
    padding-top: 200rpx;

    // 自定义wd-status-tip组件样式
    :deep(.wd-status-tip) {
      --status-tip-image-size: 240rpx;
      --status-tip-image-margin-bottom: 40rpx;
      --status-tip-tip-margin-bottom: 20rpx;
      --status-tip-tip-font-size: 28rpx;
      --status-tip-tip-color: #999999;
    }
  }

  // 加载中状态
  .loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;

    .loading-text {
      margin-left: 16rpx;
      font-size: 28rpx;
      color: #666666;
    }
  }

  .search-wrapper {
    display: flex;
    flex: 1;
    align-items: center;
    .search-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 72rpx;
      height: 60rpx;
      padding: 0 24rpx;
      margin-left: 8rpx;
      font-family: 'PingFang SC', Arial, sans-serif;
      font-size: 26rpx;
      font-weight: normal;
      line-height: 60rpx;
      color: #333333;
      cursor: pointer;
      user-select: none;
      border-radius: 15px;
    }
  }
}
