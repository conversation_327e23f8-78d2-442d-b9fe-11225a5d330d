import { ref, onMounted, reactive } from 'vue'
import { useUserStore } from '@/store'
import { fetchOrderBySerial, auditAfterSale } from '@/service/order'
import { transformOrderData as apiTransformOrderData } from '@/utils/transform'

// 响应式状态
export const statusBarHeight = ref(0)

// 订单号搜索
export const orderSearchInput = ref('')
export const isSearching = ref(false)
export const hasSearched = ref(false)

// 订单列表数据
export const orderList = ref<any[]>([])

// 获取状态栏高度
const getStatusBarHeight = () => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
}

// 验证输入是否为数字或字母
export const validateInput = (input: string): boolean => {
  // 只允许输入数字或字母
  const regex = /^[a-zA-Z0-9]*$/
  return regex.test(input)
}

// 检查登录状态
const checkLoginStatus = () => {
  const userStore = useUserStore()
  const token = userStore.userInfo?.token

  if (!token) {
    console.log('用户未登录，跳转到登录页')
    uni.reLaunch({
      url: '/pages/login/detail/index',
    })
    return false
  }

  return true
}

// 返回上一页
export const onBackClick = () => {
  uni.navigateBack()
  orderSearchInput.value = ''
}

// 清空输入
export const onClearInput = () => {
  orderSearchInput.value = ''
  // orderList.value = []
  // hasSearched.value = false
}

// 处理搜索
export const onSearch = async () => {
  if (!orderSearchInput.value.trim()) {
    uni.showToast({
      title: '请输入订单号',
      icon: 'none',
    })
    return
  }

  // 验证输入是否合法
  if (!validateInput(orderSearchInput.value)) {
    uni.showToast({
      title: '只能输入数字或字母',
      icon: 'none',
    })
    return
  }

  // 检查登录状态
  if (!checkLoginStatus()) {
    return
  }

  isSearching.value = true
  hasSearched.value = true

  try {
    console.log('搜索订单号:', orderSearchInput.value.trim())

    // 使用新的单个订单查询接口
    const orderData = await fetchOrderBySerial(orderSearchInput.value.trim())

    // 将单个订单放入数组中，保持组件接口一致性
    orderList.value = orderData?.data ? [orderData.data] : []

    console.log('搜索结果:', orderList.value)
  } catch (error) {
    console.error('搜索订单失败:', error)
    orderList.value = []
    uni.showToast({
      title: '未找到该订单',
      icon: 'none',
    })
  } finally {
    isSearching.value = false
  }
}

// 处理输入变化
export const onInputChange = (event: any) => {
  const value = event.detail.value
  if (validateInput(value)) {
    orderSearchInput.value = value
  } else {
    // 如果输入不合法，提示用户
    uni.showToast({
      title: '只能输入数字或字母',
      icon: 'none',
    })
    // 回退到上一个合法的值
    orderSearchInput.value = value.replace(/[^a-zA-Z0-9]/g, '')
  }
}

// 处理订单操作
export const onOrderAction = async (orderData: any, action: string) => {
  console.log('订单操作:', action, orderData)
  // 转发到首页的订单操作处理函数
  const pages = getCurrentPages()
  const indexPage = pages.find((page) => page.route === 'pages/index/index')

  if (indexPage && indexPage.$vm) {
    // 调用首页的订单操作方法
    indexPage.$vm.onOrderAction(orderData, action)
  } else {
    // 如果找不到首页实例，提示用户
    uni.showToast({
      title: '操作失败，请在首页进行操作',
      icon: 'none',
    })
  }
}

// 数据转换函数：将现有订单数据转换为新组件格式
export const _transformOrderData = (order: any) => {
  if (!order) {
    return {} as any
  }

  return apiTransformOrderData(order)
}
// 数据转换函数：将现有订单数据转换为新组件格式
export const transformOrderData = (order: any, isAfterSaleMode?: boolean) => {
  if (!order) {
    return {} as any
  }

  // 如果订单对象中已经包含完整的原始API数据，则直接使用新的转换函数
  if (order) {
    const transformedData = apiTransformOrderData(order)

    // 如果是售后模式，确保售后数据正确处理
    if (isAfterSaleMode && order.refundRecords) {
      // 确保售后数据是数组格式
      transformedData.afterSale = Array.isArray(order.refundRecords)
        ? order.refundRecords
        : [order.refundRecords]
    }

    return transformedData
  }

  // 否则使用旧的转换逻辑，将Order格式转换为OrderData格式
  // 尝试解析扩展字段的地址信息
  let address = order.address.full

  try {
    if (order.address && order.address.full && order.address.full.includes('extlOrderDetail')) {
      const extlOrderDetail = JSON.parse(order.address.full)
      if (extlOrderDetail.syncThirdOrderReqDto) {
        address = extlOrderDetail.syncThirdOrderReqDto.address || ''
      }
    }
  } catch (e) {
    console.error('解析地址信息失败:', e)
  }

  // 返回符合组件期望的格式
  return {
    extlChannel: order.extlChannel,
    id: order.id,
    orderNo: order.orderNumber,
    deliveryTime: order.deliveryTime,
    sendTime: order.sendTime,
    status: order.statusInfo.main,
    type: order.type,
    tenantId: order.tenantId,
    instanceId: order.instanceId,
    bizType: order.bizType,
    isBook: order.isBook,
    saleChannel: order.saleChannel,
    buyerRemark: order.buyerRemark,
    merchantConfirmTime: order.merchantConfirmTime,
    merchant: {
      avatar: order.merchant.avatar || '/static/images/img/mt.png',
      name: order.merchant.name,
      id: order.merchant.id,
    },
    customer: {
      name: order.customer.name,
      phone: order.customer.fullPhone,
    },
    address: {
      detail: address,
      distance: order.address.distance || '<1km',
    },

    orderStatus: {
      text: order.statusInfo.main,
      time: order.statusInfo.time || '',
      description: order.buyerRemark || order.statusInfo.description || '',
    },
    delivery: {
      method: '外卖配送',
      time: order.deliveryTime ? `预计${order.deliveryTime.substring(11, 16)}送达` : '',
      platform: order.channel || '',
    },
    goods: {
      count: order.items?.length || 1,
      summary: order.items?.length > 0 ? order.items[0].name : '商品',
      items: order.items || [],
    },
    fees: {
      merchantIncome: order.fees?.merchantIncome || 0,
      deliveryFee: order.fees?.deliveryFee || 0,
      packagingFee: order.fees?.packagingFee || 0,
      totalAmount: order.fees?.totalAmount || 0,
    },
    times: {
      createTime: order.createTime || '',
      placeTime: order.createTime || '',
      deliveryTime: order.deliveryTime || '',
    },
  }
}

// 生命周期相关的初始化函数（供Vue组件调用）
export const initPage = () => {
  console.log('订单搜索页面 - initPage')
  getStatusBarHeight()

  // 检查登录状态
  if (!checkLoginStatus()) {
    console.log('登录检查失败，停止初始化')
  }
}

/**
 * 处理订单卡片操作结果
 */
export const onOperationResult = async (result: {
  action: string
  success: boolean
  message: string
}) => {
  console.log('订单操作结果:', result)

  // 售后操作不在这里刷新，避免与弹窗冲突导致页面闪动
  // 售后操作的刷新在弹窗确认后进行
  if (result.action === 'after-sale-approve' || result.action === 'after-sale-reject') {
    console.log('售后操作，跳过立即刷新，等待弹窗确认后刷新')
    return
  }

  // 其他操作成功时刷新订单列表
  if (result.success) {
    try {
      // 重新搜索刷新结果
      await onSearch()
    } catch (error) {
      console.error('刷新订单列表失败:', error)
    }
  }
}
/**
 * 退款
 */
export const onRefundOrder = async (thirdOrderCode: string) => {
  console.log('点击退款按钮:', thirdOrderCode)

  // 跳转到取消订单页面，并传递订单编号参数
  uni.navigateTo({
    url: `/pages/order/cancelOrder/index?thirdOrderCode=${thirdOrderCode}`,
  })
}

// ==================== 售后弹窗相关 ====================

/**
 * 售后同意处理
 */
export const onAfterSaleApprove = async (
  tradeNo: string,
  instanceId?: string,
  tenantId?: string,
) => {
  console.log('售后同意:', tradeNo, '实例ID:', instanceId, '租户ID:', tenantId)

  // 显示确认弹窗，并保存instanceId和tenantId
  showApprovePopup(tradeNo, instanceId, tenantId)
}

/**
 * 售后拒绝处理
 */
export const onAfterSaleReject = async (
  tradeNo: string,
  instanceId?: string,
  tenantId?: string,
) => {
  console.log('售后拒绝:', tradeNo, '实例ID:', instanceId, '租户ID:', tenantId)

  // 显示确认弹窗，并保存instanceId和tenantId
  showRejectPopup(tradeNo, instanceId, tenantId)
}

/**
 * 同意弹窗配置
 */
export const approvePopupConfig = reactive({
  visible: false,
  title: '操作确认',
  content: '同意退款后，该订单款项不再计入结算。定责结果和赔付信息可在【订单-申诉】中查询。',
  cancelText: '取消',
  confirmText: '同意退款',
  tradeNo: '', // 当前操作的tradeNo
  instanceId: '', // 实例ID
  tenantId: '', // 租户ID
})

/**
 * 拒绝弹窗配置
 */
export const rejectPopupConfig = reactive({
  visible: false,
  title: '建议您与顾客沟通协商后处理',
  content:
    '当您未与顾客协商一致，直接拒绝退款用户有权向客服申诉，如被平台审核为商家原因导致订单取消，将影响您的排名。',
  cancelText: '已沟通，拒绝退款',
  confirmText: '取消',
  tradeNo: '', // 当前操作的tradeNo
  instanceId: '', // 实例ID
  tenantId: '', // 租户ID
})

/**
 * 显示同意弹窗
 */
export const showApprovePopup = (tradeNo: string, instanceId?: string, tenantId?: string) => {
  approvePopupConfig.tradeNo = tradeNo
  approvePopupConfig.instanceId = instanceId || ''
  approvePopupConfig.tenantId = tenantId || ''
  approvePopupConfig.visible = true
}

/**
 * 显示拒绝弹窗
 */
export const showRejectPopup = (tradeNo: string, instanceId?: string, tenantId?: string) => {
  rejectPopupConfig.tradeNo = tradeNo
  rejectPopupConfig.instanceId = instanceId || ''
  rejectPopupConfig.tenantId = tenantId || ''
  rejectPopupConfig.visible = true
}

/**
 * 确认同意售后
 */
export const handleApproveConfirm = async () => {
  // 隐藏弹窗
  approvePopupConfig.visible = false

  // 如果有订单ID，执行同意操作
  if (approvePopupConfig.tradeNo) {
    await executeApproveAfterSale(approvePopupConfig.tradeNo)
    // 重置订单ID和其他信息
    approvePopupConfig.tradeNo = ''
    approvePopupConfig.instanceId = ''
    approvePopupConfig.tenantId = ''
    // 刷新搜索结果
    setTimeout(() => {
      if (orderSearchInput.value) {
        // onSearch()
      }
    }, 2000)
  }
}

/**
 * 取消同意售后
 */
export const handleApproveCancel = () => {
  approvePopupConfig.visible = false
  approvePopupConfig.tradeNo = ''
  approvePopupConfig.instanceId = ''
  approvePopupConfig.tenantId = ''
}

/**
 * 确认拒绝售后
 */
export const handleRejectConfirm = async () => {
  // 拒绝弹窗中，确认按钮是"取消"操作
  rejectPopupConfig.visible = false
  rejectPopupConfig.tradeNo = ''
  rejectPopupConfig.instanceId = ''
  rejectPopupConfig.tenantId = ''
}

/**
 * 执行拒绝售后
 */
export const handleRejectCancel = async () => {
  // 拒绝弹窗中，取消按钮是"拒绝退款"操作
  // 隐藏弹窗
  rejectPopupConfig.visible = false

  // 如果有订单ID，执行拒绝操作
  if (rejectPopupConfig.tradeNo) {
    await executeRejectAfterSale(rejectPopupConfig.tradeNo)
    // 重置订单ID和其他信息
    rejectPopupConfig.tradeNo = ''
    rejectPopupConfig.instanceId = ''
    rejectPopupConfig.tenantId = ''
    // 刷新搜索结果
    setTimeout(() => {
      if (orderSearchInput.value) {
        // onSearch()
      }
    }, 1000)
  }
}

/**
 * 执行售后同意操作（实际调用API）
 */
export const executeApproveAfterSale = async (tradeNo: string) => {
  console.log(
    '执行售后同意:',
    tradeNo,
    '实例ID:',
    approvePopupConfig.instanceId,
    '租户ID:',
    approvePopupConfig.tenantId,
  )

  try {
    uni.showLoading({
      title: '处理中...',
      mask: true,
    })

    // 调用售后审核接口
    const result = await auditAfterSale({
      tradeNo,
      refundStatus: 30,
      agreeReason: '同意退款',
      instanceId: approvePopupConfig.instanceId,
      tenantId: approvePopupConfig.tenantId,
    })

    console.log('售后同意结果:', result)

    if (result?.resultCode === '0') {
      uni.showToast({
        title: '同意成功',
        icon: 'success',
      })

      uni.setStorageSync('refresh_order_list', 'refresh')
      // 延迟刷新搜索结果，避免页面闪动
      setTimeout(async () => {
        if (orderSearchInput.value) {
          await onSearch()
        }
      }, 1000)
    } else {
      uni.showToast({
        title: result?.resultMsg || '操作失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('售后同意失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

/**
 * 执行售后拒绝操作（实际调用API）
 */
export const executeRejectAfterSale = async (tradeNo: string) => {
  console.log(
    '执行售后拒绝:',
    tradeNo,
    '实例ID:',
    rejectPopupConfig.instanceId,
    '租户ID:',
    rejectPopupConfig.tenantId,
  )

  try {
    uni.showLoading({
      title: '处理中...',
      mask: true,
    })

    // 调用售后审核接口
    const result = await auditAfterSale({
      tradeNo,
      refundStatus: 40,
      agreeReason: '已沟通，拒绝退款',
      instanceId: rejectPopupConfig.instanceId,
      tenantId: rejectPopupConfig.tenantId,
    })

    console.log('售后拒绝结果:', result)

    if (result?.resultCode === '0') {
      uni.showToast({
        title: '拒绝成功',
        icon: 'success',
      })

      uni.setStorageSync('refresh_order_list', 'refresh')
      // 延迟刷新搜索结果，避免页面闪动
      setTimeout(async () => {
        if (orderSearchInput.value) {
          await onSearch()
        }
      }, 1000)
    } else {
      uni.showToast({
        title: result?.resultMsg || '操作失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('售后拒绝失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}
