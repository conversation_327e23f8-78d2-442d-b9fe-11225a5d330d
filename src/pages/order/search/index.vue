<route lang="json5">
{
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>

<template>
  <view class="order-search-page" :style="{ '--status-bar-height': statusBarHeight + 'px' }">
    <!-- 固定在顶部的自定义头部 -->
    <view class="fixed-header">
      <view class="custom-navbar">
        <!-- 返回按钮 -->
        <view class="back-button" @click="onBackClick">
          <wd-icon name="arrow-left" size="40rpx" color="#333"></wd-icon>
        </view>

        <!-- 使用wd-search组件替换自定义搜索框 -->
        <view class="search-wrapper flex items-center">
          <wd-search
            v-model="orderSearchInput"
            placeholder="请输入订单号"
            cancel-button
            input-align="left"
            placeholder-left
            clearable
            focus
            cancel-txt="搜索"
            hide-cancel
            hide-cancel-onblur
            use-suffix-slot
            @search="onSearch"
            @clear="onClearInput"
            @input="handleSearchInput"
            :style="{ flex: 1 }"
          ></wd-search>
          <view class="search-btn" @click="onSearch">搜索</view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view
      class="content-area"
      scroll-y="true"
      :style="{ height: `calc(100vh - ${statusBarHeight + 88}px)` }"
    >
      <!-- 搜索中状态 -->
      <view v-if="isSearching" class="loading-state">
        <wd-loading size="32rpx" color="#0064FF" />
        <text class="loading-text">搜索中...</text>
      </view>

      <!-- 空状态/无结果统一 -->
      <view
        v-else-if="!hasSearched || (hasSearched && orderList.length === 0)"
        class="no-result-state"
      >
        <wd-status-tip
          :image-size="{
            height: '240',
            width: '240',
          }"
          image="search"
          tip="暂无订单"
        />
      </view>

      <!-- 搜索结果列表 -->
      <view v-else class="order-list">
        <SyOrderCard
          v-for="order in orderList"
          :key="order.id"
          :order-data="
            transformOrderData(order, order.refundRecords && order.refundRecords.length > 0)
          "
          :is-after-sale-mode="order.refundRecords && order.refundRecords.length > 0"
          @operation-result="onOperationResult"
          @after-sale-approve="onAfterSaleApprove"
          @after-sale-reject="onAfterSaleReject"
          @refund-order="onRefundOrder"
        />
        <!-- 底部安全区占位 -->
        <view style="height: env(safe-area-inset-bottom, 32px)"></view>
      </view>
    </scroll-view>

    <!-- 确认弹窗组件 - 同意退款 -->
    <sy-confirm-popup
      v-model="approvePopupConfig.visible"
      :title="approvePopupConfig.title"
      :content="approvePopupConfig.content"
      :cancel-text="approvePopupConfig.cancelText"
      :confirm-text="approvePopupConfig.confirmText"
      @confirm="handleApproveConfirm"
      @cancel="handleApproveCancel"
    />

    <!-- 确认弹窗组件 - 拒绝退款 -->
    <sy-confirm-popup
      v-model="rejectPopupConfig.visible"
      :title="rejectPopupConfig.title"
      :content="rejectPopupConfig.content"
      :cancel-text="rejectPopupConfig.cancelText"
      :confirm-text="rejectPopupConfig.confirmText"
      @confirm="handleRejectConfirm"
      @cancel="handleRejectCancel"
    />
  </view>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { onShow, onHide, onLoad } from '@dcloudio/uni-app'
import { watchStorageKey } from '@/utils'
import SyOrderCard from '@/components/sy-order-card'
import SyConfirmPopup from '@/components/sy-confirm-popup/sy-confirm-popup.vue'
import {
  statusBarHeight,
  orderSearchInput,
  isSearching,
  hasSearched,
  orderList,
  onBackClick,
  onClearInput,
  onSearch,
  onInputChange,
  onOrderAction,
  transformOrderData,
  initPage,
  validateInput,
  onOperationResult,
  onRefundOrder,
  onAfterSaleApprove,
  onAfterSaleReject,
  approvePopupConfig,
  rejectPopupConfig,
  handleApproveConfirm,
  handleApproveCancel,
  handleRejectConfirm,
  handleRejectCancel,
} from './index'

// 处理搜索输入
const handleSearchInput = (value: string) => {
  // 验证输入是否为数字或字母
  if (validateInput(value)) {
    // 符合验证要求，不需要特殊处理
  } else {
    // 不符合验证要求，显示提示
    uni.showToast({
      title: '只能输入数字或字母',
      icon: 'none',
    })
  }
}

// 生命周期
onMounted(() => {
  initPage()
})

onUnmounted(() => {
  orderList.value = []
  hasSearched.value = false
})

onShow(() => {
  // 商家主动退提交返回 - 刷新订单列表
  watchStorageKey<any>(
    'refresh_order_list',
    (value) => {
      if (value === 'refresh' && orderSearchInput.value) {
        console.log('商家主动退提交返回 - 刷新订单列表')
        onSearch()
      }
    },
    3000,
  )
})
</script>

<style lang="scss" src="./index.scss" scoped></style>
