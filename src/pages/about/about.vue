<route lang="json5">
{
  layout: 'tabbar',
  style: {
    navigationBarTitleText: '关于',
  },
}
</route>

<template>
  <view
    class="bg-white overflow-hidden pt-2 px-4"
    :style="{ marginTop: safeAreaInsets?.top + 'px' }"
  >
    <!-- <view class="text-center text-3xl mt-8">
      鸽友们好，我是
      <text class="text-red-500">菲鸽</text>
    </view>
    <RequestComp /> -->
    <!-- <UploadComp /> -->

    <!-- 退出登录按钮 -->
    <view class="flex justify-center mt-20">
      <button @click="handleLogout">退出登录</button>
    </view>
  </view>
</template>

<script lang="ts" setup>
// import RequestComp from './components/request.vue'
// import UploadComp from './components/upload.vue'
import { useUserStore } from '@/store/user'

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 获取userStore
        const userStore = useUserStore()
        // 清除用户信息
        userStore.clearUserInfo()
        // 提示用户
        uni.showToast({
          title: '已退出登录',
          icon: 'success',
        })
        // 跳转到登录页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/detail/index',
          })
        }, 1500)
      }
    },
  })
}
</script>

<style lang="scss" scoped>
.test-css {
  // mt-4=>1rem=>16px;
  margin-top: 16px;
}
</style>
