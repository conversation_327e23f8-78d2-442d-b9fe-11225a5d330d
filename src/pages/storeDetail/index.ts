import { ref, watch } from 'vue'
import type { StoreInfo, StoreTab, StoreEntry } from '@/types/store'
import { getStoreDetail } from '@/service/store/index'
import type { ChannelOption } from '@/components/sy-channel-selector'
import { processChannelData } from '@/components/sy-channel-selector/utils'
import { fetchChannelEnum, fetchStoreDetailByChannel } from '@/service/order/dict'
import { useShopStore } from '@/store/shop'
import type { ShopDto } from '@/types/shop'

export function useStoreDetail() {
  // 状态栏高度
  const statusBarHeight = ref(0)

  // 当前选择的门店信息
  const currentShopInfo = ref<ShopDto | null>(null)

  // 门店基础信息
  const storeInfo = ref<StoreInfo>({} as StoreInfo)

  // 渠道数据
  const channelOptions = ref<StoreTab[]>([])
  const selectedChannels = ref<string[]>([])
  // 控制评分显示
  const hideRateing = ref(false)
  // Tab 相关数据
  const modelValue = ref('')
  // loading弹窗
  const isLoading = ref(false)
  const secondModelValue = ref('')
  // 存储当前选中的二级选项的完整数据对象
  const secondModelObj = ref<StoreTab | null>(null)

  // Tab数据
  const tabs = ref<StoreTab[]>([])

  // Tab数据
  const seconTab = ref<StoreTab[]>([])
  const notice = ref('美团外卖：下次购买返小积分(限产品使用)')
  // 业务入口按钮
  const entries = ref<StoreEntry[]>([
    { key: 'order', label: '订单', icon: '/static/images/icons/order.png' },
    { key: 'comment', label: '评价', icon: '/static/images/icons/comment.png' },
    { key: 'dish', label: '菜品', icon: '/static/images/icons/dish.png' },
    { key: 'table', label: '桌码点单', icon: '/static/images/icons/table.png' },
  ])

  // 监听 modelValue 变化
  watch(modelValue, (newValue) => {
    if (newValue) {
      // 立即清空当前门店信息，避免显示上一个渠道的数据
      storeInfo.value = {} as StoreInfo

      hideRateing.value = !['JingDongTakeOutBrand', 'SYZY'].includes(newValue)
      // 在这里可以根据选中的渠道执行相应的操作
      selectedChannels.value = [newValue]
      // 可以在这里调用获取门店详情的方法，根据选中的渠道获取数据
      if (['DouYinXiaoshiDa'].includes(newValue)) {
        calculateComponentHeights()
        return
      }
      fetchStoreDetailBySelectedChannel()
    }
  })

  // 监听 secondModelValue 变化，同步更新 secondModelObj
  watch(secondModelValue, (newValue) => {
    if (newValue) {
      // 立即清空当前门店信息，避免显示上一个门店的数据
      storeInfo.value = {} as StoreInfo

      // 查找对应的完整数据对象并更新 secondModelObj
      const selectedItem = seconTab.value.find((item) => item.key === newValue)
      secondModelObj.value = selectedItem || null
      if (['DouYinXiaoshiDa'].includes(selectedChannels.value[0])) {
        return
      }
      fetchStoreDetailBySelectedChannel()
    }
  })
  // 添加组件高度计算相关的引用和状态
  const mainContentHeight = ref(0)
  const leftCategoryHeight = ref(0)
  const rightDishListHeight = ref(0)

  // 计算各组件高度
  const calculateComponentHeights = () => {
    // 使用setTimeout确保DOM已经渲染
    setTimeout(() => {
      // 创建查询选择器
      const query = uni.createSelectorQuery()

      // 获取各组件高度
      // query.select('.sy-order-fixed-header').boundingClientRect()
      query.select('.sy-store-tab-header').boundingClientRect()
      query.select('.sy-store-tab-nav').boundingClientRect()
      query.select('.shop-box').boundingClientRect()

      // 获取系统信息
      uni.getSystemInfo({
        success: (sysInfo) => {
          query.exec((res) => {
            console.log('RES===>', res)

            // 检查结果是否有效
            if (!res || !res.length) {
              // 使用默认值作为备选方案
              const defaultHeight = 400
              mainContentHeight.value = defaultHeight
              leftCategoryHeight.value = defaultHeight
              rightDishListHeight.value = defaultHeight
              return
            }

            // 计算固定组件的总高度
            const headerHeight = res[0]?.height || 0 // SyOrderFixedHeader高度
            const shopBoxHeight = res[1]?.height || 0 // 顶部店铺信息高度
            const tabNavHeight = res[2]?.height || 0 // SyStoreTabNav高度
            const tabContainerHeight = res[3]?.height || 0 // 标签栏高度
            // 获取底部安全区高度和tabbar高度
            const safeAreaBottom = sysInfo.safeAreaInsets?.bottom || 0
            const tabbarHeight = 70 // tabbar高度（一般为50px）
            // 计算页面总高度和主内容区域可用高度
            const windowHeight = sysInfo.windowHeight
            const fixedComponentsHeight =
              headerHeight + shopBoxHeight + tabNavHeight + tabContainerHeight
            const bottomHeight = safeAreaBottom + tabbarHeight
            // 计算主内容区域可用高度（考虑了安全区域和底部导航栏）
            const availableHeight = windowHeight - fixedComponentsHeight - bottomHeight
            // 确保高度不会太小，同时考虑一些额外的空间
            const finalHeight = Math.max(availableHeight - 20, 300) // 减去20px的安全边距

            // 更新主内容区域高度
            mainContentHeight.value = finalHeight
          })
        },
        fail: (err) => {
          console.log('获取系统信息失败:', err)
          // 使用默认高度
          mainContentHeight.value = 400
        },
      })
    }, 300) // 给DOM渲染一些时间
  }
  // 根据选中的渠道获取门店详情
  const fetchStoreDetailBySelectedChannel = async () => {
    try {
      isLoading.value = true

      // 先清空当前门店信息，避免显示上一个渠道的数据
      storeInfo.value = {} as StoreInfo

      const shopStore = useShopStore()
      if (!shopStore.hasSelectedShop) {
        console.error('未选择门店')
        return
      }

      const shop = shopStore.currentShop
      const params = {
        shopId: secondModelObj.value.id,
        tenantId: secondModelObj.value.tenantId,
        channelCode: modelValue.value, // 使用选中的渠道
      }

      // 判断参数是否为空，如果任一字段为空或空字符串，则不执行后续代码
      // 注意：这里使用 === '' 而不是 !params.xxx，因为 0 不应该被判定为空值
      if (
        params.shopId === undefined ||
        params.shopId === null ||
        params.shopId === '' ||
        params.tenantId === undefined ||
        params.tenantId === null ||
        params.tenantId === '' ||
        params.channelCode === undefined ||
        params.channelCode === null ||
        params.channelCode === ''
      ) {
        console.error('参数不完整，无法获取门店详情', params)
        // 参数不完整时，设置空数据
        storeInfo.value = {} as StoreInfo
        return
      }

      const { data, resultCode } = await fetchStoreDetailByChannel(params)
      console.log('storeDetailRes', data, resultCode)
      if ('' + resultCode === '0' && data) {
        // 处理数据
        processStoreData(data)
      } else {
        // 接口返回异常或无数据，设置空数据
        console.log('接口返回异常或无数据，设置空数据')
        storeInfo.value = {} as StoreInfo
      }
      nextTick(() => {
        isLoading.value = false
      })
    } catch (error) {
      console.error('根据渠道获取门店详情失败:', error)
      // 发生错误时，也设置空数据
      storeInfo.value = {} as StoreInfo
      nextTick(() => {
        isLoading.value = false
      })
    }
  }

  // 处理门店数据，设置兼容属性
  const processStoreData = (data: any) => {
    // 如果没有数据或数据为空对象，则不处理
    if (!data || Object.keys(data).length === 0) {
      console.log('门店数据为空，不进行处理')
      storeInfo.value = {} as StoreInfo
      return
    }
    console.log('处理门店数据:', data)

    // 处理电话号码
    if (typeof data.phones === 'string') {
      data.phones = data.phones.split(',')
      if (data.phones.join('').includes('/')) {
        data.phones = data.phones.join('').split('/')
      }
      console.log(' data.phones', data.phones)
    }

    // 处理营业时间
    if (typeof data.businessTime === 'string') {
      data.openTime = data.businessTime.split(',')
    }

    // 设置兼容属性
    data.rating = data.avgScore
    data.taste = data.avgTasteScore
    data.packaging = data.avgPackingScore
    data.deliveryRate = data.avgDeliveryScore
    data.category = data.businessCategory
    data.phone = data.phones
    data.status = data.businessStatus === 1 ? '营业中' : '未营业'

    // 更新门店信息
    storeInfo.value = data
  }

  // 获取状态栏高度
  const getStatusBarHeight = () => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight || 0
    console.log(' systemInfo.statusBarHeight', systemInfo.statusBarHeight)
  }

  // 初始化门店信息
  const initStoreInfo = () => {
    try {
      // 获取状态栏高度
      getStatusBarHeight()

      const shopStore = useShopStore()

      if (shopStore.hasSelectedShop) {
        const shop = shopStore.currentShop
        // 更新当前选择的门店信息
        currentShopInfo.value = {
          name: shop?.name || '',
          id: shop?.id || '',
          address: shop?.raw?.address || '',
          shopDtoList: shop?.raw?.shopDtoList || [],
        } as ShopDto

        // 将 shopDtoList 映射到 seconTab
        if (shop?.raw?.shopDtoList && Array.isArray(shop.raw.shopDtoList)) {
          seconTab.value = shop.raw.shopDtoList.map((item) => ({
            key: item.id || '',
            label: item.name || '',
            ...item,
          }))

          // 默认选中第一个二级标签
          if (seconTab.value.length > 0 && !modelValue.value) {
            secondModelValue.value = seconTab.value[0].key
            // 同步更新 secondModelObj
            secondModelObj.value = shop.raw.shopDtoList[0]
          }
        }
      }
    } catch (error) {
      console.error('Error in initStoreInfo:', error)
    }
  }

  // 获取渠道数据
  const getChannelData = async () => {
    try {
      const res = await fetchChannelEnum({
        type: 'shop',
      })
      if (res?.data) {
        // 处理渠道数据，将其转换为 StoreTab 格式
        channelOptions.value = res.data.map((item) => ({
          key: item.code,
          label: item.name,
        }))

        // 默认选中第一个渠道
        if (channelOptions.value.length > 0 && !modelValue.value) {
          modelValue.value = channelOptions.value[0].key
          selectedChannels.value = [channelOptions.value[0].key]
        }
      }
    } catch (error) {
      console.error('获取渠道数据失败:', error)
    }
  }

  // 分享事件
  function onShare() {
    uni.showShareMenu && uni.showShareMenu()
  }
  // 拨打电话
  function callPhone(phone: string) {
    if (!phone) return

    // 直接拨打传入的电话号码
    uni.makePhoneCall({
      phoneNumber: phone,
      fail: (err) => {
        console.error('拨打电话失败:', err)
        uni.showToast({
          title: '拨打电话失败',
          icon: 'none',
        })
      },
    })
  }
  // 打开地图
  function openMap(address: string) {
    // 这里可根据实际业务调用uni.openLocation
    uni.showToast({ title: '跳转地图：' + address, icon: 'none' })
  }

  // 跳转到选择默认物理门店页
  function goToSelectShop() {
    uni.navigateTo({
      url: '/pages/shop/list/index',
      success: () => {
        console.log('跳转到选择门店页面成功')
      },
      fail: (err) => {
        console.error('跳转到选择门店页面失败:', err)
        uni.showToast({
          title: '跳转失败',
          icon: 'none',
        })
      },
    })
  }

  // 业务入口点击
  function onEntryClick(entry: StoreEntry) {
    uni.showToast({ title: `点击了${entry.label}`, icon: 'none' })
  }

  // 获取门店详情
  const fetchStoreDetail = async () => {
    try {
      // 初始化门店基础信息
      initStoreInfo()
    } catch (error) {
      console.error('获取门店详情失败:', error)
    }
  }

  // 处理营业时间显示
  function showOpenTimeDetail() {
    if (!storeInfo.value?.openTime || storeInfo.value.openTime.length <= 1) return

    // 创建一个格式化的时间段列表
    const formattedTimeList = storeInfo.value.openTime.map((time, index) => {
      return `${index + 1}. ${time}`
    })

    uni.showActionSheet({
      itemList: formattedTimeList,
      success: () => {
        // 仅展示，无需额外操作
      },
    })
  }

  // 处理点击查看更多电话
  const showPhoneList = () => {
    console.log('1111')
    if (!storeInfo.value?.phone || storeInfo.value.phone.length <= 1) return

    // 准备电话列表数据
    const phoneList = storeInfo.value.phone.map((phone) => ({
      text: phone,
      value: phone,
    }))

    // 显示操作菜单
    uni.showActionSheet({
      itemList: phoneList.map((item) => item.text),
      success: function (res) {
        // 用户点击了某个电话
        if (res.tapIndex >= 0 && res.tapIndex < phoneList.length) {
          callPhone(phoneList[res.tapIndex].value)
        }
      },
      fail: function (res) {
        console.log(res.errMsg)
      },
    })
  }

  return {
    currentShopInfo,
    storeInfo,
    tabs,
    seconTab,
    notice,
    entries,
    channelOptions,
    selectedChannels,
    onShare,
    callPhone,
    openMap,
    onEntryClick,
    showOpenTimeDetail,
    showPhoneList,
    fetchStoreDetail,
    initStoreInfo,
    getChannelData,
    modelValue,
    secondModelValue,
    secondModelObj,
    goToSelectShop,
    statusBarHeight,
    getStatusBarHeight,
    hideRateing,
    isLoading,
    mainContentHeight,
  }
}
