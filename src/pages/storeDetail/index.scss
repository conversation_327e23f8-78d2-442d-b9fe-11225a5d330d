.store-detail-page {
  box-sizing: border-box;
  width: 100%;
  // min-height: calc(100vh - var(--status-bar-height));
  padding-top: var(--status-bar-height);
  padding-bottom: 10rpx;
  overflow-x: hidden; // 防止横向滚动
  background: white;

  // 确保所有子元素不会溢出
  * {
    box-sizing: border-box;
    max-width: 100%;
  }

  // 防止图片溢出
  image {
    max-width: 100%;
  }

  .text-primary-box {
    padding-top: 106rpx;
    padding-bottom: 38rpx;
  }
  .text-shoptext-shop-box {
    position: relative;
    z-index: 10;
    padding-top: 30rpx;
    padding-bottom: 20rpx;
    margin-bottom: 20rpx;
    // background: white;
    // 移除底部边框
    border-bottom: 2rpx solid #f2f2f4;
  }
  .text-primary {
    font-size: 30rpx;
    font-weight: 600;
    color: #222222;
  }
  .text-shop {
    font-size: 40rpx;
    font-weight: 600;
    color: #222222;
  }
  .arrow-icon {
    width: 48rpx;
    height: 48rpx;
    font-size: 22rpx;
    color: #000000;
  }
  // 顶部栏
  .store-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 32rpx;
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;
    .store-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #222;
    }
    .share-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;
      background: #f5f6fa;
      border-radius: 50%;
    }
  }

  // Tab导航
  .store-tab-nav {
    background: #fff;
    .tab-item {
      position: relative;
      padding: 0 12rpx 12rpx 12rpx;
      margin-right: 32rpx;
      font-size: 28rpx;
      color: #666;
      &.active {
        font-weight: bold;
        color: #f33429;
        &::after {
          position: absolute;
          bottom: 0;
          left: 50%;
          display: block;
          width: 40rpx;
          height: 6rpx;
          content: '';
          background: #f33429;
          border-radius: 3rpx;
          transform: translateX(-50%);
        }
      }
    }
  }

  // 红色提示条
  .store-notice-bar {
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    margin: 16rpx 32rpx 0 32rpx;
    font-size: 24rpx;
    color: #f33429;
    background: #fff0f0;
    border-radius: 12rpx;
    image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 12rpx;
    }
  }

  // 评分区
  .store-rating {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    margin: 24rpx 32rpx 0 32rpx;
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.04);
    .main-score {
      margin-right: 12rpx;
      font-size: 48rpx;
      font-weight: bold;
      color: #f33429;
    }
    .star-list {
      display: flex;
      align-items: center;
      image {
        width: 32rpx;
        height: 32rpx;
        margin-right: 4rpx;
      }
    }
    .score-detail {
      margin-left: 24rpx;
      font-size: 24rpx;
      color: #666;
    }
    .score-tag {
      padding: 2rpx 10rpx;
      margin-left: 12rpx;
      font-size: 22rpx;
      color: #f33429;
      background: #fff0f0;
      border-radius: 8rpx;
    }
  }

  // 信息区域
  .store-info-block {
    margin: 0 32rpx;
    overflow: hidden;
    background-color: #ffffff;
    border-radius: 20rpx;
    // box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.04);

    .info-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 88rpx;
      padding: 24rpx 0;
      padding: 0 24rpx;

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        font-size: 28rpx;
        color: #000000;
      }

      .info-value {
        position: relative;
        display: flex;
        align-items: center;
        max-width: 400rpx;
        overflow: hidden; /* 隐藏超出容器的文本 */
        font-size: 28rpx;
        color: #666666;
        text-overflow: ellipsis; /* 超出部分显示省略号 */
        white-space: nowrap; /* 保持文本在一行内显示 */
      }
      .info-value-no-width {
        // max-width: 500px;
      }
      .info-value-esc {
        display: block;
        max-width: 400rpx;
        overflow: hidden;
        font-size: 28rpx;
        line-height: 40rpx;
        color: #666666;
        text-align: left;
        text-overflow: ellipsis;
        white-space: nowrap; /* 保持文本在一行内显示 */
      }
      .open-time-text {
        display: flex;
        flex-direction: column;
        max-width: 400rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        text {
          line-height: 36rpx;

          &.ml-1 {
            margin-left: 8rpx;
          }
        }
      }

      .question-icon-logo {
        position: relative;
        top: 0px;
        width: 24rpx;
        height: 24rpx;
        margin-left: 6rpx;
      }
      .status-tag-red {
        display: flex;
        align-items: center;
        padding: 4rpx 12rpx;
        background-color: #fff8f2;
        border-radius: 100rpx;

        .status-dot {
          width: 18rpx;
          height: 18rpx;
          margin-right: 4rpx;
          background-color: #fa6b00;
          border-radius: 50%;
        }

        .status-text {
          font-size: 24rpx;
          color: #333333;
        }
      }
      .status-tag {
        display: flex;
        align-items: center;
        padding: 4rpx 12rpx;
        background-color: #e6faf3;
        border-radius: 100rpx;

        .status-dot {
          width: 18rpx;
          height: 18rpx;
          margin-right: 4rpx;
          background-color: #00bf7f;
          border-radius: 50%;
        }

        .status-text {
          font-size: 24rpx;
          color: #333333;
        }
      }

      .arrow-right {
        width: 8rpx;
        height: 14rpx;
        margin-left: 8rpx;
        border-top: 2rpx solid #222222;
        border-right: 2rpx solid #222222;
        transform: rotate(45deg);
      }

      .store-logo {
        width: 114rpx;
        height: 34rpx;
      }

      // 电话容器样式
      .phones-container {
        display: flex;
        flex-direction: column;
        width: 100%;
        max-width: 380rpx;

        .phone-number {
          margin-bottom: 6rpx;
          font-size: 28rpx;
          color: #333; // 普通文本颜色
          text-decoration: none; // 移除下划线
        }

        .more-phones {
          display: flex;
          align-items: center;
          margin-top: 4rpx;

          .more-phones-text {
            font-size: 24rpx;
            color: #666666;
          }

          .item-icon {
            width: 24rpx;
            height: 24rpx;
            margin-left: 6rpx;
          }
        }
      }
    }
  }

  // 业务入口区
  .store-entry-list {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin: 32rpx 0 0 0;
    .entry-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100rpx;
      height: 120rpx;
      background: #f5f6fa;
      border-radius: 20rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
      image {
        width: 64rpx;
        height: 64rpx;
        margin-bottom: 8rpx;
        background: #fff;
        border-radius: 16rpx;
      }
    }
  }

  .shop-box {
    padding: 16rpx 28rpx 20rpx 24rpx;
    // 移除底部边框
    // border-bottom: 1px solid #f5f5f5;
  }

  .text-shoptext-shop {
    color: #333;
  }

  .luoshi-icon {
    width: 48rpx;
    height: 48rpx;
  }
}

// 覆盖固定头部组件的样式
:deep(.fixed-header) {
  position: relative; // 在这个页面中不需要固定定位
  z-index: 10;

  .store-header {
    // 移除底部边框
    // border-bottom: 1px solid #f5f5f5;
  }
}

.gap-line {
  background: rgba(251, 251, 251, 1);
}
.frosted-glass {
  height: 10rpx;
  /* 背景色设置半透明 */
  background: rgba(255, 255, 255, 0.5);
  /* 添加模糊效果 */
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  /* 添加微弱的边框 */
  border: 1px solid rgba(255, 255, 255, 0.2);
  /* 可选：添加阴影效果 */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
.item-icon {
  position: relative;
  top: 2rpx;
  width: 24rpx;
  height: 24rpx;
  margin-left: 6rpx;
}
.hide-btn {
  font-size: 20rpx;
  font-weight: 400;
  color: #3d3d3d;
}
.xiangshang-icon-logo {
  width: 24rpx;
  height: 24rpx;
}
.xiangxia-box {
  justify-content: center;
  width: 702rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #3d3d3d;
}
.xiangxia-icon-logo {
  // width: 100%;
  width: 24rpx;
  height: 24rpx;
}
.xiangyou-icon {
  position: relative;
  top: -2rpx;
  width: 36rpx;
  height: 36rpx;
}
/* 渠道选择弹出层样式 - 图2 */
.channel-popup-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
}

.channel-popup-container {
  position: absolute;
  top: 1rpx;
  left: 0;
  z-index: 1000;
  width: 100%;
  background-color: #ffffff;
  border-bottom-right-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.channel-popup-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
}

.channel-popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
}

.channel-popup-close {
  position: absolute;
  top: 20rpx;
  right: 32rpx;
  width: 32rpx;
  height: 32rpx;
}

.channel-popup-content {
  padding: 20rpx 30rpx 40rpx;
}

.channel-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.channel-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 196rpx;
  height: 96rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}

.channel-item-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #000000;
}
/* 确保这些样式优先级高于图1的样式 */
.store-detail-page .channel-popup-overlay,
.store-detail-page .channel-popup-container {
  display: block !important;
}
/* 渠道选择触发按钮样式 */
.channel-trigger {
  padding: 16rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f2f2f4;
}

.channel-current-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #000000;
}
/* App 平台特定样式 */
/* #ifdef APP-PLUS */
.store-detail-page {
  width: 100% !important;
  // 防止App端出现滚动条
  overflow: hidden !important;

  // 修复可能的内容溢出问题
  .info-value {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
  }
  .store-name {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
  }
  .open-time-text {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
  }
}

// 修复App端可能的滚动问题
uni-page-body,
uni-page-wrapper {
  min-height: 100% !important;
  overflow: hidden !important;
}
/* #endif */
.margin-top {
  margin-top: 24rpx;
}
.toast-box {
  padding: 24rpx 20rpx 0 22rpx;
  .toast-title {
    font-size: 26rpx;
    font-weight: 400;
    color: #3d3d3d;
  }
  .toast-img-box {
    margin-top: 24rpx;
    overflow-y: scroll;
    .toast-img {
      width: 708rpx;
    }
  }
}
