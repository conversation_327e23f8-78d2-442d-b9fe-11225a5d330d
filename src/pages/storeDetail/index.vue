<route type="page" lang="json5">
{
  layout: 'tabbar',
  needLogin: true,
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>
<template>
  <view
    class="store-detail-page flex flex-col"
    :style="{ '--status-bar-height': statusBarHeight + 'px' }"
  >
    <!-- 系统导航栏（由uni-app自动渲染，标题在route配置）  -->
    <!-- 顶部门店信息组件 -->
    <sy-order-fixed-header
      :current-store="currentShopInfo"
      :is-store-expanded="isStoreInfoVisible"
      @store-select="goToSelectShop"
      class="sy-store-tab-header"
      @toggle-store-expand="toggleStoreInfo"
    />

    <!-- 原有的门店标题部分 -->
    <view class="flex items-center justify-between shop-box bg-white text-shoptext-shop-box">
      <text class="text-lg font-bold text-shoptext-shop">门店详情</text>
      <!-- 使用 SyAuth 组件控制设置按钮权限 -->
      <SyAuth
        code="jidanbao_menu_storeDetail_settingbtn"
        toast-mode="toast"
        v-slot="{ isAllowed, onNoPermissionClick }"
      >
        <image
          class="w-6 h-6 luoshi-icon"
          src="/static/images/img/luoshi.png"
          mode="aspectFit"
          :style="{
            filter: !isAllowed ? 'grayscale(100%)' : 'none',
            opacity: !isAllowed ? 0.7 : 1,
          }"
          @click="isAllowed ? goToGeneralSettings() : onNoPermissionClick()"
        />
      </SyAuth>
    </view>
    <!-- 门店导航栏 -->
    <SyStoreTabNav
      v-model="modelValue"
      v-model:secondModelValue="secondModelValue"
      :firstTab="channelOptions"
      :seconTab="seconTab"
      class="sy-store-tab-nav"
      :filterChannleList="filterChannleList"
    />
    <!-- <view class="frosted-glass"></view> -->
    <!-- 红色提示条 -->
    <!-- <StoreNoticeBar v-if="notice" :text="notice" /> -->
    <block v-if="filterChannleList.includes(selectedChannels[0])">
      <view class="toast-box">
        <view class="toast-title">
          {{
            selectedChannels[0] === 'JingDongTakeOutBrand'
              ? '京东渠道的菜品管理暂不支持查看和操作，请在京东APP操作'
              : '抖音小时达渠道的菜品管理暂不支持查看和操作，请在抖音小时达APP操作'
          }}
        </view>
        <view class="toast-img-box" :style="{ height: (mainContentHeight - 80) * 2 + 'rpx' }">
          <image
            class="toast-img"
            mode="widthFix"
            :src="
              selectedChannels[0] === 'JingDongTakeOutBrand'
                ? '/static/images/img/jingdong_caipin.png'
                : '/static/images/img/douyin_caipin.png'
            "
          ></image>
        </view>
      </view>
    </block>
    <block v-else>
      <!-- 评分与基础信息区 -->
      <view v-if="!isLoading">
        <view class="margin-top">
          <SyStoreRating
            :rating="storeInfo.avgScore || storeInfo.rating"
            :taste="storeInfo.avgTasteScore || storeInfo.taste"
            :packaging="storeInfo.avgPackingScore || storeInfo.packaging"
            :deliveryRate="storeInfo.deliveryRate"
            :hideRateing="hideRateing"
            @click-rule="onClickRule"
          />
        </view>

        <!-- 基本信息区域 - 合并后 -->
        <view class="store-info-block">
          <view class="info-item">
            <text class="info-label">出餐上报率</text>
            <view class="info-value flex items-center">
              <text>不支持</text>
              <view
                class="question-icon"
                v-if="
                  ['MeiTuanTakeOutBrand', 'ELeMeTakeOutBrand', 'JingDongTakeOutBrand'].includes(
                    selectedChannels[0],
                  )
                "
                @click="jumpGuidePage('shopGuide', 'chucanshangbao')"
              >
                <image
                  class="question-icon-logo"
                  src="/static/images/img/wenhao-shop.png"
                  mode="aspectFit"
                />
              </view>
            </view>
          </view>
          <view class="info-item" v-if="!['JingDongTakeOutBrand'].includes(selectedChannels[0])">
            <text class="info-label">经营品类</text>
            <text class="info-value">
              {{ storeInfo?.businessCategory || storeInfo?.category || '-' }}
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">店铺头像</text>
            <image
              class="store-logo"
              :src="storeInfo?.imageUrl || '/static/images/img/haidilao-icon.png'"
              mode="aspectFit"
            />
          </view>
          <view class="info-item">
            <text class="info-label">营业状态</text>
            <view class="status-tag" v-if="storeInfo?.businessStatus === 1">
              <view class="status-dot"></view>
              <text class="status-text">营业中</text>
            </view>
            <view class="status-tag-red" v-if="storeInfo?.businessStatus === 0">
              <view class="status-dot"></view>
              <text class="status-text">休息中</text>
            </view>
          </view>
          <view
            class="info-item"
            v-if="!['JingDongTakeOutBrand'].includes(selectedChannels[0])"
            @click="ShowMoreDetial(storeInfo?.promotionInfo)"
          >
            <text class="info-label">门店公告</text>
            <view class="info-value-esc">
              <rich-text
                class="info-value-no-width"
                :nodes="formatPromotionInfo(storeInfo?.promotionInfo)"
              ></rich-text>
            </view>
          </view>
          <view class="info-item">
            <text class="info-label">餐厅电话</text>
            <view class="info-value flex items-center">
              <!-- 电话号码处理 -->
              <view v-if="storeInfo?.phone?.length > 0" class="phones-container">
                <!-- 电话号码显示 -->
                <text
                  class="phone-number"
                  @click="
                    storeInfo?.phone?.length > 1
                      ? showPhoneList()
                      : callPhone(storeInfo?.phone?.[0])
                  "
                >
                  {{ storeInfo?.phone?.[0] || '-' }} 共{{ storeInfo?.phone.length }}个
                  <!-- {{ storeInfo?.phone?.length > 1 ? '' : '' }} -->
                </text>
              </view>
              <text v-else>-</text>
            </view>
          </view>
          <view class="info-item">
            <text class="info-label">提醒备餐时间</text>
            <view class="info-value flex items-center">
              <text>不支持</text>
              <view class="question-icon" @click="jumpGuidePage('shopGuide', 'tixingbeican')">
                <image
                  class="question-icon-logo"
                  src="/static/images/img/wenhao-shop.png"
                  mode="aspectFit"
                />
              </view>
            </view>
          </view>
          <view class="info-item" @click="ShowMoreDetial(storeInfo?.address)">
            <text class="info-label">餐厅地址</text>
            <text class="info-value-esc">
              {{ storeInfo?.address || '-' }}
            </text>
          </view>
          <view class="info-item" @click="showMoTime">
            <text class="info-label">营业时间</text>
            <view class="info-value flex items-center">
              <text class="open-time-text">
                {{ timeStr }}
              </text>
              <image class="item-icon" src="/static/images/img/arrow-icon-2.png" mode="aspectFit" />
            </view>
          </view>
          <!-- <view class="info-item">
        <text class="info-label">配送信息</text>
        <image
          class="item-icon"
          src="/static/images/img/arrow-icon-2.png"
          mode="aspectFit"
          @click="onClickDeliveryInfo"
        />
      </view> -->
        </view>
      </view>
      <view class="loading-box" v-else>
        <SyLoading
          :show="true"
          src="/static/images/img/goods-loading.gif"
          toastText="数据加载中..."
          :imgWidth="200"
          :imgHeight="200"
          :showMask="false"
          :fixed="false"
        />
      </view>
    </block>
    <!-- 评分规则弹窗 -->
    <sy-alert-popup
      v-model="ratingRulePopupVisible"
      title="提示"
      :content="isCalculationDetail ? calculationDetailContent : normalRuleContent"
      @confirm="ratingRulePopupVisible = false"
    />

    <!-- 配送信息弹窗 -->
    <sy-alert-popup
      v-model="deliveryInfoPopupVisible"
      title="提示"
      :content="deliveryInfoContent"
      @confirm="deliveryInfoPopupVisible = false"
    />
  </view>
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni'
import { ref, onMounted, onUnmounted } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { useShopStore } from '@/store/shop'
import SyStoreTabNav from '@/components/sy-store-tab-nav/sy-store-tab-nav.vue'
import SyStoreRating from '@/components/sy-store-rating/sy-store-rating.vue'
import SyLoading from '@/components/sy-loading/sy-loading.vue'
import SyAlertPopup from '@/components/sy-alert-popup/sy-alert-popup.vue'
import SyAuth from '@/components/sy-auth'
import { useStoreDetail } from './index'
const message = useMessage()
// 添加控制门店信息显示/隐藏的状态变量，默认为展开状态
const isStoreInfoVisible = ref(true)
const timeStr = computed(() => {
  let str = '-'
  console.log('storeInfo?.openTime', storeInfo.value?.openTime)
  if (storeInfo.value?.openTime && storeInfo.value?.openTime[0]) {
    str = storeInfo.value?.openTime?.[0]
  }
  if (storeInfo.value?.openTime && storeInfo.value?.openTime[1]) {
    str = str + ` ${storeInfo.value?.openTime?.[1]}`
  }
  if (storeInfo.value?.openTime?.length > 2) {
    str += '...'
  }
  // if(storeInfo?.openTime?.[0)
  return str
})
// 切换门店信息显示/隐藏状态
const toggleStoreInfo = (expanded?: boolean) => {
  if (expanded !== undefined) {
    isStoreInfoVisible.value = expanded
  } else {
    isStoreInfoVisible.value = !isStoreInfoVisible.value
  }
}
// 跳转操作指引
const jumpGuidePage = (type: string, status: string) => {
  if (type === 'shopGuide' && selectedChannels.value) {
    if (
      !['MeiTuanTakeOutBrand', 'ELeMeTakeOutBrand', 'JingDongTakeOutBrand'].includes(
        selectedChannels.value[0],
      ) &&
      status === 'chucanshangbao'
    ) {
      return
    }

    if (
      !['MeiTuanTakeOutBrand', 'ELeMeTakeOutBrand', 'JingDongTakeOutBrand'].includes(
        selectedChannels.value[0],
      ) &&
      status === 'tixingbeican'
    ) {
      return
    }
    uni.navigateTo({
      url: `/pages/operationGuide/index?type=${type}&channle=${selectedChannels.value[0]}&status=${status}`,
    })
  }
}
// 渠道选择弹出层控制
const isChannelPopupVisible = ref(false)
const filterChannleList = ref(['DouYinXiaoshiDa'])
// 当前选中的渠道
const currentChannel = ref('美团外卖')

// 渠道列表
const channelList = ref([
  { key: 'meituan', label: '美团外卖' },
  { key: 'eleme', label: '饿了么' },
  { key: 'jd', label: '京东秒送' },
  { key: 'miniapp', label: '自营小程序' },
  { key: 'douyin', label: '抖音随心团' },
])

// 系统状态栏高度
const statusBarHeight = ref(0)

// 打开渠道选择弹出层
const openChannelPopup = () => {
  isChannelPopupVisible.value = true
}

// 关闭渠道选择弹出层
const closeChannelPopup = () => {
  isChannelPopupVisible.value = false
}

// 选择渠道
const selectChannel = (channel) => {
  console.log('选择渠道:', channel)
  currentChannel.value = channel.label
  closeChannelPopup()
}
const ShowMoreDetial = (text?: string | any) => {
  console.log('text===>', text)

  // 处理公告信息的特殊逻辑
  if (text) {
    try {
      // 如果是字符串，尝试解析成JSON
      let parsedContent = text
      if (typeof text === 'string') {
        try {
          parsedContent = JSON.parse(text)
        } catch (e) {
          // 如果解析失败，直接显示原文本
          message.alert({ title: text })
          return
        }
      }
      console.log('parsedContent', parsedContent)

      // 如果是数组，判断数量
      if (Array.isArray(parsedContent)) {
        // 获取处理后的内容列表
        const contentItems = parsedContent
          .map((item) => item?.content || item)
          .filter((item) => item)

        // 如果数组长度大于1，或者第一个成员字符串长度大于15，展示所有成员的content
        const firstItemLength = typeof contentItems[0] === 'string' ? contentItems[0].length : 0

        if (parsedContent.length > 1 || firstItemLength > 14) {
          const contentList = contentItems.join('\n\n')
          message.alert({ title: contentList })
        } else if (parsedContent.length === 1) {
          // 如果只有一个元素且长度不超过15，不弹窗，直接在页面上显示
        } else {
          // 空数组情况
          message.alert({ title: '暂无内容' })
        }
      } else {
        // 非数组情况，直接显示
        message.alert({ title: text })
      }
    } catch (e) {
      console.error('处理公告信息失败:', e)
      message.alert({ title: String(text) })
    }
  } else {
    message.alert({ title: '暂无内容' })
  }
}
const showMoTime = () => {
  if (storeInfo.value?.openTime && storeInfo.value?.openTime?.length > 2) {
    message.alert({
      // msg: '提示文案',
      title: storeInfo.value?.openTime.join(','),
    })
  }
}
const {
  currentShopInfo,
  storeInfo,
  tabs,
  seconTab,
  notice,
  entries,
  channelOptions,
  selectedChannels,
  onShare,
  callPhone,
  openMap,
  onEntryClick,
  showOpenTimeDetail,
  showPhoneList,
  fetchStoreDetail,
  initStoreInfo,
  getChannelData,
  modelValue,
  secondModelValue,
  secondModelObj,
  goToSelectShop,
  hideRateing,
  isLoading,
  mainContentHeight,
} = useStoreDetail()

// 二级标签页选中值
const activeSecondTab = ref('a')

// 评分规则弹窗控制
const ratingRulePopupVisible = ref(false)
const isCalculationDetail = ref(false)

// 配送信息弹窗控制
const deliveryInfoPopupVisible = ref(false)
const deliveryInfoContent = ref(['评分规则请至平台APP查看', '路径：门店顾客评价-计算详情'])

// 评分规则弹窗内容
const normalRuleContent = ref([
  '暂无出餐上报率信息，如需查看请到美团外卖商家版查看',
  '操作路径: 店铺-经营数据-服务-出餐',
])
const calculationDetailContent = ref(['评分规则请至平台APP查看', '路径：门店顾客评价-计算详情'])

// 在 onShow 生命周期中初始化门店信息并获取门店详情
onShow(() => {
  // 先初始化门店信息
  initStoreInfo()
  // 然后获取门店详情
  fetchStoreDetail()
  getChannelData()

  // 获取系统状态栏高度
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight || 0
    },
  })
})

// 点击计算详情
const onClickRule = () => {
  isCalculationDetail.value = true
  ratingRulePopupVisible.value = true
}

// 点击配送信息
const onClickDeliveryInfo = () => {
  ratingRulePopupVisible.value = true
  isCalculationDetail.value = false
}

// 添加点击出餐上报率的处理函数
const onClickDeliveryRate = () => {
  ratingRulePopupVisible.value = true
  // 不使用计算详情模式
  isCalculationDetail.value = false
}

// 前往通用设置页面
const goToGeneralSettings = () => {
  uni.navigateTo({
    url: '/pages/general/index',
  })
}

// 格式化公告信息，将字符串数组转为富文本
const formatPromotionInfo = (info: any): string => {
  if (!info) return '-'
  console.log('info===>1', info, Array.isArray(info))
  try {
    // 如果是字符串，尝试解析成JSON
    if (typeof info === 'string') {
      try {
        const parsedInfo = JSON.parse(info)
        info = parsedInfo

        // 如果解析后是数组，并且第一个元素有content属性，直接返回该content
        if (Array.isArray(parsedInfo) && parsedInfo.length > 0 && parsedInfo[0]?.content) {
          return `<div style="max-width: 400rpx; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${parsedInfo[0].content}</div>`
        }
      } catch (e) {
        // 如果解析失败，保持原样
        return `<div style="max-width: 400rpx; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${info}</div>`
      }
    }

    // 如果是数组，检查是否包含content属性
    if (Array.isArray(info)) {
      // 如果数组第一个元素有content属性，优先使用
      if (info.length > 0 && info[0]?.content) {
        return `<div style="max-width: 400rpx; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${info[0].content}</div>`
      }

      // 否则按原逻辑处理
      return `<div style="max-width: 400rpx; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${info
        .map((item) => {
          if (typeof item === 'string') {
            return item.replace(/\\n/g, '<br>')
          }
          // 如果是对象但没有content属性，尝试转为字符串
          return item?.toString() || ''
        })
        .join('<br>')}</div>`
    }

    // 如果已经是HTML格式或其他类型，直接返回
    return `<div style="max-width: 400rpx; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${info}</div>`
  } catch (e) {
    console.error('格式化公告信息失败:', e)
    return String(info) // 确保返回字符串
  }
}

// 二级标签页变化处理
const onSecondTabChange = (value: string) => {
  activeSecondTab.value = value
  console.log('二级标签页切换:', value)
}

// 添加默认导出
defineOptions({
  name: 'StoreDetail',
})
</script>

<style lang="scss" src="./index.scss"></style>
