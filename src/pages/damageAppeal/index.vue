<route lang="json5">
{
  layout: 'default',
  needLogin: true,
  style: {
    navigationBarTitleText: '餐损申诉',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>
<template>
  <view class="appeal-page bg-#FFFFFF min-h-screen">
    <wd-navbar title="餐损申诉" left-text="" left-arrow @click-left="handleClickBack"></wd-navbar>

    <view class="appeal-body">
      <view class="form-container">
        <view class="form-item" @click="openPopup">
          <view class="item-label">
            <span style="color: red">*</span>
            申诉类型
          </view>
          <text class="item-value">{{ selectAppealType ? selectAppealType : '请选择' }}</text>
          <wd-icon name="chevron-right" size="22px" color="#999999"></wd-icon>
        </view>

        <view class="form-description">
          <view class="form-description-item">
            <view class="item-label">
              <span style="color: red">*</span>
              申诉描述
            </view>
            <wd-icon name="chevron-right" size="22px" color="#999999"></wd-icon>
          </view>

          <wd-textarea
            clear-trigger="focus"
            v-model="appealDescription"
            :maxlength="140"
            clearable
            show-word-limit
            placeholder="请填写申诉描述"
          />

          <view class="form-upload-pic">
            <text class="item-label">举证图片</text>
            <text class="upload-tip">请根据申诉原因上传相关举证图片</text>
            <view class="upload-container">
              <wd-upload
                :file-list="fileList"
                :limit="5"
                multiple
                image-mode="aspectFill"
                :action="VITE_UPLOAD_BASEURL"
                @change="handleChange"
              />
            </view>
          </view>
        </view>
      </view>

      <view class="appeal-footer">
        <text class="footer-tip">提交后，三方平台将在规定时间内审核完毕，请您耐心等待</text>
        <wd-button
          class="submit-button pb-safe"
          type="primary"
          size="large"
          block
          @click="doSubmitAppleal"
        >
          提交申诉
        </wd-button>
      </view>
    </view>

    <wd-popup
      v-model="showPopup"
      position="bottom"
      custom-style="height: 60vh;"
      @close="onPopupClose"
    >
      <view class="popup-body">
        <view class="popup-header">
          <wd-button type="text" @click="onCancel">取消</wd-button>
          <text>申诉类型</text>
          <wd-button type="text" @click="onConfirm">完成</wd-button>
        </view>
        <view class="popup-content">
          <wd-sidebar class="sidebar-left" v-model="currentParentItem" @change="handleAppealChange">
            <wd-sidebar-item
              v-for="(item, index) in appealTypeParent"
              :key="index"
              :value="index"
              :label="item.code"
            />
          </wd-sidebar>
          <scroll-view class="sub-content" :scroll-y="true" scroll-with-animation>
            <wd-cell-group title="" border>
              <view
                class="cell-view"
                v-for="(cell, index) in sunTypeValue"
                :key="index"
                @click="handleSubAppealType(index, cell)"
              >
                <text :class="currentSunIndex == index ? 'active-sun-text' : ''">{{ cell }}</text>
              </view>
            </wd-cell-group>
          </scroll-view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { getStatusBarHeight, getEnvBaseUploadUrl } from '@/utils/index'
import { useUserStore } from '@/store'
import { useDamageAppeal } from './index'

const VITE_UPLOAD_BASEURL = `${getEnvBaseUploadUrl()}`
const {
  selectAppealType,
  appealDescription,
  fileList,
  currentParentItem,
  appealTypeParent,
  sunTypeValue,
  showPopup,
  handleChange,
  handleAppealChange,
  handleSubAppealType,
  onConfirm,
  openPopup,
  onPopupClose,
  onCancel,
  getAppealTypeList,
  doSubmitAppleal,
  currentSunIndex,
  requestParams,
} = useDamageAppeal()
// 状态栏高度
const statusBarHeight = ref(0)

const handleClickBack = () => {
  uni.navigateBack()
}

onLoad((option) => {
  statusBarHeight.value = getStatusBarHeight()

  requestParams.value = JSON.parse(option.routeParams)
  console.log('餐损申诉页面 - onLoad:', requestParams)
})

onMounted(() => {
  toGetAppealTypeList()
})

const toGetAppealTypeList = () => {
  // ELeMeTakeOutBrand
  getAppealTypeList(requestParams.value.saleChannel).then((res) => {
    console.log('获取申诉类型返回：', res)
    if (res.resultCode === '0') {
      appealTypeParent.value = res.data

      // 默认子项为第一个
      sunTypeValue.value = appealTypeParent.value[0].nameList
    }
  })
}
</script>
<style lang="scss" scoped>
@import './index';
</style>
