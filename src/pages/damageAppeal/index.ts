import { ref, watch } from 'vue'
import { useUserStore, useShopStore } from '@/store'
import {
  queryDamageAppealTypeList,
  submitAppleal,
  AppealParams,
} from '@/service/damageAppeal/index'
import { getStartDate, getCurrentTime, getDaysAfter, addOneDay } from '../../utils/datetime'
import { isEmptyValue } from '@/utils'

export function useDamageAppeal() {
  // 选择的申诉类型
  const selectAppealType = ref('')
  // 申诉描述
  const appealDescription = ref('')

  const fileList = ref<any[]>([])

  const currentParentItem = ref(0)
  const appealTypeParent = ref([])
  const sunTypeValue = ref([])
  const showPopup = ref(false)

  // 选中的子项索引
  const currentSunIndex = ref<number | null>()

  // 接收到的数据
  // 定义申诉页面参数的接口
  interface AppealPageParams {
    extlOrderSerial: string
    saleChannel: string
    extlRefundSerial: string
    appealAmount: number
    shopId: string
    tenantId: string
    orderCode: string
    refundTime: string
    appealTime: string
  }

  // 创建响应式请求参数对象
  const requestParams = ref<AppealPageParams>({
    extlOrderSerial: '',
    saleChannel: '',
    extlRefundSerial: '',
    appealAmount: 0,
    shopId: '',
    tenantId: '',
    orderCode: '',
    refundTime: '',
    appealTime: '',
  })

  function handleChange({ fileList: files }) {
    fileList.value = files
    console.log('监听到图片上传：', fileList.value)
  }

  const handleAppealChange = (value) => {
    console.log('handleAppealChange:', value)

    sunTypeValue.value = appealTypeParent.value[value.value].nameList
  }

  const getAppealTypeList = async (channelCode: string) => {
    return await queryDamageAppealTypeList(channelCode)
  }

  const doSubmitAppleal = async () => {
    if (isEmptyValue(selectAppealType.value)) {
      uni.showToast({
        title: '请先选择申诉类型',
        icon: 'none',
      })
      return
    }
    if (isEmptyValue(appealDescription.value)) {
      uni.showToast({
        title: '请先填写申诉描述',
        icon: 'none',
      })
      return
    }

    const params: Partial<AppealParams> = {
      ...requestParams,
      appealDescribe: appealDescription.value,
      appealType: sunTypeValue.value[0],
      appealImages: '',
    }

    const res = await submitAppleal(params)
    console.log('提交申诉返回：', res)
    if (res.resultMsg === 'SUCCESS') {
      uni.navigateBack()
    }
  }

  // 选择申诉类型
  const handleSubAppealType = (index: number, appealType: string) => {
    selectAppealType.value = appealType
    currentSunIndex.value = index
  }
  // 完成操作
  const onConfirm = () => {
    // 这里可获取 selectedValue 做提交等逻辑，比如拿到选中的申诉类型
    onPopupClose()
    console.log('selectedValue:', selectAppealType.value)
  }

  // 打开弹框
  const openPopup = () => {
    showPopup.value = true
  }

  // 关闭弹框
  const onPopupClose = () => {
    showPopup.value = false
  }

  // 取消操作
  const onCancel = () => {
    onPopupClose()
  }
  return {
    selectAppealType,
    appealDescription,
    fileList,
    currentParentItem,
    appealTypeParent,
    sunTypeValue,
    showPopup,
    handleChange,
    handleAppealChange,
    handleSubAppealType,
    onConfirm,
    openPopup,
    onPopupClose,
    onCancel,
    getAppealTypeList,
    doSubmitAppleal,
    currentSunIndex,
    requestParams,
  }
}
