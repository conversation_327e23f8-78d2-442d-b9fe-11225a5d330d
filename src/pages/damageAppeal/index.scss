.appeal-page {
  padding-top: var(--status-bar-height);

  .appeal-body {
    background-color: #f5f6fa;
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;

    .form-container {
      padding: 24rpx 24rpx 0rpx 24rpx;

      .form-item {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        background-color: white;
        padding: 24rpx;
        border-radius: 16rpx;
        .item-value {
          flex: 1;
          margin-left: 40rpx;
          font-size: 24rpx;
          font-weight: normal;
          line-height: normal;
          letter-spacing: normal;
          color: #999999;
        }
      }

      .item-label {
        font-size: 24rpx;
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #222222;
      }
      :deep(.wd-textarea) {
        padding: 0rpx 16rpx 8rpx 16rpx;
      }
      :deep(.wd-textarea__value) {
        background-color: #f5f6fa;
        padding: 16rpx 14rpx;
        border-radius: 8rpx;
      }
      :deep(.wd-textarea__clear) {
        background-color: #f5f6fa;
      }
      :deep(.wd-textarea__count) {
        background-color: #f5f6fa;
        margin-right: 16rpx;
      }

      .form-description {
        background-color: white;
        margin-top: 16rpx;
        border-radius: 16rpx;

        .form-description-item {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          padding: 24rpx;
        }
      }
      .form-upload-pic {
        margin: 24rpx 16rpx;
        display: flex;
        flex-direction: column;

        .upload-tip {
          font-size: 22rpx;
          font-weight: normal;
          line-height: normal;
          letter-spacing: normal;
          color: #999999;
          margin-top: 4rpx;
        }
        .upload-container {
          margin-top: 16rpx;
        }
      }
    }
  }

  .popup-body {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
  }

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 24rpx;
    background: #ffffff;
    box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.1);
  }

  .popup-content {
    display: flex;
    justify-content: space-evenly;
    margin-top: 3rpx;

    .sidebar-left {
      width: 35%;
      height: calc(60vh - 104rpx);
    }
    .sub-content {
      background-color: white;
      height: calc(60vh - 104rpx);

      .cell-view {
        padding: 36rpx;
        font-size: 24rpx;
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #222222;
      }
      .active-sun-text {
        color: #f33429;
      }
    }
    :deep(.wd-sidebar-item) {
      font-size: 24rpx;
      font-weight: 500;
      line-height: normal;
      letter-spacing: normal;
      background-color: #f5f6fa;
    }
    :deep(.wd-sidebar-item--active) {
      background-color: white;
    }
  }
}

.appeal-footer {
  background-color: white;
  padding: 20rpx;
  bottom: 0rpx;
  left: 0rpx;
  right: 0rpx;
  position: fixed;
  display: flex;
  flex-direction: column;
  align-self: center;

  .footer-tip {
    font-size: 22rpx;
    font-weight: normal;
    line-height: normal;
    text-align: center;
    letter-spacing: normal;
    color: #999999;
  }

  .submit-button {
    width: 100%;
    margin-top: 24rpx;
  }
}
