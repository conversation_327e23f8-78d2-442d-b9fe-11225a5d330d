<route lang="json5">
{
  layout: 'default',
  needLogin: true,
  style: {
    navigationBarTitleText: '申诉规则',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>
<template>
  <view class="appeal-page bg-#FFFFFF min-h-screen">
    <wd-navbar title="申诉规则" left-text="" left-arrow @click-left="handleClickBack"></wd-navbar>

    <view class="appeal-body">
      <view class="appeal-tip-item">
        <text class="appeal-tip-title">可申诉标准说明：</text>
        <text class="appeal-tip-content">
          商家可以在48小时内发起申诉。如果不满足上诉条件，则不可发起申诉，如有疑问，可联系平台客服进行咨询，
        </text>
      </view>
      <wd-divider style="height: 1rpx" color="#F6F6F6"></wd-divider>
      <view class="appeal-tip-item">
        <text class="appeal-tip-title">申诉流程说明:</text>
        <text class="appeal-tip-content">
          商家发起申诉后，将由平台工作人员进行人工核实，如申诉通过，原因为平台或者骑手，平台将进行餐损赔付。
        </text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const handleClickBack = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
@import './index';
</style>
