<route lang="json5">
{
  style: {
    navigationBarTitleText: '操作指引',

    'app-plus': {},
  },
}
</route>

<template>
  <view class="operation-guide-page">
    <!-- 顶部标签页 -->
    <view class="tab-container">
      <view class="tab-wrapper">
        <view
          v-for="(tab, index) in tabs"
          :key="index"
          class="tab-item"
          :class="{ active: activeTabIndex === index }"
          @click="changeChannle(index)"
        >
          <text class="tab-text">{{ tab }}</text>
          <view v-if="activeTabIndex === index" class="tab-indicator"></view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 统一的内容区域 -->
      <view class="guide-content">
        <!-- 左侧一级 Tab -->
        <view class="left-tab-container">
          <view
            v-for="(item, i) in getLeftTabs()"
            :key="i"
            class="left-tab-item"
            :class="{ 'left-tab-active': activeLeftTab === i }"
            @click="handleLeftTabClick(i)"
          >
            <text class="left-tab-text">{{ item }}</text>
          </view>
        </view>

        <!-- 右侧内容区域 -->
        <view class="right-content">
          <!-- 操作类型横向滚动标签 -->
          <scroll-view
            scroll-x
            class="operation-tabs-scroll"
            :scroll-left="scrollLeft"
            :scroll-with-animation="true"
            :id="scrollViewId"
          >
            <view class="operation-tabs">
              <view
                v-for="(item, index) in getOperationTabs()"
                :key="index"
                class="operation-tab-item"
                :class="{ 'operation-tab-active': activeOperationTab === index }"
                @click="handleOperationTabClick(index)"
                :id="`tab-${index}`"
              >
                <text class="operation-tab-text">{{ item }}</text>
              </view>
            </view>
          </scroll-view>

          <!-- 指引内容详情 -->
          <view class="guide-detail">
            <view class="guide-tip">
              <view class="tip-text">
                {{ getCurrentTipText() }}
              </view>
            </view>

            <!-- 图片示例区域 -->
            <view
              class="guide-image-placeholder"
              v-if="currentPlatform !== '京东秒送' && currentShowImg"
            >
              <image :src="currentShowImg"></image>
            </view>
            <block v-else>
              <view
                class="guide-image-placeholder-esc"
                v-if="currentShowImg && Array.isArray(currentShowImg)"
              >
                <image :src="currentShowImg[0]"></image>
                <image :src="currentShowImg[1]" class="margin-top-10"></image>
              </view>
              <view class="guide-image-placeholder" v-else>
                <image :src="currentShowImg"></image>
              </view>
            </block>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
// 顶部标签页
const tabs = ref(['美团外卖', '饿了么', '京东秒送', '抖音小时达'])
const activeTabIndex = ref(0)

// 左侧一级标签页索引
const activeLeftTab = ref(0)

// 当前操作标签页索引
const activeOperationTab = ref(0)

// 滚动相关状态
const scrollLeft = ref(0)
const scrollViewId = 'operationTabsScroll'

// 获取当前平台名称
const currentPlatform = computed(() => {
  return tabs.value[activeTabIndex.value]
})

// 处理左侧标签点击事件
const handleLeftTabClick = (index: number) => {
  activeLeftTab.value = index
  // 重置操作标签为第一个
  activeOperationTab.value = 0
  // 重置滚动位置
  scrollLeft.value = 0
}

// 处理操作标签点击事件
const handleOperationTabClick = (index: number) => {
  activeOperationTab.value = index
  scrollToActiveTab()
}

// 滚动到当前激活的标签
const scrollToActiveTab = () => {
  // 使用简单的估算方法计算滚动位置
  // 假设每个标签宽度大约为100rpx，将选中标签滚动到中间位置
  const tabWidth = 150 // 估计每个标签的平均宽度(rpx)
  const screenWidth = uni.getSystemInfoSync().windowWidth
  const tabPosition = activeOperationTab.value * tabWidth
  const centerPosition = tabPosition - screenWidth / 2 + tabWidth / 2

  // 确保不会出现负值滚动
  scrollLeft.value = Math.max(0, centerPosition)
}

// 解析路由参数并跳转到指定的标签页
onLoad((options) => {
  console.log('收到路由参数:', options)

  // 解析渠道参数并设置顶部Tab
  if (options.channle) {
    // 根据渠道代码设置顶部标签页索引
    if (options.channle === 'MeiTuanTakeOutBrand') {
      activeTabIndex.value = 0 // 美团外卖
    } else if (options.channle === 'ELeMeTakeOutBrand') {
      activeTabIndex.value = 1 // 饿了么
    } else if (options.channle === 'JingDongTakeOutBrand') {
      activeTabIndex.value = 2 // 京东秒送
    } else if (options.channle === 'douyinxiaoshida') {
      activeTabIndex.value = 3 // 抖音小时达
    }
  }

  // 解析类型参数并设置左侧标签页
  if (options.type) {
    // 这里可能需要根据实际类型参数进行映射
    if (options.type === 'orderGuid') {
      activeLeftTab.value = 0 // 订单指引
    }
    if (options.type === 'shopGuide') {
      if (options.channle === 'MeiTuanTakeOutBrand') {
        activeLeftTab.value = 1 // 美团外卖
      } else if (options.channle === 'ELeMeTakeOutBrand') {
        activeLeftTab.value = 1 // 饿了么
      } else if (options.channle === 'JingDongTakeOutBrand') {
        activeLeftTab.value = 1 // 京东秒送
      }
    }
    if (options.type === 'commontGuide') {
      if (options.channle === 'MeiTuanTakeOutBrand') {
        activeLeftTab.value = 2 // 美团外卖
      } else if (options.channle === 'ELeMeTakeOutBrand') {
        activeLeftTab.value = 2 // 饿了么
      } else if (options.channle === 'JingDongTakeOutBrand') {
        activeLeftTab.value = 2 // 京东秒送
      }
    }
    // 如有需要，可添加其他类型映射
    if (options.type === 'afterSale') {
      if (options.channle === 'douyinxiaoshida') {
        activeLeftTab.value = 0 // 美团外卖
      }
    }
  }

  // 解析状态参数并设置操作标签页
  if (options.status) {
    setTimeout(() => {
      // 为了确保操作标签已加载完成，使用 setTimeout 延迟执行
      const operationTabs = getOperationTabs()
      let operationIndex = -1

      // 根据status参数查找对应的操作标签索引
      switch (options.status) {
        case 'cancelDelivery':
          operationIndex = operationTabs.findIndex((tab) => tab === '取消配送')
          break
        case 'addConsumption':
          operationIndex = operationTabs.findIndex((tab) => tab === '加小费')
          break
        case 'changeRider':
          operationIndex = operationTabs.findIndex((tab) => tab === '换骑手/催取餐')
          break
        case 'urgePickup':
          operationIndex = operationTabs.findIndex((tab) => tab === '换骑手/催取餐')
          break
        case 'reissue':
          operationIndex = operationTabs.findIndex((tab) => tab === '补发')
          break
        case 'tellRider':
          operationIndex = operationTabs.findIndex((tab) => tab === '投诉骑手')
          break
        case 'logo':
          operationIndex = operationTabs.findIndex((tab) => tab === '限制接单骑手')
          break
        case 'chucanshangbao':
          operationIndex = operationTabs.findIndex((tab) => tab === '出餐上报率')
          break
        case 'tixingbeican':
          operationIndex = operationTabs.findIndex((tab) => tab === '提醒备餐时间')
          break
        case 'gukepingjiashensu':
          operationIndex = operationTabs.findIndex((tab) => tab === '顾客评价申诉')
          break
        case 'zhudongtuikuan':
          operationIndex = operationTabs.findIndex((tab) => tab === '主动退款')
          break
        default:
          operationIndex = 0
      }

      // 如果找到对应的操作标签，则设置索引
      if (operationIndex !== -1) {
        activeOperationTab.value = operationIndex
        // 滚动到激活的标签
        setTimeout(() => {
          scrollToActiveTab()
        }, 300) // 稍微延迟一点，确保UI已渲染
      }
    }, 100)
  }
})

const currentShowImg = computed(() => {
  let imgUrl: any = ''
  const platform = currentPlatform.value
  const leftTab = getLeftTabs()[activeLeftTab.value]

  // 仅处理餐损指引的图片
  if (leftTab === '订单指引') {
    // 美团外卖
    if (platform === '美团外卖') {
      if (activeOperationTab.value === 0) {
        imgUrl = '/static/images/img/meituan_quxiao.png'
      } else if (activeOperationTab.value === 1) {
        imgUrl = '/static/images/img/meituan_huanqishou.png'
      } else if (activeOperationTab.value === 2) {
        imgUrl = '/static/images/img/meituan_xiaofei.png'
      } else if (activeOperationTab.value === 3) {
        imgUrl = '/static/images/img/meituan_bufa.png'
      } else if (activeOperationTab.value === 4) {
        imgUrl = '/static/images/img/meituan_kacan.png'
      }
    }
    // 饿了么
    else if (platform === '饿了么') {
      if (activeOperationTab.value === 0) {
        imgUrl = '/static/images/img/elm_xiaofei.png'
      } else if (activeOperationTab.value === 1) {
        imgUrl = '/static/images/img/elm_kacan.png'
      }
    }
    // 京东秒送
    else if (platform === '京东秒送') {
      if (activeOperationTab.value === 1) {
        imgUrl = [
          '/static/images/img/jingdong_tousu1.png',
          '/static/images/img/jingdong_tousu2.png',
        ]
      } else if (activeOperationTab.value === 0) {
        imgUrl = [
          '/static/images/img/jingdong_xianzhi1.png',
          '/static/images/img/jingdong_xianzhi2.png',
        ]
      }
    }
  } else if (leftTab === '售后指引') {
    // 美团外卖
    if (platform === '抖音小时达') {
      if (activeOperationTab.value === 0) {
        imgUrl = '/static/images/img/meituan_chucanshangbao.png'
      } else if (activeOperationTab.value === 1) {
        imgUrl = '/static/images/img/douyinxiaoshida_shouhou.jpg'
      }
    }
  } else if (leftTab === '门店指引') {
    if (platform === '美团外卖') {
      if (activeOperationTab.value === 0) {
        imgUrl = '/static/images/img/meituan_chucanshangbao.png'
      } else if (activeOperationTab.value === 1) {
        imgUrl = '/static/images/img/meituan_beicanshijian.png'
      }
    }
    // 饿了么
    else if (platform === '饿了么') {
      if (activeOperationTab.value === 0) {
        imgUrl = '/static/images/img/elm_chucanshangbao.png'
      } else if (activeOperationTab.value === 1) {
        imgUrl = '/static/images/img/elm_beicanshijian.png'
      }
    }
    // 京东秒送
    else if (platform === '京东秒送') {
      if (activeOperationTab.value === 0) {
        imgUrl = ['/static/images/img/jingdong_chucanshangbao.png']
      } else if (activeOperationTab.value === 1) {
        imgUrl = ['/static/images/img/jingdong_beicanshijian.png']
      }
    }
  } else if (leftTab === '评价指引') {
    if (platform === '美团外卖') {
      if (activeOperationTab.value === 0) {
        imgUrl = '/static/images/img/meituan_pingjiashensu.png'
      }
    }
    if (platform === '饿了么') {
      if (activeOperationTab.value === 0) {
        imgUrl = '/static/images/img/elm_pingjiashensu.png'
      }
    }
    console.log('activeOperationTab.value', activeOperationTab.value, platform)

    if (platform === '京东秒送') {
      if (activeOperationTab.value === 0) {
        imgUrl = '/static/images/img/jingdong_pingjiashensu.png'
      }
    }
  }
  // 可以在这里为其他左侧标签添加图片
  // 例如：售后指引、门店指引、评价指引的图片

  return imgUrl
})

const changeChannle = (index: number) => {
  activeTabIndex.value = index
  activeLeftTab.value = 0
  scrollLeft.value = 0 // 重置滚动位置
  setTimeout(() => {
    activeOperationTab.value = 0
  }, 0)
  console.log('index===>', index, getLeftTabs())
}
// 根据平台获取左侧标签
const getLeftTabs = () => {
  if (activeTabIndex.value === 2) {
    // 京东秒送
    return ['订单指引', '门店指引', '评价指引']
  } else if (activeTabIndex.value === 1) {
    // 美团外卖、饿了么
    return ['订单指引', '门店指引', '评价指引']
  } else if (activeTabIndex.value === 0) {
    // 美团外卖、饿了么
    return ['订单指引', '门店指引', '评价指引']
  } else if (activeTabIndex.value === 3) {
    // 美团外卖、饿了么
    return ['售后指引']
  }
}

// 获取操作标签列表（根据当前平台和类型动态生成）
const getOperationTabs = () => {
  const platform = currentPlatform.value
  const leftTab = getLeftTabs()[activeLeftTab.value]

  // 根据左侧标签和平台返回对应的操作标签
  if (leftTab === '订单指引') {
    if (platform === '美团外卖') {
      return ['取消配送', '换骑手/催取餐', '加小费', '补发', '卡餐上报']
    } else if (platform === '饿了么') {
      return ['加小费', '卡餐上报']
    } else if (platform === '京东秒送') {
      return ['限制接单骑手', '投诉骑手']
    }
  } else if (leftTab === '门店指引') {
    if (platform === '美团外卖') {
      return ['出餐上报率', '提醒备餐时间']
    } else if (platform === '饿了么') {
      return ['出餐上报率', '提醒备餐时间']
    } else if (platform === '京东秒送') {
      return ['出餐上报率', '提醒备餐时间']
    }
  } else if (leftTab === '评价指引') {
    console.log('111111111')

    if (platform === '美团外卖') {
      return ['顾客评价申诉']
    } else if (platform === '饿了么') {
      return ['顾客评价申诉']
    } else if (platform === '京东秒送') {
      return ['顾客评价申诉']
    }
  } else if (leftTab === '售后指引') {
    if (platform === '抖音小时达') {
      return ['主动退款', '售后详情']
    }
  }
  console.log('leftTab===>', leftTab, platform)

  // 默认返回空数组
  return []
}

// 获取当前选中的操作提示文本
const getCurrentTipText = () => {
  const platform = currentPlatform.value
  const operationTabs = getOperationTabs()
  const currentTab = operationTabs[activeOperationTab.value]
  const leftTab = getLeftTabs()[activeLeftTab.value]

  // 餐损指引
  if (leftTab === '订单指引') {
    if (platform === '美团外卖') {
      if (currentTab === '取消配送') {
        return '骑手还未接单的场景，需要操作【取消配送】，可以在美团外卖商家APP操作'
      } else if (currentTab === '换骑手/催取餐') {
        return '骑手已接单还未到店取餐的场景，需要操作【换骑手/催取餐】，可以在美团外卖商家APP操作'
      } else if (currentTab === '加小费') {
        return '无骑手接单需要加小费的场景，需要操作【加小费】，可以在美团外卖商家APP操作'
      } else if (currentTab === '补发') {
        return '骑手配送中需要补发商品的场景，需要操作【补发】，可以在美团外卖商家APP操作'
      } else if (currentTab === '卡餐上报') {
        return '门店订单爆满无法即时上报出餐，需要操作【卡餐上报】，可以在美团外卖商家APP操作'
      }
    } else if (platform === '饿了么' && currentTab === '卡餐上报') {
      return '门店订单爆满无法即时上报出餐，需要操作【卡餐上报】，可以在饿了么商家版APP操作'
    } else if (platform === '京东秒送') {
      if (currentTab === '限制接单骑手') {
        // 限制接单骑手
        return '骑手已接单不取餐的场景，需要操作【限制接单骑手】，可以在京东APP操作'
      } else if (currentTab === '投诉骑手') {
        // 投诉骑手
        return '骑手服务不满意的场景，需要操作【投诉骑手】，可以在京东APP操作'
      }
    }
  }
  // 售后指引
  else if (leftTab === '售后指引') {
    if (platform === '抖音小时达') {
      if (currentTab === '主动退款') {
        return '需要操作【退款】，可以在抖店APP操作'
      } else if (currentTab === '售后详情') {
        return `需要查看【售后详情】，可以在抖店APP查看`
      }
    }
  }
  // 门店指引
  else if (leftTab === '门店指引') {
    if (currentTab === '门店设置') {
      return `需要设置门店基本信息的场景，可以在${platform}商家APP操作【门店设置】`
    } else if (currentTab === '营业时间') {
      return `需要设置门店营业时间的场景，可以在${platform}商家APP操作【营业时间】`
    } else if (currentTab === '配送范围') {
      return `需要设置门店配送范围的场景，可以在${platform}商家APP操作【配送范围】`
    }
  }
  // 评价指引
  else if (leftTab === '评价指引') {
    if (currentTab === '评价回复') {
      return `需要回复用户评价的场景，可以在${platform}商家APP操作【评价回复】`
    } else if (currentTab === '差评处理') {
      return `需要处理用户差评的场景，可以在${platform}商家APP操作【差评处理】`
    }
  }

  // 默认文案格式
  return currentTab ? `需要操作【${currentTab}】的场景，可以在${platform}商家APP操作` : ''
}
</script>

<style lang="scss" src="./index.scss" scoped></style>
