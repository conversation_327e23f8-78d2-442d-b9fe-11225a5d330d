.operation-guide-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-top: env(safe-area-inset-top); /* 预留顶部安全区域 */
  background-color: #f6f6f6;
}

.tab-container {
  position: sticky;
  top: 0;
  z-index: 10;
  width: 100%;
  background-color: #ffffff;
  // border-bottom: 1rpx solid #eeeeee;
}

.tab-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 88rpx;
  padding: 0 20rpx;
}

.tab-item {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.tab-text {
  font-size: 26rpx;
  color: #666666;
  transition: color 0.3s;
}

.active .tab-text {
  font-size: 26rx;
  font-weight: 600;
  // color: #e60112;
  color: #1e1e1e;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  width: 38rpx;
  height: 6rpx;
  background-color: #e60112;
  border-radius: 4rpx;
}

.content-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  background-color: #ffffff;
}

.guide-content {
  display: flex;
  flex: 1;
  height: calc(100vh - 88rpx - env(safe-area-inset-top));
}

.left-tab-container {
  width: 160rpx;
  min-width: 160rpx;
  // height: 100%;
  height: calc(100vh - 88rpx - env(safe-area-inset-top));
  padding-top: 0;
  overflow-y: auto;
  background: #f6f6f6;
}

.left-tab-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 120rpx;
  font-size: 24rpx;
}

.left-tab-text {
  padding: 0 10rpx;
  font-size: 24rpx;
  color: #999999;
  text-align: center;
  // letter-spacing: 4rpx;
}

.left-tab-active {
  background-color: #ffffff;

  // &::before {
  //   position: absolute;
  //   top: 50%;
  //   left: 0;
  //   width: 8rpx;
  //   height: 36rpx;
  //   content: '';
  //   background-color: #e60112;
  //   border-radius: 0 4rpx 4rpx 0;
  //   transform: translateY(-50%);
  // }

  .left-tab-text {
    font-weight: 600;
    color: #e60112;
  }
}

.right-content {
  flex: 1;
  // width: 544rpx;
  height: 100%;
  padding: 0;
  overflow: hidden;
  background-color: #ffffff;
}

.guide-detail {
  width: 90%;
  padding: 0 20rpx;
}

.guide-tip {
  // margin-bottom: 10rpx;
  width: 544rpx;
  // padding: 20rpx 0rpx;
  background-color: #ffffff;
}

.tip-text {
  padding-right: 24rpx;
  font-size: 26rpx;
  // line-height: 40rpx;
  color: rgba(61, 61, 61, 1);
}

.guide-image-placeholder {
  display: flex;

  align-items: center;
  justify-content: center;
  width: 544rpx;
  // min-height: 500rpx;
  height: 1068rpx;
  // padding: 20rpx;
  margin: 20rpx 0;
  text-align: center;
  background-color: #ffffff;
  // border: 1rpx solid #eeeeee;
  border-radius: 16rpx;
  image {
    width: 100%;
    height: 100%;
    border-radius: 16rpx;
  }
}
.guide-image-placeholder-esc {
  height: 1068rpx;
  overflow-y: scroll;
  image {
    width: 544rpx;
    // min-height: 500rpx;
    height: 1068rpx;

    border-radius: 16rpx;
  }
  .margin-top-10 {
    margin-top: 16rpx;
  }
}
.instruction-image {
  width: 100%;
  max-width: 272px;
  height: auto;
  border-radius: 8rpx;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999999;
  text-align: center;
}

// 操作类型横向滚动标签
.operation-tabs-scroll {
  position: sticky;
  top: 0;
  z-index: 5;
  width: 100%;
  height: 80rpx;
  padding: 0;
  margin-top: 28rpx;
  margin-left: 24rpx;
  white-space: nowrap;
  background-color: #ffffff;
  // border-bottom: 1px solid #eeeeee;
}

.operation-tabs {
  display: inline-flex;
  height: 100%;
  // padding: 0 20rpx;
}

.operation-tab-item {
  // line-height: 42rpx;
  display: flex;
  // display: flex;
  align-items: center;
  height: 42rpx;
  // position: relative;
  // display: inline-flex;
  // align-items: center;
  // justify-content: center;
  // height: 80rpx;
  // padding: 0 20rpx;
  // margin-right: 30rpx;
  padding: 0rpx 16rpx;
  margin-right: 32rpx;
  color: #666666;
  background: #f3f3f3;
  border-radius: 200rpx;
}

.operation-tab-text {
  font-size: 24rpx;
  color: #666666;
  white-space: nowrap;
}

.operation-tab-active {
  background: rgba(243, 52, 41, 0.1);
  .operation-tab-text {
    font-weight: 400;
    color: rgba(243, 52, 41, 1);
  }
}
