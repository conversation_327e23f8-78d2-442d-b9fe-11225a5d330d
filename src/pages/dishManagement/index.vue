<route type="page" lang="json5">
{
  layout: 'tabbar',
  needLogin: true,
  style: {
    navigationBarTitleText: '菜品管理',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>

<template>
  <view class="dish-management-page" :style="{ '--status-bar-height': statusBarHeight + 'px' }">
    <!-- 系统导航栏（由uni-app自动渲染，标题在route配置）  -->
    <!-- 顶部门店信息组件 -->
    <!-- <sy-order-fixed-header
      :current-store="currentShopInfo"
      :is-store-expanded="isStoreInfoVisible"
      @store-select="goToSelectShop"
      @toggle-store-expand="toggleStoreInfo"
      class="sy-order-fixed-header"
    /> -->
    <view class="flex items-center justify-between shop-box bg-white text-shoptext-shop-box">
      <text class="text-lg font-bold text-shoptext-shop">菜品管理</text>
      <view class="flex items-center">
        <view class="search-icon-wrapper" @click="openSearch">
          <view class="iconfont icon-sousuo2"></view>
        </view>
        <!-- <image class="w-6 h-6 luoshi-icon" src="/static/images/img/luoshi.png" mode="aspectFit" /> -->
      </view>
    </view>

    <!-- 菜品导航栏 -->
    <SyStoreTabNav
      v-model="modelValue"
      v-model:secondModelValue="secondModelValue"
      :firstTab="channelOptions"
      :seconTab="seconTab"
      class="sy-store-tab-nav"
      :filterChannleList="filterChannleList"
    />

    <block v-if="selectedChannels && filterChannleList.includes(selectedChannels[0])">
      <view class="toast-box">
        <view class="toast-title">
          {{
            selectedChannels[0] === 'JingDongTakeOutBrand'
              ? '京东渠道的菜品管理暂不支持查看和操作，请在京东APP操作'
              : '抖音小时达渠道的菜品管理暂不支持查看和操作，请在抖音小时达APP操作'
          }}
        </view>
        <view class="toast-img-box" :style="{ height: mainContentHeight * 2 + 'rpx' }">
          <image
            class="toast-img"
            mode="widthFix"
            :src="
              selectedChannels[0] === 'JingDongTakeOutBrand'
                ? '/static/images/img/jingdong_caipin.png'
                : '/static/images/img/douyin_caipin.png'
            "
          ></image>
        </view>
      </view>
    </block>
    <bolck v-else>
      <!-- 标签栏 -->
      <view class="tab-container">
        <view
          v-for="tab in statusTabs"
          :key="tab.label"
          class="tab-item"
          :class="{ active: currentStatusTab === tab.label }"
          @click="changeStatusTab(tab.label)"
        >
          <text class="tab-text">{{ tab.label }}</text>
          <text class="tab-count">{{ tab.count }}</text>
        </view>
        <view class="tip-box">
          <wd-icon name="help-circle" size="32rpx" color="red" @tap.stop="toggleTooltip"></wd-icon>
          <view class="tooltip" v-show="showTooltip">
            <view class="tooltip-content">
              <view class="tooltip-item">商品数量按去重统计</view>
            </view>
            <view class="tooltip-arrow"></view>
            <view class="tooltip-close" @tap="toggleTooltip"></view>
          </view>
        </view>
      </view>

      <!-- 主内容区域 -->
      <view class="main-content" :style="{ height: mainContentHeight * 2 + 'rpx' }">
        <!-- 全屏加载状态 -->
        <SyLoading
          v-if="pageLoading"
          :show="true"
          src="/static/images/img/goods-loading.gif"
          toastText="数据加载中..."
          :imgWidth="200"
          :imgHeight="200"
          :showMask="false"
          :fixed="false"
        />

        <!-- 内容区域 - 不再使用v-if条件 -->
        <view class="content-container" :style="{ visibility: pageLoading ? 'hidden' : 'visible' }">
          <!-- 左侧分类菜单 -->
          <view class="category-menu" :style="{ height: leftCategoryHeight * 2 + 'rpx' }">
            <view v-if="categoryLoading" class="category-loading">
              <text>加载中...</text>
            </view>
            <template v-else>
              <view v-for="category in categories" :key="category.id">
                <view
                  v-if="filterGoodsNums(category.originalData) > 0"
                  class="category-item"
                  :class="{ active: currentCategory === category.id.toString() }"
                  @click="changeCategory(category.id)"
                >
                  <text>{{ category.name }}</text>
                  <!-- <text v-if="category.dishCount" class="category-count">({{ category.dishCount }})</text> -->
                </view>
              </view>
            </template>
          </view>

          <!-- 右侧菜品列表 -->
          <scroll-view
            class="dish-list hide-scrollbar"
            scroll-y
            ref="dishListRef"
            @scroll="handleDishListScroll"
            enhanced
            :scroll-with-animation="true"
            :scroll-into-view="scrollIntoViewId"
            :style="{ height: rightDishListHeight * 2 + 'rpx' }"
          >
            <view v-if="loading" class="dish-loading">
              <text>加载中...</text>
            </view>
            <!-- 无数据提示 -->
            <view
              v-else-if="!categoriesWithDishes || categoriesWithDishes.length === 0"
              class="no-data"
            >
              <text>暂无菜品数据</text>
            </view>
            <template v-else>
              <!-- 按分类分组显示菜品 -->
              <view
                v-for="category in categoriesWithDishes"
                :key="category.id"
                :id="`category-${category.id}`"
                class="category-section"
              >
                <!-- 只有当该分类有商品时才显示 -->
                <template v-if="category.dishes && category.dishes.length > 0">
                  <!-- 分类标题 -->
                  <view class="category-title">
                    {{ category.name }}
                  </view>

                  <!-- 该分类下的菜品列表 -->
                  <view
                    v-for="dish in category.dishes"
                    :key="dish.id"
                    class="dish-item"
                    :class="{ available: isAvailableDish(dish.status, dish) }"
                  >
                    <view class="dish-image-container">
                      <block v-if="dish.image && dish.image.length > 0">
                        <image
                          class="dish-image"
                          :src="dish.image"
                          mode="aspectFill"
                          @error="handleImageError"
                        />
                      </block>
                      <block v-else>
                        <image
                          class="dish-image"
                          src="/static/images/img/no-goods.png"
                          mode="aspectFill"
                          @error="handleImageError"
                        />
                      </block>
                      <view class="image-overlay" v-if="!isAvailableDish(dish.status, dish)"></view>
                    </view>
                    <view class="dish-info">
                      <view class="dish-name">{{ dish.name }}</view>
                      <!-- <view class="tag-container">
                      <view class="tag" v-for="(tag, index) in dish.tags" :key="index">
                        {{ getTagName(tag) }}
                      </view>
                    </view> -->
                      <view class="bottom-box">
                        <view class="price">
                          <text class="price-value">¥{{ dish.price }}</text>
                          <text class="price-unit">起</text>
                        </view>
                        <view class="status">
                          {{ getDishStatusText(dish) }}
                        </view>
                      </view>
                    </view>
                  </view>
                </template>
              </view>
            </template>
          </scroll-view>
        </view>
      </view>
    </bolck>

    <!-- 搜索组件 - 固定定位，覆盖整个屏幕 -->
    <view v-if="showSearch" class="search-overlay">
      <view class="search-container">
        <!-- 顶部导航栏 -->
        <view class="search-header">
          <!-- <view class="status-bar"></view> -->
          <view class="nav-bar">
            <view class="back-icon" @click="closeSearch">
              <view class="back-arrow"></view>
            </view>
            <view class="search-wrapper flex items-center">
              <wd-search
                v-model="searchKeyword"
                placeholder="搜索商品名称"
                cancel-button
                input-align="left"
                placeholder-left
                clearable
                focus
                cancel-txt="搜索"
                hide-cancel
                hide-cancel-onblur
                use-suffix-slot
                @change="performSearch"
                :style="{ flex: 1 }"
              ></wd-search>
              <view class="search-button" @click="performSearch()">搜索</view>
            </view>
          </view>
        </view>

        <!-- 搜索结果区域 -->
        <scroll-view class="search-results" scroll-y>
          <!-- 搜索结果内容 -->
          <view v-if="searchResults.length > 0" class="search-result-list">
            <!-- 搜索结果商品卡片 -->
            <view v-for="item in searchResults" :key="item.id" class="search-item">
              <view class="search-item-content">
                <view class="search-item-image">
                  <!-- 商品图片 -->
                  <image
                    v-if="item.image"
                    :src="item.image"
                    mode="aspectFill"
                    @error="handleImageError"
                  />
                  <image
                    v-else
                    class="dish-image"
                    :src="'https://saas-sy-res.dtyunxi.com/cube/home/<USER>'"
                    mode="aspectFill"
                    @error="handleImageError"
                  />
                  <!-- 非可售状态时显示遮罩 -->
                  <view class="image-overlay" v-if="getDishStatusText(item) !== ''"></view>
                </view>
                <view class="search-item-info">
                  <!-- 根据商品状态设置不同颜色 -->
                  <text
                    class="search-item-title"
                    :style="{ color: getDishStatusText(item) === '' ? '#222222' : '#999999' }"
                  >
                    {{ item.name }}
                  </text>
                  <view class="search-item-bottom">
                    <view class="search-item-price">
                      <text
                        class="price-value"
                        :style="{ color: getDishStatusText(item) === '' ? '#3D3D3D' : '#999999' }"
                      >
                        ¥{{ item.price }}
                      </text>
                      <text
                        class="price-unit"
                        :style="{ color: getDishStatusText(item) === '' ? '#666666' : '#999999' }"
                      >
                        起
                      </text>
                    </view>
                    <text
                      class="search-item-status"
                      :style="{ color: getDishStatusText(item) === '' ? '#222222' : '#999999' }"
                    >
                      {{ getDishStatusText(item) }}
                    </text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 底部提示 -->
            <view class="search-no-more">
              <text class="no-more-text">没有更多了</text>
            </view>
          </view>

          <!-- 无搜索结果显示 -->
          <view class="search-empty" v-else>
            <view class="empty-icon-container">
              <view class="empty-icon-box">
                <!-- 预留空状态图标位置 -->
                <image
                  class="w-6 h-6 luoshi-icon"
                  src="/static/images/img/white-order.png"
                  mode="aspectFit"
                />
              </view>
            </view>
            <text class="empty-text">暂无对应商品</text>
          </view>
        </scroll-view>

        <!-- 底部Home指示器 -->
        <view class="home-indicator">
          <view class="indicator-bar"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import SyOrderFixedHeader from '@/components/sy-order-fixed-header'
import SyStoreTabNav from '@/components/sy-store-tab-nav/sy-store-tab-nav.vue'
import SyLoading from '@/components/sy-loading/sy-loading.vue'
import { useDishManagement } from './index'

// 添加控制门店信息显示/隐藏的状态变量，默认为展开状态
const isStoreInfoVisible = ref(true)
const filterChannleList = ref(['DouYinXiaoshiDa', 'JingDongTakeOutBrand'])
// 切换门店信息显示/隐藏状态
const toggleStoreInfo = (expanded?: boolean) => {
  if (expanded !== undefined) {
    isStoreInfoVisible.value = expanded
  } else {
    isStoreInfoVisible.value = !isStoreInfoVisible.value
  }
}

// 判断菜品是否可售（非售罄/下架状态）
const isAvailableDish = (status: string | number, dish?: any): boolean => {
  // 如果提供了dish对象，优先使用原始数据判断
  if (dish && dish.originalData) {
    const originalData = dish.originalData
    // 只有status=1（上架）且soldOut=0（非售罄）的菜品才是可售状态
    return originalData.status === 1 && originalData.soldOut === 0
  }

  // 如果传入的是字符串状态
  if (typeof status === 'string') {
    return status === '已上架'
  }

  // 如果传入的是数字状态
  // status为1表示已上架，为0表示已下架
  return status === 1
}

// 判断当前筛选条件下的tab有多少商品
const filterGoodsNums = (goodsObj?: any): number => {
  let list:
    | {
        status?: number
        soldOut?: number
      }[]
    | null = null
  list = goodsObj.itemList || null
  if (currentStatusTab.value === '全部') {
    return list.length
  } else if (currentStatusTab.value === '已上架') {
    return list.filter((item) => item.status === 1).length
  } else if (currentStatusTab.value === '已下架') {
    return list.filter((item) => item.status === 0).length
  } else if (currentStatusTab.value === '已售罄') {
    return list.filter((item) => item.soldOut === 1).length
  }
}

// 根据status和soldOut获取状态文本
const getStatusText = (status: number, soldOut: number): string => {
  if (soldOut === 1) return '已售罄'
  if (status === 0) return '已下架'
  if (status === 1) return '已上架'
  return '未知状态'
}

// 处理图片加载错误
const handleImageError = (e: any) => {
  // 图片加载失败时使用默认图片
  e.target.src = '/static/images/img/default-dish.png'
}

// 根据分类ID获取分类名称
const getCategoryName = (categoryId: string) => {
  const category = categories.value.find((c) => c.id.toString() === categoryId)
  return category ? category.name : '未分类'
}

// 添加防抖函数
const debounce = <T extends (...args: any[]) => any>(fn: T, delay: number) => {
  let timer: number | null = null
  return function (this: any, ...args: Parameters<T>) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay) as unknown as number
  }
}

// 添加搜索相关变量和方法
const showSearch = ref(false)
const searchKeyword = ref('')
const searchResults = ref<any[]>([])

// 打开搜索页面
const openSearch = () => {
  showSearch.value = true
}

// 关闭搜索页面
const closeSearch = () => {
  showSearch.value = false
  searchKeyword.value = ''
  searchResults.value = []
}

// 实际执行搜索的函数
const doSearch = () => {
  if (!searchKeyword.value.trim()) {
    searchResults.value = []
    return
  }

  // 使用dishList作为数据源进行模糊搜索
  const keyword = searchKeyword.value.toLowerCase().trim()

  // 从dishList中筛选匹配的商品
  const filteredResults = dishList.value
    .filter((item) => item.name && item.name.toLowerCase().includes(keyword))
    .map((item) => ({
      id: item.id,
      name: item.name,
      image: item.image || '',
      price: item.price || 0,
      status: item.status || '',
      tags: item.tags || [],
    }))

  // 添加去重逻辑，基于id和name同时一样的条件
  const uniqueMap = new Map()
  searchResults.value = filteredResults.filter((item) => {
    const key = `${item.id}_${item.name}`
    if (!uniqueMap.has(key)) {
      uniqueMap.set(key, true)
      return true
    }
    return false
  })
}

// 使用防抖包装搜索函数
const performSearch = debounce(doSearch, 300)

// 格式化标签
const getTagName = (tag: any) => {
  // 根据实际数据结构来格式化标签
  return typeof tag === 'string' ? tag : tag.name || ''
}

// 根据当前标签和商品原始数据返回合适的状态文本
const getDishStatusText = (dish: any) => {
  if (!dish || !dish.originalData) {
    if (typeof dish.status !== 'string') {
      return getStatusText(dish.status, 0)
    } else {
      return dish.status === '已上架' ? '' : dish.status
    }
  }

  const originalData = dish.originalData

  // 根据当前标签决定显示的状态文本
  if (currentStatusTab.value === '已下架' && originalData.status === 0) {
    return '已下架'
  } else if (currentStatusTab.value === '已售罄' && originalData.soldOut === 1) {
    return '已售罄'
  } else if (originalData.soldOut === 1) {
    // 全部标签下，售罄优先
    return '已售罄'
  } else if (originalData.status === 0) {
    return '已下架'
  } else if (originalData.status === 1) {
    return ''
  }

  // 默认返回dish中的status文本
  return dish.status
}

// 获取页面逻辑
const {
  currentShopInfo,
  channelOptions,
  seconTab,
  modelValue,
  secondModelValue,
  statusBarHeight,
  currentStatusTab,
  statusTabs,
  currentCategory,
  categories,
  dishList,
  loading,
  categoryLoading,
  changeStatusTab,
  changeCategory,
  goToSelectShop,
  initData,
  dishListRef,
  handleDishListScroll,
  groupedDishes,
  pageLoading,
  mainContentHeight,
  leftCategoryHeight,
  rightDishListHeight,
  scrollIntoViewId,
  categoriesWithDishes,
  updateCategoriesWithDishes,
  selectedChannels,
} = useDishManagement()

// 添加tooltip相关变量和方法
const showTooltip = ref(false)
const toggleTooltip = (e) => {
  console.log('弹出气泡提示111')

  // e.stopPropagation()
  showTooltip.value = !showTooltip.value
}

// 添加点击页面关闭提示框的功能
</script>

<style lang="scss" src="./index.scss"></style>
