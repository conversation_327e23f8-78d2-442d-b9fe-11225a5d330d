/* 菜品管理页面样式 */
.dish-management-page {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  max-height: 100%;
  padding-top: var(--status-bar-height);
  overflow: hidden; /* 防止页面内容溢出 */
  background-color: #fff;
  /* 应用全局box-sizing以确保尺寸计算正确 */
  &,
  & * {
    box-sizing: border-box;
  }

  .shop-box {
    padding: 20rpx 32rpx 20rpx 24rpx;
    border-bottom: 2rpx solid #f5f5f5;

    .search-icon-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;

      .iconfont {
        font-size: 40rpx;
        color: #222222;
      }
    }
  }

  .main-content {
    position: relative;
    display: flex;
    flex: none; /* 改为none，使用显式高度 */
    padding: 0;
    margin: 0;
    overflow: hidden;
    /* 高度通过内联样式动态设置，使用rpx单位 */
  }
  /* 内容容器样式 */
  .content-container {
    display: flex;
    width: 100%;
    height: 100%;
  }
  /* 全屏加载样式 */
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);

    .loading-image {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 20rpx;
    }

    .loading-text {
      font-size: 28rpx;
      color: #666;
    }
  }

  .tab-container {
    // border-bottom: 2rpx solid #f5f5f5;
    position: relative;
    display: flex;
    padding: 20rpx 32rpx;
    margin: 0;
    background: #fff;
    .tip-box {
      position: absolute;
      right: 22rpx;
      .tooltip-close {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9999;
        width: 100vw;
        height: 100vh;
        opacity: 0;
      }
      // Tooltip styles
      .tooltip {
        position: absolute;
        top: 50rpx;
        right: -4rpx;
        z-index: 100;
        width: 240rpx;
        padding: 20rpx;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 8rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);

        .tooltip-content {
          .tooltip-item {
            margin-bottom: 10rpx;
            font-size: 22rpx;
            color: white;

            &:last-child {
              margin-bottom: 0;
            }

            .tooltip-title {
              font-size: 24rpx;
              font-weight: 600;
              color: white;
            }

            .tooltip-text {
              font-size: 24rpx;
              color: #666;
            }
          }
        }

        .tooltip-arrow {
          position: absolute;
          top: -10rpx;
          right: 10rpx;
          width: 0;
          height: 0;
          border-right: 10rpx solid transparent;
          border-bottom: 10rpx solid rgba(0, 0, 0, 0.5);
          border-left: 10rpx solid transparent;
        }
      }
    }
    .tab-item {
      display: flex;
      align-items: center;
      margin-right: 40rpx;

      .tab-text {
        font-size: 28rpx;
        color: #666;
      }

      .tab-count {
        // padding: 2rpx 12rpx;
        margin-left: 8rpx;
        font-size: 22rpx;
        color: #999;
        // background: #f5f5f5;
        // border-radius: 20rpx;
      }

      &.active {
        position: relative;
        .tab-text {
          position: relative;
          font-size: 30rpx;
          font-weight: 600;
          color: #222222;
        }
        .tab-count {
          color: #222222;
          // background: #ff4d4f;
        }

        .tab-text::after {
          position: absolute;
          bottom: -20rpx;
          left: 50%;
          width: 38rpx;
          height: 4rpx;
          content: '';
          background-color: #e60112;
          transform: translateX(-50%);
        }
      }
    }
  }

  .category-menu {
    flex-shrink: 0;
    width: 160rpx;
    padding-bottom: 40rpx;
    margin: 0;
    overflow-y: auto;
    background-color: #f6f6f6;
    /* 高度通过内联样式动态设置，使用rpx单位 */

    .category-item {
      // height: 94rpx;

      // display: flex;
      // align-items: center;
      // justify-content: center;
      // line-height: 94rpx;
      padding: 30rpx 16rpx;
      font-size: 24rpx;
      color: #3d3d3d;
      text-align: center;

      &.active {
        font-weight: 500;
        color: #3d3d3d;
        background-color: #ffffff;
      }

      &:not(.active) {
        color: #999999;
        background-color: #f6f6f6;
      }

      .category-count {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .dish-list {
    flex: 1;
    padding: 16rpx 32rpx 64rpx 32rpx;
    margin: 0;
    overflow-y: auto;
    background-color: #fff;
    /* IE和Edge */
    -ms-overflow-style: none;
    /* Firefox */
    scrollbar-width: none;
    /* 高度通过内联样式动态设置，使用rpx单位 */
    /* 隐藏滚动条 - 各平台兼容性处理 */
    &::-webkit-scrollbar {
      display: none;
      width: 0 !important;
      height: 0 !important;
      -webkit-appearance: none;
      background: transparent;
    }

    .category-title {
      position: relative;
      display: inline-block;
      padding-top: 8rpx;
      padding-bottom: 6rpx;
      font-size: 24rpx;
      color: #3d3d3d;

      // position: sticky;
      // top: 0;
      // z-index: 1;
      // padding: 20rpx 0 10rpx;
      // margin-bottom: 10rpx;
      // font-size: 32rpx;
      // font-weight: 500;
      // color: #333;
      // background-color: #fff;
    }
    .category-title::after {
      position: absolute;
      bottom: 2rpx;
      left: 0;
      width: 100%;
      content: '';
      // height: 1rpx;
      border-bottom: 1rpx solid #3d3d3d;
      // background: #3d3d3d;
    }
    .dish-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      margin-bottom: 16rpx;

      .dish-image-container {
        position: relative;
        width: 120rpx;
        height: 120rpx;
        margin-right: 16rpx;
        overflow: hidden;
        border-radius: 8rpx;

        .dish-image {
          width: 100%;
          height: 100%;
        }

        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(255, 255, 255, 0.5);
        }
      }

      .dish-info {
        position: relative;
        flex: 1;
        height: 120rpx;

        .dish-name {
          margin-bottom: 6rpx;
          font-size: 28rpx;
          font-weight: 600;
          color: #999999; /* 售罄/下架状态下文字为浅灰色 */
        }

        .tag-container {
          display: flex;
          // margin-bottom: 12rpx;
          height: 20rpx;
          margin-bottom: 16rpx;
          .tag {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 28rpx;
            padding: 4rpx 8rpx;
            margin-right: 8rpx;
            font-size: 20rpx;
            color: #999999; /* 售罄/下架状态下标签文字为浅灰色 */
            background-color: #ffffff;
            border: 1rpx solid #999999; /* 售罄/下架状态下标签边框为浅灰色 */
            border-radius: 4rpx;
          }
        }

        .price {
          display: flex;
          align-items: flex-end;

          .price-value {
            font-size: 30rpx;
            font-weight: 600;
            color: #999999; /* 售罄/下架状态下价格为浅灰色 */
          }

          .price-unit {
            margin-left: 4rpx;
            font-size: 24rpx;
            color: #999999; /* 售罄/下架状态下单位为浅灰色 */
          }
        }
      }
      /* 可售状态样式 */
      &.available {
        .dish-image-container {
          .image-overlay {
            display: none; /* 可售状态不显示遮罩层 */
          }
        }

        .dish-info {
          display: flex;
          flex-direction: column;
          flex-wrap: wrap;
          justify-content: space-between;
          view {
            width: 100%;
          }
          .dish-name {
            color: #222222; /* 可售状态下文字为黑色 */
          }

          .tag-container {
            .tag {
              color: #f33429; /* 可售状态下标签文字为红色 */
              border-color: #f33429; /* 可售状态下标签边框为红色 */
            }
          }

          .price {
            .price-value {
              color: #3d3d3d; /* 可售状态下价格为深灰色 */
            }

            .price-unit {
              color: #666666; /* 可售状态下单位为中灰色 */
            }
          }
        }
      }
    }
  }

  .category-loading,
  .dish-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200rpx;
    font-size: 28rpx;
    color: #999;
  }

  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300rpx;
    font-size: 28rpx;
    color: #999;
  }

  // 全屏加载状态
  .full-page-loading {
    position: absolute;
    top: -300rpx;
    left: 0;
    z-index: 100;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #fff;

    .loading-icon {
      position: relative;
      width: 40rpx;
      height: 40rpx;
      margin-bottom: 20rpx;

      &::before {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        content: '';
        background-color: #ff4d4f;
        border-radius: 50%;
        opacity: 0.2;
      }

      &::after {
        position: absolute;
        top: 10rpx;
        left: 10rpx;
        width: 20rpx;
        height: 20rpx;
        content: '';
        background-color: #ff4d4f;
        border-radius: 50%;
        animation: bounce 1.5s infinite ease-in-out;
      }
    }

    .loading-text {
      font-size: 28rpx;
      color: #666;
    }
  }

  @keyframes bounce {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.2);
    }
  }
  .loading-img {
    width: 200rpx;
    height: 200rpx;
  }
}
/* 搜索组件样式 */
.search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 201;
  width: 100%;
  max-width: 100%;
  height: 100vh;
  padding-top: var(--status-bar-height);
  overflow: hidden; /* 防止整体溢出 */
  background-color: #f1f1f1;
  /* 应用全局box-sizing以确保尺寸计算正确 */
  &,
  & * {
    box-sizing: border-box;
  }

  .search-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 100%;
    height: 100%;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .search-header {
      background-color: #ffffff;

      // .status-bar {
      //   width: 100%;
      //   height: 44rpx;
      // }

      .nav-bar {
        display: flex;
        align-items: center;
        height: 88rpx;
        padding: 0 20rpx;

        .back-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 60rpx;
          height: 60rpx;

          .back-arrow {
            position: relative;
            width: 24rpx;
            height: 48rpx;

            &::before {
              position: absolute;
              top: 50%;
              left: 50%;
              width: 18rpx;
              height: 18rpx;
              content: '';
              border-bottom: 3rpx solid rgba(0, 0, 0, 0.9);
              border-left: 3rpx solid rgba(0, 0, 0, 0.9);
              transform: translate(-25%, -50%) rotate(45deg);
            }
          }
        }

        // 搜索组件包装器
        .search-wrapper {
          flex: 1;
          // padding: 0 16rpx;

          // 自定义wd-search组件样式
          :deep(.wd-search) {
            --search-background: #f7f7f7;
            --search-inner-padding: 0 24rpx;
            --search-input-height: 68rpx;
            --search-input-border-radius: 34rpx;
            --search-cancel-padding: 0 8rpx 0 16rpx;
            --search-placeholder-left: 8rpx;
            --search-input-font-size: 28rpx;
            padding: 0rpx 45rpx;
          }
        }

        .search-input-container {
          flex: 1;
          width: 412rpx;
          height: 60rpx;
          margin: 0 20rpx;

          .search-input-box {
            display: flex;
            align-items: center;
            width: 372rpx; /* 宽度412rpx减去左右padding 40rpx */
            height: 100%;
            padding: 0 20rpx;
            margin-left: 78rpx;
            background-color: #eeeeee;
            border-radius: 30rpx;

            .search-indicator {
              width: 4rpx;
              height: 32rpx;
              margin-right: 12rpx;
              background-color: #f33429;
              border-radius: 2rpx;
            }

            .search-input {
              flex: 1;
              width: 320rpx; /* 减去搜索指示器宽度和margin */
              height: 100%;
              font-size: 26rpx;
              color: #222222;
            }
          }
        }

        .search-button {
          padding: 0 10rpx;
          font-size: 26rpx;
          color: #222222;
        }
      }
    }

    .search-results {
      flex: 1;
      width: 100%;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
      background-color: rgba(241, 241, 241, 1);

      .search-empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding-top: 244rpx;

        .empty-icon-container {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 240rpx;
          height: 240rpx;
          margin-bottom: 40rpx;

          .empty-icon-box {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 196rpx;
            height: 194rpx;
            // border: 2rpx dashed #d6d6d6;
            border-radius: 8rpx;
            image {
              width: 100%;
              height: 100%;
            }
          }
        }

        .empty-text {
          font-size: 24rpx;
          color: #8f959e; /* 中性色/字体/yx_yxColor_neutral_3 */
          text-align: center;
        }
      }

      .search-result-list {
        padding: 0;
        margin-top: 14rpx;
        background-color: #f1f1f1;

        .search-item {
          // margin-bottom: 2rpx;
          background-color: #ffffff;

          .search-item-content {
            position: relative;
            box-sizing: content-box;
            display: flex;
            height: 120rpx; /* 调整高度适应图片大小 */
            padding: 26rpx 32rpx 30rpx 32rpx;

            .search-item-image {
              position: relative;
              width: 120rpx;
              height: 120rpx;
              margin-right: 24rpx;
              overflow: hidden;
              background-color: #d8d8d8;
              border-radius: 8rpx;
              image {
                width: 120rpx;
                height: 120rpx;
              }
              /* 移除默认的遮罩，使用条件遮罩代替 */
              /* &::after {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                content: '';
                background-color: rgba(255, 255, 255, 0.5);
                border-radius: 8rpx;
              } */
              /* 图片遮罩层样式 */
              .image-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(255, 255, 255, 0.5);
                border-radius: 8rpx;
              }
            }

            .search-item-info {
              display: flex;
              flex: 1;
              flex-direction: column;
              justify-content: space-between;
              height: 100%;

              .search-item-title {
                margin-bottom: 0;
                font-size: 30rpx;
                font-weight: 500;
                line-height: 42rpx;
                color: #999999;
              }

              .search-item-bottom {
                display: flex;
                align-items: center;
                justify-content: space-between;
                // padding-bottom: 4rpx;
                margin-top: auto;

                .search-item-price {
                  display: flex;
                  align-items: baseline;

                  .price-value {
                    font-size: 30rpx;
                    font-weight: 500;
                    color: #999999;
                  }

                  .price-unit {
                    margin-left: 4rpx;
                    font-size: 24rpx;
                    color: #666666;
                  }
                }

                .search-item-status {
                  font-size: 24rpx;
                  font-weight: 500;
                  color: #999999;
                }
              }
            }
          }
        }

        .search-no-more {
          display: flex;
          justify-content: center;
          padding: 64rpx 0 64rpx 0;

          .no-more-text {
            font-size: 24rpx;
            color: #8f959e; /* 中性色/字体/yx_yxColor_neutral_3 */
            text-align: center;
          }
        }
      }

      .home-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 68rpx;
        background-color: #ffffff;

        .indicator-bar {
          width: 268rpx;
          height: 10rpx;
          background-color: #000000;
          border-radius: 5rpx;
        }
      }
    }
  }
}
/* 消除iOS安全区域中重复的底部间距 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) or
  (padding-bottom: env(safe-area-inset-bottom)) {
  .search-overlay .search-container {
    height: calc(100% - constant(safe-area-inset-bottom));
    height: calc(100% - env(safe-area-inset-bottom));
  }
}
/* 搜索输入框的占位符样式 */
.search-placeholder {
  font-size: 26rpx;
  color: #999999;
}
.empty-img-box-img {
  width: 196rpx;
  height: 194rpx;
}
.text-shoptext-shop-box {
  position: relative;
  z-index: 100;
  margin-bottom: 20rpx;
}
.bottom-box {
  position: relative;
  display: flex;
  align-items: self-start;
  justify-content: space-between;
  .status {
    // position: absolute;
    // right: 0;
    // bottom: 16rpx;
    position: relative;
    // top: 4rpx;
    font-size: 24rpx;
    color: #999999;
    text-align: right;
  }
}
.toast-box {
  padding: 24rpx 20rpx 0 22rpx;
  .toast-title {
    font-size: 26rpx;
    font-weight: 400;
    color: #3d3d3d;
  }
  .toast-img-box {
    margin-top: 24rpx;
    overflow-y: scroll;
    .toast-img {
      width: 708rpx;
    }
  }
}
