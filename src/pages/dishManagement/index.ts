import { ref, computed, watch, reactive, nextTick, onMounted, onActivated } from 'vue'
import { useShopStore } from '@/store/shop'
import type { ShopItem } from '@/service/shop/types'
import type { ShopDto } from '@/types/shop'
import type { StoreTab } from '@/types/store'
import { fetchChannelEnum } from '@/service/order/dict'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { useSystemInfo } from '@/hooks/useSystemInfo'
import { fetchDishList, fetchDishCategories } from '@/service/dish'
import type {
  DishCategory,
  DishStatus,
  QueryDishListParams,
  DishItem as DishItemType,
} from '@/service/dish/types'

// 定义状态标签类型
export type TabStatus = '全部' | '已上架' | '已下架' | '已售罄'

// 定义菜品分类类型
export interface CategoryItem {
  id: string | number
  name: string
}

// 定义菜品项类型
export interface DishItem {
  id: string | number
  name: string
  image: string
  tags: string[]
  price: number
  status: TabStatus
}

// 扩展StoreTab类型，添加可能存在的字段
interface ExtendedStoreTab extends StoreTab {
  id?: string
  tenantId?: string
  code?: string
}

/**
 * 菜品管理页面逻辑
 */
export function useDishManagement() {
  // 系统信息
  const { statusBarHeight, screenHeight } = useSystemInfo()

  // 当前店铺信息
  const currentShopInfo = ref<any>(null)

  // 渠道选项
  const channelOptions = ref<StoreTab[]>([])
  const selectedChannels = ref<string[]>([])

  // 二级标签
  const seconTab = ref<ExtendedStoreTab[]>([])

  // 一级标签和二级标签的选中值
  const modelValue = ref('')
  const secondModelValue = ref('')
  // 存储当前选中的二级选项的完整数据对象
  const secondModelObj = ref<ExtendedStoreTab | null>(null)

  // 顶部状态标签
  const currentStatusTab = ref<TabStatus>('全部')
  const statusTabs = ref<{ label: TabStatus; count: number }[]>([
    { label: '全部', count: 0 },
    { label: '已上架', count: 0 },
    { label: '已下架', count: 0 },
    { label: '已售罄', count: 0 },
  ])

  // 左侧菜品分类
  const currentCategory = ref('')
  const categories = ref<DishCategory[]>([])

  // 菜品列表
  const dishList = ref<DishItemType[]>([])

  // 存储接口返回的完整菜品数据
  const allDishData = ref<DishItemType[]>([])

  // 加载状态
  const loading = ref(false)
  const categoryLoading = ref(false)
  // 添加页面全局加载状态
  const pageLoading = ref(false)

  // 是否通过点击左侧分类触发的滚动
  const isScrollingByClick = ref(false)

  // 菜品列表滚动区域引用
  const dishListRef = ref<any>(null)

  // 按分类对菜品进行分组 - 使用缓存机制优化性能
  const dishListCache = ref<DishItemType[]>([])
  const groupedDishesCache = ref<Record<string, DishItemType[]>>({})
  const orderedCategoryIdsCache = ref<string[]>([])
  // 存储分类和菜品关联的数组
  const categoriesWithDishes = ref<Array<{ id: string; name: string; dishes: DishItemType[] }>>([])

  const groupedDishes = computed(() => {
    // 检查dishList是否变化，如果没有变化则返回缓存结果
    if (
      dishList.value === dishListCache.value &&
      Object.keys(groupedDishesCache.value).length > 0
    ) {
      return {
        dishes: groupedDishesCache.value,
        orderedIds: orderedCategoryIdsCache.value,
      }
    }

    // 有变化时重新计算
    const grouped: Record<string, DishItemType[]> = {}

    // 首先创建一个有序的分类ID数组，确保与左侧分类列表顺序一致
    const orderedCategoryIds = categories.value.map((category) => category.id.toString())

    // 对已筛选的菜品按分类分组
    dishList.value.forEach((dish) => {
      if (dish.categoryId) {
        const categoryId = dish.categoryId.toString()
        if (!grouped[categoryId]) {
          grouped[categoryId] = []
        }
        grouped[categoryId].push(dish)
      }
    })

    // 更新缓存
    dishListCache.value = dishList.value
    groupedDishesCache.value = grouped
    orderedCategoryIdsCache.value = orderedCategoryIds

    // 返回包含分组数据和有序ID的对象
    return {
      dishes: grouped,
      orderedIds: orderedCategoryIds,
    }
  })

  // 更新分类和菜品关联数据的函数
  const updateCategoriesWithDishes = () => {
    if (!categories.value || !groupedDishes.value || !groupedDishes.value.dishes) {
      categoriesWithDishes.value = []
      return
    }
    console.log('categories', JSON.parse(JSON.stringify(categories.value)))

    const result = categories.value
      .map((category) => {
        const categoryId = category.id.toString()
        const dishes = groupedDishes.value.dishes[categoryId] || []

        return {
          ...category,
          id: categoryId,
          dishes,
        }
      })
      .filter((category) => category.dishes.length > 0)
    console.log('categoriesWithDishes.value', categoriesWithDishes.value, result)

    categoriesWithDishes.value = result
  }

  // 监听 modelValue 变化
  watch(modelValue, (newValue) => {
    if (newValue) {
      // 在这里可以根据选中的渠道执行相应的操作
      selectedChannels.value = [newValue]
      // 可以在这里调用获取门店详情的方法，根据选中的渠道获取数据
      if (['DouYinXiaoshiDa', 'JingDongTakeOutBrand'].includes(newValue)) {
        return
      }
      fetchDishDataBySelectedChannel()
    }
  })

  // 监听 secondModelValue 变化，同步更新 secondModelObj
  watch(secondModelValue, (newValue) => {
    if (newValue) {
      // 查找对应的完整数据对象并更新 secondModelObj
      const selectedItem = seconTab.value.find((item) => item.key === newValue)
      secondModelObj.value = selectedItem || null
      if (['DouYinXiaoshiDa', 'JingDongTakeOutBrand'].includes(selectedChannels.value[0])) {
        return
      }
      fetchDishDataBySelectedChannel()
    }
  })

  // 监听 currentStatusTab 变化
  watch(currentStatusTab, (newValue) => {
    if (newValue) {
      // 基于存储的数据进行前端筛选
      filterDishList()
    }
  })

  // 监听dishList变化，更新categoriesWithDishes
  watch(
    dishList,
    () => {
      updateCategoriesWithDishes()
    },
    { deep: true },
  )

  // 根据选中的渠道获取商品数据
  const fetchDishDataBySelectedChannel = async () => {
    try {
      // 显示全屏加载状态
      pageLoading.value = true
      statusTabs.value = [
        {
          label: '全部',
          count: 0,
        },
        { label: '已上架', count: 0 },
        { label: '已下架', count: 0 },
        { label: '已售罄', count: 0 },
      ]

      const shopStore = useShopStore()
      if (!shopStore.hasSelectedShop) {
        console.error('未选择门店')
        return
      }

      if (!secondModelObj.value || !secondModelObj.value.id || !secondModelObj.value.tenantId) {
        console.error('未选择店铺或店铺信息不完整')
        return
      }

      const params = {
        shopId: secondModelObj.value.id,
        tenantId: secondModelObj.value.tenantId,
        channelCode: modelValue.value, // 使用选中的渠道
      }

      // 判断参数是否为空，如果任一字段为空或空字符串，则不执行后续代码
      if (
        params.shopId === undefined ||
        params.shopId === null ||
        params.shopId === '' ||
        params.tenantId === undefined ||
        params.tenantId === null ||
        params.tenantId === '' ||
        params.channelCode === undefined ||
        params.channelCode === null ||
        params.channelCode === ''
      ) {
        console.error('参数不完整，无法获取商品数据', params)
        return
      }

      await fetchAllDishData()
    } catch (error) {
      console.error('根据渠道获取商品数据失败:', error)
    } finally {
      // 隐藏全屏加载状态
      setTimeout(() => {
        pageLoading.value = false
      }, 500) // 添加短暂延迟，确保视觉上的流畅性
    }
  }

  // 获取所有商品数据
  const fetchAllDishData = async () => {
    try {
      loading.value = true
      categoryLoading.value = true

      if (!secondModelObj.value || !secondModelObj.value.id || !secondModelObj.value.tenantId) {
        console.log('secondModelObj数据不完整，无法获取菜品数据')
        return
      }

      const params: QueryDishListParams = {
        shopId: secondModelObj.value.id,
        tenantId: secondModelObj.value.tenantId,
        channelCode: modelValue.value,
        shopCode: secondModelObj.value.code,
        // 不添加分类和状态筛选，获取所有数据
      }

      const { data, resultCode } = await fetchDishList(params)

      if ('' + resultCode === '0' && data) {
        // 处理接口返回的数据
        if (data.categoryList && data.categoryList.length > 0) {
          // 处理左侧分类列表
          categories.value = data.categoryList.map((category, index) => {
            return {
              id: index.toString(), // 直接使用index作为id，确保唯一性
              name: category.categoryName,
              dishCount: category.itemList?.length || 0,
              sort: index, // 使用index作为sort值确保唯一性
              originalData: category as any, // 保存原始数据并类型转换
            }
          })

          // 处理所有商品数据，合并所有分类下的商品
          const allItems: DishItemType[] = []

          data.categoryList.forEach((category, categoryIndex) => {
            if (category.itemList && category.itemList.length > 0) {
              console.log(`分类 ${category.categoryName} 有 ${category.itemList.length} 个菜品`)
              // 将每个商品添加分类信息后加入总列表
              const categoryItems = category.itemList.map((item) => {
                return {
                  id: item.itemCode,
                  name: item.itemName,
                  price: item.minSalePrice,
                  image: item.imageUrl || '',
                  // 在status字段生成时，优先判断售罄状态
                  status: item.soldOut === 1 ? '已售罄' : getStatusText(item.status, item.soldOut),
                  tags: [], // 接口中没有标签信息，使用空数组
                  categoryId: categoryIndex.toString(), // 直接使用index作为categoryId，确保唯一性
                  categoryName: category.categoryName,
                  soldOut: item.soldOut,
                  originalData: item, // 保存原始数据
                }
              })
              allItems.push(...categoryItems)
            }
          })

          // 存储所有商品数据
          allDishData.value = allItems
          console.log('处理后的菜品总数:', allItems.length)

          // 更新状态标签的数量
          updateStatusTabsCount(allItems)

          // 默认选中第一个分类
          if (categories.value.length > 0) {
            currentCategory.value = categories.value[0].id.toString()
          }

          // 根据当前状态筛选菜品
          filterDishList()

          // 初始化后，滚动到第一个分类
          nextTick(() => {
            scrollToCategorySection(categories.value[0]?.id.toString())
          })

          // 更新分类与菜品关联数据
          updateCategoriesWithDishes()
        } else {
          // 分类列表为空
          categories.value = []
          allDishData.value = []
          dishList.value = []
          categoriesWithDishes.value = []
        }

        // 更新顶部状态标签的数量
        statusTabs.value = [
          {
            label: '全部',
            count: data.total || 0,
          },
          { label: '已上架', count: data.totalOnShelfCount || 0 },
          { label: '已下架', count: data.totalOffShelfCount || 0 },
          { label: '已售罄', count: data.totalSoldOutCount || 0 },
        ]
      } else {
        // 接口返回异常
        categories.value = []
        allDishData.value = []
        dishList.value = []
        statusTabs.value = [
          { label: '全部', count: 0 },
          { label: '已上架', count: 0 },
          { label: '已下架', count: 0 },
          { label: '已售罄', count: 0 },
        ]
      }
    } catch (error) {
      const params: QueryDishListParams = {
        shopId: secondModelObj.value.id,
        tenantId: secondModelObj.value.tenantId,
        channelCode: modelValue.value,
        shopCode: secondModelObj.value.code,
        // 不添加分类和状态筛选，获取所有数据
      }
      console.error('获取商品数据失败:', error, params)
      // 发生错误时
      categories.value = []
      allDishData.value = []
      dishList.value = []
      statusTabs.value = [
        { label: '全部', count: 0 },
        { label: '已上架', count: 0 },
        { label: '已下架', count: 0 },
        { label: '已售罄', count: 0 },
      ]
    } finally {
      loading.value = false
      categoryLoading.value = false
    }
  }

  // 根据status和soldOut获取状态文本
  const getStatusText = (status: number, soldOut: number): string => {
    if (soldOut === 1) return '已售罄'
    if (status === 0) return '已下架'
    if (status === 1) return '已上架'
    return '未知状态'
  }

  // 判断菜品是否可售（非售罄/下架状态）
  const isAvailableDish = (status: string | number, dish?: any): boolean => {
    // 如果提供了dish对象，优先使用原始数据判断
    if (dish && dish.originalData) {
      const originalData = dish.originalData
      // 只有status=1（上架）且soldOut=0（非售罄）的菜品才是可售状态
      return originalData.status === 1 && originalData.soldOut === 0
    }

    // 如果传入的是字符串状态
    if (typeof status === 'string') {
      return status === '已上架'
    }

    // 如果传入的是数字状态
    // status为1表示已上架，为0表示已下架
    return status === 1
  }

  // 根据状态筛选菜品数据，使用前端过滤
  const filterDishList = () => {
    loading.value = true

    try {
      // 使用存储的数据源
      let filteredList = [...allDishData.value]

      // 根据状态筛选
      if (currentStatusTab.value !== '全部') {
        filteredList = filteredList.filter((item) => {
          // 获取原始数据，处理不同状态的匹配逻辑
          const originalData = (item as any).originalData

          if (currentStatusTab.value === '已上架') {
            // 上架状态：status=1 且 soldOut=0
            return originalData?.status === 1 && originalData?.soldOut === 0
          } else if (currentStatusTab.value === '已下架') {
            // 下架状态：status=0
            return originalData?.status === 0
          } else if (currentStatusTab.value === '已售罄') {
            // 售罄状态：soldOut=1
            return originalData?.soldOut === 1
          }

          // 如果以上条件都不符合，则尝试使用status字段直接匹配
          return item.status === currentStatusTab.value
        })
      }

      // 更新菜品列表
      dishList.value = filteredList

      // 不再自动滚动，提高性能
      // 只有当没有任何菜品时才滚动到顶部
      if (filteredList.length === 0) {
        // 简单滚动到顶部，而不是使用复杂的scrollToCategorySection
        nextTick(() => {
          if (dishListRef.value) {
            dishListRef.value.scrollTo({
              top: 0,
              duration: 100,
            })
          }
        })
      }

      // 更新分类与菜品关联数据

      updateCategoriesWithDishes()
    } catch (error) {
      console.error('筛选菜品数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 更新状态标签的数量 - 优化版本
  const updateStatusTabsCount = (list: DishItemType[]) => {
    if (!list || list.length === 0) {
      // 如果列表为空，直接设置所有数量为0
      statusTabs.value = [
        { label: '全部', count: 0 },
        { label: '已上架', count: 0 },
        { label: '已下架', count: 0 },
        { label: '已售罄', count: 0 },
      ]
      return
    }

    // 使用一次循环计算所有状态的数量，而不是多次filter
    const counts = {
      全部: 0,
      已上架: 0,
      已下架: 0,
      已售罄: 0,
    }

    // 单次遍历计算所有状态数量
    list.forEach((item) => {
      counts.全部++

      // 获取原始数据来判断状态
      const originalData = (item as any).originalData

      if (originalData) {
        // 使用原始数据中的status和soldOut来判断状态
        if (originalData.soldOut === 1) {
          // 售罄状态优先：soldOut=1
          counts.已售罄++
        } else if (originalData.status === 0) {
          // 下架状态：status=0
          counts.已下架++
        } else if (originalData.status === 1) {
          // 上架状态：status=1 且 soldOut=0
          counts.已上架++
        }
      } else {
        // 如果没有原始数据，则使用当前status字段判断
        if (typeof item.status === 'string') {
          if (item.status === '已上架') counts.已上架++
          else if (item.status === '已下架') counts.已下架++
          else if (item.status === '已售罄') counts.已售罄++
        }
        // 处理数字类型的状态
        else if (typeof item.status === 'number') {
          if (item.status === 1) counts.已上架++
          else if (item.status === 0) counts.已下架++
        }
      }
    })

    // 更新状态标签的数量
    statusTabs.value = [
      { label: '全部', count: counts.全部 },
      { label: '已上架', count: counts.已上架 },
      { label: '已下架', count: counts.已下架 },
      { label: '已售罄', count: counts.已售罄 },
    ]
  }

  // 切换顶部状态标签
  const changeStatusTab = (tab: TabStatus) => {
    // 如果点击的是当前已选中的标签，不做任何操作
    if (currentStatusTab.value === tab) return

    currentStatusTab.value = tab
    // 根据状态筛选菜品，使用前端筛选
    filterDishList()
  }

  // 处理右侧菜品列表滚动事件
  const handleDishListScroll = (e: any) => {
    // 如果是通过点击左侧分类触发的滚动，不处理
    if (isScrollingByClick.value) {
      return
    }

    const scrollTop = e.detail.scrollTop
    const scrollViewHeight = e.target.offsetHeight || uni.getSystemInfoSync().windowHeight

    // 检查是否触底
    const isReachBottom = e.detail.scrollHeight - (scrollTop + scrollViewHeight) <= 5

    // 获取所有分类区域的位置
    const query = uni.createSelectorQuery()
    query
      .selectAll('.category-section')
      .boundingClientRect((rects: any) => {
        if (!rects || rects.length === 0) return

        // 找到当前可见的分类
        let currentVisibleCategory = ''
        let minDistance = Infinity

        // 计算可视区域的上下边界
        const viewportTop = 0
        const viewportBottom = scrollViewHeight
        const triggerOffset = 100 // 触发区域为可视区域顶部的100px内

        rects.forEach((rect: any) => {
          if (!rect) return

          // 获取分类ID
          const categoryId = rect.id?.replace('category-', '')
          if (!categoryId) return

          // 计算元素相对于可视区域的位置
          const elementTop = rect.top
          const elementBottom = rect.bottom

          // 判断元素是否在可视区域内
          const isVisible = elementBottom > viewportTop && elementTop < viewportBottom

          // 计算元素顶部到视口顶部的距离
          const distanceToTop = Math.abs(elementTop - triggerOffset)

          // 如果元素在可视区域内且距离顶部最近
          if (isVisible && distanceToTop < minDistance) {
            minDistance = distanceToTop
            currentVisibleCategory = categoryId
          }

          // 特殊处理：如果已经滚动到底部，选择最后一个分类
          if (isReachBottom && rect === rects[rects.length - 1]) {
            currentVisibleCategory = categoryId
          }
        })

        // 如果找到可见分类，更新当前选中的分类
        if (currentVisibleCategory && currentVisibleCategory !== currentCategory.value) {
          currentCategory.value = currentVisibleCategory
        }
      })
      .exec()
  }

  // 切换左侧分类 - 滚动到对应区域
  const changeCategory = (categoryId: string | number) => {
    scrollToCategorySection(categoryId.toString())
  }

  // 添加一个变量用于控制scroll-view的scroll-into-view属性
  const scrollIntoViewId = ref('')

  // 滚动到指定分类区域
  const scrollToCategorySection = (categoryId: string) => {
    // 参数检查
    if (!categoryId) return

    // 防止重复执行
    if (isScrollingByClick.value) return

    // 标记正在滚动中
    isScrollingByClick.value = true

    // 更新当前分类

    currentCategory.value = categoryId

    // 使用scroll-into-view属性滚动到指定元素
    scrollIntoViewId.value = `category-${categoryId}`

    // 延时重置滚动状态
    setTimeout(() => {
      isScrollingByClick.value = false
      // 重置scrollIntoViewId，以便下次滚动生效
      scrollIntoViewId.value = ''
    }, 800)
  }

  // 跳转到选择店铺页面
  const goToSelectShop = () => {
    uni.navigateTo({
      url: '/pages/selectShop/index',
    })
  }

  // 获取渠道数据
  const getChannelData = async (isNotFresh = false) => {
    try {
      const res = await fetchChannelEnum({
        type: 'item',
      })

      if (res?.data) {
        // 处理渠道数据，将其转换为 StoreTab 格式
        channelOptions.value = res.data.map((item) => ({
          key: item.code,
          label: item.name,
        }))

        // 默认选中第一个渠道
        console.log('isNotFresh===>', isNotFresh)

        if (channelOptions.value.length > 0 && !isNotFresh) {
          modelValue.value = channelOptions.value[0].key
          selectedChannels.value = [channelOptions.value[0].key]
        }
      } else {
        // 接口返回异常，使用模拟数据
        channelOptions.value = [
          // { key: 'meituan', label: '美团外卖' },
          // { key: 'eleme', label: '饿了么' },
          // { key: 'jd', label: '京东秒送' },
          // { key: 'miniapp', label: '自营小程序' },
          // { key: 'douyin', label: '抖音随心团' },
        ]

        // modelValue.value = 'meituan'
        // selectedChannels.value = ['meituan']
      }
    } catch (error) {
      console.error('获取渠道数据失败:', error)
      // // 发生错误时使用模拟数据
      // channelOptions.value = [
      //   { key: 'meituan', label: '美团外卖' },
      //   { key: 'eleme', label: '饿了么' },
      //   { key: 'jd', label: '京东秒送' },
      //   { key: 'miniapp', label: '自营小程序' },
      //   { key: 'douyin', label: '抖音随心团' },
      // ]

      // modelValue.value = 'meituan'
      // selectedChannels.value = ['meituan']
    }
  }

  // 初始化门店信息
  const initStoreInfo = (isNotFresh = false) => {
    try {
      const shopStore = useShopStore()

      if (shopStore.hasSelectedShop) {
        const shop = shopStore.currentShop

        // 更新当前选择的门店信息
        currentShopInfo.value = {
          name: shop?.name || '',
          id: shop?.id || '',
          address: shop?.raw?.address || '',
        }

        // 尝试从不同位置获取 shopDtoList
        const shopDtoList =
          (shop?.raw as any)?.shopDtoList || shop?.raw?.extFields?.shopDtoList || []

        // 将 shopDtoList 映射到 seconTab
        if (Array.isArray(shopDtoList) && shopDtoList.length > 0) {
          seconTab.value = shopDtoList.map((item: any) => ({
            key: item.id || '',
            label: item.name || '',
            id: item.id || '',
            tenantId: item.tenantId || '',
            ...item,
          }))

          // 默认选中第一个二级标签

          const findIndex = seconTab.value.findIndex((item) => item.key === secondModelValue.value)
          console.log(
            ' secondModelValue.value===>',
            secondModelValue.value,
            seconTab.value,
            findIndex,
          )
          if (seconTab.value.length > 0 && findIndex === -1) {
            secondModelValue.value = seconTab.value[0].key
            // 同步更新 secondModelObj
            secondModelObj.value = seconTab.value[0]
          }
        } else {
          // 如果没有获取到shopDtoList，使用模拟数据
          seconTab.value = [
            { key: 'shop1', label: '海底捞·火锅(创意产业园店)', id: 'shop1', tenantId: 'tenant1' },
            {
              key: 'shop2',
              label: '海底捞·下饭火锅菜(创意产业园店)',
              id: 'shop2',
              tenantId: 'tenant2',
            },
            {
              key: 'shop3',
              label: '海底捞·小火锅(创意产业园店)',
              id: 'shop3',
              tenantId: 'tenant3',
            },
          ]

          secondModelValue.value = 'shop1'
          secondModelObj.value = seconTab.value[0]
        }
      } else {
        // 如果没有选择门店，使用模拟数据
        currentShopInfo.value = {
          name: '测试门店',
          id: 'test001',
          address: '测试地址',
        }

        seconTab.value = [
          { key: 'shop1', label: '海底捞·火锅(创意产业园店)', id: 'shop1', tenantId: 'tenant1' },
          {
            key: 'shop2',
            label: '海底捞·下饭火锅菜(创意产业园店)',
            id: 'shop2',
            tenantId: 'tenant2',
          },
          { key: 'shop3', label: '海底捞·小火锅(创意产业园店)', id: 'shop3', tenantId: 'tenant3' },
        ]

        secondModelValue.value = 'shop1'
        secondModelObj.value = seconTab.value[0]
      }
    } catch (error) {
      console.error('Error in initStoreInfo:', error)
      // 发生错误时使用模拟数据
      currentShopInfo.value = {
        name: '测试门店',
        id: 'test001',
        address: '测试地址',
      }

      seconTab.value = [
        { key: 'shop1', label: '海底捞·火锅(创意产业园店)', id: 'shop1', tenantId: 'tenant1' },
        {
          key: 'shop2',
          label: '海底捞·下饭火锅菜(创意产业园店)',
          id: 'shop2',
          tenantId: 'tenant2',
        },
        { key: 'shop3', label: '海底捞·小火锅(创意产业园店)', id: 'shop3', tenantId: 'tenant3' },
      ]

      secondModelValue.value = 'shop1'
      secondModelObj.value = seconTab.value[0]
    }
  }

  // 添加组件高度计算相关的引用和状态
  const mainContentHeight = ref(0)
  const leftCategoryHeight = ref(0)
  const rightDishListHeight = ref(0)

  // 计算各组件高度
  const calculateComponentHeights = () => {
    // 使用setTimeout确保DOM已经渲染
    setTimeout(() => {
      // 创建查询选择器
      const query = uni.createSelectorQuery()

      // 获取各组件高度
      // query.select('.sy-order-fixed-header').boundingClientRect()
      query.select('.shop-box').boundingClientRect()
      query.select('.sy-store-tab-nav').boundingClientRect()
      query.select('.tab-container').boundingClientRect()

      // 获取系统信息
      uni.getSystemInfo({
        success: (sysInfo) => {
          query.exec((res) => {
            // 检查结果是否有效
            if (!res || !res.length) {
              // 使用默认值作为备选方案
              const defaultHeight = 400
              mainContentHeight.value = defaultHeight
              leftCategoryHeight.value = defaultHeight
              rightDishListHeight.value = defaultHeight
              return
            }

            // 计算固定组件的总高度
            const headerHeight = res[0]?.height || 0 // SyOrderFixedHeader高度
            const shopBoxHeight = res[1]?.height || 0 // 顶部店铺信息高度
            const tabNavHeight = res[2]?.height || 0 // SyStoreTabNav高度
            const tabContainerHeight = res[3]?.height || 0 // 标签栏高度
            // 获取底部安全区高度和tabbar高度
            const safeAreaBottom = sysInfo.safeAreaInsets?.bottom || 0
            const tabbarHeight = 70 // tabbar高度（一般为50px）
            // 计算页面总高度和主内容区域可用高度
            const windowHeight = sysInfo.windowHeight
            const fixedComponentsHeight =
              headerHeight + shopBoxHeight + tabNavHeight + tabContainerHeight
            const bottomHeight = safeAreaBottom + tabbarHeight
            // 计算主内容区域可用高度（考虑了安全区域和底部导航栏）
            const availableHeight = windowHeight - fixedComponentsHeight - bottomHeight
            // 确保高度不会太小，同时考虑一些额外的空间
            const finalHeight = Math.max(availableHeight - 20, 300) // 减去50px的安全边距

            // 更新主内容区域高度
            mainContentHeight.value = finalHeight
            leftCategoryHeight.value = finalHeight
            rightDishListHeight.value = finalHeight - 30
          })
        },
        fail: (err) => {
          console.log('获取系统信息失败:', err)
          // 使用默认高度
          mainContentHeight.value = 400
          leftCategoryHeight.value = 400
          rightDishListHeight.value = 400
        },
      })
    }, 300) // 给DOM渲染一些时间
  }

  // 在数据变化时重新计算高度
  watch(
    [
      categories,
      secondModelValue,
      modelValue,
      currentStatusTab,
      () => dishList.value.length,
      groupedDishes,
    ],
    () => {
      // 数据变化后重新计算高度
    },
    { deep: true },
  )

  // 初始化数据
  const initData = async () => {
    // 初始化门店信息
    initStoreInfo()

    // 获取所有渠道数据
    if (channelOptions.value.length === 0) {
      await getChannelData()
    }

    // 设置默认渠道
    setTimeout(() => {
      // 如果有渠道选项，默认选中第一个
      if (channelOptions.value.length > 0 && !modelValue.value) {
        modelValue.value = channelOptions.value[0].key
      }

      // 更新分类与菜品关联数据
      updateCategoriesWithDishes()
    }, 300)

    // 在数据加载完成后计算组件高度
    calculateComponentHeights()
  }

  // 页面初始化逻辑
  onLoad(() => {
    // 初始化数据
  })
  onShow(async () => {
    initData()
  })

  return {
    statusBarHeight,
    screenHeight,
    currentShopInfo,
    channelOptions,
    selectedChannels,
    seconTab,
    modelValue,
    secondModelValue,
    secondModelObj,
    currentStatusTab,
    statusTabs,
    currentCategory,
    categories,
    dishList,
    loading,
    categoryLoading,
    pageLoading,
    categoriesWithDishes,
    changeStatusTab,
    changeCategory,
    goToSelectShop,
    getChannelData,
    initStoreInfo,
    initData,
    calculateComponentHeights,
    dishListRef,
    handleDishListScroll,
    groupedDishes,
    mainContentHeight,
    leftCategoryHeight,
    rightDishListHeight,
    scrollIntoViewId,
    updateCategoriesWithDishes,
    getStatusText,
    isAvailableDish,
  }
}
