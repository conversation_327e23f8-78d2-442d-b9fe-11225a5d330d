// 门店选择页面样式
.shop-list-page {
  display: flex;
  flex-direction: column;
  align-items: center;

  // 门店列表容器
  .shop-list-container {
    width: calc(100% - 64rpx);
    height: calc(100vh - 140rpx);
    // padding: 32rpx; // 设计稿 16px * 2 = 32rpx
    overflow: scroll;

    // 覆盖 Wot Design Uni 的 cell-group 样式
    :deep(.wd-cell-group) {
      padding: 0rpx;
      margin: 0rpx;
      overflow: scroll;
      background-color: transparent;
      border-radius: 0;

      .wd-cell {
        padding: 32rpx; // 设计稿 16px * 2 = 32rpx
        margin-bottom: 16rpx; // 减少间距使列表更紧凑
        background-color: #ffffff; // 白色背景
        // border: 1rpx solid #e8e8e8; // 更浅的边框色
        // border-radius: 16rpx; // 更圆润的圆角
        // box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04); // 轻微阴影
        transition: all 0.2s ease-in-out; // 添加过渡动画

        // 移除最后一个元素的下边距
        &:last-child {
          margin-bottom: 0;
        }

        // 门店名称文本样式
        .wd-cell__title {
          font-family: PingFang SC;
          font-size: 26rpx; // 增大字体 16px * 2 = 32rpx
          font-weight: 500; // 增加字重
          color: #333333; // 更深的文本颜色，提升可读性
          word-break: break-all; // 处理长文本换行
          white-space: normal; // 允许换行
        }
        .wd-cell__right {
          flex: none;
        }
        .wd-cell__left,
        .wd-cell__right {
          align-items: center;
        }
        // 右侧箭头图标
        .wd-cell__right-icon {
          font-size: 24rpx; // 调整箭头大小
          color: #222222; // 设计稿箭头颜色 paint_51:2043
        }

        // 单元格内容区域
        .wd-cell__body {
          align-items: flex-start; // 顶部对齐，适应多行文本
          min-height: 42rpx; // 设计稿最小高度 21px * 2 = 42rpx
        }

        // 处理长文本的特殊样式
        &.long-text {
          .wd-cell__title {
            display: -webkit-box;
            -webkit-line-clamp: 2; // 最多显示2行
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        // 点击态效果
        // &:active {
        //   background-color: #f8f9fa;
        //   box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
        //   transform: translateY(-2rpx);
        // }

        // // 悬停效果（H5端）
        // &:hover {
        //   box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
        //   transform: translateY(-1rpx);
        // }
      }
    }
  }
}

// 空状态样式
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;

  .empty-icon {
    margin-bottom: 24rpx;
    font-size: 80rpx;
    opacity: 0.6;
  }

  .empty-text {
    margin-bottom: 32rpx;
    font-size: 28rpx;
    line-height: 1.5;
    color: #999999;
  }

  .empty-action {
    padding: 16rpx 32rpx;
    background-color: #f7f8fa;
    border: 1rpx solid #e8e8e8;
    border-radius: 24rpx;

    .clear-search-text {
      font-size: 26rpx;
      color: #666666;
    }
  }
}

// 导航栏样式覆盖
:deep(.wd-navbar) {
  background-color: #ffffff; // 白色背景 paint_1:0239
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1); // 设计稿分割线 paint_51:07463

  .wd-navbar__title {
    font-family: 'SFProDisplay-Medium', sans-serif; // 设计稿字体 font_51:07513
    font-size: 34rpx; // 设计稿 17px * 2 = 34rpx
    font-weight: 500;
    line-height: 40rpx; // 设计稿 20px * 2 = 40rpx
    color: #000000; // 设计稿标题颜色 paint_11:15798
    opacity: 0.9; // 设计稿透明度
  }

  .wd-navbar__left {
    .wd-icon {
      font-size: 32rpx; // 调整返回图标大小
      color: rgba(0, 0, 0, 0.9); // 设计稿返回图标颜色 paint_51:07487
    }
  }
}
// 搜索容器样式
.search-container {
  background-color: #ffffff;
  border-bottom: 1rpx solid #f5f5f5;
  // transition: all 0.3s ease;
  // 搜索中状态
  &.searching {
    background-color: #f8f9fa;
  }

  // 搜索操作按钮样式
  .search-action {
    .search-text {
      font-family: PingFang SC;
      font-size: 26rpx;
      font-weight: normal;
      line-height: normal;
      color: #e60112;
      text-align: right;
      letter-spacing: normal;
    }

    // &:active {
    //   opacity: 0.8;
    //   transform: scale(0.98);
    // }
  }
}

// 加载状态样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  padding: 80rpx 32rpx;

  .loading-text {
    margin-top: 24rpx;
    font-size: 28rpx;
    color: #666666;
  }
}

// 错误状态样式
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  padding: 80rpx 32rpx;

  .error-icon {
    margin-bottom: 24rpx;
    font-size: 64rpx;
    opacity: 0.6;
  }

  .error-text {
    margin-bottom: 32rpx;
    font-size: 28rpx;
    color: #999999;
    text-align: center;
  }
}

// 空状态样式
.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  padding: 80rpx 32rpx;

  .empty-text {
    font-size: 28rpx;
    color: #999999;
  }
}

// 滚动视图样式
.shop-scroll-view {
  height: calc(100vh - 200rpx); // 减去搜索框高度
}

// 底部状态容器样式
.bottom-status-container {
  padding: 32rpx;
}

// 加载更多样式
.load-more-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;

  .load-more-text {
    margin-left: 16rpx;
    font-size: 28rpx;
    color: #666666;
  }
}

// 没有更多数据样式
.no-more-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;

  .no-more-text {
    font-size: 28rpx;
    color: #999999;
  }
}

// 安全区域样式
.safe-area {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

// 加载更多状态样式
.load-more-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 32rpx;

  .load-more-text {
    margin-top: 16rpx;
    font-size: 24rpx;
    color: #999999;
  }
}

// 没有更多数据样式
.no-more-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 32rpx;

  .no-more-text {
    font-size: 24rpx;
    color: #999999;
  }
}

.shop-list {
  display: flex;
  flex-direction: column;
  // gap: 24rpx;
  width: 100%;
}

.shop-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;
  background: #fff;
  border-radius: 16rpx;
  transition: background 0.2s;
  // border: 1rpx solid #e8e8e8; // 如需边框可打开
  // box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04); // 如需阴影可打开
  &:active {
    background: #f8f9fa;
  }
}

.shop-item-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  min-width: 0;
}

.shop-item-title {
  margin-bottom: 8rpx;
  font-family: PingFang SC;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 40rpx;
  color: #333;
  word-break: break-all;
}

.shop-item-address {
  font-family: PingFang SC;
  font-size: 24rpx;
  line-height: 34rpx;
  color: #999;
  word-break: break-all;
}

.shop-item-arrow {
  display: flex;
  align-items: center;
  margin-left: 24rpx;
}

// 注释掉原有 cell 相关样式，避免冲突
// :deep(.wd-cell-group) { /* ... */ }
