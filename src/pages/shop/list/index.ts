import { reactive, computed, ref, watch } from 'vue'
import { queryManagerShopPage } from '@/service/shop/shop'
import type {
  ShopItem,
  RawShopItem,
  QueryManagerShopPageParams,
  QueryManagerShopPageResponse,
} from '@/service/shop/types'
import { useShopStore } from '@/store/shop'
import { useUserStore } from '@/store/user'
import { useDeviceStore } from '@/store/device'
import { saveDeviceAPI } from '@/service/user'

// 扩展查询参数类型
declare module '@/service/shop/types' {
  interface QueryManagerShopPageParams {
    name?: string
  }

  interface QueryManagerShopPageResponse {
    list: RawShopItem[]
    total: number
  }
}

// 门店数据类型定义 - 导出以便其他文件使用
export type { ShopItem }

// 导航栏高度
export const statusBarHeight = ref(0)

// 搜索关键字
export const searchKeyword = ref('')

// 搜索状态
export const isSearching = ref(false)

// 分页状态
export const pageNum = ref(1)
export const pageSize = ref(10)
export const hasMore = ref(true)
export const loading = ref(false)
export const isRefreshing = ref(false)

// 门店列表
export const shopList = ref<ShopItem[]>([])

// 移除错误状态变量，统一使用空状态展示

// 无门店权限弹窗状态
export const showNoShopPermissionPopup = ref(false)

/**
 * 将后端原始门店数据转换为前端使用的格式
 * @param rawShop 后端原始门店数据
 * @returns 前端门店数据格式
 */
export function transformShopData(rawShop: RawShopItem): ShopItem {
  // 优先使用alias，其次使用name，最后才使用组织名称+地址
  let name = rawShop.alias || rawShop.name || ''

  // 如果name和alias都为空，则使用组织名称+地址
  if (!name) {
    name = `${rawShop.extFields?.orgName || ''}${rawShop.address ? `(${rawShop.address})` : ''}`
  }

  console.log('transformShopData--', rawShop)
  return {
    id: rawShop.id, // 使用id作为唯一标识
    code: rawShop.code, // 使用code作为唯一标识
    name: name.trim() || rawShop.code, // 如果名称为空，则使用code
    raw: rawShop, // 保留原始数据
  }
}

/**
 * 增强版防抖函数，支持取消操作
 */
function debounce<T extends (...args: any[]) => any>(fn: T, delay: number) {
  let timeoutId: number | undefined

  const debouncedFn = (...args: Parameters<T>) => {
    if (timeoutId) clearTimeout(timeoutId)
    timeoutId = setTimeout(() => fn(...args), delay)
  }

  // 添加取消方法
  debouncedFn.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = undefined
    }
  }

  return debouncedFn
}

/**
 * 获取门店列表数据
 */
export async function fetchShopList(isLoadMore = false) {
  if (loading.value) return

  try {
    loading.value = true

    const params: QueryManagerShopPageParams = {
      pageNum: isLoadMore ? pageNum.value : 1,
      pageSize: pageSize.value,
    }

    // 如果有搜索关键字，添加到参数中
    if (searchKeyword.value.trim()) {
      params.name = searchKeyword.value.trim()
    }

    console.log('请求门店列表参数:', params)

    const response = await queryManagerShopPage(params)

    console.log('门店列表API完整响应:', JSON.stringify(response, null, 2))

    // 检查API响应是否成功
    if (!response || response.resultCode !== '0') {
      console.log('API响应不成功', response?.resultCode, response?.resultMsg)

      // 特殊处理401状态码
      if (String(response?.resultCode) === '401') {
        console.log('检测到401错误，清理用户信息并跳转到登录页')
        const userStore = useUserStore()
        userStore.clearUserInfo()

        // 使用setTimeout避免页面渲染问题
        setTimeout(() => {
          uni.reLaunch({ url: '/pages/login/detail/index' })
        }, 100)
      }

      throw new Error(response?.resultMsg || '获取门店列表失败')
    }

    // 检查响应数据结构
    console.log('response.data:', response.data)
    console.log('response.data.data:', response.data?.data)

    // 获取实际的响应数据
    const pageData = response.data

    // 检查数据格式
    if (!pageData || !Array.isArray(pageData.list)) {
      shopList.value = []
      pageNum.value = 1
      // 只有在第一页且非搜索状态下才显示无门店权限弹窗
      if (!isLoadMore && !searchKeyword.value.trim()) {
        console.log('检测到无门店权限，显示提示弹窗')
        showNoShopPermissionPopup.value = true
      }
      return
    }

    // 转换数据格式
    const newShops = pageData.list.map(transformShopData)

    console.log('转换后的数据', newShops)

    // 更新分页状态
    if (isLoadMore) {
      shopList.value = [...shopList.value, ...newShops]
      pageNum.value += 1
    } else {
      shopList.value = newShops
      pageNum.value = 2 // 下次加载第2页
    }

    // 判断是否还有更多数据
    hasMore.value = shopList.value.length < pageData.total

    // 自动选择门店的逻辑
    const userStore = useUserStore()
    const tenantIds = userStore.userInfo.tenantIds.split(',') as Array<string>
    // 如果只有一个租户，一个门店，默认选中门店，并直接进入首页
    if (tenantIds.length === 1 && shopList.value.length === 1 && !isLoadMore) {
      // 自动选择唯一的门店（包含保存设备信息的逻辑）
      setTimeout(() => {
        handleShopSelect(shopList.value[0])
      }, 1000)
    }
  } catch (err) {
    console.error('获取门店列表失败:', err)

    // 显示错误提示
    // uni.showToast({
    //   title: err instanceof Error ? err.message : '获取门店列表失败',
    //   icon: 'none',
    // })
  } finally {
    loading.value = false
    // isRefreshing 现在由调用方管理
  }
}

/**
 * 实际执行搜索的函数
 */
const performSearch = (keyword: string) => {
  console.log('执行搜索:', keyword)

  // 避免重复搜索相同关键字
  // if (searchKeyword.value === keyword && !loading.value) {
  //   console.log('搜索关键字未变化，跳过搜索')
  //   return
  // }

  isSearching.value = true
  searchKeyword.value = keyword
  pageNum.value = 1
  hasMore.value = true

  // 执行搜索后重置搜索状态
  fetchShopList(false).finally(() => {
    isSearching.value = false
  })
}

/**
 * 搜索门店（防抖处理）
 */
export const searchShops = debounce(performSearch, 300)

/**
 * 下拉刷新
 */
export const onRefresh = () => {
  console.log('下拉刷新')

  // 取消正在进行的搜索防抖和清空防抖
  searchShops.cancel()
  clearSearchRefresh.cancel()

  isRefreshing.value = true
  isSearching.value = false // 重置搜索状态
  pageNum.value = 1
  hasMore.value = true
  searchKeyword.value = '' // 刷新时清空搜索

  fetchShopList(false).finally(() => {
    isRefreshing.value = false
  })
}

/**
 * 加载更多
 */
export const onLoadMore = () => {
  console.log('加载更多')
  if (!hasMore.value || loading.value) {
    console.log('没有更多数据或正在加载中', '有更多数据:', hasMore.value, '加载中:', loading.value)
    return
  }

  fetchShopList(true)
}

/**
 * 处理搜索输入（防抖处理）
 */
export const onSearchInput = (value: string | Event) => {
  console.log('搜索输入事件:', value)

  // 处理不同类型的输入事件
  let searchValue = ''
  if (typeof value === 'string') {
    searchValue = value
  } else if (value && typeof value === 'object' && 'detail' in value) {
    // uni-app 事件对象
    searchValue = (value as any).detail?.value || ''
  } else {
    // 从当前的 searchKeyword 值获取
    searchValue = searchKeyword.value
  }

  console.log('搜索值:', searchValue)

  // 如果正在刷新中，不执行搜索
  if (isRefreshing.value) {
    console.log('正在刷新中，跳过搜索')
    return
  }

  // 触发防抖搜索
  searchShops(searchValue)
}

/**
 * 清空搜索的数据刷新（防抖处理）
 */
const clearSearchRefresh = debounce(() => {
  console.log('执行清空搜索数据刷新')

  isSearching.value = true
  pageNum.value = 1
  hasMore.value = true

  // 执行刷新后重置搜索状态
  fetchShopList(false).finally(() => {
    isSearching.value = false
  })
}, 150) // 较短的防抖时间，提升清除响应速度

/**
 * 清空搜索
 */
export const onClearSearch = () => {
  console.log('清空搜索触发')

  // 取消正在进行的搜索防抖和清空防抖
  searchShops.cancel()
  clearSearchRefresh.cancel()

  // 立即更新UI显示
  searchKeyword.value = ''

  // 防抖执行数据刷新
  clearSearchRefresh()
}

/**
 * 门店列表数据 hook
 */
export function useShopListData() {
  console.log('初始化useShopListData')

  // 计算错误信息
  // 移除错误消息计算属性，统一使用空状态展示

  // 重试函数
  const refetch = () => {
    pageNum.value = 1
    hasMore.value = true
    fetchShopList(false)
  }

  return {
    shopList: computed(() => shopList.value),
    isLoading: computed(() => loading.value),
    refetch,
  }
}

// 底部Tab的页面路由
const tabRoutes = ['pages/review/index', 'pages/storeDetail/index', 'pages/dishManagement/index']

/**
 * 选择门店
 * @param shop 门店信息
 */
export const handleShopSelect = async (shop: ShopItem) => {
  try {
    // 获取门店store
    const shopStore = useShopStore()

    // 保存选中的门店到store
    shopStore.setCurrentShop(shop)

    // 保存门店ID到极光推送设备信息
    const deviceStore = useDeviceStore()
    const deviceInfo = deviceStore.deviceInfo

    if (deviceInfo?.registerId) {
      console.log('门店选择后保存设备信息，门店ID:', shop.id)

      await saveDeviceAPI(
        deviceInfo.registerId, // deviceId
        undefined, // phone (不修改)
        undefined, // loginStatus (不修改)
        shop.id, // loginShopId (保存选中的门店ID)
      )

      console.log('门店ID保存成功，门店:', shop.name, 'ID:', shop.id)
    } else {
      console.warn('设备信息中缺少registerId，无法保存门店ID')
    }
  } catch (error) {
    console.error('选择门店时保存设备信息失败:', error)
    // 不阻断用户选择门店的流程，仅记录错误
  }

  // 获取当前页面路由信息
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] // 当前页面
  const prevPage = pages[pages.length - 2] // 上一个页面

  // 记录路由信息
  console.log('获取路由：', tabRoutes, prevPage?.route, tabRoutes.includes(prevPage?.route))
  if (tabRoutes.includes(prevPage?.route)) {
    if (prevPage?.route.includes('pages/storeDetail/index')) {
      uni.$emit('refreshStoreTabNav')
      uni.reLaunch({
        url: '/pages/storeDetail/index?time=' + Date.now(),
      })
      // 触发全局事件，通知刷新TabNav
    } else if (prevPage?.route.includes('pages/dishManagement/index')) {
      uni.$emit('refreshStoreTabNav')
      uni.switchTab({
        url: '/pages/dishManagement/index?time=' + Date.now(),
      })
    } else {
      uni.navigateBack()
    }
  } else {
    // 选择门店后跳转到首页，重置 tabbar 高亮状态
    try {
      const { tabbarStore } = await import('@/components/sy-tabbar/sy-tabbar')
      tabbarStore.resetToFirst() // 重置为第一个 tabbar 页面
      console.log('[Shop Select] tabbar 高亮状态已重置为第一个页面')
    } catch (e) {
      console.error('[Shop Select] 重置 tabbar 高亮状态失败:', e)
    }

    uni.reLaunch({
      url: '/pages/index/index',
    })
  }
}

// 获取状态栏高度
export const getStatusBarHeight = () => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  console.log(' systemInfo.statusBarHeight', systemInfo.statusBarHeight)
}

/**
 * 处理无门店权限，返回登录
 */
export const handleNoShopPermission = () => {
  console.log('处理无门店权限，退出登录')

  // 清理用户信息
  const userStore = useUserStore()
  userStore.clearUserInfo()

  // 隐藏弹窗
  showNoShopPermissionPopup.value = false

  // 延迟跳转到登录页，避免页面渲染问题
  setTimeout(() => {
    uni.reLaunch({
      url: '/pages/login/detail/index',
    })
  }, 100)
}

/**
 * 清理所有防抖和状态（页面销毁时调用）
 */
export const cleanup = () => {
  console.log('清理搜索相关状态和防抖')

  // 取消所有防抖
  searchShops.cancel()
  clearSearchRefresh.cancel()

  // 重置状态
  isSearching.value = false
  loading.value = false
  isRefreshing.value = false
  showNoShopPermissionPopup.value = false
}

/**
 * 处理搜索按钮点击
 */
export const onSearchClick = () => {
  console.log('搜索按钮点击')
  if (!searchKeyword.value.trim()) {
    uni.showToast({
      title: '请输入搜索内容',
      icon: 'none',
    })
    return
  }

  // 设置搜索状态
  isSearching.value = true

  // 重置分页状态
  pageNum.value = 1
  hasMore.value = true

  // 执行搜索
  fetchShopList()
}
