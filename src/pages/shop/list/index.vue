<route lang="json5">
{
  needLogin: false,
  style: {
    navigationBarTitleText: '选择默认物理门店',
  },
}
</route>

<template>
  <view class="shop-list-page" :style="{ '--status-bar-height': statusBarHeight + 'px' }">
    <!-- 搜索框 -->
    <view
      class="search-container w-100% flex items-center justify-between"
      :class="{ searching: isSearching }"
    >
      <wd-search
        v-model="searchKeyword"
        placeholder="请输入门店名称"
        @clear="onClearSearch"
        hide-cancel
        :disabled="isSearching"
        @search="onSearchClick"
        custom-class="search-input flex-1"
        shape="round"
      ></wd-search>
      <view class="search-action mr-32rpx ml-32rpx" @click="onSearchClick">
        <text class="search-text">搜索</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="isLoading && shopList.length === 0" class="loading-container">
      <wd-loadmore custom-class="loadmore" state="loading" />
    </view>

    <!-- 门店列表容器 -->
    <view v-else class="shop-list-container">
      <!-- 空状态/无结果统一 -->
      <view v-if="shopList.length === 0 && !loading" class="no-result-state">
        <wd-status-tip
          :image-size="{ height: '240', width: '240' }"
          image="search"
          :tip="emptyTipText"
        />
      </view>

      <!-- 门店列表 -->
      <scroll-view
        v-else
        scroll-y
        class="shop-scroll-view"
        @scrolltolower="onLoadMore"
        :refresher-enabled="true"
        :refresher-triggered="isRefreshing"
        @refresherrefresh="onRefresh"
        lower-threshold="100"
      >
        <view class="shop-list">
          <template v-for="shop in shopList" :key="shop.id">
            <view class="shop-item" @click="handleShopSelect(shop)">
              <view class="shop-item-content">
                <view class="shop-item-title">{{ shop.name }}</view>
                <view class="shop-item-address">{{ shop.raw.address }}</view>
              </view>
              <view class="shop-item-arrow">
                <wd-icon name="arrow-right" size="32rpx" color="#222222" />
              </view>
            </view>
          </template>
        </view>

        <!-- 底部状态区域 -->
        <view class="bottom-status-container">
          <!-- 加载更多状态 -->
          <view v-if="isLoading && shopList.length > 0" class="load-more-container">
            <wd-loadmore custom-class="loadmore" state="loading" />
          </view>

          <!-- 没有更多数据 -->
          <view v-else-if="!hasMore && shopList.length > 0" class="no-more-container">
            <text class="no-more-text">没有更多数据了</text>
          </view>

          <!-- 安全区域 -->
          <view class="safe-area"></view>
        </view>
      </scroll-view>
    </view>

    <!-- 无门店权限弹窗 -->
    <SyPopup
      v-model="showNoShopPermissionPopup"
      title="错误提示"
      confirm-text="返回登录"
      :show-cancel="false"
      :close-on-click-modal="false"
      @confirm="handleNoShopPermission"
    >
      当前账号无门店权限，请联系管理员开通
    </SyPopup>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue'
import { onShow, onHide } from '@dcloudio/uni-app'
import {
  handleShopSelect,
  getStatusBarHeight,
  statusBarHeight,
  fetchShopList,
  onRefresh,
  onLoadMore,
  onSearchClick,
  onClearSearch,
  searchKeyword,
  shopList,
  loading,
  isRefreshing,
  hasMore,
  isSearching,
  cleanup,
  showNoShopPermissionPopup,
  handleNoShopPermission,
} from './index'
import { useUserStore } from '@/store'
import SyPopup from '@/components/sy-popup'

// 检查登录状态
const userStore = useUserStore()
console.log('门店列表页面 - 登录状态', userStore.isLogined)

// 计算属性
const isLoading = computed(() => loading.value)

// 空状态提示文本
const emptyTipText = computed(() => {
  return searchKeyword.value ? `未找到"${searchKeyword.value}"相关的门店` : '暂无门店数据'
})

// 页面初始化
onMounted(() => {
  getStatusBarHeight()
  fetchShopList()
})

// 每次页面显示时重新获取数据
onShow(() => {
  const systemInfo = uni.getSystemInfoSync()
  console.log('systemInfo:', systemInfo)
  console.log('门店列表页面 - onShow')
  getStatusBarHeight()
  console.log('门店列表页面 - 登录状态', userStore.isLogined)

  // 如果列表为空，重新获取数据
  if (shopList.value.length === 0) {
    fetchShopList()
  }
})

// 页面隐藏时清理防抖
onHide(() => {
  console.log('门店列表页面 - onHide')
  cleanup()
})

// 组件销毁时清理
onUnmounted(() => {
  console.log('门店列表页面 - onUnmounted')
  cleanup()
})
</script>

<style lang="scss" scoped>
@import './index';
</style>
