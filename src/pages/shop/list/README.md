# 门店选择页面

## 页面路径

`src/pages/shop/list/`

## 文件结构（三文件分离）

```
src/pages/shop/list/
├── index.vue      # 页面模板和组件引用
├── index.ts       # 页面业务逻辑和数据处理
├── index.scss     # 页面专属样式
└── README.md      # 页面说明文档
```

## 功能描述

该页面用于用户选择默认的物理门店，展示可用门店列表供用户选择。页面已集成API数据获取功能，支持加载状态、错误处理和数据重试。

## 设计稿来源

基于 MasterGo 设计稿生成：

- 文件ID: `155767347512493`
- 图层ID: `154:27429`

## API 集成

### API 接口

- **接口地址**: `v1/shop/manager/queryManagerShopList`
- **请求方法**: GET
- **功能**: 获取门店管理员可管理的门店列表

### 服务文件

相关服务文件位于 `src/service/shop/` 目录：

```
src/service/shop/
├── index.ts         # 服务入口文件，导出所有API功能
├── types.ts         # TypeScript类型定义
├── shop.ts          # API服务函数
└── shop.vuequery.ts # vue-query集成hooks
```

### 数据流

1. 页面加载时自动调用 `useQueryManagerShopList()` hook
2. hook 使用 `@tanstack/vue-query` 管理API请求状态
3. 从API响应的 `data.list` 中提取门店列表数据
4. 页面根据loading、error、data状态渲染不同UI

## 页面特性

### 1. 导航栏

- 标题：选择默认物理门店
- 使用 uni-app 原生导航栏（通过 `<route>` 配置）
- 白色背景，底部有分割线

### 2. 门店列表

- 使用 Wot Design Uni 的 `wd-cell` 组件
- 每个列表项包含门店名称和右侧箭头
- 支持长文本自动换行
- 卡片式设计，白色背景，圆角边框
- 列表项间有间距，提供良好的视觉层次

### 3. 状态管理

- **加载状态**: 显示loading指示器和"加载中..."文本
- **错误状态**: 显示错误信息和重试按钮
- **空状态**: 当API返回空列表时显示"暂无门店数据"
- **数据状态**: 正常渲染门店列表

### 4. 交互功能

- 点击列表项显示选择提示
- 支持点击态效果（缩放动画）
- 自动返回功能（原生导航栏）
- 错误状态下支持点击重试

## 技术实现

### 文件职责分离

- **index.vue**: 负责页面模板结构、组件引用和状态显示
- **index.ts**: 负责业务逻辑、API数据获取和事件处理
- **index.scss**: 负责页面样式定义和组件样式覆盖

### 技术栈

- **Vue 3**: 使用 Composition API 和 `<script setup>` 语法
- **TypeScript**: 完整的类型安全保障
- **@tanstack/vue-query**: API数据获取和状态管理
- **Wot Design Uni**: UI组件库

### 样式规范

- **像素转换**: 设计稿 1px = 代码 2rpx
- **颜色**: 严格按照设计稿的颜色标记 (paint tokens)
- **字体**: 使用设计稿指定的字体族和大小
- **响应式**: 支持长文本换行和省略号处理

### 数据结构

```typescript
// 后端返回的原始门店数据
interface RawShopItem {
  code: string // 门店代码
  address: string // 地址
  addressLevel: string // 地址级别
  addressLevelName: string | null // 地址级别名称
  alias: string | null // 别名
  cityCode: string // 城市代码
  cityName: string | null // 城市名称
  countyCode: string // 区县代码
  countyName: string | null // 区县名称
  companyId: string | null // 公司ID
  companyName: string | null // 公司名称
  createPerson: string // 创建人
  createTime: string // 创建时间
  extFields: {
    // 扩展字段
    orgName: string // 组织名称（如"海底捞"）
    posShopCode: string // POS门店代码
    yunxiShopCode: string // 云犀门店代码
    contactPhone: string // 联系电话
    [key: string]: any // 其他扩展字段
  }
}

// 前端使用的门店数据格式
interface ShopItem {
  id: string // 门店ID（使用code作为唯一标识）
  name: string // 门店名称（组合orgName和address）
  raw?: RawShopItem // 原始数据（保留完整信息）
}

// API响应格式
interface QueryManagerShopListResponse {
  data: RawShopItem[] // 门店数据数组
  exceptCauseApp: string | null // 异常应用信息
  exceptCauseIp: string | null // 异常IP信息
  exceptClass: string | null // 异常类
  extFields: Record<string, any> // 扩展字段
  resultCode: string // 结果代码（"0"表示成功）
  resultMsg: string // 结果消息
}
```

### 数据转换规则

- **门店ID**: 使用 `code` 字段作为唯一标识
- **门店名称**: 组合 `extFields.orgName` 和 `address`，格式为 "组织名称(地址)"
- **错误判断**: 检查 `resultCode` 是否为 "0"，不为 "0" 时显示 `resultMsg` 作为错误信息
- **原始数据**: 保留在 `raw` 字段中，供后续功能使用

## API 使用示例

### 在其他页面中使用门店API

```typescript
import { useQueryManagerShopList } from '@/service/shop/shop.vuequery'

// 使用API hook
const { data, isLoading, isError, error, refetch } = useQueryManagerShopList({
  params: {
    pageNum: 1,
    pageSize: 10,
  },
  enabled: true,
})
```

### 在其他组件中使用类型

```typescript
import type { ShopItem } from '@/service/shop/types'

const handleShop = (shop: ShopItem) => {
  console.log('处理门店:', shop.name)
}
```

## 导入和使用

### 页面数据和逻辑

```typescript
import { useShopListData, handleShopSelect, type ShopItem } from '@/pages/shop/list/index'
```

### 样式导入

```scss
@import '@/pages/shop/list/index.scss';
```

## 错误处理

- **网络错误**: 显示"网络异常，请重试"
- **API错误**: 根据error.message显示具体错误信息
- **空数据**: 显示"暂无门店数据"
- **重试机制**: 点击重试按钮重新请求数据

## 待完善功能

- [ ] ~~接入真实的门店数据 API~~ ✅ 已完成
- [ ] 实现门店选择后的数据持久化
- [ ] 添加门店搜索功能
- [ ] 支持门店地理位置显示
- [ ] 添加门店详情页面跳转
- [ ] 支持分页加载
- [ ] 添加下拉刷新功能

## 页面注册

页面已在 `src/pages.json` 中注册：

```json
{
  "path": "pages/shop/list/index",
  "type": "page"
}
```

## 使用方法

```javascript
// 跳转到门店选择页面
uni.navigateTo({
  url: '/pages/shop/list/index',
})
```

## 开发规范遵循

- ✅ **三文件分离**: Vue、TypeScript、SCSS 分别独立文件
- ✅ **TypeScript 类型安全**: 完整的类型定义和导出
- ✅ **SCSS 样式隔离**: 使用 scoped 和深度选择器
- ✅ **组件化开发**: 使用 Wot Design Uni 组件库
- ✅ **像素转换规范**: 严格按照 1px = 2rpx 转换
- ✅ **代码注释**: 详细的 JSDoc 注释和说明
- ✅ **API 规范**: 使用 @tanstack/vue-query 进行数据请求
- ✅ **错误处理**: 统一的错误处理和用户友好提示
- ✅ **状态管理**: 完整的加载、错误、空状态处理

## 更新日志

### v2.0.0 - API集成版本

- ✅ 集成 `v1/shop/manager/queryManagerShopList` API
- ✅ 使用 @tanstack/vue-query 进行数据管理
- ✅ 添加加载状态、错误状态、空状态处理
- ✅ 实现数据重试功能
- ✅ 创建完整的API服务层
- ✅ 移除静态测试数据，使用真实API数据
