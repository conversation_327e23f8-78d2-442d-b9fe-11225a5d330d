// 订单列表页面样式
.order-list-page {
  box-sizing: border-box;
  width: 100%;
  height: calc(100vh - 100rpx);
  padding-top: var(--status-bar-height);
  overflow-x: hidden; // 防止横向滚动
  background-color: #fff;

  // 固定头部区域
  .fixed-header-index {
    position: relative;
    z-index: 9;
    width: 100%;
    background-color: #fff;

    .order-filter {
      display: flex;
      justify-content: space-between;

      .date-filter-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        margin-right: 32rpx;

        .date-filter-value {
          margin-left: 10rpx;
          font-size: 13px;
          font-weight: 500;
          line-height: 17px;
          color: #333;
          text-align: right;
          letter-spacing: 0px;
        }
      }
    }
  }

  // 滚动内容区域
  .scrollable-content {
    width: 100%;
    //height: calc(100vh - var(--header-height, 400rpx)); /* 计算剩余高度 */
    background-color: #f1f1f1;

    .compensation-center {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 42rpx;
      padding: 24rpx;
      margin: 24rpx 24rpx;
      background-color: white;
      border-radius: 16rpx;

      .compensation-label {
        font-size: 30rpx;
        font-weight: 600;
        line-height: normal;
        color: #3d3d3d;
      }
      .compensation-tip {
        font-size: 24rpx;
        font-weight: normal;
        line-height: normal;
        color: #222222;
        text-align: right;
      }
    }
    // 加载状态
    .loading-container {
      display: flex;
      gap: 16rpx;
      align-items: center;
      justify-content: center;
      padding: 40rpx 0;

      .loading-text {
        font-size: 28rpx;
        color: #666666;
      }
    }

    // 无更多数据
    .no-more {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40rpx 0;

      .no-more-text {
        font-size: 28rpx;
        color: #999999;
      }
    }

    // 空状态
    .empty-state {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 120rpx 0;

      .empty-text {
        font-size: 32rpx;
        color: #999999;
      }
    }
  }

  // 登录状态信息
  .login-status-message {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300rpx;

    .login-message-text {
      font-size: 28rpx;
      color: #999999;
    }
  }

  // 渠道筛选样式
  .channel-filter {
    &-popup {
      overflow: hidden;
      border-radius: 24rpx 24rpx 0 0;
    }

    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100rpx;
      padding: 0 24rpx;
      border-bottom: 1px solid #f2f2f4;

      &-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #222222;
      }

      &-close {
        padding: 10rpx;
      }
    }

    &-content {
      max-height: 60vh;
      padding: 0 24rpx;
      overflow-y: auto;
    }

    &-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100rpx;
      border-bottom: 1px solid #f2f2f4;

      &:last-child {
        border-bottom: none;
      }

      &-checkbox {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28rpx;
        height: 28rpx;
        margin-right: 20rpx;
        border: 1px solid #cccccc;
        border-radius: 4rpx;

        &.checked {
          background-color: #0064ff;
          border-color: #0064ff;
        }
      }

      &-icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 16rpx;
        overflow: hidden;
        border-radius: 50%;
      }

      &-text {
        font-size: 30rpx;
        color: #222222;

        &.active {
          color: #0064ff;
        }
      }
    }
  }

  // 状态标签容器
  // 排序容器
  .sort-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 12rpx 24rpx;
    background-color: #f1f1f1;

    .action-item {
      display: flex;
      align-items: center;
      padding: 8rpx 16rpx;
      background-color: #ffffff;
      border-radius: 32rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

      &:active {
        opacity: 0.8;
      }

      .action-text {
        margin-left: 8rpx;
        font-size: 24rpx;
        color: #333333;
      }
    }
  }

  // 订单统计样式已移至 sy-order-stats-line 组件内部
}

.back-to-top-btn {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
  width: 1px;
  height: 1px;
  opacity: 0;
}

// 返回顶部按钮样式
.back-to-top-button {
  position: fixed;
  bottom: 25%;
  left: 16rpx;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 68rpx;
  height: 68rpx;
  visibility: hidden;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: all 0.3s ease;
  transform: translateX(-50rpx);

  // 显示状态
  &.show {
    visibility: visible;
    opacity: 1;
    transform: translateX(0);
  }

  // 滚动时隐藏一半到左侧
  &.sliding {
    transform: translateX(-34rpx);
  }

  // 点击效果
  &:active {
    transform: scale(0.95);
  }
}

// 自定义下拉刷新样式
.custom-refresher {
  position: absolute;
  top: 0;
  left: 50%;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 240rpx;
  height: 80rpx;
  background-color: transparent;
  transition: all 0.3s ease;
  transform: translateX(-50%);

  .refresher-content {
    display: flex;
    gap: 16rpx;
    align-items: center;
    justify-content: center;
    padding: 16rpx 24rpx;

    .refresher-icon {
      // 只有在加载状态下才旋转
      &.refresher-spinning {
        animation: refresher-spinning 1s linear infinite;
      }

      // 完成状态的图标不旋转
      &:not(.refresher-spinning) {
        animation: none;
      }
    }

    .refresher-text {
      font-size: 28rpx;
      font-weight: 400;
      color: #666666;
      white-space: nowrap;

      // 完成状态的特殊样式
      &.completed {
        font-weight: 500;
        color: #52c41a;
      }
    }
  }
}

// 刷新图标旋转动画
@keyframes refresher-spinning {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
