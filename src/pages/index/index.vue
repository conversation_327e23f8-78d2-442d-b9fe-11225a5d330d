<route type="home" lang="json5">
{
  layout: 'tabbar',
  needLogin: true,
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>

<template>
  <view
    class="order-list-page flex flex-col"
    :style="{
      '--status-bar-height': statusBarHeight + 'px',
    }"
  >
    <!-- '--header-height': headerHeight ? headerHeight + 'px' : statusBarHeight + 200 + 'px', -->
    <!-- 回到顶部按钮（隐藏的） -->
    <view id="backToTopBtn" ref="backToTopBtn" class="back-to-top-btn" @click="scrollToTop"></view>

    <!-- 返回顶部按钮 -->
    <view
      class="back-to-top-button"
      :class="{ show: showBackToTop, sliding: isScrolling }"
      @click="handleBackToTop"
    >
      <wd-icon name="arrow-up" color="#000000" size="30rpx"></wd-icon>
    </view>

    <!-- 固定在顶部的部分 -->
    <view class="fixed-header-index">
      <!-- 门店头部 -->
      <!-- <sy-order-fixed-header
        :current-store="currentStore"
        :is-store-expanded="isStoreExpanded"
        @toggle-store-expand="toggleStoreExpand"
      /> -->

      <!-- 订单头部组件 -->
      <sy-order-header
        :active-tab="activeTab"
        :show-pre-order-button="true"
        @tab-change="onTabChange"
        @pre-order-click="onPreOrderClick"
        @search-click="onSearchClick"
      />

      <view class="order-filter">
        <!-- 筛选栏 -->
        <sy-order-filters
          :current-filters="selectedFilters"
          :channel-options="processedChannelEnum"
          :order-type-options="orderTypeEnum"
          :date-options="dateOptions"
          @filter-item-click="onFilterClick"
          @filter-change="onFilterChange"
          :is-need-filters="isNeedFilters"
        />

        <view
          v-if="activeTab === 'damage-appeal'"
          class="date-filter-container"
          @click="onHandleDateFilter"
        >
          <text class="date-filter-value">
            {{
              showFilterDateValues.length > 0
                ? showFilterDateValues[0] + '至' + showFilterDateValues[1]
                : '添加时间筛选'
            }}
          </text>
          <wd-icon name="chevron-down" size="24rpx"></wd-icon>
        </view>
      </view>

      <!-- 状态标签栏区域 -->
      <SyOrderStatusTabs
        :status-list="statusTabs"
        :active-status-id="activeStatus"
        :show-expand-button="true"
        @status-change="onStatusChange"
        @expand-click="onExpandClick"
      />
      <!-- 新订单提示弹窗 -->
      <sy-new-order-toast
        v-model="newOrderToast.visible"
        :channel-text="newOrderToast.channelText"
        :order-no="newOrderToast.orderNo"
        :duration="newOrderToast.duration"
        :auto-close="newOrderToast.autoClose"
        :header-height="headerHeight"
        @close="onNewOrderToastClose"
        @content-click="onNewOrderToastContentClick"
      />
    </view>

    <!-- 内容滚动区域 -->
    <scroll-view
      ref="scrollViewRef"
      class="scrollable-content"
      :scroll-y="true"
      @scrolltolower="onLoadMore"
      :refresher-enabled="true"
      refresher-default-style="none"
      refresher-background="#f1f1f1"
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @refresherpulling="onRefresherPulling"
      @refresherrestore="onRefresherRestore"
      :scroll-top="scrollTop"
      :scroll-with-animation="true"
      @scroll="onScroll"
      :style="{
        height: headerHeight
          ? `calc(100vh - ${headerHeight + 100 + statusBarHeight}px)`
          : 'calc(100vh - 200px)',
      }"
    >
      <!-- 自定义下拉刷新组件 -->
      <view
        v-if="showCustomRefresher"
        class="custom-refresher"
        :style="{ transform: `translateY(${refresherTransform}px) translateX(-50%)` }"
        :data-status="refresherStatus"
      >
        <view class="refresher-content">
          <!-- 根据不同状态显示不同图标 -->
          <wd-icon
            v-if="refresherStatus === 'completed'"
            name="check1"
            size="22px"
            class="refresher-icon"
            color="#52c41a"
          ></wd-icon>
          <wd-icon
            v-else
            name="refresh"
            size="22px"
            :class="{
              'refresher-icon': true,
              'refresher-spinning': refresherStatus === 'loading',
            }"
          ></wd-icon>

          <!-- 根据不同状态显示不同文本 -->
          <text
            :class="{
              'refresher-text': true,
              completed: refresherStatus === 'completed',
            }"
          >
            {{
              refresherStatus === 'pulling'
                ? '松开即可刷新'
                : refresherStatus === 'releasing'
                  ? '松开即可刷新'
                  : refresherStatus === 'loading'
                    ? '努力加载中'
                    : refresherStatus === 'completed'
                      ? '刷新完毕'
                      : '下拉刷新'
            }}
          </text>
        </view>
      </view>

      <!-- 页面级加载组件 - 覆盖订单列表区域 -->
      <SyLoading
        v-if="pageLoading"
        :show="true"
        src="/static/images/img/goods-loading.gif"
        toastText="数据加载中..."
        :imgWidth="200"
        :imgHeight="200"
        :showMask="false"
        :fixed="false"
      />

      <!-- 顶部锚点元素 -->
      <view id="top" style="width: 1rpx; height: 1rpx"></view>

      <!-- 订单统计信息 -->
      <sy-order-stats-line
        v-if="activeTab !== 'damage-appeal'"
        :stats="orderStats"
        :is-expanded="isStatsExpanded"
        :show-view-toggle="activeStatus === 'IN_PREPARE'"
        :is-mini-card="showAsMiniCard"
        @expand="onStatsExpand"
        @toggle-view="toggleDisplayMode"
      />

      <!-- 待出餐状态提示 -->
      <view
        v-if="activeStatus === 'IN_PREPARE'"
        class="flex px-16rpx py-8rpx mb-16rpx bg-#FFF9EC z-1"
      >
        <wd-icon name="info-circle-filled" color="#F33429" size="24rpx"></wd-icon>
        <text class="text-20rpx text-#F33429 line-clamp-2">
          为确保骑手能及时上门取餐，请一定要点击上报出餐。
        </text>
      </view>

      <!-- 登录状态检查 -->
      <view v-if="!isLoggedIn" class="login-status-message">
        <text class="login-message-text">正在检查登录状态...</text>
      </view>

      <!-- 订单卡片列表 -->
      <template v-else>
        <template v-if="activeStatus === 'IN_PREPARE' && !pageLoading">
          <template v-if="showAsMiniCard">
            <view class="flex flex-wrap justify-between px-10rpx">
              <SyOrderMiniCard
                v-for="order in orderList"
                :key="order.id"
                :order-data="transformOrderData(order, activeTab === 'after-sale')"
                @report-order="
                  onOrderAction(transformOrderData(order, activeTab === 'after-sale'), 'report')
                "
                class="mb-24rpx"
              />
            </view>
          </template>
          <template v-else>
            <SyOrderCard
              v-for="order in orderList"
              :key="order.id"
              :id="'order-' + order.id"
              :order-data="transformOrderData(order, activeTab === 'after-sale')"
              :expanded="orderCardExpandedMap[order.id]"
              :is-after-sale-mode="activeTab === 'after-sale'"
              @expand-toggle="(expanded) => onOrderCardExpand(order.id, expanded)"
              @call="onCallCustomer"
              @address-click="(address) => onAddressClick(order)"
              @operation-result="onOperationResult"
              @after-sale-approve="onAfterSaleApprove"
              @after-sale-reject="onAfterSaleReject"
              @refund-order="onRefundOrder"
            />
          </template>
        </template>
        <template v-else-if="!pageLoading">
          <view v-if="activeTab !== 'damage-appeal'">
            <SyOrderCard
              v-for="order in orderList"
              :key="order.id"
              :id="'order-' + order.id"
              :order-data="transformOrderData(order, activeTab === 'after-sale')"
              :expanded="orderCardExpandedMap[order.id]"
              :is-after-sale-mode="activeTab === 'after-sale'"
              @expand-toggle="(expanded) => onOrderCardExpand(order.id, expanded)"
              @call="onCallCustomer"
              @address-click="(address) => onAddressClick(order)"
              @operation-result="onOperationResult"
              @after-sale-approve="onAfterSaleApprove"
              @after-sale-reject="onAfterSaleReject"
              @refund-order="onRefundOrder"
            />
          </view>
          <template v-else>
            <view class="compensation-center">
              <text class="compensation-label">赔付中心</text>
              <text class="compensation-tip">请到平台APP查看赔付订单及审核进度</text>
            </view>

            <SyAppealOrder
              v-for="order in orderList"
              :key="order.id"
              :id="'order-' + order.id"
              :order-data="transformOrderData(order, true)"
              :is-after-sale-mode="true"
              @handle-to-appeal="(index) => onHandleToAppeal(index)"
            />
          </template>
        </template>

        <!-- 加载状态 -->
        <view v-if="loading && !pageLoading" class="loading-container">
          <wd-loadmore custom-class="loadmore" state="loading" />
        </view>

        <!-- 空状态 -->
        <view v-if="!loading && !pageLoading && orderList.length === 0" class="empty-state">
          <text class="empty-text">暂无订单数据</text>
        </view>

        <!-- 无更多数据 -->
        <view v-if="!hasMore && orderList.length > 0 && !loading && !pageLoading" class="no-more">
          <text class="no-more-text">没有更多数据了</text>
        </view>
      </template>
    </scroll-view>

    <!-- MQTT 状态组件 (仅开发环境显示) -->
    <!-- 暂时注释掉MQTT状态组件，避免类型错误 -->
    <!--
    <SyMqttStatus
      v-if="isDevelopment"
      :is-connected="mqttConnected"
      :is-connecting="mqttConnecting"
      :reconnect-attempts="mqttReconnectAttempts"
      :max-reconnect-attempts="5"
      @reconnect="handleMqttReconnect"
      @get-info="handleGetMqttInfo"
    />
    -->

    <!-- 确认弹窗组件 - 同意退款 -->
    <sy-confirm-popup
      v-model="approvePopupConfig.visible"
      :title="approvePopupConfig.title"
      :content="approvePopupConfig.content"
      :cancel-text="approvePopupConfig.cancelText"
      :confirm-text="approvePopupConfig.confirmText"
      @confirm="handleApproveConfirm"
      @cancel="handleApproveCancel"
    />

    <!-- 确认弹窗组件 - 拒绝退款 -->
    <sy-confirm-popup
      v-model="rejectPopupConfig.visible"
      :title="rejectPopupConfig.title"
      :content="rejectPopupConfig.content"
      :cancel-text="rejectPopupConfig.cancelText"
      :confirm-text="rejectPopupConfig.confirmText"
      @confirm="handleRejectConfirm"
      @cancel="handleRejectCancel"
    />

    <sy-date-filter
      v-model:visible="isShowDateFilter"
      :top-offset="channelSelectorTop"
      :left-offset="240"
      @confirmFilterDate="(values) => handleConfirmDate(values)"
    ></sy-date-filter>

    <!-- 异常提醒组件 -->
    <sy-exception-reminder-bar
      :visible="showExceptionReminderBar"
      @click="onExceptionReminderClick"
    />

    <!-- 异常提醒弹窗 -->
    <sy-exception-popup
      v-model:visible="showExceptionPopup"
      @ignore="onExceptionIgnore"
      @goSetting="onExceptionGoSetting"
    />

    <!-- 高危风险确认弹窗 -->
    <sy-risk-confirm-popup
      v-model:visible="showRiskConfirmPopup"
      @ignore="onRiskIgnore"
      @goSetting="onRiskGoSetting"
    />
  </view>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, computed } from 'vue'
import { onShow, onHide, onLoad } from '@dcloudio/uni-app'
import SyOrderFixedHeader from '@/components/sy-order-fixed-header'
import SyOrderHeader from '@/components/sy-order-header'
import SyOrderStatusTabs from '@/components/sy-order-status-tabs'
import SyOrderStatsLine from '@/components/sy-order-stats-line'
import SyOrderCard from '@/components/sy-order-card'
import SyAppealOrder from '@/components/sy-appeal-order'
import SyOrderMiniCard from '@/components/sy-order-mini-card'
import SyOrderFilters from '@/components/sy-order-filters'
import SyConfirmPopup from '@/components/sy-confirm-popup/sy-confirm-popup.vue'
import SyNewOrderToast from '@/components/sy-new-order-toast'
import SyDateFilter from '@/components/sy-date-filter'
import SyLoading from '@/components/sy-loading/sy-loading.vue'
import SyExceptionReminderBar from '@/components/sy-exception-reminder-bar/sy-exception-reminder-bar.vue'
import SyExceptionPopup from '@/components/sy-exception-popup/sy-exception-popup.vue'
import SyRiskConfirmPopup from '@/components/sy-risk-confirm-popup/sy-risk-confirm-popup.vue'
import { watchStorageKey } from '@/utils'

import {
  statusBarHeight,
  currentStore,
  activeTab,
  selectedFilters,
  statusTabs,
  activeStatus,
  orderList,
  isRefreshing,
  loading,
  hasMore,
  onTabChange,
  onPreOrderClick,
  onSearchClick,
  onFilterClick,
  onStatusChange,
  onExpandClick,
  onSortClick,
  onLoadMore,
  onRefresh,
  onCallCustomer,
  onAddressClick,
  onExpandOrder,
  transformOrderData,
  initPage,
  handlePageShow,
  channelEnum,
  orderTypeEnum,
  orderStatusEnum,
  onFilterChange,
  dateOptions,
  processedChannelEnum,
  isLoggedIn,
  headerHeight,
  isStatsExpanded,
  onStatsExpand,
  orderStats,
  isOrderCardsExpanded,
  onOrderExpandToggle,
  onOrderAction,
  orderCardExpandedMap,
  onOrderCardExpand,
  isStoreExpanded,
  toggleStoreExpand,
  showAsMiniCard,
  toggleDisplayMode,
  onOperationResult,
  onAfterSaleApprove,
  onAfterSaleReject,
  onRefundOrder,
  mqttConnected,
  mqttConnecting,
  mqttReconnectAttempts,
  cleanupMqtt,
  reconnectMqtt,
  getMqttConnectionInfo,
  cleanupEventListeners,
  approvePopupConfig,
  rejectPopupConfig,
  handleApproveConfirm,
  handleApproveCancel,
  handleRejectConfirm,
  handleRejectCancel,
  newOrderToast,
  onNewOrderToastClose,
  onNewOrderToastContentClick,
  scrollViewRef,
  scrollToView,
  onScroll,
  backToTopBtn,
  scrollToTop,
  showBackToTop,
  isScrolling,
  handleBackToTop,
  scrollTop,
  isNeedFilters,
  isShowDateFilter,
  onHandleDateFilter,
  handleConfirmDate,
  showFilterDateValues,
  channelSelectorTop,
  pageLoading,
  showExceptionReminderBar,
  showExceptionPopup,
  showRiskConfirmPopup,
  onExceptionReminderClick,
  onExceptionIgnore,
  onExceptionGoSetting,
  onRiskIgnore,
  onRiskGoSetting,
  showCustomRefresher,
  refresherTransform,
  refresherStatus,
  onRefresherPulling,
  onRefresherRestore,
  onHandleToAppeal,
} from './index'
import { useUserStore } from '@/store'

// 开发环境判断
const isDevelopment = computed(() => {
  // #ifdef H5
  return import.meta.env.DEV
  // #endif
})

// MQTT 相关事件处理
const handleMqttReconnect = async () => {
  try {
    await reconnectMqtt()
  } catch (error) {
    console.error('重连MQTT失败:', error)
  }
}

const handleGetMqttInfo = () => {
  const info = getMqttConnectionInfo()
  console.log('MQTT连接信息:', info)

  uni.showModal({
    title: 'MQTT连接信息',
    content: `连接状态: ${info.isConnected ? '已连接' : '未连接'}\n连接中: ${info.isConnecting ? '是' : '否'}\n重连次数: ${info.reconnectAttempts}/${info.maxReconnectAttempts}`,
    showCancel: false,
  })
}

// 生命周期
onLoad(() => {
  initPage()
  // handlePageShow()
})

onShow(() => {
  handlePageShow()
})

// 移除onHide中的cleanupMqtt调用，保持MQTT连接
// onHide(() => {
//   cleanupMqtt()
// })

onUnmounted(() => {
  // cleanupMqtt()
  cleanupEventListeners()
})
</script>

<style lang="scss" scoped>
@import './index';
</style>
