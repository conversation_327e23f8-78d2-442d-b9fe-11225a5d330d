import { ref, reactive, computed, onMounted, watch, onUnmounted } from 'vue'
import { useUserStore } from '@/store'
import { onShow } from '@dcloudio/uni-app'
import { useShopStore } from '@/store/shop'
import { ToastOptions } from '@/components/sy-new-order-toast/toast'
import { getCurrentTime, dateStrToFormat, getDaysAfter } from '../../utils/datetime'

import {
  fetchOrderTypeEnum,
  fetchChannelEnum,
  fetchOrderStatusEnum,
  fetchRefundOrderStatusEnum,
} from '@/service/order/dict'
import {
  fetchOrderList,
  QueryOrderParams,
  reportMeals,
  confirmOrder,
  fetchOrderByThirdOrderCode,
  auditAfterSale,
  fetchDamageOrderList,
} from '@/service/order'
import { channelIconMap, getChannelIcon } from '@/components/sy-channel-selector/utils'
import { transformOrderData as apiTransformOrderData } from '@/utils/transform'
import { useMqtt } from '@/hooks/useMqtt'

import { watchStorageKey } from '@/utils'

// 异常提醒相关状态
export const showExceptionReminderBar = ref(false)
export const showExceptionPopup = ref(false)
export const showRiskConfirmPopup = ref(false)
// #ifdef APP-PLUS
const jpushModule = uni.requireNativePlugin('JG-JPush')
// #endif

// scroll-view引用
export const scrollViewRef = ref<any>(null)
// scroll-view滚动控制
export const scrollToView = ref<string>('')
// 回到顶部按钮引用
export const backToTopBtn = ref<any>(null)

// 返回顶部按钮状态
export const showBackToTop = ref(false)
export const isScrolling = ref(false)
export const currentScrollTop = ref(0)
// APP端滚动控制
export const scrollTop = ref(0)

// 类型定义
export interface Store {
  id: string
  code: string
  name: string
  address?: string
  avatar?: string
  subShopIds?: string[]
}

export interface Tab {
  key: string
  label: string
}

export interface FilterOption {
  key: string
  label: string
  value: string
}

export interface StatusTab {
  key: string
  label: string
  count?: number
}

export interface Customer {
  name: string
  phone: string
  fullPhone: string
}

// 默认需要显示的筛选条件
export const isNeedFilters = reactive({
  needChannel: true,
  needStore: true,
  needOrderType: true,
  needDate: true,
  needDateFilter: false,
  // 是否渠道多选
  isMultipleChannelMode: true,
})

export const isShowDateFilter = ref(false)

export interface Order {
  extlChannel: string
  id: string
  orderNumber: string
  status: string
  type: string
  bizType: string
  instanceId: string
  tenantId: string
  deliveryTime: string
  sendTime?: string
  createTime?: string // 添加创建时间字段
  saleChannel?: string
  merchantConfirmTime?: string
  buyerRemark?: string // 添加买家备注字段
  merchant: {
    id: string
    name: string
    avatar: string
  }
  customer: Customer
  address: {
    full: string
    distance?: string
  }
  warning?: {
    text: string
    countdown: {
      minutes: string
      seconds: string
    }
  }
  statusInfo: {
    main: string
    time?: string
    description?: string
  }
  delivery?: {
    text: string
    time: string
    platform?: string
  }
  deliveryRecord?: {
    // 添加配送记录字段
    courierName?: string
    courierMobile?: string
    statusName?: string
    channelName?: string
    deliveryStatusName?: string
    operateTime?: string
  }
  channel?: string // 添加渠道字段
  items: any[] | string // 修改为可以是数组或字符串
  amount: string
  fees?: {
    // 添加费用字段
    merchantIncome: number
    deliveryFee: number
    packagingFee: number
    totalAmount: number
  }
  apiData?: any // 添加原始API数据字段
}

export interface OrderListResponse {
  list: any[]
  hasNextPage: boolean
  [key: string]: any
}

// 响应式状态
export const statusBarHeight = ref(0)
export const headerHeight = ref(0)

// 门店信息
export const currentStore = ref<Store>({
  id: '',
  code: '',
  name: '',
  address: '',
  avatar: '',
})

// Tab状态
export const activeTab = ref<'order' | 'after-sale' | 'damage-appeal'>('order')

// 日期选项
export const dateOptions = [
  { label: '今日', value: 'today' },
  { label: '近7天', value: 'last7days' },
  { label: '近30天', value: 'last30days' },
  { label: '自定义', value: 'custom' },
]

// 筛选条件
export const selectedFilters = ref({
  channelText: '渠道',
  channelCodes: [] as string[],
  // 单选模式选择的渠道
  singleChannelCodes: [] as string[],
  // 多选模式选择的渠道
  multipleChannelCodes: [] as string[],
  storeText: '门店列表',
  storeCode: '',
  shopIds: [] as string[],
  orderTypeText: '订单类型',
  orderTypeCodes: [] as string[],
  dateText: '今日',
  _startPlaceTime: '', // 日期开始时间
  _endPlaceTime: '', // 日期结束时间
})

// 状态标签
export const statusTabs = ref([
  // {
  //   id: 'ALL',
  //   name: '全部',
  //   count: 0,
  //   remark: null,
  // },
  // {
  //   id: 'PAYED',
  //   name: '待接单',
  //   count: 0,
  //   remark: null,
  // },
  // {
  //   id: 'IN_PREPARE',
  //   name: '待出餐',
  //   count: 0,
  //   remark: null,
  // },
  // {
  //   id: 'WAIT_CELL_DELIVERY',
  //   name: '待发配送',
  //   count: 0,
  //   remark: null,
  // },
  // {
  //   id: 'WAIT_RIDER_ACCEPT',
  //   name: '待骑手接单',
  //   count: 0,
  //   remark: null,
  // },
  // {
  //   id: '80',
  //   name: '待取餐',
  //   count: 0,
  //   remark: null,
  // },
  // {
  //   id: '60',
  //   name: '配送中',
  //   count: 0,
  //   remark: null,
  // },
  // {
  //   id: 'CANCEL',
  //   name: '已取消',
  //   count: 0,
  //   remark: null,
  // },
])

export const activeStatus = ref<string>('ALL')

// 加载状态
export const isRefreshing = ref(false)

// 自定义下拉刷新相关状态
export const showCustomRefresher = ref(false)
export const refresherTransform = ref(0)
export const refresherStatus = ref<'pulling' | 'releasing' | 'loading' | 'completed'>('pulling')
export const loading = ref(false)
export const hasMore = ref(true)
// 添加页面级loading状态，用于遮盖订单列表区域
export const pageLoading = ref(false)

export const orderTypeEnum = ref<any[]>([])
export const channelEnum = ref<any[]>([])
export const processedChannelEnum = computed(() => {
  return processChannelData(channelEnum.value)
})
export const orderStatusEnum = ref<any[]>([])
export const refundOrderStatusEnum = ref<any[]>([])

export const pageNum = ref(1)
export const pageSize = ref(5)
export const waitPageSize = ref(6) // 待出餐一次性查询6条

// 获取所有门店ID列表（需要由后端提供，或通过其他方式获取）
export const allShopIds = ref<string[]>([])

// 添加登录状态变量
export const isLoggedIn = ref(false)

// 订单统计信息展开状态
export const isStatsExpanded = ref(false)

/**
 * 订单卡片展开状态
 */
export const isOrderCardsExpanded = ref(false)

/**
 * 订单卡片展开状态映射
 */
export const orderCardExpandedMap = ref<Record<string, boolean>>({})

// 门店展开状态
export const isStoreExpanded = ref(false)

// 切换门店展开状态
export const toggleStoreExpand = (expanded?: boolean) => {
  if (expanded !== undefined) {
    isStoreExpanded.value = expanded
  } else {
    isStoreExpanded.value = !isStoreExpanded.value
  }
}

// 事件处理方法
export const onStoreSelect = () => {
  if (!isStoreExpanded.value) {
    // 如果当前是收起状态，则展开
    toggleStoreExpand(true)
    return
  }

  console.log('选择门店')
  // 跳转到门店列表页面
  uni.navigateTo({
    url: '/pages/shop/list/index',
  })
}

export const onTabChange = async (tab: 'order' | 'after-sale' | 'damage-appeal') => {
  console.log('切换Tab:', tab)

  // 显示页面loading
  pageLoading.value = true

  console.log(
    '查看选择的渠道：',
    selectedFilters.value.multipleChannelCodes,
    selectedFilters.value.singleChannelCodes,
  )
  try {
    activeTab.value = tab
    if (tab === 'damage-appeal') {
      isNeedFilters.needOrderType = false
      isNeedFilters.needDate = false
      isNeedFilters.isMultipleChannelMode = false
      if (selectedFilters.value.singleChannelCodes.length === 0) {
        selectedFilters.value.channelCodes = ['MeiTuanTakeOutBrand']
      } else {
        selectedFilters.value.channelCodes = [...selectedFilters.value.singleChannelCodes]
      }
      nextTick(async () => {
        getDateFilterPosition()
      })
    } else {
      isNeedFilters.needOrderType = true
      isNeedFilters.needDate = true
      isNeedFilters.isMultipleChannelMode = true
      if (selectedFilters.value.multipleChannelCodes.length > 0) {
        selectedFilters.value.channelCodes = [...selectedFilters.value.multipleChannelCodes]
      } else {
        selectedFilters.value.channelCodes = channelEnum.value.map((item) => item.code)
      }
    }

    // 这里因为tab 切换会乱序 所以需要重新获取状态标签（临时方案）
    activeStatus.value = 'ALL'
    statusTabs.value = []
    // 当切换到售后场景时，关闭新订单弹窗
    if ((tab === 'after-sale' || tab === 'damage-appeal') && newOrderToast.value.visible) {
      console.log('切换到售后场景，关闭新订单弹窗')
      newOrderToast.value.visible = false
    }

    // 重新加载枚举数据
    await loadEnums(false)

    // 重置页码和筛选状态
    pageNum.value = 1
    hasMore.value = true
    activeStatus.value = 'ALL'
    orderTotal.value = 0
    // 立即清空订单列表，避免闪现问题
    orderList.value = []

    // 重新获取数据
    await fetchOrders()
    scrollToTop()
  } finally {
    // 隐藏页面loading
    pageLoading.value = false
  }
}

// 操作申诉的Tab下日期筛选框
export const onHandleDateFilter = () => {
  isShowDateFilter.value = !isShowDateFilter.value
}

// 用于显示的筛选的日期范围
export const showFilterDateValues = ref([])
const filterDateValues = ref([])
// 确认筛选日期事件
export const handleConfirmDate = (values) => {
  const countDay = getDaysAfter(dateStrToFormat(values[0]), dateStrToFormat(values[1]))
  if (countDay > 1) {
    uni.showToast({
      title: '最多只能选择2天',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  filterDateValues.value = values
  showFilterDateValues.value = [dateStrToFormat(values[0]), dateStrToFormat(values[1])]
}

export const onPreOrderClick = () => {
  console.log('预订单')
  // TODO: 跳转到预订单页面
  uni.navigateTo({
    url: '../preOrder/index?shopIds=' + currentStore.value.subShopIds,
  })
}

export const onSearchClick = () => {
  console.log('搜索')
  // TODO: 跳转到搜索页面
  uni.navigateTo({
    url: '/pages/order/search/index',
  })
}

export const onFilterClick = (type: 'channel' | 'store' | 'orderType' | 'date') => {
  console.log('筛选:', type)

  if (type === 'channel') {
    // 不再直接显示渠道选择器，改为触发事件或者由sy-order-filters组件处理
    console.log('渠道筛选由sy-order-filters组件处理')
  }
}

export const onStatusChange = async (status: string) => {
  console.log('状态切换:', status)

  // 显示页面loading
  pageLoading.value = true

  try {
    activeStatus.value = status

    // 在待出餐状态下隐藏时间筛选组件
    if (status === 'IN_PREPARE') {
      isNeedFilters.needDate = false
    } else {
      // 其他状态恢复时间筛选组件显示
      isNeedFilters.needDate = true
    }

    pageNum.value = 1
    hasMore.value = true
    orderTotal.value = 0
    // 立即清空订单列表，避免闪现问题
    orderList.value = []
    await fetchOrders()
  } finally {
    // 隐藏页面loading
    pageLoading.value = false
  }
}

export const onExpandClick = () => {
  console.log('展开')
  // TODO: 实现展开逻辑
}

export const onSortClick = () => {
  console.log('排序')
  // TODO: 实现排序逻辑
}

export const onLoadMore = () => {
  console.log('加载更多')
  if (!hasMore.value || loading.value) {
    console.log('没有更多数据或正在加载中', '有更多数据:', hasMore.value, '加载中:', loading.value)

    return
  }

  pageNum.value += 1
  console.log('开始加载第', pageNum.value, '页数据')
  fetchOrders()
}

export const onRefresh = async () => {
  console.log('下拉刷新')

  // 设置为加载状态
  refresherStatus.value = 'loading'

  pageNum.value = 1
  hasMore.value = true
  orderTotal.value = 0
  isRefreshing.value = true

  try {
    await fetchOrders()

    // 刷新完成，显示完成状态
    refresherStatus.value = 'completed'
    console.log('刷新完成，状态设置为 completed')

    // 1.5秒后隐藏刷新器
    setTimeout(() => {
      showCustomRefresher.value = false
      refresherTransform.value = 0
      refresherStatus.value = 'pulling'
      console.log('刷新器已隐藏，状态重置为 pulling')
    }, 1500)
  } catch (error) {
    console.error('刷新失败:', error)
    // 失败时也要隐藏刷新器
    showCustomRefresher.value = false
    refresherTransform.value = 0
    refresherStatus.value = 'pulling'
  }
}

export const onCallCustomer = (phone: string) => {
  console.log('拨打电话:', phone)
  uni.makePhoneCall({
    phoneNumber: phone,
    fail: (err) => {
      console.error('拨打电话失败:', err)
      uni.showToast({
        title: '拨打电话失败',
        icon: 'error',
      })
    },
  })
}

export const onAddressClick = (order: Order) => {
  console.log('查看地址:', order.address)
}

export const onExpandOrder = (order: Order) => {
  console.log('展开订单:', order.id)
}

// 数据加载方法
const loadOrderList = () => {
  console.log('加载订单列表')
  loading.value = true

  // TODO: 实际API调用
  setTimeout(() => {
    loading.value = false
  }, 500)
}

// 修改checkLoginStatus函数
const checkLoginStatus = () => {
  console.log('检查登录状态', useUserStore())
  const userStore = useUserStore()
  const token = userStore.userInfo?.token

  isLoggedIn.value = !!token

  if (!token) {
    console.log('用户未登录，跳转到登录页')
    uni.reLaunch({
      url: '/pages/login/detail/index',
    })
    return false
  }

  return true
}

// 获取状态栏高度
const getStatusBarHeight = () => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
}

// 获取头部区域高度
export const getHeaderHeight = () => {
  const query = uni.createSelectorQuery()
  query
    .select('.fixed-header-index')
    .boundingClientRect((data) => {
      if (data && !Array.isArray(data) && data.height) {
        headerHeight.value = data.height - statusBarHeight.value
        console.log('头部高度获取成功:', headerHeight.value)

        // 设置CSS变量，用于scroll-view的top定位
        // 头部高度已经包含了状态栏高度（padding-top），所以直接使用
        const totalHeaderHeightPx = headerHeight.value + 'rpx'

        // 在页面根元素上设置CSS变量
        try {
          // #ifndef H5
          // 在小程序环境中，我们可以通过其他方式设置
          // 这里不再使用固定值，而是使用实际获取的高度
          console.log('APP端设置CSS变量:', totalHeaderHeightPx)
          // #endif
        } catch (error) {
          console.warn('设置CSS变量失败:', error)
        }
      } else {
        console.warn('获取头部高度失败，尝试重试', data)
        // 如果获取失败，延迟重试
        setTimeout(() => {
          console.log('重试获取头部高度...')
          getHeaderHeight()
        }, 100)
      }
    })
    .exec()
}

// 新增：确保头部高度获取成功的函数
export const ensureHeaderHeight = async () => {
  return new Promise<void>((resolve) => {
    const maxRetries = 10 // 最多重试10次
    let retryCount = 0

    const tryGetHeight = () => {
      console.log(`尝试获取头部高度，第 ${retryCount + 1} 次`)

      const query = uni.createSelectorQuery()
      query
        .select('.fixed-header-index')
        .boundingClientRect((data) => {
          if (data && !Array.isArray(data) && data.height && data.height > 0) {
            headerHeight.value = data.height
            console.log('头部高度获取成功:', headerHeight.value)
            resolve()
          } else {
            retryCount++
            if (retryCount < maxRetries) {
              console.log(`第 ${retryCount} 次获取头部高度失败，继续重试...`)
              setTimeout(tryGetHeight, 200)
            } else {
              console.warn('获取头部高度失败，使用默认值')
              // 使用一个合理的默认值（根据不同平台设置）
              // #ifdef APP-PLUS
              headerHeight.value = 360 // APP端的估算高度
              // #endif
              // #ifdef MP
              headerHeight.value = 340 // 小程序端的估算高度
              // #endif
              // #ifdef H5
              headerHeight.value = 320 // H5端的估算高度
              // #endif
              // #ifndef APP-PLUS || MP || H5
              headerHeight.value = 350 // 其他平台的默认高度
              // #endif
              console.log('使用默认头部高度:', headerHeight.value)
              resolve()
            }
          }
        })
        .exec()
    }

    tryGetHeight()
  })
}

export const channelSelectorTop = ref(0)
// 方法：获取"渠道"文本容器的位置
const getDateFilterPosition = () => {
  const query = uni.createSelectorQuery()
  query
    .select('.date-filter-container')
    .boundingClientRect((res) => {
      if (res && !Array.isArray(res) && res.top) {
        // res.top 是 px 值，将其转换为 rpx
        const systemInfo = uni.getSystemInfoSync()
        const rpx = (res.top * systemInfo.windowHeight) / systemInfo.windowWidth
        channelSelectorTop.value = rpx + statusBarHeight.value + 30
      }
    })
    .exec()
}

// 初始化门店信息
export const initStoreInfo = () => {
  console.log('initStoreInfo called')
  try {
    const shopStore = useShopStore()
    console.log('shopStore', shopStore)
    console.log('hasSelectedShop', shopStore.hasSelectedShop)
    console.log('currentShop', shopStore.currentShop)

    if (shopStore.hasSelectedShop) {
      // 检查是否存在子门店列表
      const shopDtoList = (shopStore?.currentShop?.raw as any)?.shopDtoList || []
      const subShopIds = []

      // 提取所有子门店ID
      if (shopDtoList && shopDtoList.length > 0) {
        shopDtoList.forEach((shop: any) => {
          if (shop.id) {
            subShopIds.push(shop.id)
          }
        })
      }

      currentStore.value = {
        id: shopStore.currentShopId,
        code: shopStore.currentShopCode,
        name: shopStore?.currentShop?.name || '',
        address: shopStore?.currentShop?.raw?.address || '',
        avatar: '',
        // 保存子门店ID列表
        subShopIds,
      }

      console.log('当前门店及子门店ID:', currentStore.value.id, subShopIds)

      // 如果当前门店有子门店ID列表，则使用这个列表
      if (currentStore.value.subShopIds && currentStore.value.subShopIds.length > 0) {
        // 确保子门店ID列表不包含无效值
        allShopIds.value = currentStore.value.subShopIds.filter((id) => !!id)

        // 将所有门店ID存入shopIds，实现默认选中所有门店
        selectedFilters.value.shopIds = [...allShopIds.value]
        // 使用特殊标识"ALL"表示选中所有门店
        selectedFilters.value.storeCode = 'ALL'
        selectedFilters.value.storeText = '全部门店'
        console.log('初始化全部门店:', selectedFilters.value.shopIds)
      } else {
        // 确保当前门店ID有效
        if (currentStore.value.id) {
          // 否则只使用当前门店ID
          allShopIds.value = [currentStore.value.id]
          selectedFilters.value.shopIds = [currentStore.value.id]
          selectedFilters.value.storeCode = currentStore.value.id
          selectedFilters.value.storeText = currentStore.value.name
        } else {
          console.warn('当前门店ID无效，使用默认值')
          allShopIds.value = []
          selectedFilters.value.shopIds = []
          selectedFilters.value.storeCode = ''
          selectedFilters.value.storeText = '门店列表'
        }

        console.log('初始化单个门店:', selectedFilters.value.shopIds)
      }

      console.log('currentStore updated', currentStore.value)
      console.log('allShopIds updated', allShopIds.value)
      console.log('selectedFilters updated', selectedFilters.value)
    } else {
      console.log('No shop selected')
    }
  } catch (error) {
    console.error('Error in initStoreInfo:', error)
  }
}

// 筛选条件变更处理
export const onFilterChange = async (
  type: 'channel' | 'store' | 'orderType' | 'date',
  value: string | string[],
  label: string | string[],
  extraData?: any, // 添加额外数据参数
) => {
  console.log('筛选条件变更:', type, value, label, extraData)

  // 标记是否需要重新加载数据
  let shouldFetchOrders = false

  // 更新筛选条件显示文本
  switch (type) {
    case 'channel':
      // 处理渠道多选
      if (Array.isArray(value)) {
        // 确保所有的值都是字符串类型并且有效
        const channelCodes = value
          .map((v) => String(v))
          .filter((v) => v !== 'false' && v !== 'undefined' && v !== 'null')
        console.log('处理后的渠道代码:', channelCodes)

        let channelText = '渠道'

        // 如果数组为空或只有一个值且是空字符串，表示选择了"全部"
        if (channelCodes.length === 0 || (channelCodes.length === 1 && channelCodes[0] === '')) {
          channelText = '渠道'
          // 确保传递空数组，而不是包含错误值的数组
          selectedFilters.value.channelCodes = []
        }
        // 如果有多个值，显示"已选择n个渠道"
        else if (channelCodes.length > 1) {
          channelText = `已选${channelCodes.length}个渠道`
          selectedFilters.value.channelCodes = [...channelCodes] // 确保是新数组
          // 缓存多选模式选择的渠道
          selectedFilters.value.multipleChannelCodes = [...channelCodes]
        }
        // 如果只有一个值，显示渠道名称
        else if (channelCodes.length === 1) {
          // 查找对应的渠道名称
          const channelOption = channelEnum.value.find((item) => item.code === channelCodes[0])
          if (channelOption) {
            channelText = channelOption.name
          }
          selectedFilters.value.channelCodes = [...channelCodes] // 确保是新数组
          if (isNeedFilters.isMultipleChannelMode) {
            selectedFilters.value.multipleChannelCodes = [...channelCodes]
          } else {
            selectedFilters.value.singleChannelCodes = [...channelCodes]
          }
        }

        selectedFilters.value.channelText = channelText
        console.log('更新渠道筛选:', selectedFilters.value.channelCodes, channelText)

        // 标记需要重新加载数据
        shouldFetchOrders = true
      }
      break
    case 'store':
      // 处理门店多选
      if (Array.isArray(value)) {
        const shopIds = value.filter((id) => id !== 'ALL')
        console.log('选择的门店IDs (已过滤ALL):', shopIds)

        // 更新门店ID数组
        selectedFilters.value.shopIds = shopIds

        // 更新显示文本
        if (typeof label === 'string') {
          selectedFilters.value.storeText = label
          console.log('设置门店文本:', label)
        }

        // 向后兼容，保留storeCode字段
        if (shopIds.length === 1) {
          selectedFilters.value.storeCode = shopIds[0]
          console.log('单选门店:', shopIds[0])
        } else {
          selectedFilters.value.storeCode = 'ALL' // 多选或全部时使用ALL标识
          console.log('多选门店或全部门店')
        }
        // 标记需要重新加载数据
        shouldFetchOrders = true
      } else {
        // 兼容旧的单选模式
        console.log('旧的单选模式:', value, label)
        selectedFilters.value.storeText = value === '' ? '门店列表' : (label as string)
        selectedFilters.value.storeCode = value
        selectedFilters.value.shopIds = value && value !== 'ALL' ? [value] : []
        // 标记需要重新加载数据
        shouldFetchOrders = true
      }
      break
    case 'orderType':
      // 处理订单类型，支持多选逗号分隔的情况
      if (typeof value === 'string' && value.includes(',')) {
        // 如果是逗号分隔的字符串，表示多选的情况
        selectedFilters.value.orderTypeText = label as string
        // 将逗号分隔的字符串转换为数组存储到 orderTypeCodes
        selectedFilters.value.orderTypeCodes = value.split(',').filter((code) => code.trim() !== '')
        // 标记需要重新加载数据
        shouldFetchOrders = true
      } else {
        // 单选或全部的情况
        selectedFilters.value.orderTypeText =
          Array.isArray(value) || value === '' ? '订单类型' : (label as string)
        // 更新 orderTypeCodes
        if (value === '') {
          selectedFilters.value.orderTypeCodes = []
        } else {
          selectedFilters.value.orderTypeCodes = [value as string]
        }
        // 标记需要重新加载数据
        shouldFetchOrders = true
      }
      break
    case 'date':
      selectedFilters.value.dateText = Array.isArray(label) ? '今日' : (label as string)

      // 存储日期时间范围信息，如果提供了的话
      if (extraData && extraData.startPlaceTime && extraData.endPlaceTime) {
        selectedFilters.value._startPlaceTime = extraData.startPlaceTime
        selectedFilters.value._endPlaceTime = extraData.endPlaceTime
      } else {
        // 清除之前可能存在的时间范围信息
        delete selectedFilters.value._startPlaceTime
        delete selectedFilters.value._endPlaceTime
      }
      // 标记需要重新加载数据
      shouldFetchOrders = true
      break
  }

  // 只有在用户真正选择了筛选选项后才重新加载数据
  if (shouldFetchOrders) {
    // 显示页面loading
    pageLoading.value = true

    try {
      // 重置页码和加载状态并重新加载数据
      pageNum.value = 1
      hasMore.value = true
      orderTotal.value = 0
      // 立即清空订单列表，避免闪现问题
      orderList.value = []
      await fetchOrders()
    } finally {
      // 隐藏页面loading
      pageLoading.value = false
    }
  }
}

// 数据加载方法
export const loadEnums = async (isResetFilter) => {
  try {
    // 检查登录状态
    const userStore = useUserStore()
    if (!userStore.userInfo?.token) {
      console.log('用户未登录，暂不加载枚举数据')
      return
    }

    console.log('开始加载枚举数据...', {
      currentTab: activeTab.value,
      isResetFilter,
      currentFilters: JSON.stringify(selectedFilters.value),
    })

    // 根据当前tab类型决定加载哪些枚举
    if (activeTab.value === 'after-sale') {
      // 售后模式：加载售后状态枚举
      const [typeRes, channelRes, refundStatusRes] = await Promise.all([
        fetchOrderTypeEnum(),
        fetchChannelEnum(),
        fetchRefundOrderStatusEnum(),
      ])

      orderTypeEnum.value = typeRes?.data || []
      channelEnum.value = channelRes?.data || []
      // 订单页前端隐藏待餐损申述
      refundOrderStatusEnum.value =
        refundStatusRes?.data.filter((item: any) => item.code !== '50') || []

      if (isResetFilter) {
        // 默认所有渠道
        selectedFilters.value.channelCodes = channelEnum.value.map((item) => item.code)

        // 默认所有订单类型
        selectedFilters.value.orderTypeCodes = orderTypeEnum.value.map((item) => item.code)

        console.log('售后模式筛选条件已重置:', {
          channelCodes: selectedFilters.value.channelCodes,
          orderTypeCodes: selectedFilters.value.orderTypeCodes,
        })
      }
      // 更新状态标签为售后状态
      if (refundOrderStatusEnum.value && refundOrderStatusEnum.value.length > 0) {
        statusTabs.value = refundOrderStatusEnum.value.map((item) => ({
          id: item.code,
          name: item.name,
          count: 0,
          remark: null,
        }))
      }

      console.log('售后枚举数据加载完成', {
        orderTypeEnum: orderTypeEnum.value,
        channelEnum: channelEnum.value,
        refundOrderStatusEnum: refundOrderStatusEnum.value,
        statusTabs: statusTabs.value,
      })
    }

    if (activeTab.value === 'order') {
      // 订单模式：加载订单状态枚举
      const [typeRes, channelRes, statusRes] = await Promise.all([
        fetchOrderTypeEnum(),
        fetchChannelEnum(),
        fetchOrderStatusEnum(),
      ])

      orderTypeEnum.value = typeRes?.data || []
      channelEnum.value = channelRes?.data || []
      orderStatusEnum.value = statusRes?.data || []

      if (isResetFilter) {
        // 默认所有渠道
        selectedFilters.value.channelCodes = channelEnum.value.map((item) => item.code)

        // 默认所有订单类型
        selectedFilters.value.orderTypeCodes = orderTypeEnum.value.map((item) => item.code)

        console.log('订单模式筛选条件已重置:', {
          channelCodes: selectedFilters.value.channelCodes,
          orderTypeCodes: selectedFilters.value.orderTypeCodes,
        })
      }
      // 更新状态标签为订单状态
      if (orderStatusEnum.value && orderStatusEnum.value.length > 0) {
        statusTabs.value = orderStatusEnum.value.map((item) => ({
          id: item.code,
          name: item.name,
          count: 0,
          remark: null,
        }))
      }

      console.log('订单枚举数据加载完成', {
        orderTypeEnum: orderTypeEnum.value,
        channelEnum: channelEnum.value,
        orderStatusEnum: orderStatusEnum.value,
        statusTabs: statusTabs.value,
      })
    }

    if (activeTab.value === 'damage-appeal') {
      // 申诉模式：加载申诉相关枚举
      const [channelRes] = await Promise.all([fetchChannelEnum()])
      channelEnum.value = channelRes?.data || []

      if (isResetFilter) {
        // 默认所有渠道
        selectedFilters.value.channelCodes = channelEnum.value.map((item) => item.code)

        console.log('申诉模式筛选条件已重置:', {
          channelCodes: selectedFilters.value.channelCodes,
        })
      }

      // 加载申诉页的tab枚举
      statusTabs.value = [
        {
          id: 'ALL',
          name: '待餐损申诉',
          count: 0,
          remark: null,
        },
        {
          id: 'appealed',
          name: '已餐损申诉',
          count: 0,
          remark: null,
        },
      ]
    }

    console.log('枚举数据加载完成后的最终筛选条件:', {
      activeTab: activeTab.value,
      channelCodes: selectedFilters.value.channelCodes,
      orderTypeCodes: selectedFilters.value.orderTypeCodes,
      shopIds: selectedFilters.value.shopIds,
    })
  } catch (error) {
    console.error('加载枚举数据失败', error)
  }
}

function getDateRange(dateText: string) {
  // 首先检查selectedFilters中是否有存储的时间范围
  if (selectedFilters.value._startPlaceTime && selectedFilters.value._endPlaceTime) {
    return {
      startPlaceTime: selectedFilters.value._startPlaceTime,
      endPlaceTime: selectedFilters.value._endPlaceTime,
    }
  }

  // 如果没有存储的时间范围，则根据dateText生成
  const now = new Date()
  const start = new Date(now)
  const end = new Date(now)

  // 检查是否是特定日期格式（如：3月26日）
  const dateMatch = dateText.match(/(\d+)月(\d+)日/)
  if (dateMatch) {
    const month = parseInt(dateMatch[1]) - 1 // 月份从0开始
    const day = parseInt(dateMatch[2])

    // 设置年份（假设是当前年份）
    const year = now.getFullYear()

    // 创建特定日期
    start.setFullYear(year, month, day)
    start.setHours(0, 0, 0, 0)

    end.setFullYear(year, month, day)
    end.setHours(23, 59, 59, 999)

    // 使用简单日期格式，与SyDateSelector组件保持一致
    return {
      startPlaceTime: formatSimpleDate(start, true),
      endPlaceTime: formatSimpleDate(end, false),
    }
  }

  // 处理预定义的日期范围
  if (dateText === '今日') {
    start.setHours(0, 0, 0, 0)
    end.setHours(23, 59, 59, 999)
  } else if (dateText === '近7天') {
    start.setDate(now.getDate() - 6)
    start.setHours(0, 0, 0, 0)
    end.setHours(23, 59, 59, 999)
  } else if (dateText === '近30天') {
    start.setDate(now.getDate() - 29)
    start.setHours(0, 0, 0, 0)
    end.setHours(23, 59, 59, 999)
  }

  // 使用简单日期格式，与SyDateSelector组件保持一致
  return {
    startPlaceTime: formatSimpleDate(start, true),
    endPlaceTime: formatSimpleDate(end, false),
  }
}

// 格式化日期为简单字符串格式 YYYY-MM-DD HH:mm:ss
function formatSimpleDate(date: Date, isStart: boolean): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const time = isStart ? '00:00:00' : '23:59:59'
  return `${year}-${month}-${day} ${time}`
}

// 添加一个辅助函数来检查值是否为空
const isEmptyValue = (value: any): boolean => {
  if (value === null || value === undefined || value === '') {
    return true
  }
  if (Array.isArray(value) && value.length === 0) {
    return true
  }
  if (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0) {
    return true
  }
  return false
}

/**
 * 判断订单是否符合当前筛选条件
 * @param order 订单数据
 * @returns 是否符合条件
 */
const isOrderMatchFilter = (order: any): boolean => {
  // 无效订单数据直接返回不匹配
  if (!order) return false

  // 1. 检查订单状态筛选
  if (activeStatus.value !== 'ALL' && order.orderStatus !== activeStatus.value) {
    console.log('订单状态不匹配:', {
      orderStatus: order.orderStatus,
      currentFilter: activeStatus.value,
    })
    return false
  }

  // 2. 检查渠道筛选
  if (selectedFilters.value.channelCodes && selectedFilters.value.channelCodes.length > 0) {
    const orderChannelCode = order.saleChannel || order.channelCode
    if (!orderChannelCode || !selectedFilters.value.channelCodes.includes(orderChannelCode)) {
      console.log('订单渠道不匹配:', {
        orderChannel: orderChannelCode,
        selectedChannels: selectedFilters.value.channelCodes,
      })
      return false
    }
  }

  // 3. 检查门店筛选
  if (selectedFilters.value.shopIds && selectedFilters.value.shopIds.length > 0) {
    const orderShopId = order.shopId
    if (!orderShopId || !selectedFilters.value.shopIds.includes(orderShopId)) {
      console.log('订单门店不匹配:', {
        orderShopId,
        selectedShops: selectedFilters.value.shopIds,
      })
      return false
    }
  }

  // 4. 检查订单类型筛选
  if (selectedFilters.value.orderTypeCodes && selectedFilters.value.orderTypeCodes.length > 0) {
    const orderBizType = order.bizType
    if (!orderBizType || !selectedFilters.value.orderTypeCodes.includes(orderBizType)) {
      console.log('订单类型不匹配:', {
        orderBizType,
        selectedTypes: selectedFilters.value.orderTypeCodes,
      })
      return false
    }
  }

  // 5. 检查时间范围筛选
  if (selectedFilters.value._startPlaceTime && selectedFilters.value._endPlaceTime) {
    const orderTime = order.placeTime || order.createTime
    if (!orderTime) {
      console.log('订单缺少时间信息')
      return false
    }

    const startTime = new Date(selectedFilters.value._startPlaceTime).getTime()
    const endTime = new Date(selectedFilters.value._endPlaceTime).getTime()
    const orderDateTime = new Date(orderTime).getTime()

    if (orderDateTime < startTime || orderDateTime > endTime) {
      console.log('订单时间不在筛选范围内:', {
        orderTime,
        startTime: selectedFilters.value._startPlaceTime,
        endTime: selectedFilters.value._endPlaceTime,
      })
      return false
    }
  }

  // 所有条件都满足
  return true
}

// 新增：统一的订单匹配函数
export const isSameOrder = (order1: any, order2: any): boolean => {
  if (!order1 || !order2) return false

  const apiData1 = order1.apiData || order1
  const apiData2 = order2.apiData || order2

  if (!apiData1 || !apiData2) return false

  // 使用多个字段进行匹配，确保去重准确性
  const matches = [
    // 主要字段匹配
    apiData1.id && apiData2.id && apiData1.id === apiData2.id,
    apiData1.extlOrderSerial &&
      apiData2.extlOrderSerial &&
      apiData1.extlOrderSerial === apiData2.extlOrderSerial,
    apiData1.channelOrderNo &&
      apiData2.channelOrderNo &&
      apiData1.channelOrderNo === apiData2.channelOrderNo,
    apiData1.thirdOrderCode &&
      apiData2.thirdOrderCode &&
      apiData1.thirdOrderCode === apiData2.thirdOrderCode,
    apiData1.orderNo && apiData2.orderNo && apiData1.orderNo === apiData2.orderNo,
    apiData1.tradeNo && apiData2.tradeNo && apiData1.tradeNo === apiData2.tradeNo,
  ]

  return matches.some((match) => match === true)
}

/**
 * 检查订单是否已存在于列表中
 */
export const isOrderExistsInList = (targetOrder: any): number => {
  return orderList.value.findIndex((existingOrder) => {
    return isSameOrder(existingOrder, targetOrder)
  })
}

export const fetchOrders = async () => {
  try {
    // 检查登录状态
    const userStore = useUserStore()
    if (!userStore.userInfo?.token) {
      console.log('用户未登录，暂不获取订单数据')
      return
    }

    // 确保有门店ID
    if (!currentStore.value.id) {
      console.log('未选择门店，尝试初始化门店信息')
      initStoreInfo()

      // 如果初始化后仍然没有门店ID，显示提示并返回
      if (!currentStore.value.id) {
        console.error('无法获取门店ID，请先选择门店')
        uni.showToast({
          title: '请先选择门店',
          icon: 'none',
        })
        return
      }
    }

    const { startPlaceTime, endPlaceTime } = getDateRange(selectedFilters.value.dateText)

    // 处理渠道代码
    let channelCodes
    if (selectedFilters.value.channelCodes && selectedFilters.value.channelCodes.length > 0) {
      // 确保所有值都是字符串且有效
      channelCodes = selectedFilters.value.channelCodes
        .map((code) => String(code))
        .filter(
          (code) => code !== 'false' && code !== 'undefined' && code !== 'null' && code !== '',
        )

      // 记录一下当前处理的渠道值
      console.log('处理前的渠道代码:', selectedFilters.value.channelCodes)
      console.log('处理后的渠道代码:', channelCodes)

      // 如果过滤后数组为空，则设为undefined
      if (channelCodes.length === 0) {
        channelCodes = undefined
      }
    } else {
      channelCodes = undefined
    }

    console.log('最终处理后的渠道代码:', channelCodes)

    // 处理门店ID
    const shopIds = selectedFilters.value.shopIds

    // 打印原始的门店ID数组，用于调试
    console.log('原始门店ID:', shopIds)

    // 过滤掉空字符串、'false'、'undefined'、'null'、'ALL'等无效值
    // if (Array.isArray(shopIds)) {
    //   shopIds = shopIds.filter(
    //     (id) =>
    //       id && id !== '' && id !== 'false' && id !== 'undefined' && id !== 'null' && id !== 'ALL',
    //   )

    //   // 如果过滤后数组为空，则使用当前门店ID或默认门店ID列表
    //   if (shopIds.length === 0) {
    //     console.log('门店ID数组为空，使用默认门店ID')
    //     shopIds = allShopIds.value.length > 0 ? allShopIds.value : [currentStore.value.id]
    //   }
    // } else {
    //   // 如果不是数组，确保使用默认门店ID列表
    //   shopIds = allShopIds.value.length > 0 ? allShopIds.value : [currentStore.value.id]
    // }

    console.log('处理后的门店ID:', shopIds)

    // 构建基础参数对象
    const params: Partial<QueryOrderParams> = {}

    // 只添加非空值参数
    // 门店IDs - 这是必填项，即使为空也会使用默认值
    if (!isEmptyValue(shopIds)) {
      params.shopIds = shopIds

      // 同时传递对应的shopCodes参数
      const shopStore = useShopStore()
      // 获取shopIds对应的shopCodes
      const shopCodes = shopIds
        .map((id) => {
          // 如果是当前店铺ID，直接使用当前店铺编码
          if (id === shopStore.currentShopId && shopStore.currentShop?.raw?.code) {
            return shopStore.currentShop.raw.code
          }
          // 如果是子门店ID，需要从子门店列表中查找对应编码
          const subShop = (shopStore.currentShop?.raw as any)?.shopDtoList?.find(
            (shop: any) => shop.id === id,
          )
          return subShop?.code || ''
        })
        .filter((code) => code !== '') // 过滤掉空值

      if (shopCodes.length > 0) {
        params.shopCodes = shopCodes
      }
    } else {
      uni.showToast({
        title: '请先选择门店',
        icon: 'none',
      })
    }

    // 渠道代码 - 只有非空时才添加
    if (!isEmptyValue(channelCodes)) {
      params.channelCodes = channelCodes
    }

    // 订单状态 - 只有不是"全部"状态且非空时才添加
    if (activeStatus.value !== 'ALL' && !isEmptyValue(activeStatus.value)) {
      if (activeTab.value === 'after-sale') {
        params.refundOrderStatus = activeStatus.value
      }
      if (activeTab.value === 'order') {
        params.orderStatus = activeStatus.value
      }
    }

    // 如果选择的是申诉Tab页
    if (activeTab.value === 'damage-appeal') {
      params.appealStatus = activeStatus.value === 'ALL' ? 'notAppealed' : activeStatus.value

      // 筛选日期不为空才添加
      if (!isEmptyValue(filterDateValues.value)) {
        params.startAppealTime = filterDateValues.value[0] + ' 00:00:00'
        params.endAppealTime = filterDateValues.value[1] + ' 23:59:59'
      }
    }

    // 日期范围 - 只有非申诉且非空时才添加
    if (activeTab.value !== 'damage-appeal') {
      // 订单类型 - 只有非空时才添加
      if (!isEmptyValue(selectedFilters.value.orderTypeCodes)) {
        params.bizTypes = selectedFilters.value.orderTypeCodes
      }

      if (!isEmptyValue(startPlaceTime)) {
        params.startPlaceTime = startPlaceTime
      }

      if (!isEmptyValue(endPlaceTime)) {
        params.endPlaceTime = endPlaceTime
      }
    }

    // if (activeTab.value === 'order') {
    //   params.isBook = 1
    // }

    // 售后模式下增加 isRefundOrder 参数
    if (activeTab.value === 'after-sale') {
      params.isRefundOrder = 1
    }

    // 排序和分页 - 始终添加这些基本参数
    params.orderByPlaceTimeDesc = true
    params.pageNum = pageNum.value
    if (activeStatus.value === 'IN_PREPARE') {
      params.pageSize = waitPageSize.value
    } else {
      params.pageSize = pageSize.value
    }

    let res = { data: { list: [] } }

    loading.value = true
    // 餐损申诉请求订单列表
    if (activeTab.value === 'damage-appeal') {
      res = (await fetchDamageOrderList(params as QueryOrderParams)) as unknown as {
        data: OrderListResponse
      }
    } else {
      // 订单、售后请求订单数据
      res = (await fetchOrderList(params as QueryOrderParams)) as unknown as {
        data: OrderListResponse
      }
    }

    // 直接使用后端返回的数据
    const orderData = res.data?.list || []

    // 如果是第一页且没有数据，直接清空列表
    if (pageNum.value === 1 && orderData.length === 0) {
      orderList.value = []
      hasMore.value = false
      console.log('接口返回空数据，清空订单列表')
      loading.value = false
      isRefreshing.value = false
      return
    }

    // 转换数据
    const newOrders = orderData.map((item) => ({
      extlChannel: item.extlChannel || item.saleChannel || '',
      id: item.id || '',
      orderNumber: item.orderNo || item.tradeNo || '',
      status: item.orderStatus || '',
      type: item.bizTypeName || '',
      instanceId: item.instanceId || '',
      tenantId: item.tenantId || '',
      bizType: item.bizType || '',
      deliveryTime: item.sendTime ? item.sendTime.substring(0, 16) : '',
      createTime: item.createTime || item.placeTime || '',
      sendTime: item.sendTime ? item.sendTime.substring(0, 16) : '',
      merchant: {
        id: item.shopId || '',
        name: item.shopName || '',
        avatar: '/static/images/img/mt.png',
      },
      customer: {
        name: item.contactName || (item.customerInfo ? item.customerInfo.userName : ''),
        phone: item.phone
          ? item.phone.substring(item.phone.length - 4)
          : item.hiddenPhone
            ? item.hiddenPhone.substring(item.hiddenPhone.length - 4)
            : '',
        fullPhone: item.phone || item.reservedPhone || '',
      },
      address: {
        full: (() => {
          try {
            if (item.extlOrderDetail) {
              const detail = JSON.parse(item.extlOrderDetail)
              return detail.syncThirdOrderReqDto?.address || item.deliveryAddress || ''
            }
          } catch (e) {
            console.error('解析地址信息失败:', e)
          }
          return item.deliveryAddress || ''
        })(),
        distance: '<1km',
      },
      warning:
        item.orderStatus === 'PAYED' || item.orderStatus === '20'
          ? {
              text: '订单即将超时',
              countdown: {
                minutes: '05',
                seconds: '00',
              },
            }
          : undefined,
      statusInfo: {
        main: (() => {
          switch (item.orderStatus) {
            case '10':
              return '待接单'
            case '20':
              return '待出餐'
            case 'WAIT_REPORT_MEALS':
              return '待出餐'
            case 'WAIT_CELL_DELIVERY':
              return '待发配送'
            case 'WAIT_RIDER_ACCEPT':
              return '骑手待接单'
            case 'INSHOP':
              return '待处理'
            case 'WAIT_MERCHANT_CONFIRM':
              return '待商家确认'
            case 'CONFIRM':
              return '已接单'
            case 'COMPLETED':
              return '已送达'
            case '50':
              return '配送中'
            case '80':
              return '已完成'
            case 'RIDER_ACCEPT':
              return '骑手已接单'
            case 'RIDER_ARRIVE_SHOP':
              return '骑手已到店'
            case 'DELIVERING':
              return '配送中'
            case 'DELIVERING_OVERTIME':
              return '配送超时'
            case 'OVERTIME':
              return '超时未送达'
            case '60':
              return '已取餐'
            case 'TAKED':
              return '已取餐'
            case 'FINISHED':
              return '已完成'
            case 'ARRIVED':
              return '已送达'
            case '-10':
              return '已取消'
            case '-20':
              return '已取消'
            case 'CANCELED':
              return '已取消'
            case 'CLOSED':
              return '已关闭'
            default:
              return item.bizStatusDesc || item.statusDesc || '未知状态'
          }
        })(),
        time: item.placeTime ? item.placeTime.substring(11, 16) : '',
        description: '',
      },
      channel: item.saleChannelDesc || '',
      items: item.items || [],
      amount: item.totalAmount || '0',
      fees: {
        merchantIncome: parseFloat(item.incomeAmount || '0'),
        deliveryFee: parseFloat(item.deliveryFee || '0'),
        packagingFee: parseFloat(item.packageAmount || '0'),
        totalAmount: parseFloat(item.totalAmount || '0'),
      },
      // 保存原始API数据，方便上报出餐等功能使用
      apiData: item,
    }))

    // 修改这里：根据页码决定是替换还是追加数据
    if (pageNum.value === 1) {
      orderList.value = newOrders
    } else {
      // 使用统一的去重逻辑
      const filteredNewOrders = newOrders.filter((newOrder) => {
        const existingIndex = isOrderExistsInList(newOrder)
        return existingIndex === -1
      })

      console.log(
        `订单去重结果: 新增${newOrders.length}个，过滤后${filteredNewOrders.length}个，去重${newOrders.length - filteredNewOrders.length}个`,
      )

      orderList.value = [...orderList.value, ...filteredNewOrders]
    }

    // 使用total和已加载的数据量来判断是否有更多数据
    const total = (res.data as any)?.total || 0
    const loadedCount = orderList.value.length
    hasMore.value = loadedCount < total

    // 更新后端返回的订单总数
    orderTotal.value = total

    // 计算总页数

    let totalPages = 0
    if (activeStatus.value === 'IN_PREPARE') {
      totalPages = Math.ceil(total / waitPageSize.value)
    } else {
      totalPages = Math.ceil(total / pageSize.value)
    }
    const currentPage = pageNum.value

    console.log(
      '获取订单列表成功',
      orderList.value.length,
      '总数据量:',
      total,
      '当前页:',
      currentPage,
      '总页数:',
      totalPages,
    )
  } catch (error) {
    console.error('获取订单列表失败:', error)
    // 获取失败时也要重置刷新状态
    if (refresherStatus.value === 'loading') {
      showCustomRefresher.value = false
      refresherTransform.value = 0
      refresherStatus.value = 'pulling'
    }
  } finally {
    loading.value = false
    // 只有在不是刷新完成状态时才设置 isRefreshing 为 false
    // 这样可以避免过早隐藏刷新完成状态
    if (refresherStatus.value !== 'completed') {
      isRefreshing.value = false
    } else {
      // 刷新完成状态下，延迟设置 isRefreshing 为 false
      setTimeout(() => {
        isRefreshing.value = false
      }, 1500)
    }
  }
}

// 数据转换函数：将现有订单数据转换为新组件格式
export const transformOrderData = (order: Order, isAfterSaleMode?: boolean) => {
  if (!order) return {} as any

  // 如果订单对象中已经包含完整的原始API数据，则直接使用新的转换函数
  if (order.apiData) {
    const transformedData = apiTransformOrderData(order.apiData)

    // 如果是售后模式，确保售后数据正确处理
    if (isAfterSaleMode && order.apiData.refundRecords) {
      // 确保售后数据是数组格式
      transformedData.afterSale = Array.isArray(order.apiData.refundRecords)
        ? order.apiData.refundRecords
        : [order.apiData.refundRecords]
    }

    return transformedData
  }

  // 否则使用旧的转换逻辑，将Order格式转换为OrderData格式
  // 尝试解析扩展字段的地址信息
  let address = order.address.full

  try {
    if (order.address && order.address.full && order.address.full.includes('extlOrderDetail')) {
      const extlOrderDetail = JSON.parse(order.address.full)
      if (extlOrderDetail.syncThirdOrderReqDto) {
        address = extlOrderDetail.syncThirdOrderReqDto.address || ''
      }
    }
  } catch (e) {
    console.error('解析地址信息失败:', e)
  }

  // 返回符合组件期望的格式
  return {
    extlChannel: order.extlChannel,
    id: order.id,
    orderNo: order.orderNumber,
    deliveryTime: order.deliveryTime,
    sendTime: order.sendTime,
    status: order.statusInfo.main,
    type: order.type,
    tenantId: order.tenantId,
    instanceId: order.instanceId,
    bizType: order.apiData.bizType,
    isBook: order.apiData.isBook,
    saleChannel: order.saleChannel,
    buyerRemark: order.buyerRemark,
    merchantConfirmTime: order.merchantConfirmTime,
    merchant: {
      avatar: order.merchant.avatar || '/static/images/img/mt.png',
      name: order.merchant.name,
      id: order.merchant.id,
    },
    customer: {
      name: order.customer.name,
      phone: order.customer.fullPhone,
    },
    address: {
      detail: address,
      distance: order.address.distance || '<1km',
    },

    orderStatus: {
      text: order.statusInfo.main,
      time: order.statusInfo.time || '',
      description: order.buyerRemark || order.statusInfo.description || '',
    },
    delivery: {
      method: '外卖配送',
      time: order.deliveryTime ? `预计${order.deliveryTime.substring(11, 16)}送达` : '',
      platform: order.channel || '',
    },
    goods: {
      count: order.items?.length || 1,
      summary: order.items?.length > 0 ? order.items[0].name : '商品',
      items: order.items || [],
    },
    fees: {
      merchantIncome: order.fees?.merchantIncome || 0,
      deliveryFee: order.fees?.deliveryFee || 0,
      packagingFee: order.fees?.packagingFee || 0,
      totalAmount: order.fees?.totalAmount || 0,
    },
    times: {
      createTime: order.createTime || '',
      placeTime: order.createTime || '',
      deliveryTime: order.deliveryTime || '',
    },
  }
}

// 新的事件处理函数
export const onOrderAction = async (orderData: any, action: string) => {
  console.log('订单操作:', action, orderData)
  switch (action) {
    case 'accept':
      console.log('接单')
      try {
        // 获取渠道单号、订单渠道、门店ID和租户ID
        const thirdOrderCode = orderData?.channelOrderNo
        const channelCode = orderData?.saleChannel
        const shopId = orderData?.merchant?.id
        const tenantId = orderData?.tenantId

        if (!thirdOrderCode || !channelCode || !shopId || !tenantId) {
          uni.showToast({
            title: '订单信息不完整，无法接单',
            icon: 'none',
          })
          return
        }

        // 显示加载提示
        uni.showLoading({
          title: '接单中...',
          mask: true,
        })

        // 调用确认接单接口
        const result = await confirmOrder({
          thirdOrderCode,
          channelCode,
          shopId,
          tenantId,
        })

        if (result && result.resultCode === '0') {
          uni.showToast({
            title: '接单成功',
            icon: 'success',
          })
          // 刷新订单列表
          await fetchOrders()
        } else {
          uni.showToast({
            title: result?.resultMsg || '接单失败',
            icon: 'none',
          })
        }
      } catch (error) {
        console.error('接单失败:', error)
        uni.showToast({
          title: '接单失败，请稍后重试',
          icon: 'none',
        })
      } finally {
        // 隐藏加载提示
        uni.hideLoading()
      }
      break
    case 'report':
      console.log('上报出餐')
      try {
        // 获取渠道单号和订单渠道
        const thirdOrderCode = orderData?.channelOrderNo
        const orderChannel = orderData?.saleChannel

        if (!thirdOrderCode || !orderChannel) {
          uni.showToast({
            title: '订单信息不完整，无法上报出餐',
            icon: 'none',
          })
          return
        }

        // 调用上报出餐接口
        const result = await reportMeals({
          thirdOrderCode,
          orderChannel,
        })
        console.log('result', result)

        if (result && result.resultCode === '0') {
          uni.showToast({
            title: '上报出餐成功',
            icon: 'success',
          })
          // 刷新订单列表
          await fetchOrders()
        } else {
          uni.showToast({
            title: result?.resultMsg || '上报出餐失败',
            icon: 'none',
          })
        }
      } catch (error) {
        console.error('上报出餐失败:', error)
        uni.showToast({
          title: '上报出餐失败，请稍后重试',
          icon: 'none',
        })
      }
      break
    case 'finish':
      console.log('出餐完成')
      break
    case 'refund':
      console.log('退款')
      break
    case 'cancel':
      console.log('取消订单')
      break
    default:
      console.log('未知操作:', action)
  }
}

export const onOrderCardClick = (orderData: any) => {
  console.log('订单卡片点击:', orderData)
  // TODO: 跳转到订单详情页面
}

export const onOrderExpandToggle = (expanded: boolean) => {
  console.log('订单展开状态:', expanded)
  isOrderCardsExpanded.value = expanded
}

// 生命周期相关的初始化函数（供Vue组件调用）
export const initPage = async () => {
  console.log('订单列表页面 - initPage')
  getStatusBarHeight()
  // 检查登录状态
  if (!checkLoginStatus()) {
    console.log('用户未登录，不加载数据')
    isLoggedIn.value = false
    return
  }

  // 设置登录状态为true
  isLoggedIn.value = true

  // 显示页面级loading - 初始化时渲染订单列表
  pageLoading.value = true

  try {
    // 先获取门店信息
    initStoreInfo()

    // 默认设置门店为收起状态
    isStoreExpanded.value = false

    // 加载枚举数据
    await loadEnums(true)

    // 确保选择了"全部"状态
    activeStatus.value = 'ALL'

    // 确保页码从1开始
    pageNum.value = 1

    // 获取订单列表
    await fetchOrders()

    // 确保头部高度获取成功（使用新的方法）
    await ensureHeaderHeight()

    // 确保 MQTT 连接
    await ensureMqttConnection()
  } catch (error) {
    console.error('初始化页面失败:', error)
  } finally {
    // 隐藏页面级loading
    pageLoading.value = false
  }
}

export const handlePageShow = async () => {
  console.log('订单列表页面 - handlePageShow')

  // 确保首页 tabbar 高亮状态正确
  try {
    const { tabbarStore } = await import('@/components/sy-tabbar/sy-tabbar')
    tabbarStore.resetToFirst() // 重置为第一个 tabbar 页面
    console.log('[Index Page Show] tabbar 高亮状态已重置为第一个页面')
  } catch (e) {
    console.error('[Index Page Show] 重置 tabbar 高亮状态失败:', e)
  }

  // 商家主动退提交返回 - 刷新订单列表
  watchStorageKey<any>(
    'refresh_order_list',
    (value) => {
      if (value === 'refresh') {
        console.log('商家主动退提交返回 - 刷新订单列表')
        fetchOrders()
      }
    },
    3000,
    1000,
  )
  // 检查登录状态
  // if (!checkLoginStatus()) {
  //   console.log('用户未登录，不加载数据')
  //   isLoggedIn.value = false
  //   return
  // }

  // // 设置登录状态为true
  // isLoggedIn.value = true

  // // 显示页面级loading - 门店切换时重新渲染订单列表
  // pageLoading.value = true

  try {
    // 更新门店信息
    // initStoreInfo()

    // // 重置订单总数，防止显示上一个门店的数据
    // orderTotal.value = 0

    // // 立即清空订单列表，避免显示上一个门店的数据
    // orderList.value = []

    // // 直接获取订单列表
    // await fetchOrders()

    // 确保 MQTT 连接（关键改动）
    try {
      const connected = await ensureMqttConnection()
      if (connected) {
        console.log('页面显示时MQTT连接成功')
      } else {
        console.warn('页面显示时MQTT连接失败')
      }
    } catch (error) {
      console.error('页面显示时确保MQTT连接失败:', error)
    }
    // 检测通知权限
    setTimeout(() => {
      checkNotificationPermission()
    }, 500)
  } catch (error) {
    console.error('页面显示时数据加载失败:', error)
  } finally {
    // 隐藏页面级loading
    pageLoading.value = false
  }
}

// 处理渠道数据，添加图标
export function processChannelData(channels: any[]): any[] {
  if (!channels || !Array.isArray(channels)) return []

  return channels.map((channel) => {
    // 使用智能匹配获取图标
    return {
      ...channel,
      icon: getChannelIcon(channel),
    }
  })
}

// 订单列表数据
export const orderList = ref<Order[]>([])

// 后端返回的订单总数
export const orderTotal = ref(0)

/**
 * 处理订单统计信息展开/收起
 */
export const onStatsExpand = (expanded: boolean) => {
  console.log('统计信息展开状态变更前:', expanded, JSON.stringify(orderCardExpandedMap.value))

  // 先更新统计信息的展开状态
  isStatsExpanded.value = expanded
  isOrderCardsExpanded.value = expanded

  // 确保 orderList 存在
  if (orderList.value && orderList.value.length > 0) {
    // 创建一个新对象来更新 orderCardExpandedMap，确保触发响应式更新
    const newMap = { ...orderCardExpandedMap.value }

    // 同步更新所有订单卡片的展开状态
    orderList.value.forEach((order) => {
      if (order && order.id) {
        newMap[order.id] = expanded
      }
    })

    // 一次性更新整个对象，确保触发响应式更新
    orderCardExpandedMap.value = newMap
  }

  console.log('统计信息展开状态变更后:', expanded, JSON.stringify(orderCardExpandedMap.value))
}

/**
 * 处理单个订单卡片展开/收起
 */
export const onOrderCardExpand = (orderId: string, expanded: boolean) => {
  orderCardExpandedMap.value[orderId] = expanded
}

// 订单统计信息
export const orderStats = computed(() => {
  const total = orderTotal.value
  const processing = orderList.value.filter((order) =>
    ['20', 'WAIT_REPORT_MEALS', 'WAIT_CELL_DELIVERY', 'WAIT_RIDER_ACCEPT', '80', '60'].includes(
      order.status,
    ),
  ).length
  const cancelled = orderList.value.filter((order) => order.status === '-10').length

  return {
    totalCount: total,
    processingCount: processing,
    cancelledCount: cancelled,
  }
})

// 添加显示模式控制变量
export const showAsMiniCard = ref(true)

/**
 * 新订单提示弹窗配置
 */
export const newOrderToast = ref<ToastOptions>({
  visible: false,
  channelText: '',
  orderNo: '',
  duration: 3000,
  autoClose: true,
})

/**
 * 处理新订单提示弹窗关闭
 */
export const onNewOrderToastClose = (orderNo?: string) => {
  console.log('新订单提示关闭', {
    orderNo,
    currentTab: activeTab.value,
    toastVisible: newOrderToast.value.visible,
    timestamp: new Date().toISOString(),
  })
  newOrderToast.value.visible = false
}

/**
 * 切换显示模式（小卡片/普通卡片）
 */
export const toggleDisplayMode = () => {
  showAsMiniCard.value = !showAsMiniCard.value
}

/**
 * 处理订单卡片操作结果
 */
export const onOperationResult = async (result: {
  action: string
  success: boolean
  message: string
}) => {
  console.log('订单操作结果:', result)

  // 如果操作成功，刷新订单列表
  if (result.success) {
    // 根据操作类型处理不同的逻辑
    switch (result.action) {
      case 'accept':
      case 'report':
        // 接单和上报出餐成功后刷新订单列表
        await fetchOrders()
        break
      case 'finish':
        // 出餐完成处理
        console.log('出餐完成')
        break
      case 'refund':
        // 退款处理
        console.log('退款')
        break
      case 'after-sale-approve':
        // 售后同意处理
        console.log('售后同意')
        // await fetchOrders()
        break
      case 'after-sale-reject':
        // 售后拒绝处理
        console.log('售后拒绝')
        // await fetchOrders()
        break
      default:
        console.log('未知操作:', result.action)
    }
  }
}

/**
 * 售后同意处理
 */
export const onAfterSaleApprove = async (
  tradeNo: string,
  instanceId?: string,
  tenantId?: string,
) => {
  console.log('售后同意:', tradeNo, '实例ID:', instanceId, '租户ID:', tenantId)

  // 显示确认弹窗，并保存instanceId和tenantId
  showApprovePopup(tradeNo, instanceId, tenantId)
}

/**
 * 售后拒绝处理
 */
export const onAfterSaleReject = async (
  tradeNo: string,
  instanceId?: string,
  tenantId?: string,
) => {
  console.log('售后拒绝:', tradeNo, '实例ID:', instanceId, '租户ID:', tenantId)

  // 显示确认弹窗，并保存instanceId和tenantId
  showRejectPopup(tradeNo, instanceId, tenantId)
}

/**
 * 退款
 */
export const onRefundOrder = async (thirdOrderCode: string) => {
  console.log('点击退款按钮:', thirdOrderCode)

  // 跳转到取消订单页面，并传递订单编号参数
  uni.navigateTo({
    url: `/pages/order/cancelOrder/index?thirdOrderCode=${thirdOrderCode}`,
  })
}

// ==================== MQTT 相关功能 ====================

// MQTT 相关状态
let mqttInstance: ReturnType<typeof useMqtt> | null = null
export const mqttConnected = ref(false)
export const mqttConnecting = ref(false)
export const mqttReconnectAttempts = ref(0)

/**
 * 清理 MQTT 连接
 */
export const cleanupMqtt = () => {
  if (mqttInstance) {
    try {
      mqttInstance.disconnect()
      mqttInstance = null
      mqttConnected.value = false
      mqttConnecting.value = false
      mqttReconnectAttempts.value = 0
      console.log('MQTT 连接已清理')
    } catch (error) {
      console.error('清理 MQTT 连接失败:', error)
    }
  }
}

/**
 * 检查MQTT连接状态
 */
export const checkMqttConnection = () => {
  console.log('检查MQTT连接状态:', {
    mqttInstance: !!mqttInstance,
    isConnected: mqttConnected.value,
    isConnecting: mqttConnecting.value,
  })

  // 如果没有实例或者已断开连接，返回false
  if (!mqttInstance || !mqttConnected.value) {
    return false
  }

  return true
}

/**
 * 确保MQTT连接（带重试机制）
 */
export const ensureMqttConnection = async () => {
  const shopStore = useShopStore()

  // 检查门店代码
  if (!shopStore.currentShop?.raw?.code) {
    console.warn('当前店铺代码为空，无法初始化 MQTT')
    return false
  }

  console.log('确保MQTT连接...')

  try {
    // 检查当前连接状态
    if (checkMqttConnection()) {
      console.log('MQTT已连接，无需重连')
      return true
    }

    // 如果正在连接中，等待连接完成
    if (mqttConnecting.value) {
      console.log('MQTT正在连接中，等待连接完成...')

      // 等待连接完成或超时（最多等待10秒）
      let attempts = 0
      const maxAttempts = 20 // 10秒，每500ms检查一次

      while (mqttConnecting.value && attempts < maxAttempts) {
        await new Promise((resolve) => setTimeout(resolve, 500))
        attempts++
      }

      // 检查连接结果
      if (mqttConnected.value) {
        console.log('MQTT连接等待成功')
        return true
      } else {
        console.log('MQTT连接等待超时或失败')
      }
    }

    // 如果没有连接，尝试建立连接
    console.log('MQTT未连接，尝试建立连接...')
    await setupMqtt()

    return mqttConnected.value
  } catch (error) {
    console.error('确保MQTT连接失败:', error)
    return false
  }
}

/**
 * 获取 MQTT 连接选项，生成唯一的 Client ID
 */
const getMqttConnectOptions = async () => {
  const shopStore = useShopStore()
  let deviceId = ''

  try {
    // 尝试获取设备唯一ID
    const deviceInfo = await uni.getDeviceInfo()
    deviceId = deviceInfo.deviceId || ''
    console.log('获取到设备ID:', deviceId)
  } catch (error) {
    console.warn('获取设备ID失败，使用随机字符串:', error)
    // 降级使用随机字符串
    deviceId = Math.random().toString(36).substr(2, 9)
  }

  // 如果设备ID为空，使用随机字符串
  if (!deviceId) {
    deviceId = Math.random().toString(36).substr(2, 9)
  }

  // 构造唯一的客户端ID
  const clientId = `uni_shop_${deviceId}_${shopStore.currentShop?.raw?.code || 'unknown'}_${Date.now()}`

  console.log('生成的MQTT客户端ID:', clientId)

  return {
    clientId,
    username: import.meta.env.VITE_MQTT_USERNAME,
    password: import.meta.env.VITE_MQTT_PASSWORD,
    keepalive: 60,
    connectTimeout: 10000,
  }
}

/**
 * 处理订单更新
 */
/**
 * 静默处理订单状态更新
 */
const handleUpdateOrder = (payload: any, orderIndex: number) => {
  // 只有在订单标签下才处理订单更新
  if (activeTab.value !== 'order') {
    console.log('当前在售后标签，忽略订单更新')
    return
  }

  console.log('静默更新订单:', { payload, orderIndex, orderId: orderList.value[orderIndex]?.id })

  try {
    // 验证索引有效性
    if (orderIndex < 0 || orderIndex >= orderList.value.length) {
      console.error('订单索引无效:', orderIndex)
      return
    }

    const orderToUpdate = { ...orderList.value[orderIndex] }
    const originalApiData = { ...orderToUpdate.apiData }

    // 更新原始API数据
    if (orderToUpdate.apiData) {
      Object.assign(orderToUpdate.apiData, payload)
    } else {
      orderToUpdate.apiData = payload
    }

    // 记录原始状态与更新后状态
    const originalStatus = originalApiData?.orderStatus
    const newStatus = payload.orderStatus || originalStatus

    // 1. 新需求：不校验订单状态，但订单状态和接收的状态不一致时只更新非状态字段
    let skipStatusUpdate = false
    if (payload.orderStatus && originalStatus && payload.orderStatus !== originalStatus) {
      console.log('订单状态变更不一致，将只更新非状态字段:', {
        orderId: orderToUpdate.id,
        oldStatus: originalStatus,
        newStatus: payload.orderStatus,
      })
      skipStatusUpdate = true
      // 不再直接return，而是继续处理其他字段的更新
    }

    // 检查更新后的订单是否仍然符合筛选条件
    const updatedOrderMatchesFilter = isOrderMatchFilter(orderToUpdate.apiData)

    // 根据匹配情况决定如何处理
    if (!updatedOrderMatchesFilter) {
      console.log('订单更新后不再符合筛选条件，将静默移除:', {
        orderId: orderToUpdate.id,
        oldStatus: originalStatus,
        newStatus,
        currentFilter: activeStatus.value,
      })

      // 从列表中移除订单
      setTimeout(() => {
        const currentIndex = orderList.value.findIndex((order) => order.id === orderToUpdate.id)
        if (currentIndex !== -1) {
          orderList.value.splice(currentIndex, 1)
        }
      }, 100)

      return
    }

    // 更新订单状态字段
    if (payload.orderStatus && !skipStatusUpdate) {
      orderToUpdate.status = payload.orderStatus

      // 根据订单状态获取对应的状态名称
      const getStatusName = (status: string) => {
        switch (status) {
          case '10':
          case 'WAIT_MERCHANT_CONFIRM':
            return '待接单'
          case '20':
          case 'WAIT_REPORT_MEALS':
            return '待出餐'
          case 'WAIT_CELL_DELIVERY':
            return '待发配送'
          case 'WAIT_RIDER_ACCEPT':
            return '骑手待接单'
          case 'INSHOP':
            return '待处理'
          case 'CONFIRM':
            return '已接单'
          case 'COMPLETED':
            return '已送达'
          case '50':
          case 'DELIVERING':
            return '配送中'
          case '80':
          case 'FINISHED':
            return '已完成'
          case 'RIDER_ACCEPT':
            return '骑手已接单'
          case 'RIDER_ARRIVE_SHOP':
            return '骑手已到店'
          case 'DELIVERING_OVERTIME':
            return '配送超时'
          case 'OVERTIME':
            return '超时未送达'
          case '60':
          case 'TAKED':
            return '已取餐'
          case 'ARRIVED':
            return '已送达'
          case '-10':
          case '-20':
          case 'CANCELED':
            return '已取消'
          case 'CLOSED':
            return '已关闭'
          default:
            return payload.orderStatusName || payload.bizStatusDesc || payload.statusDesc || status
        }
      }

      orderToUpdate.statusInfo = {
        ...orderToUpdate.statusInfo,
        main: getStatusName(payload.orderStatus),
      }
    }

    // 更新订单状态名称
    if (payload.orderStatusName && !skipStatusUpdate) {
      orderToUpdate.statusInfo = {
        ...orderToUpdate.statusInfo,
        main: payload.orderStatusName,
      }
    }

    // 更新金额相关字段
    if (payload.totalAmount) {
      orderToUpdate.amount = payload.totalAmount
      if (orderToUpdate.fees) {
        orderToUpdate.fees.totalAmount = parseFloat(payload.totalAmount)
      }
    }

    // 更新收入金额
    if (payload.incomeAmount && orderToUpdate.fees) {
      orderToUpdate.fees.merchantIncome = parseFloat(payload.incomeAmount)
    }

    // 更新配送费
    if (payload.deliveryFee && orderToUpdate.fees) {
      orderToUpdate.fees.deliveryFee = parseFloat(payload.deliveryFee)
    }

    // 更新包装费
    if (payload.packageAmount && orderToUpdate.fees) {
      orderToUpdate.fees.packagingFee = parseFloat(payload.packageAmount)
    }

    // 更新配送时间
    if (payload.sendTime) {
      orderToUpdate.deliveryTime = payload.sendTime.substring(0, 16)
    }

    if (payload.deliveryTime) {
      orderToUpdate.deliveryTime = payload.deliveryTime.substring(0, 16)
    }

    // 更新联系人信息
    if (payload.contactName) {
      orderToUpdate.customer = {
        ...orderToUpdate.customer,
        name: payload.contactName,
      }
    }

    // 更新电话信息
    if (payload.phone) {
      orderToUpdate.customer = {
        ...orderToUpdate.customer,
        phone: payload.phone.substring(payload.phone.length - 4),
        fullPhone: payload.phone,
      }
    }

    // 更新地址信息
    if (payload.deliveryAddress) {
      orderToUpdate.address = {
        ...orderToUpdate.address,
        full: payload.deliveryAddress,
      }
    }

    // 更新门店信息
    if (payload.shopName) {
      orderToUpdate.merchant = {
        ...orderToUpdate.merchant,
        name: payload.shopName,
      }
    }

    // 更新业务类型
    if (payload.bizTypeName) {
      orderToUpdate.type = payload.bizTypeName
    }

    // 更新渠道信息
    if (payload.saleChannelDesc) {
      orderToUpdate.channel = payload.saleChannelDesc
    }

    // 更新配送信息（新增）
    if (
      payload.courierName ||
      payload.courierMobile ||
      payload.courierStatus ||
      payload.deliveryChannelName ||
      payload.deliveryStatusName ||
      payload.courierOperateTime
    ) {
      // 确保 deliveryRecord 对象存在
      if (!orderToUpdate.apiData.deliveryRecord) {
        orderToUpdate.apiData.deliveryRecord = {}
      }

      // 更新配送记录信息到 apiData.deliveryRecord
      if (payload.courierName) {
        orderToUpdate.apiData.deliveryRecord.courierName = payload.courierName
      }

      if (payload.courierMobile) {
        orderToUpdate.apiData.deliveryRecord.courierMobile = payload.courierMobile
      }

      if (payload.courierStatus) {
        orderToUpdate.apiData.deliveryRecord.statusName = payload.courierStatus
      }

      if (payload.deliveryChannelName) {
        orderToUpdate.apiData.deliveryRecord.channelName = payload.deliveryChannelName
      }

      if (payload.deliveryStatusName) {
        orderToUpdate.apiData.deliveryRecord.deliveryStatusName = payload.deliveryStatusName
      }

      if (payload.courierOperateTime) {
        orderToUpdate.apiData.deliveryRecord.operateTime = payload.courierOperateTime
      }

      // 更新展示层的配送信息
      if (!orderToUpdate.deliveryRecord) {
        orderToUpdate.deliveryRecord = {}
      }

      // 同步更新到展示数据
      if (payload.courierName) {
        orderToUpdate.deliveryRecord.courierName = payload.courierName
      }

      if (payload.courierMobile) {
        orderToUpdate.deliveryRecord.courierMobile = payload.courierMobile
      }

      // 更新配送状态名称 - 优先使用courierStatus，回退到deliveryStatusName
      if (payload.courierStatus) {
        orderToUpdate.deliveryRecord.statusName = payload.courierStatus
      } else if (payload.deliveryStatusName) {
        orderToUpdate.deliveryRecord.statusName = payload.deliveryStatusName
      }

      // 更新配送渠道名称
      if (payload.deliveryChannelName) {
        orderToUpdate.deliveryRecord.channelName = payload.deliveryChannelName
      }

      // 更新操作时间 - 同时映射到operateTime和arriveStoreTime以确保组件兼容性
      if (payload.courierOperateTime) {
        orderToUpdate.deliveryRecord.operateTime = payload.courierOperateTime
        // @ts-expect-error - 组件需要arriveStoreTime字段，虽然类型定义中没有
        orderToUpdate.deliveryRecord.arriveStoreTime = payload.courierOperateTime
      }

      // 如果有额外的deliveryStatusName，也保存起来
      if (payload.deliveryStatusName) {
        orderToUpdate.deliveryRecord.deliveryStatusName = payload.deliveryStatusName
      }

      if (payload.deliveryChannelName) {
        orderToUpdate.deliveryRecord.channelName = payload.deliveryChannelName
      }

      console.log('配送信息已更新:', {
        orderId: orderToUpdate.id,
        courierName: payload.courierName,
        courierStatus: payload.courierStatus,
        deliveryChannel: payload.deliveryChannelName,
        deliveryStatus: payload.deliveryStatusName,
        operateTime: payload.courierOperateTime,
      })
    }

    // 使用splice替换对象以触发响应式更新
    orderList.value.splice(orderIndex, 1, orderToUpdate)

    console.log('订单静默更新成功:', {
      orderId: orderToUpdate.id,
      newStatus: orderToUpdate.status,
      statusName: orderToUpdate.statusInfo.main,
      updateFields: Object.keys(payload),
      skipStatusUpdate,
      updatedFields: skipStatusUpdate ? '仅非状态字段' : '全部字段',
    })
  } catch (error) {
    console.error('静默更新订单失败:', error)
  }
}

/**
 * 静默处理新订单创建
 */
const handleCreateOrder = async (payload: any) => {
  // 只有在订单标签下才处理新订单创建
  if (activeTab.value !== 'order') {
    console.log('当前在售后标签，忽略新订单创建')
    return
  }

  // ！！！重要：提前定义筛选匹配函数，用于判断订单是否符合当前筛选条件（不考虑订单状态）
  const matchesAllFiltersExceptStatus = (orderData?: any) => {
    // 如果没有传入订单数据，基于MQTT payload进行基础判断
    const dataToCheck = orderData || payload

    console.log('开始检查订单是否匹配筛选条件（不含状态）:', {
      thirdOrderCode: payload.thirdOrderCode || payload.channelOrderNo || payload.extlOrderSerial,
      dataToCheck: {
        shopId: dataToCheck.shopId,
        saleChannel: dataToCheck.saleChannel,
        channelCode: dataToCheck.channelCode,
        bizType: dataToCheck.bizType,
      },
      currentFilters: {
        shopIds: selectedFilters.value.shopIds,
        channelCodes: selectedFilters.value.channelCodes,
        orderTypeCodes: selectedFilters.value.orderTypeCodes,
      },
    })

    // 1. 检查门店筛选
    if (selectedFilters.value.shopIds && selectedFilters.value.shopIds.length > 0) {
      const orderShopId = dataToCheck.shopId
      if (!orderShopId || !selectedFilters.value.shopIds.includes(orderShopId)) {
        console.log('订单门店不匹配:', {
          orderShopId,
          selectedShops: selectedFilters.value.shopIds,
        })
        return false
      }
    }

    // 2. 检查渠道筛选
    if (selectedFilters.value.channelCodes && selectedFilters.value.channelCodes.length > 0) {
      const orderChannelCode = dataToCheck.saleChannel || dataToCheck.channelCode
      if (!orderChannelCode || !selectedFilters.value.channelCodes.includes(orderChannelCode)) {
        console.log('订单渠道不匹配:', {
          orderChannel: orderChannelCode,
          selectedChannels: selectedFilters.value.channelCodes,
        })
        return false
      }
    }

    // 3. 检查订单类型筛选
    if (selectedFilters.value.orderTypeCodes && selectedFilters.value.orderTypeCodes.length > 0) {
      const orderBizType = dataToCheck.bizType
      if (!orderBizType || !selectedFilters.value.orderTypeCodes.includes(orderBizType)) {
        console.log('订单类型不匹配:', {
          orderBizType,
          selectedTypes: selectedFilters.value.orderTypeCodes,
        })
        return false
      }
    }

    // 4. 检查时间范围筛选
    if (selectedFilters.value._startPlaceTime && selectedFilters.value._endPlaceTime) {
      const orderTime = dataToCheck.placeTime || dataToCheck.createTime
      if (!orderTime) {
        console.log('订单缺少时间信息')
        return false
      }

      const startTime = new Date(selectedFilters.value._startPlaceTime).getTime()
      const endTime = new Date(selectedFilters.value._endPlaceTime).getTime()
      const orderDateTime = new Date(orderTime).getTime()

      if (orderDateTime < startTime || orderDateTime > endTime) {
        console.log('订单时间不在筛选范围内:', {
          orderTime,
          startTime: selectedFilters.value._startPlaceTime,
          endTime: selectedFilters.value._endPlaceTime,
        })
        return false
      }
    }

    // 所有条件都满足
    console.log('订单通过所有筛选条件检查（不含状态）')
    return true
  }

  // 检查是否是仅显示弹窗模式
  const onlyShowToast = payload._onlyShowToast === true

  console.log('处理新订单:', payload)
  console.log('处理新订单---selectedFilters:', selectedFilters)
  console.log('处理新订单---allShopIds:', allShopIds)
  console.log('处理新订单---currentStore:', currentStore)
  console.log('处理新订单---currentStore.value:', currentStore.value)

  try {
    const thirdOrderCode =
      payload.thirdOrderCode || payload.channelOrderNo || payload.extlOrderSerial

    if (!thirdOrderCode) {
      console.warn('新订单消息缺少订单标识:', payload)
      return
    }

    // 提前判断：如果当前筛选状态不是"全部"，直接显示横幅，不请求接口
    if (activeStatus.value !== 'ALL') {
      console.log('当前筛选状态不是"全部"，只显示横幅，不请求接口:', {
        thirdOrderCode,
        currentStatus: activeStatus.value,
      })

      // 根据后端的channelCode从渠道接口数据processedChannelEnum中获取渠道名称
      const channelCode = payload.saleChannel || payload.channelCode
      let channelName = 'XXX' // 默认值

      if (channelCode && processedChannelEnum.value?.length > 0) {
        const matchedChannel = processedChannelEnum.value.find(
          (channel: any) => channel.code === channelCode,
        )
        if (matchedChannel?.name) {
          channelName = matchedChannel.name
        }
      }

      // 如果从枚举中未找到，则使用payload中的备用字段
      if (channelName === 'XXX') {
        channelName = payload.extlChannelName || payload.bizTypeName || 'XXX'
      }

      console.log('渠道名称解析结果:', {
        channelCode,
        channelName,
        processedChannelEnumLength: processedChannelEnum.value?.length || 0,
      })

      // 显示新订单横幅提示
      newOrderToast.value = {
        visible: true,
        autoClose: true,
        channelText: channelName,
        orderNo: thirdOrderCode,
        duration: 3000,
      }
      return
    }

    // 提前判断：使用MQTT payload进行基础筛选条件检查（不包括状态）
    const matchesFiltersFromPayload = matchesAllFiltersExceptStatus()
    if (!matchesFiltersFromPayload) {
      console.log('新订单不符合筛选条件（基于MQTT payload检查），不显示横幅也不请求接口:', {
        thirdOrderCode,
        payload,
      })
      return
    }

    // 通过基础筛选检查且当前状态是"全部"，请求接口获取完整订单数据
    console.log('通过基础筛选检查且状态是"全部"，请求接口获取订单详情:', {
      thirdOrderCode,
      currentStatus: activeStatus.value,
    })

    // 构建门店ID列表用于查询
    const queryShopIds =
      selectedFilters.value.shopIds.length > 0
        ? selectedFilters.value.shopIds
        : allShopIds.value.length > 0
          ? allShopIds.value
          : [currentStore.value.id]

    // 获取shopIds对应的shopCodes
    const shopStore = useShopStore()
    const queryShopCodes = queryShopIds
      .map((id) => {
        // 如果是当前店铺ID，直接使用当前店铺编码
        if (id === shopStore.currentShopId && shopStore.currentShop?.raw?.code) {
          return shopStore.currentShop.raw.code
        }
        // 如果是子门店ID，需要从子门店列表中查找对应编码
        const subShop = (shopStore.currentShop?.raw as any)?.shopDtoList?.find(
          (shop: any) => shop.id === id,
        )
        return subShop?.code || ''
      })
      .filter((code) => code !== '') // 过滤掉空值

    // 根据第三方订单号查询订单详情
    const orderRes = await fetchOrderByThirdOrderCode({
      thirdOrderCode,
      shopIds: queryShopIds,
      shopCodes: queryShopCodes.length > 0 ? queryShopCodes : undefined,
    })

    const orderData = orderRes?.data?.list?.[0]
    if (!orderData) {
      console.log('未查询到新订单数据:', thirdOrderCode)
      return
    }

    // 使用完整订单数据进行精确筛选条件检查（不考虑订单状态）
    const orderMatchesAllFiltersExceptStatus = matchesAllFiltersExceptStatus(orderData)
    // 原有的筛选匹配函数，包含订单状态
    const orderMatchesFilter = isOrderMatchFilter(orderData)

    console.log('新订单筛选匹配结果:', {
      thirdOrderCode,
      matchesAllFiltersExceptStatus: orderMatchesAllFiltersExceptStatus,
      matchesAllFiltersIncludingStatus: orderMatchesFilter,
      currentFilter: {
        status: activeStatus.value,
        channels: selectedFilters.value.channelCodes,
        shops: selectedFilters.value.shopIds,
        types: selectedFilters.value.orderTypeCodes,
      },
    })

    // 如果不匹配所有筛选条件（不包括状态），不显示弹窗也不插入列表
    if (!orderMatchesAllFiltersExceptStatus) {
      console.log('新订单不符合筛选条件（基于完整数据检查），不显示弹窗:', {
        thirdOrderCode,
        orderStatus: orderData.orderStatus,
      })
      return
    }

    // 新单先显示弹窗提示，然后再判断是否插入列表
    const shouldShowToast =
      payload.operationType === 'createOrder' || payload.action === 'createOrder'

    // 转换订单数据格式
    const newOrder = {
      extlChannel: orderData.extlChannel || orderData.saleChannel || '',
      id: orderData.id || '',
      orderNumber: orderData.orderNo || orderData.tradeNo || '',
      status: orderData.orderStatus || '',
      type: orderData.bizTypeName || '',
      bizType: orderData.bizType || '',
      instanceId: orderData.instanceId || '',
      tenantId: orderData.tenantId || '',
      deliveryTime: orderData.sendTime ? orderData.sendTime.substring(0, 16) : '',
      createTime: orderData.createTime || orderData.placeTime || '',
      merchant: {
        id: orderData.shopId || '',
        name: orderData.shopName || '',
        avatar: '/static/images/img/mt.png',
      },
      customer: {
        name:
          orderData.contactName || (orderData.customerInfo ? orderData.customerInfo.userName : ''),
        phone: orderData.phone
          ? orderData.phone.substring(orderData.phone.length - 4)
          : orderData.hiddenPhone
            ? orderData.hiddenPhone.substring(orderData.hiddenPhone.length - 4)
            : '',
        fullPhone: orderData.phone || orderData.reservedPhone || '',
      },
      address: {
        full: (() => {
          try {
            if (orderData.extlOrderDetail) {
              const detail = JSON.parse(orderData.extlOrderDetail)
              return detail.syncThirdOrderReqDto?.address || orderData.deliveryAddress || ''
            }
          } catch (e) {
            console.error('解析地址信息失败:', e)
          }
          return orderData.deliveryAddress || ''
        })(),
        distance: '<1km',
      },
      warning:
        orderData.orderStatus === 'PAYED' || orderData.orderStatus === '20'
          ? {
              text: '订单即将超时',
              countdown: {
                minutes: '05',
                seconds: '00',
              },
            }
          : undefined,
      statusInfo: {
        main: (() => {
          switch (orderData.orderStatus) {
            case '10':
              return '待接单'
            case '20':
              return '待出餐'
            case 'WAIT_REPORT_MEALS':
              return '待出餐'
            case 'WAIT_CELL_DELIVERY':
              return '待发配送'
            case 'WAIT_RIDER_ACCEPT':
              return '骑手待接单'
            case 'INSHOP':
              return '待处理'
            case 'WAIT_MERCHANT_CONFIRM':
              return '待商家确认'
            case 'CONFIRM':
              return '已接单'
            case 'COMPLETED':
              return '已送达'
            case '50':
              return '配送中'
            case '80':
              return '已完成'
            case 'RIDER_ACCEPT':
              return '骑手已接单'
            case 'RIDER_ARRIVE_SHOP':
              return '骑手已到店'
            case 'DELIVERING':
              return '配送中'
            case 'DELIVERING_OVERTIME':
              return '配送超时'
            case 'OVERTIME':
              return '超时未送达'
            case '60':
              return '已取餐'
            case 'TAKED':
              return '已取餐'
            case 'FINISHED':
              return '已完成'
            case 'ARRIVED':
              return '已送达'
            case '-10':
              return '已取消'
            case '-20':
              return '已取消'
            case 'CANCELED':
              return '已取消'
            case 'CLOSED':
              return '已关闭'
            default:
              return orderData.bizStatusDesc || orderData.statusDesc || '未知状态'
          }
        })(),
        time: orderData.placeTime ? orderData.placeTime.substring(11, 16) : '',
        description: '',
      },
      channel: orderData.saleChannelDesc || '',
      items: orderData.items || [],
      amount: orderData.totalAmount || '0',
      fees: {
        merchantIncome: parseFloat(orderData.incomeAmount || '0'),
        deliveryFee: parseFloat(orderData.deliveryFee || '0'),
        packagingFee: parseFloat(orderData.packageAmount || '0'),
        totalAmount: parseFloat(orderData.totalAmount || '0'),
      },
      // 保存原始API数据，方便上报出餐等功能使用
      apiData: orderData,
    }

    // 如果匹配所有筛选条件（不包括状态），但不匹配包含状态的条件，显示弹窗但不插入列表
    if (orderMatchesAllFiltersExceptStatus && !orderMatchesFilter && shouldShowToast) {
      newOrderToast.value = {
        visible: true,
        autoClose: true,
        channelText: orderData.extlChannelName || orderData.bizTypeName || 'XXX',
        orderNo: orderData.id || '',
        duration: 3000,
      }
      console.log('新单符合筛选条件（不包括状态），但状态不匹配，仅显示弹窗不插入列表')
      return
    }

    // 如果是仅显示弹窗模式，不插入列表
    if (!onlyShowToast) {
      // 在插入之前检查订单是否已存在，避免重复
      const existingIndex = isOrderExistsInList(newOrder)
      if (existingIndex === -1) {
        // 订单不存在，静默将新订单插入到列表顶部，不影响当前滚动位置
        orderList.value = [newOrder, ...orderList.value]
        console.log('新订单已插入到列表顶部:', {
          orderId: newOrder.id,
          thirdOrderCode,
          listLength: orderList.value.length,
        })
      } else {
        console.log('订单已存在于列表中，跳过插入:', {
          orderId: newOrder.id,
          thirdOrderCode,
          existingIndex,
          existingOrderId: orderList.value[existingIndex]?.id,
        })
      }
    } else {
      console.log('仅显示弹窗模式，不插入订单到列表')
    }

    // 对于新建订单，显示弹窗提示
    // 显示新订单提示
    // 更新新订单提示弹窗配置
    newOrderToast.value = {
      visible: true,
      autoClose: true,
      channelText: orderData.extlChannelName || orderData.bizTypeName || 'XXX',
      orderNo: newOrder.id,
      duration: 3000,
    }
  } catch (error) {
    console.error('处理新订单失败:', error)
  }
}

/**
 * MQTT 消息处理 - 智能调度中心
 */
const handleMqttMessage = (topic: string, payload: any) => {
  console.log('收到 MQTT 消息:', {
    topic,
    payload,
    currentTab: activeTab.value,
    currentStatus: activeStatus.value,
    orderListLength: orderList.value.length,
  })

  // 只有在订单标签下才处理MQTT消息
  if (activeTab.value !== 'order') {
    console.log('当前在售后标签，忽略MQTT订单消息')
    return
  }

  try {
    // 验证消息格式
    if (!payload || typeof payload !== 'object') {
      console.warn('MQTT消息格式无效:', payload)
      return
    }

    // 第一步：获取订单信息
    const thirdOrderCode =
      payload.thirdOrderCode || payload.channelOrderNo || payload.extlOrderSerial

    if (!thirdOrderCode) {
      console.warn('MQTT消息缺少订单标识，无法处理:', {
        payload,
        availableFields: Object.keys(payload),
      })
      return
    }

    // 检查是否为新订单创建
    const isCreateOrder =
      payload.operationType === 'createOrder' || payload.action === 'createOrder'

    // 使用统一的订单匹配逻辑查找订单
    const targetOrderData = {
      extlOrderSerial: thirdOrderCode,
      channelOrderNo: thirdOrderCode,
      thirdOrderCode,
    }
    const orderIndex = isOrderExistsInList(targetOrderData)
    const isOrderExists = orderIndex !== -1

    // 第三步：分发逻辑 - 新需求：只有新建订单才会插入或更新
    console.log('开始处理MQTT消息分发:', {
      thirdOrderCode,
      isOrderExists,
      isCreateOrder,
      searchFields: ['extlOrderSerial', 'channelOrderNo', 'thirdOrderCode'],
    })

    // 只有新建订单才处理（第三次确认条件，防止漏网之鱼）
    if (payload.operationType === 'createOrder' || payload.action === 'createOrder') {
      // 未找到订单且是创建操作，执行新增操作
      console.log('未找到订单且为创建操作，执行新增:', {
        thirdOrderCode,
        operationType: payload.operationType || payload.action,
        currentListLength: orderList.value.length,
      })
      handleCreateOrder(payload)
    } else if (isOrderExists) {
      // 非创建操作但订单存在于列表，进行静默更新
      console.log('非创建操作，但订单存在于列表中，进行静默更新:', {
        thirdOrderCode,
        operationType: payload.operationType || payload.action,
        orderIndex,
        orderId: orderList.value[orderIndex]?.id,
      })
      handleUpdateOrder(payload, orderIndex)
    } else {
      console.log('非创建操作且订单不在列表中，忽略消息:', {
        thirdOrderCode,
        operationType: payload.operationType || payload.action,
        availableOperations: ['createOrder'],
        currentListLength: orderList.value.length,
      })
    }
  } catch (error) {
    console.error('处理 MQTT 消息失败:', {
      error,
      topic,
      payload,
      errorMessage: error instanceof Error ? error.message : String(error),
    })

    // 显示错误提示（仅在开发环境）
    // #ifdef H5
    if (import.meta.env.DEV) {
      uni.showToast({
        title: 'MQTT消息处理失败',
        icon: 'error',
        duration: 2000,
      })
    }
    // #endif
  }
}

/**
 * 初始化 MQTT 连接
 */
export const setupMqtt = async () => {
  const shopStore = useShopStore()

  try {
    if (!shopStore.currentShop?.raw?.code) {
      console.warn('当前店铺代码为空，无法初始化 MQTT')
      return
    }

    // 如果已经在连接中，不重复连接
    if (mqttConnecting.value) {
      console.log('MQTT正在连接中，跳过重复连接')
      return
    }

    // 如果已经连接且实例存在，检查连接状态
    if (mqttInstance && mqttConnected.value) {
      console.log('MQTT已连接，检查连接状态...')

      // 验证连接是否真的有效
      try {
        const connectionInfo = mqttInstance.getConnectionInfo()
        if (connectionInfo.isConnected) {
          console.log('MQTT连接状态正常，无需重新连接')
          return
        }
      } catch (error) {
        console.warn('检查MQTT连接状态失败，将重新连接:', error)
      }
    }

    console.log('开始初始化 MQTT 连接...')

    // 清理现有连接
    if (mqttInstance) {
      try {
        mqttInstance.disconnect()
      } catch (error) {
        console.warn('清理现有MQTT连接时出错:', error)
      }
      mqttInstance = null
    }

    // 初始化 MQTT 实例
    mqttInstance = useMqtt()

    // 设置消息处理回调
    mqttInstance.onMessage(handleMqttMessage)

    // 监听连接状态变化
    watch(
      () => mqttInstance?.isConnected.value,
      (newValue) => {
        mqttConnected.value = newValue || false
        console.log('MQTT连接状态变化:', newValue)
      },
      { immediate: true },
    )

    watch(
      () => mqttInstance?.isConnecting.value,
      (newValue) => {
        mqttConnecting.value = newValue || false
        console.log('MQTT连接中状态变化:', newValue)
      },
      { immediate: true },
    )

    watch(
      () => mqttInstance?.reconnectAttempts.value,
      (newValue) => {
        mqttReconnectAttempts.value = newValue || 0
        console.log('MQTT重连次数变化:', newValue)
      },
      { immediate: true },
    )

    try {
      // 获取连接选项（包含唯一的客户端ID）
      const connectOptions = await getMqttConnectOptions()

      // 连接 MQTT
      await mqttInstance.connect(import.meta.env.VITE_MQTT_BASEURL, connectOptions)

      console.log('MQTT 连接成功，开始订阅主题...')

      // 订阅店铺相关主题
      const topic = `order/${shopStore.currentShop?.raw?.code}`
      await mqttInstance.subscribe(topic)

      console.log('MQTT 初始化完成，连接状态:', mqttConnected.value)
    } catch (error) {
      console.error('MQTT 连接或订阅失败:', error)
      mqttConnected.value = false
      mqttConnecting.value = false
    }
  } catch (error) {
    console.error('MQTT 设置失败:', error)
    mqttConnected.value = false
    mqttConnecting.value = false
  }
}

/**
 * 手动重连 MQTT
 */
export const reconnectMqtt = async () => {
  const shopStore = useShopStore()

  if (!shopStore.currentShop?.raw?.code) {
    console.warn('当前店铺代码为空，无法重连 MQTT')
    return
  }

  if (!mqttInstance) {
    console.log('MQTT 实例不存在，重新初始化...')
    await setupMqtt()
    return
  }

  try {
    console.log('手动重连 MQTT...')

    // 获取连接选项（包含唯一的客户端ID）
    const connectOptions = await getMqttConnectOptions()

    await mqttInstance.reconnect(import.meta.env.VITE_MQTT_BASEURL, connectOptions)

    // 重新订阅主题
    const topic = `order/${shopStore.currentShop?.raw?.code}`
    await mqttInstance.subscribe(topic)

    console.log('MQTT 重连成功')

    uni.showToast({
      title: 'MQTT 重连成功',
      icon: 'success',
    })
  } catch (error) {
    console.error('MQTT 重连失败:', error)

    uni.showToast({
      title: 'MQTT 重连失败',
      icon: 'error',
    })
  }
}

/**
 * 获取 MQTT 连接信息
 */
export const getMqttConnectionInfo = () => {
  if (!mqttInstance) {
    return {
      isConnected: false,
      isConnecting: false,
      reconnectAttempts: 0,
      maxReconnectAttempts: 5,
    }
  }

  return mqttInstance.getConnectionInfo()
}

/**
 * 监听店铺变化，重新订阅主题
 */
watch(
  () => {
    const shopStore = useShopStore()
    return shopStore.currentShop?.raw?.code
  },
  async (newCode, oldCode) => {
    console.log('店铺代码变化:', { newCode, oldCode, mqttConnected: mqttConnected.value })

    if (newCode && newCode !== oldCode && mqttInstance && mqttInstance.isConnected.value) {
      try {
        console.log('开始切换MQTT订阅主题...')

        // 取消订阅旧主题
        if (oldCode) {
          console.log(`取消订阅旧主题: order/${oldCode}`)
          await mqttInstance.unsubscribe(`order/${oldCode}`)
          console.log(`成功取消订阅: order/${oldCode}`)
        }

        // 订阅新主题
        console.log(`订阅新主题: order/${newCode}`)
        await mqttInstance.subscribe(`order/${newCode}`)
        console.log(`成功订阅新主题: order/${newCode}`)

        // 显示切换成功提示
        // uni.showToast({
        //   title: '已切换店铺订阅',
        //   icon: 'success',
        //   duration: 1500,
        // })
      } catch (error) {
        console.error('切换 MQTT 订阅失败:', {
          error,
          newCode,
          oldCode,
          errorMessage: error instanceof Error ? error.message : String(error),
        })

        // 显示错误提示
        // uni.showToast({
        //   title: '切换店铺订阅失败',
        //   icon: 'error',
        //   duration: 2000,
        // })
      }
    } else if (
      newCode &&
      newCode !== oldCode &&
      (!mqttInstance || !mqttInstance.isConnected.value)
    ) {
      console.log('MQTT未连接，尝试重新初始化连接...')
      // 如果MQTT未连接，尝试重新初始化
      try {
        await setupMqtt()
      } catch (error) {
        console.error('重新初始化MQTT失败:', error)
      }
    }
  },
)

/**
 * 清理事件监听器
 */
export const cleanupEventListeners = () => {
  console.log('清理事件监听器')
  // 这里可以移除特定的事件监听器，但由于我们的事件总线实现简单，
  // 通常在页面销毁时会自动清理

  // 清理滚动定时器
  if (scrollingTimer) {
    clearTimeout(scrollingTimer)
    scrollingTimer = null
  }
}

// ==================== 确认弹窗相关 ====================

/**
 * 同意弹窗配置
 */
export const approvePopupConfig = reactive({
  visible: false,
  title: '操作确认',
  content: '同意退款后，该订单款项不再计入结算。定责结果和赔付信息可在【订单-申诉】中查询。',
  cancelText: '取消',
  confirmText: '同意退款',
  tradeNo: '', // 当前操作的tradeNo
  instanceId: '', // 实例ID
  tenantId: '', // 租户ID
})

/**
 * 拒绝弹窗配置
 */
export const rejectPopupConfig = reactive({
  visible: false,
  title: '建议您与顾客沟通协商后处理',
  content:
    '当您未与顾客协商一致，直接拒绝退款用户有权向客服申诉，如被平台审核为商家原因导致订单取消，将影响您的排名。',
  cancelText: '已沟通，拒绝退款',
  confirmText: '取消',
  tradeNo: '', // 当前操作的tradeNo
  instanceId: '', // 实例ID
  tenantId: '', // 租户ID
})

/**
 * 显示同意弹窗
 */
export const showApprovePopup = (tradeNo: string, instanceId?: string, tenantId?: string) => {
  approvePopupConfig.tradeNo = tradeNo
  approvePopupConfig.instanceId = instanceId || ''
  approvePopupConfig.tenantId = tenantId || ''
  approvePopupConfig.visible = true
}

/**
 * 显示拒绝弹窗
 */
export const showRejectPopup = (tradeNo: string, instanceId?: string, tenantId?: string) => {
  rejectPopupConfig.tradeNo = tradeNo
  rejectPopupConfig.instanceId = instanceId || ''
  rejectPopupConfig.tenantId = tenantId || ''
  rejectPopupConfig.visible = true
}

/**
 * 确认同意售后
 */
export const handleApproveConfirm = async () => {
  // 隐藏弹窗
  approvePopupConfig.visible = false

  // 如果有订单ID，执行同意操作
  if (approvePopupConfig.tradeNo) {
    await executeApproveAfterSale(approvePopupConfig.tradeNo)
    // 重置订单ID和其他信息
    approvePopupConfig.tradeNo = ''
    approvePopupConfig.instanceId = ''
    approvePopupConfig.tenantId = ''
    setTimeout(() => {
      fetchOrders()
    }, 2000)
  }
}

/**
 * 取消同意售后
 */
export const handleApproveCancel = () => {
  approvePopupConfig.visible = false
  approvePopupConfig.tradeNo = ''
  approvePopupConfig.instanceId = ''
  approvePopupConfig.tenantId = ''
}

/**
 * 确认拒绝售后
 */
export const handleRejectConfirm = async () => {
  // 拒绝弹窗中，确认按钮是"取消"操作
  rejectPopupConfig.visible = false
  rejectPopupConfig.tradeNo = ''
  rejectPopupConfig.instanceId = ''
  rejectPopupConfig.tenantId = ''
}

/**
 * 执行拒绝售后
 */
export const handleRejectCancel = async () => {
  // 拒绝弹窗中，取消按钮是"拒绝退款"操作
  // 隐藏弹窗
  rejectPopupConfig.visible = false

  // 如果有订单ID，执行拒绝操作
  if (rejectPopupConfig.tradeNo) {
    await executeRejectAfterSale(rejectPopupConfig.tradeNo)
    // 重置订单ID和其他信息
    rejectPopupConfig.tradeNo = ''
    rejectPopupConfig.instanceId = ''
    rejectPopupConfig.tenantId = ''
    setTimeout(() => {
      fetchOrders()
    }, 2000)
  }
}

/**
 * 执行售后同意操作（实际调用API）
 */
export const executeApproveAfterSale = async (tradeNo: string) => {
  console.log(
    '执行售后同意:',
    tradeNo,
    '实例ID:',
    approvePopupConfig.instanceId,
    '租户ID:',
    approvePopupConfig.tenantId,
  )

  try {
    uni.showLoading({
      title: '处理中...',
      mask: true,
    })

    // 调用售后审核接口
    const result = await auditAfterSale({
      tradeNo,
      refundStatus: 30,
      agreeReason: '同意退款',
      instanceId: approvePopupConfig.instanceId,
      tenantId: approvePopupConfig.tenantId,
    })

    if (result && result.resultCode === '0') {
      // 查找订单
      const orderIndex = orderList.value.findIndex((order) => {
        if (
          order.apiData &&
          order.apiData.refundRecords &&
          order.apiData.refundRecords.length > 0
        ) {
          return order.apiData.refundRecords[0].tradeNo === tradeNo
        }
        return false
      })

      if (orderIndex !== -1) {
        const orderToUpdate = { ...orderList.value[orderIndex] }

        // 更新订单状态
        orderToUpdate.status = 'REFUNDED'
        orderToUpdate.statusInfo = {
          ...orderToUpdate.statusInfo,
          main: '已退款',
        }

        // 更新到列表中
        orderList.value.splice(orderIndex, 1, orderToUpdate)
      }

      setTimeout(async () => {
        // 刷新订单列表
        await fetchOrders()
        uni.showToast({
          title: '同意成功',
          icon: 'success',
        })
        setTimeout(async () => {
          fetchOrders()
        }, 600)
      }, 1000)
    } else {
      uni.showToast({
        title: result?.resultMsg || '操作失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('售后同意失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

/**
 * 执行售后拒绝操作（实际调用API）
 */
export const executeRejectAfterSale = async (tradeNo: string) => {
  console.log(
    '执行售后拒绝:',
    tradeNo,
    '实例ID:',
    rejectPopupConfig.instanceId,
    '租户ID:',
    rejectPopupConfig.tenantId,
  )

  try {
    uni.showLoading({
      title: '处理中...',
      mask: true,
    })

    // 调用售后审核接口
    const result = await auditAfterSale({
      tradeNo,
      refundStatus: 40,
      agreeReason: '已沟通，拒绝退款',
      instanceId: rejectPopupConfig.instanceId,
      tenantId: rejectPopupConfig.tenantId,
    })

    if (result && result.resultCode === '0') {
      // 查找订单
      const orderIndex = orderList.value.findIndex((order) => {
        if (
          order.apiData &&
          order.apiData.refundRecords &&
          order.apiData.refundRecords.length > 0
        ) {
          return order.apiData.refundRecords[0].tradeNo === tradeNo
        }
        return false
      })

      if (orderIndex !== -1) {
        const orderToUpdate = { ...orderList.value[orderIndex] }

        // 更新订单状态
        orderToUpdate.status = 'REFUND_REJECTED'
        orderToUpdate.statusInfo = {
          ...orderToUpdate.statusInfo,
          main: '退款已拒绝',
        }

        // 更新到列表中
        orderList.value.splice(orderIndex, 1, orderToUpdate)
      }

      setTimeout(async () => {
        // 刷新订单列表
        // await fetchOrders()
        uni.showToast({
          title: '拒绝成功',
          icon: 'success',
        })
        setTimeout(async () => {
          fetchOrders()
        }, 600)
      }, 1000)
    } else {
      uni.showToast({
        title: result?.resultMsg || '操作失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('售后拒绝失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

/**
 * 处理新订单提示弹窗内容点击
 */
export const onNewOrderToastContentClick = (orderNo?: string) => {
  console.log('新订单提示内容点击', {
    orderNo,
    currentTab: activeTab.value,
    currentStatus: activeStatus.value,
    toastVisible: newOrderToast.value.visible,
    timestamp: new Date().toISOString(),
  })

  // 关闭弹窗
  newOrderToast.value.visible = false

  // 新需求：如果当前不是全部tab，则自动切换到全部tab
  if (activeStatus.value !== 'ALL') {
    console.log('当前不是全部状态tab，自动切换到全部tab')

    // 直接调用onStatusChange函数切换到全部状态
    // 这样可以确保UI和状态一致更新，且全部tab能够正确高亮
    onStatusChange('ALL')

    // 注意：onStatusChange内部已经包含了重置页码和获取数据的逻辑，不需要再调用fetchOrders
  } else {
    // 当前已是全部tab，只执行滚动到顶部操作，不刷新列表数据
    scrollToTop()
  }
}

// 定时器引用
let scrollingTimer: number | null = null

/**
 * 处理scroll-view的滚动事件
 */
export const onScroll = (e: any) => {
  const scrollTop = e.detail.scrollTop

  // 超过800rpx时显示返回顶部按钮
  showBackToTop.value = scrollTop > 500

  // 检测滚动状态 - 滚动时显示sliding效果
  if (!isScrolling.value) {
    isScrolling.value = true
  }

  // 清除之前的定时器
  if (scrollingTimer) {
    clearTimeout(scrollingTimer)
  }

  // 设置新的定时器，300ms后清除滚动状态
  scrollingTimer = setTimeout(() => {
    isScrolling.value = false
  }, 300)

  currentScrollTop.value = scrollTop

  // 只在开发环境打印滚动位置
  // #ifdef H5
  if (import.meta.env.DEV) {
    console.log(
      '滚动事件:',
      scrollTop,
      '显示按钮:',
      showBackToTop.value,
      '滚动中:',
      isScrolling.value,
    )
  }
  // #endif
}

/**
 * 返回顶部按钮点击处理函数
 */
export const handleBackToTop = () => {
  console.log('返回顶部按钮被点击')

  // 滚动到第一个订单位置
  scrollToFirstOrder()
}

/**
 * 滚动到第一个订单位置
 */
export const scrollToFirstOrder = () => {
  try {
    // 根据用户修改，直接滚动到顶部而不是第一个订单
    console.log('返回顶部按钮点击，执行滚动到顶部')
    scrollToTop()

    // 注释掉的代码保留，以防将来需要恢复滚动到第一个订单的功能
    // if (orderList.value && orderList.value.length > 0) {
    //   const firstOrderId = `order-${orderList.value[0].id}`
    //   console.log('滚动到第一个订单:', firstOrderId)
    //
    //   // 重置后设置
    //   scrollToView.value = ''
    //   setTimeout(() => {
    //     scrollToView.value = firstOrderId
    //
    //     // 延迟重置scrollToView，确保下次滚动能正常触发
    //     setTimeout(() => {
    //       scrollToView.value = ''
    //     }, 200)
    //   }, 50)
    // } else {
    //   // 如果没有订单，滚动到顶部
    //   scrollToTop()
    // }
  } catch (error) {
    console.error('滚动到第一个订单失败:', error)
    // fallback到普通的滚动到顶部
    scrollToTop()
  }
}

/**
 * 滚动到顶部的函数
 */
export const scrollToTop = () => {
  console.log('执行滚动到顶部')

  try {
    // 重置scroll-top值，确保能触发滚动
    scrollTop.value = currentScrollTop.value

    nextTick(async () => {
      scrollTop.value = 0
    })
  } catch (err) {
    console.error('滚动到顶部失败:', err)
  }
}

// 异常提醒相关处理函数

/**
 * 检测通知权限状态
 */
export const checkNotificationPermission = async () => {
  try {
    // #ifdef APP-PLUS
    if (!jpushModule) {
      console.warn('JPush模块未找到')
      return
    }

    jpushModule.isNotificationEnabled((result: any) => {
      console.log('通知权限检测结果:', result)

      // 根据极光推送文档，status < 2 表示未开启通知权限
      if (result.code === 0) {
        showExceptionReminderBar.value = true
      } else {
        showExceptionReminderBar.value = false
      }
    })
    // #endif

    // #ifndef APP-PLUS
    // 非App环境下的处理逻辑（如H5、小程序）
    console.log('非App环境，跳过通知权限检测')
    // #endif
  } catch (error) {
    console.error('检测通知权限失败:', error)
  }
}

/**
 * 异常提醒条点击处理
 */
export const onExceptionReminderClick = () => {
  showExceptionPopup.value = true
  showExceptionReminderBar.value = false
}

/**
 * 异常提醒弹窗 - 忽略按钮处理
 */
export const onExceptionIgnore = () => {
  showExceptionPopup.value = false
  showRiskConfirmPopup.value = true
}

/**
 * 异常提醒弹窗 - 去设置按钮处理
 */
export const onExceptionGoSetting = () => {
  // #ifdef APP-PLUS
  if (jpushModule) {
    jpushModule.openSettingsForNotification()
  }
  // #endif
  showExceptionPopup.value = false
}

/**
 * 高危风险确认弹窗处理
 */
export const onRiskConfirm = () => {
  console.log('用户确认了高危风险')
  // 可以在这里添加埋点统计等逻辑
  showRiskConfirmPopup.value = false
}

export const onRiskGoSetting = () => {
  // #ifdef APP-PLUS
  if (jpushModule) {
    jpushModule.openSettingsForNotification()
  }
  // #endif
  showRiskConfirmPopup.value = false
}

export const onRiskIgnore = () => {
  showRiskConfirmPopup.value = false
  showExceptionReminderBar.value = true
}

// 自定义下拉刷新事件处理函数
export const onRefresherPulling = (e: any) => {
  const { dy } = e.detail

  // 如果正在加载或已完成，不处理拉动事件
  if (refresherStatus.value === 'loading' || refresherStatus.value === 'completed') {
    console.log('拉动事件被忽略，当前状态:', refresherStatus.value)
    return
  }

  // 控制自定义刷新器的显示和位置
  if (dy > 50) {
    showCustomRefresher.value = true
    refresherTransform.value = Math.min(dy - 20, 60) // 调整位置偏移

    // 判断下拉状态 - 当下拉距离超过80时显示"松开即可刷新"
    const newStatus = dy > 100 ? 'releasing' : 'pulling'
    if (refresherStatus.value !== newStatus) {
      refresherStatus.value = newStatus
      console.log('刷新状态变更为:', newStatus, 'dy:', dy)
    }
  } else {
    showCustomRefresher.value = false
    refresherTransform.value = 0
    if (refresherStatus.value !== 'pulling') {
      refresherStatus.value = 'pulling'
      console.log('刷新状态重置为: pulling')
    }
  }
}

export const onRefresherRestore = (e: any) => {
  console.log('刷新恢复事件触发', e.detail, '当前状态:', refresherStatus.value)

  // 如果不是在加载或完成状态，隐藏自定义刷新器并重置位置
  if (refresherStatus.value !== 'loading' && refresherStatus.value !== 'completed') {
    showCustomRefresher.value = false
    refresherTransform.value = 0
    refresherStatus.value = 'pulling'
    console.log('刷新器状态重置')
  } else {
    console.log('当前处于加载或完成状态，保持刷新器显示')
  }
}

// 跳转申诉页面
// 定义类型接口
interface OrderItem {
  afterSale?: AfterSaleRecord[]
  extlChannel: string
  shopId?: string
  tenantId: string
  orderNo?: string
}

interface AfterSaleRecord {
  type: string
  extlRefundSerial: string
  appealAmount: number
}

interface AppealPageParams {
  extlOrderSerial: string
  saleChannel: string
  extlRefundSerial: string
  appealAmount: number
  shopId: string
  tenantId: string
  orderCode: string
  refundTime: string
  appealTime: string
}
// 跳转申诉页面
export const onHandleToAppeal = (index: number) => {
  try {
    // 获取当前订单数据并进行类型断言
    const currentItem = orderList.value[index].apiData
    console.log('跳转申诉页面,查看数据:', currentItem)

    // 查找待申诉的售后记录
    const waitAppealRecord = currentItem.refundRecords?.find(
      (record: AfterSaleRecord) => record.type === 'waitAppeal',
    )

    if (!waitAppealRecord) {
      uni.showToast({
        title: '未找到待申诉的售后记录',
        icon: 'none',
      })
      return
    }

    // 构建路由参数对象（添加了类型定义）
    const routeParams: AppealPageParams = {
      extlOrderSerial: currentItem.extlOrderSerial,
      saleChannel: currentItem.extlChannel,
      extlRefundSerial: waitAppealRecord.extlRefundSerial,
      appealAmount: waitAppealRecord.appealAmount,
      shopId: currentItem.shopId,
      tenantId: currentItem.tenantId,
      orderCode: currentItem.orderNo,
      refundTime: getCurrentTime(),
      appealTime: getCurrentTime(),
    }

    // 跳转申诉页面
    uni.navigateTo({
      url:
        '/pages/damageAppeal/index?routeParams=' + encodeURIComponent(JSON.stringify(routeParams)),
    })
  } catch (error) {
    console.error('申诉页面跳转失败:', error)
    uni.showToast({
      title: '操作失败，请稍后重试',
      icon: 'none',
    })
  }
}
