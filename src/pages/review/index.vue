<route lang="json5">
{
  layout: 'tabbar',
  needLogin: true,
  style: {
    navigationBarTitleText: '评价',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>
<template>
  <view
    class="store-detail-page flex flex-col"
    :style="{ '--status-bar-height': statusBarHeight + 'px' }"
  >
    <View class="fixed-header">
      <!-- 系统导航栏（由uni-app自动渲染，标题在route配置）  -->
      <!-- 顶部门店信息组件 -->
      <!-- <sy-order-fixed-header
        :current-store="currentShopInfo"
        :is-store-expanded="isStoreInfoVisible"
        @store-select="goToSelectShop"
        @toggle-store-expand="toggleStoreInfo"
      /> -->
      <!-- 原有的门店标题部分 -->
      <view class="flex items-center justify-between shop-box bg-white text-shoptext-shop-box">
        <text class="text-lg font-bold text-shoptext-shop">评价管理</text>
        <!-- <image
          class="w-6 h-6 luoshi-icon"
          src="/static/images/img/luoshi.png"
          mode="aspectFit"
          @click="goToGeneralSettings"
        /> -->
      </view>
      <!-- 门店导航栏 -->
      <SyStoreTabNav
        v-model="channelModelValue"
        v-model:secondModelValue="shopModelValue"
        :firstTab="channelOptions"
        :seconTab="seconTab"
        class="sy-store-tab-nav"
        :filterChannleList="filterChannleList"
      />
      <!-- 红色提示条 -->
      <!-- <StoreNoticeBar v-if="notice" :text="notice" /> -->
      <block v-if="!filterChannleList.includes(selectedChannels[0])">
        <!-- 评分与基础信息区 -->
        <SyStoreRating
          style="margin-top: 24rpx"
          :hideRateing="showRateing"
          :rating="storeInfo.rating ? storeInfo.rating : 0"
          :taste="storeInfo.taste ? storeInfo.taste : 0"
          :packaging="storeInfo.packaging ? storeInfo.packaging : 0"
          :deliveryRate="storeInfo.deliveryRate ? String(storeInfo.deliveryRate) : '0'"
          @click-rule="onClickRule"
        />

        <wd-divider dashed></wd-divider>
        <view class="review-container">
          <!-- 标题栏 -->
          <view class="review-header">
            <view class="title">
              外卖评价
              <text style="font-size: 24rpx; color: #333"></text>
            </view>
            <text class="appeal-link" @click="jumpGuidePage('commontGuide', 'gukepingjiashensu')">
              申诉指引
              <wd-icon color="#F33429" name="help-circle" size="28rpx" />
            </text>
          </view>

          <!-- 下拉筛选菜单 -->
          <wd-drop-menu
            v-if="!isJingdongChannel"
            :options="dropMenuOptions"
            v-model="selectedValues"
            active-color="#ff6700"
            inactive-color="#666"
          >
            <view class="drop-menu-title" v-for="(item, index) in dropMenuOptions" :key="index">
              <wd-drop-menu-item
                v-if="channelModelValue !== 'ELeMeTakeOutBrand'"
                v-model="selectedValues[index]"
                :options="item.options"
                @change="(value) => handleFilterChange(value, index)"
                :title="item.label"
              />
              <wd-drop-menu-item
                v-else
                v-model="selectedValues[3]"
                :options="item.options"
                @change="(value) => handleFilterChange(value, index)"
                :title="item.label"
              />
            </view>
          </wd-drop-menu>
        </view>
      </block>
    </View>
    <block v-if="filterChannleList.includes(selectedChannels[0])">
      <view class="toast-box">
        <view class="toast-title">京东商家APP-店铺-评价管理，可以查看评价和操作</view>
        <view class="toast-img-box" :style="{ height: mainContentHeight * 2 + 'rpx' }">
          <image
            class="toast-img"
            mode="widthFix"
            src="/static/images/img/jingdong_pingjiashensu.png"
          ></image>
        </view>
      </view>
    </block>
    <block v-else>
      <!-- 评价列表 -->
      <view class="review-list">
        <scroll-view
          class="scrollable-content"
          :scroll-y="true"
          @scrolltolower="onLoadMore"
          :scroll-with-animation="true"
          :refresher-enabled="true"
          :refresher-triggered="pagination.isRefreshing"
          @refresherrefresh="onRefresh(true)"
          :style="{
            height: headerHeight
              ? `calc(100vh - ${2 * headerHeight + 60}px)`
              : 'calc(100vh - 410px)',
          }"
        >
          <!-- 全屏加载状态 -->
          <SyLoading
            v-if="pageLoading"
            :show="true"
            src="/static/images/img/goods-loading.gif"
            toastText="数据加载中..."
            :imgWidth="200"
            :imgHeight="200"
            :showMask="false"
            :fixed="false"
          />

          <view v-if="reviewList.length > 0 && !pageLoading">
            <view class="review-item" v-for="(item, index) in reviewList" :key="index">
              <!-- 用户信息 -->
              <view class="user-info">
                <wd-img :width="36" :height="36" round src="/static/images/img/user_avatar.png" />
                <view class="user-details">
                  <text class="user-name">{{ item?.userName ? item.userName : '用户' }}</text>
                  <text class="score">
                    <wd-rate v-model="item.score" size="24rpx" readonly />
                    <text style="margin-left: 6rpx">
                      总分{{ item.score }} 口味{{ item.tasteScore }} 包装{{ item.packingScore }}
                    </text>
                    <text class="time">{{ item.evaluateTime }}</text>
                  </text>
                </view>
              </view>

              <view class="review-content">
                <!-- 评价内容 -->
                <text class="content">{{ item.evaluateContent }}</text>

                <SyImageNineGrid
                  style="margin-top: 12rpx"
                  :gap="10"
                  :imageList="item.evaluatePictures"
                  :handlePrePick="(index) => toPreLookPic(index, item.evaluatePictures)"
                />
              </view>

              <!-- 追评 -->
              <view class="additional">
                <template v-if="item.additional">
                  <text class="additional-title">用户当天追评</text>
                  <text class="additional-content">{{ item.addEvaluateContent }}</text>
                </template>
                <SyAuth
                  code="jidanbao_menu_review_replybtn"
                  toast-mode="toast"
                  v-slot="{ isAllowed, onNoPermissionClick }"
                >
                  <wd-button
                    v-if="item.replyStatus === 0"
                    class="additional-btn"
                    :class="{ 'sy-auth-no-permission': !isAllowed }"
                    size="small"
                    custom-class="reply-button"
                    hairline
                    :style="{
                      filter: !isAllowed ? 'grayscale(100%)' : 'none',
                      opacity: !isAllowed ? 0.7 : 1,
                    }"
                    @click="isAllowed ? toChangeVisiableReview(index) : onNoPermissionClick()"
                  >
                    回复
                  </wd-button>
                </SyAuth>
              </view>

              <!--商家回复-->
              <view v-if="item.replyContent" class="shop-reply">
                <view class="reply-title">
                  <text>
                    商家回复（{{
                      getDaysAfter(item.evaluateTime, item.replyTime) === 0
                        ? '当天'
                        : getDaysAfter(item.evaluateTime, item.replyTime)
                    }}天后）
                  </text>
                  <wd-tag v-if="item.replyStatus === 0" custom-class="space" type="primary">
                    审核中
                  </wd-tag>
                </view>
                <text>{{ item.replyContent }}</text>
              </view>
            </view>
          </view>
          <view
            class="data-tip-view"
            v-if="reviewList.length === 0 && !pageLoading && !pagination.isRefreshing"
          >
            <img
              width="210rpx"
              height="210px"
              src="https://registry.npmmirror.com/wot-design-uni-assets/*/files/search.png"
              draggable="false"
            />
            <view class="tip-text">
              <text>暂无评价信息</text>
              <wd-icon
                v-if="isJingdongChannel"
                style="margin-left: 6rpx"
                name="help-circle"
                size="30rpx"
                @click="handleShowTipModel"
              />
            </view>
          </view>

          <!-- 无更多数据 -->
          <view
            v-if="
              !pagination.hasMore &&
              reviewList.length > 0 &&
              !pageLoading &&
              !pagination.isRefreshing
            "
            class="no-more"
          >
            <text class="no-more-text">没有更多数据了</text>
          </view>
        </scroll-view>

        <!-- 底部安全区占位 -->
        <view style="height: env(safe-area-inset-bottom, 32px)"></view>
      </view>
    </block>

    <!--申诉指引弹框-->
    <SyPopup
      v-model="showAppealGuide"
      v-if="showAppealGuide"
      title="提示"
      confirm-text="确定"
      :show-cancel="false"
      @confirm="handleConfirmAppeal"
    >
      <text class="sy-popup-txt">{{ appealTxt }}</text>
    </SyPopup>

    <!--评价计算规则指引弹框-->
    <SyPopup
      v-if="ratingRulePopupVisible"
      v-model="ratingRulePopupVisible"
      title="提示"
      confirm-text="确定"
      :show-cancel="false"
      @confirm="ratingRulePopupVisible = !ratingRulePopupVisible"
    >
      <text class="sy-popup-txt">{{ ratingRuleGuideTip }}</text>
    </SyPopup>

    <!--查看评价指引弹框-->
    <SyPopup
      v-if="showReviewLookGuide"
      v-model="showReviewLookGuide"
      title="提示"
      confirm-text="确定"
      :show-cancel="false"
      @confirm="showReviewLookGuide = !showReviewLookGuide"
    >
      <text class="sy-popup-txt">{{ reviewLookTipGuide }}</text>
    </SyPopup>

    <wd-action-sheet
      class="reply-action-sheet"
      v-if="showReplyInput"
      v-model="showReplyInput"
      :z-index="100"
      title="新增回复"
      :close-on-click-overlay="true"
    >
      <!-- 主体内容：输入框 + 字数统计 + 按钮 -->
      <template #default>
        <div class="custom-header">
          <div class="custom-header-text">
            <p class="tip-text">您的回复经过审核后将会公开展示，请友善沟通哦~</p>
          </div>
          <wd-img
            class="zhuansong"
            width="60"
            height="68"
            mode="scaleToFill"
            :src="replyModelImage"
          ></wd-img>
        </div>
        <div class="action-sheet-content">
          <wd-textarea
            custom-class="reply-textarea"
            clear-trigger="focus"
            v-model="replyContentStr"
            :maxlength="300"
            clearable
            show-word-limit
            :adjust-position="true"
            :cursorSpacing="80"
            placeholder="您的回复会被公开展示，请注意措辞，最多300字。"
          />

          <wd-button class="sure-reply-btn" size="small" type="error" @click="handleReply()">
            回复
          </wd-button>
        </div>
      </template>
    </wd-action-sheet>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import SyOrderFixedHeader from '@/components/sy-order-fixed-header'
import SyStoreTabNav from '@/components/sy-store-tab-nav/sy-store-tab-nav.vue'
import SyStoreRating from '@/components/sy-store-rating/sy-store-rating.vue'
import SyLoading from '@/components/sy-loading/sy-loading.vue'
import SyImageNineGrid from '@/components/sy-image-ninegrid/sy-image-ninegrid.vue'
import SyAuth from '@/components/sy-auth'
import { useReview } from './index'
import SyPopup from '@/components/sy-popup'
const filterChannleList = ref(['JingDongTakeOutBrand'])
const isInitialized = ref(false)
// 下拉刷新是否可用
const enablePullDownRefresh = ref(true)
// 添加控制门店信息显示/隐藏的状态变量，默认为展开状态
const isStoreInfoVisible = ref(true)
// 回复框中显示的图片
const replyModelImage = ref('/static/images/img/zhuansong.png')

// 前往通用设置页面
const goToGeneralSettings = () => {
  uni.navigateTo({
    url: '/pages/general/index',
  })
}

// 切换门店信息显示/隐藏状态
const toggleStoreInfo = (expanded?: boolean) => {
  if (expanded !== undefined) {
    isStoreInfoVisible.value = expanded
  } else {
    isStoreInfoVisible.value = !isStoreInfoVisible.value
  }
}
// 跳转操作指引
const jumpGuidePage = (type: string, status: string) => {
  if (type === 'commontGuide' && selectedChannels.value) {
    uni.navigateTo({
      url: `/pages/operationGuide/index?type=${type}&channle=${selectedChannels.value[0]}&status=${status}`,
    })
  }
}
// 渠道选择弹出层控制
const isChannelPopupVisible = ref(false)

// 当前选中的渠道
const currentChannel = ref('美团外卖')

// 渠道列表
const channelList = ref([
  { key: 'meituan', label: '美团外卖' },
  { key: 'eleme', label: '饿了么' },
  { key: 'jd', label: '京东秒送' },
  { key: 'miniapp', label: '自营小程序' },
  { key: 'douyin', label: '抖音随心团' },
])

// 系统状态栏高度
const statusBarHeight = ref(0)

// 打开渠道选择弹出层
const openChannelPopup = () => {
  isChannelPopupVisible.value = true
}

// 关闭渠道选择弹出层
const closeChannelPopup = () => {
  isChannelPopupVisible.value = false
}

// 选择渠道
const selectChannel = (channel) => {
  console.log('选择渠道:', channel)
  currentChannel.value = channel.label
  closeChannelPopup()
}

const {
  currentShopInfo,
  storeInfo,
  seconTab,
  channelOptions,
  initStoreInfo,
  getChannelData,
  channelModelValue,
  shopModelValue,
  secondModelObj,
  goToSelectShop,
  reviewList,
  dropMenuOptions,
  selectedValues,
  handleFilterChange,
  toChangeVisiableReview,
  handleReply,
  showReplyInput,
  replyContentStr,
  showAppealGuide,
  handleConfirmAppeal,
  appealTxt,
  getReviewList,
  getDaysAfter,
  orderTip,
  pagination,
  toReplyReview,
  onLoadMore,
  onRefresh,
  headerHeight,
  reviewHeaderHeight,
  initPage,
  pageLoading,
  showRateing,
  isJingdongChannel,
  selectedChannels,
  mainContentHeight,
} = useReview()

// 二级标签页选中值
const activeSecondTab = ref('a')

// 评分规则弹窗控制
const ratingRulePopupVisible = ref(false)
const isCalculationDetail = ref(false)
const ratingRuleGuideTip = ref('评分规则请至平台App查看,路径:门店-顾客评价-计算详情')

// 查看评价指引弹框相关
const showReviewLookGuide = ref(false)
const reviewLookTipGuide = ref('查看评价请至平台App查看,路径:门店-顾客评价-查看评价')

// 控制评价指引弹框显示隐藏
const handleShowTipModel = () => {
  // showReviewLookGuide.value = !showReviewLookGuide.value
}

// 预览图片
const toPreLookPic = (index: number, picsArray: string[]) => {
  uni.previewImage({
    urls: picsArray,
    current: picsArray[index], // 当前显示图片的链接
  })
}

// 生命周期
onMounted(() => {
  // if (!isInitialized.value) {
  // 获取门店信息
  initStoreInfo()
  // }
})

onLoad(() => {
  initPage()
})

// 在 onShow 生命周期中初始化门店信息并获取门店详情
onShow(() => {
  if (!isInitialized.value) {
    getChannelData()

    // 获取系统状态栏高度
    uni.getSystemInfo({
      success: (res) => {
        statusBarHeight.value = res.statusBarHeight || 0
      },
    })
    isInitialized.value = true
  }
})

// 点击计算详情
const onClickRule = () => {
  isCalculationDetail.value = true
  ratingRulePopupVisible.value = true
}

// 二级标签页变化处理
const onSecondTabChange = (value: string) => {
  activeSecondTab.value = value
  console.log('二级标签页切换:', value)
}

// 添加默认导出
defineOptions({
  name: 'Review',
})
</script>

<style lang="scss" src="./index.scss" scoped></style>
