.store-detail-page {
  box-sizing: border-box;
  width: 100%;
  max-height: calc(100vh - var(--status-bar-height));
  padding-top: var(--status-bar-height);
  overflow: hidden; /* 防止页面内容溢出 */
  overflow-x: hidden; // 防止横向滚动
  background-color: #ffffff;

  // 确保所有子元素不会溢出
  * {
    box-sizing: border-box;
    max-width: 100%;
  }

  // 防止图片溢出
  image {
    max-width: 100%;
  }

  .text-primary-box {
    padding-top: 106rpx;
    padding-bottom: 38rpx;
  }
  .text-shoptext-shop-box {
    position: relative;
    z-index: 10;
    padding-top: 30rpx;
    padding-bottom: 20rpx;
    // background: white;
    // 移除底部边框
    // border-bottom: 2rpx solid #f2f2f4;
  }
  .text-primary {
    font-size: 30rpx;
    font-weight: 600;
    color: #222222;
  }
  .text-shop {
    font-size: 40rpx;
    font-weight: 600;
    color: #222222;
  }
  .arrow-icon {
    width: 48rpx;
    height: 48rpx;
    font-size: 22rpx;
    color: #000000;
  }
  // 顶部栏
  .store-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 24rpx;
    padding-top: var(--status-bar-height, 0);
    background: #fff;
    border-bottom: 1rpx solid #f0f0f0;
    .store-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #222;
    }
    .share-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;
      background: #f5f6fa;
      border-radius: 50%;
    }

    .fixed-header {
      position: relative;
      z-index: 9;
      width: 100%;
    }
  }
  // 列表数据提示组件
  .data-tip-view {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .tip-text {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: normal;
      line-height: 18px;
      color: #8f959e;
      text-align: center;
      letter-spacing: normal;
    }
  }
  // Tab导航
  .store-tab-nav {
    background: #fff;
    .tab-item {
      position: relative;
      padding: 0 12rpx 12rpx 12rpx;
      margin-right: 32rpx;
      font-size: 28rpx;
      color: #666;

      &.active {
        font-weight: bold;
        color: #f33429;
        &::after {
          position: absolute;
          bottom: 0;
          left: 50%;
          display: block;
          width: 40rpx;
          height: 6rpx;
          content: '';
          background: #f33429;
          border-radius: 3rpx;
          transform: translateX(-50%);
        }
      }
    }
  }
  :deep(.position .store-tab-nav .tab-item:last-child) {
    padding-right: 20rpx;
  }

  // 红色提示条
  .store-notice-bar {
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    margin: 16rpx 32rpx 0 32rpx;
    font-size: 24rpx;
    color: #f33429;
    background: #fff0f0;
    border-radius: 12rpx;
    image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 12rpx;
    }
  }

  // 评分区
  .store-rating {
    display: flex;
    align-items: center;
    padding: 24rpx 0;
    margin: 24rpx 32rpx 0 32rpx;
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.04);
    .main-score {
      margin-right: 12rpx;
      font-size: 48rpx;
      font-weight: bold;
      color: #f33429;
    }
    .star-list {
      display: flex;
      align-items: center;
      image {
        width: 32rpx;
        height: 32rpx;
        margin-right: 4rpx;
      }
    }
    .score-detail {
      margin-left: 24rpx;
      font-size: 24rpx;
      color: #666;
    }
    .score-tag {
      padding: 2rpx 10rpx;
      margin-left: 12rpx;
      font-size: 22rpx;
      color: #f33429;
      background: #fff0f0;
      border-radius: 8rpx;
    }
  }

  .shop-box {
    padding: 16rpx 24rpx 38rpx 24rpx;
    // 移除底部边框
    // border-bottom: 1px solid #f5f5f5;
  }

  .text-shoptext-shop {
    color: #333;
  }

  .luoshi-icon {
    width: 48rpx;
    height: 48rpx;
  }

  .review-container {
    width: 100%;
    padding: 0rpx 24rpx 0 24rpx;
    /* 标题栏 */
    .review-header {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      font-size: 18px;
      font-weight: 500;
      line-height: normal;
      color: #333333;
      letter-spacing: normal;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
      .appeal-link {
        font-size: 24rpx;
        font-weight: 500;
        line-height: normal;
        color: #333333;
        text-align: center;
        letter-spacing: normal;
      }
    }
  }
  :deep(.wd-drop-menu__item) {
    flex: none;
  }
  .review-list {
    .scrollable-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: calc(100% - 48rpx);
      margin: 0rpx 24rpx;
      overflow: scroll;

      :deep(.uni-scroll-view-content) {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .review-item {
        display: flex;
        flex-direction: column;
        padding: 24rpx 24rpx;
        // width: calc(100% - 40rpx);
        margin: 12rpx 0rpx;
        background: #f9f9f9;
        border-radius: 24rpx;
        .user-info {
          display: flex;
          align-items: center;

          .user-details {
            display: flex;
            flex-direction: column;
            margin-left: 16rpx;

            .user-name {
              margin-bottom: 4px;
              font-size: 26rpx;
              font-weight: bold;
            }

            .score {
              display: flex;
              flex-direction: row;
              align-items: center;
              font-size: 24rpx;
              font-weight: normal;
              line-height: normal;
              color: #999999;
              letter-spacing: normal;
              .time {
                margin-left: 8px;
                line-height: 2;
              }
            }
          }
        }
        .review-content {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          margin-top: 12rpx;
          .content {
            margin-bottom: 8px;
            font-size: 13px;
            font-weight: 500;
            line-height: normal;
            color: #333333;
            letter-spacing: normal;
          }

          .order-tip {
            margin-top: 16rpx;
            font-size: 12px;
            font-weight: normal;
            line-height: normal;
            color: #999999;
            letter-spacing: normal;
          }
        }

        .additional {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          margin-top: 8px;

          .additional-title {
            font-size: 13px;
            font-weight: 500;
            line-height: normal;
            color: #ff0000;
            letter-spacing: normal;
          }

          .additional-content {
            font-size: 13px;
            font-weight: 500;
            line-height: normal;
            color: #333333;
            letter-spacing: normal;
          }
          .reply-button {
            box-sizing: border-box;
            align-self: flex-end;
            color: #333;
            background: #ffffff;
            border: 1px solid #eeeeee;
            border-radius: 16px;
          }
        }

        .shop-reply {
          display: flex;
          flex-direction: column;
          padding: 20rpx 30rpx;
          font-size: 24rpx;
          background-color: #f5f7fb;

          .reply-title {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
          }
        }
      }

      // 无更多数据
      .no-more {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 40rpx 0;

        .no-more-text {
          font-size: 28rpx;
          color: #999999;
        }
      }
    }
  }
}

// popup 弹框文本样式
.sy-popup-txt {
  font-size: 13px;
  font-weight: normal;
  line-height: normal;
  color: #999999;
  text-align: center;
  letter-spacing: normal;
}

.reply-action-sheet {
  height: 280rpx;
  .custom-header {
    position: relative;
    width: 100%;
    height: 120rpx;
    margin-top: 12rpx;
  }
  .custom-header-text {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1;
    height: 120rpx;
    height: 100%;
    margin: 20rpx 28rpx 0rpx 28rpx;
    background: linear-gradient(180deg, rgba(243, 52, 41, 0.9) 0%, rgba(243, 52, 41, 0.64) 100%);
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;

    .tip-text {
      margin-top: 40rpx;
      margin-left: 130rpx;
      font-size: 13px;
      font-weight: 600;
      line-height: normal;
      color: #ffffff;
      letter-spacing: normal;
    }
  }

  .zhuansong {
    position: absolute;
    top: 0px; // 根据需要调整位置
    left: 20rpx; // 根据需要调整位置
    z-index: 2;
  }
  .action-sheet-content {
    display: flex;
    flex-direction: column;
  }
  .sure-reply-btn {
    align-self: flex-end;
    width: 150rpx;
    height: 62rpx;
    margin-right: 21rpx;
    margin-bottom: 21rpx;
  }
}

:deep(.wd-textarea__inner) {
  height: 150rpx;
}

:deep(.wd-textarea__value) {
  padding: 16rpx;
  background-color: #f5f6fa !important;
  border-bottom-right-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
}
:deep(.wd-textarea__count) {
  margin-right: 12rpx;
  background: none;
}
:deep(.wd-action-sheet__header) {
  height: 84rpx;
  padding-left: 24rpx;
  font-weight: bold;
  text-align: start;
}

// 覆盖固定头部组件的样式
:deep(.fixed-header) {
  position: relative; // 在这个页面中不需要固定定位
  z-index: 10;

  .store-header {
    // 移除底部边框
    // border-bottom: 1px solid #f5f5f5;
  }
}

.gap-line {
  background: rgba(251, 251, 251, 1);
}
.frosted-glass {
  height: 10rpx;
  /* 背景色设置半透明 */
  background: rgba(255, 255, 255, 0.5);
  /* 添加模糊效果 */
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  /* 添加微弱的边框 */
  border: 1px solid rgba(255, 255, 255, 0.2);
  /* 可选：添加阴影效果 */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
.item-icon {
  position: relative;
  top: 2rpx;
  width: 24rpx;
  height: 24rpx;
  margin-left: 6rpx;
}
.hide-btn {
  font-size: 20rpx;
  font-weight: 400;
  color: #3d3d3d;
}
.xiangshang-icon-logo {
  width: 24rpx;
  height: 24rpx;
}
.xiangxia-box {
  justify-content: center;
  width: 702rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #3d3d3d;
}
.xiangxia-icon-logo {
  // width: 100%;
  width: 24rpx;
  height: 24rpx;
}
.xiangyou-icon {
  position: relative;
  top: -2rpx;
  width: 36rpx;
  height: 36rpx;
}
/* 渠道选择弹出层样式 - 图2 */
.channel-popup-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
}

.channel-popup-container {
  position: absolute;
  top: 131rpx;
  left: 0;
  z-index: 1000;
  width: 100%;
  background-color: #ffffff;
  border-bottom-right-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.channel-popup-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
}

.channel-popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
}

.channel-popup-close {
  position: absolute;
  top: 20rpx;
  right: 32rpx;
  width: 32rpx;
  height: 32rpx;
}

.channel-popup-content {
  padding: 20rpx 30rpx 40rpx;
}

.channel-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.channel-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 196rpx;
  height: 96rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}

.channel-item-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #000000;
}
/* 确保这些样式优先级高于图1的样式 */
.store-detail-page .channel-popup-overlay,
.store-detail-page .channel-popup-container {
  display: block !important;
}
/* 渠道选择触发按钮样式 */
.channel-trigger {
  padding: 16rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f2f2f4;
}

.channel-current-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #000000;
}
/* App 平台特定样式 */
/* #ifdef APP-PLUS */
.store-detail-page {
  width: 100% !important;
  // 防止App端出现滚动条
  overflow: hidden !important;

  // 修复可能的内容溢出问题
  .info-value,
  .store-name,
  .open-time-text {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
  }
}

// 修复App端可能的滚动问题
uni-page-body,
uni-page-wrapper {
  min-height: 100% !important;
  overflow: hidden !important;
}
/* #endif */
.toast-box {
  padding: 24rpx 20rpx 0 22rpx;
  .toast-title {
    font-size: 26rpx;
    font-weight: 400;
    color: #3d3d3d;
  }
  .toast-img-box {
    height: 1100rpx;
    margin-top: 24rpx;
    overflow-y: scroll;
    .toast-img {
      width: 708rpx;
    }
  }
}
