import { ref, watch } from 'vue'
import type { StoreInfo, StoreTab, StoreEntry } from '@/types/store'
import { useUserStore, useShopStore } from '@/store'
import {
  fetchReviewList,
  QueryReviewParams,
  replyReview,
  ReplyReviewParams,
} from '@/service/review/index'
import { fetchChannelEnum, fetchStoreDetailByChannel } from '@/service/order/dict'
import type { ShopDto } from '@/types/shop'
import { getStartDate, getCurrentTime, getDaysAfter, addOneDay } from '../../utils/datetime'
import { isEmptyValue } from '../../utils/index'

export function useReview() {
  const selectedChannels = ref<string[]>([])
  // 状态栏高度
  const statusBarHeight = ref(0)
  const headerHeight = ref(0)
  const reviewHeaderHeight = ref(0)

  // 当前选择的门店信息
  const currentShopInfo = ref<ShopDto | null>(null)

  // 门店基础信息
  const storeInfo = ref<StoreInfo>({} as StoreInfo)

  // 渠道数据
  const channelOptions = ref<StoreTab[]>([])

  // Tab 相关数据
  const channelModelValue = ref<string>()
  const shopModelValue = ref('')
  // 存储当前选中的子门店的完整数据对象
  const secondModelObj = ref<StoreTab | null>(null)

  // Tab数据
  const seconTab = ref<StoreTab[]>([])
  // 是否显示回复输入框
  const showReplyInput = ref(false)
  // 回复的内容
  const replyContentStr = ref('')
  // 是否显示申诉指引
  const showAppealGuide = ref(false)
  // 分页参数
  const pagination = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0,
    hasMore: true,
    isRefreshing: false,
  })
  // 页面是否在请求中
  const pageLoading = ref(true)
  // 显示评分
  const showRateing = ref(false)
  // 是否是京东渠道（京东渠道不支持评价）
  const isJingdongChannel = ref(false)

  // 下拉菜单配置
  const dropMenuOptions = ref([])

  // 监听 channelModelValue 变化
  watch(channelModelValue, (newValue) => {
    if (newValue) {
      console.log('第一个标签页切换:', newValue)
      isJingdongChannel.value = newValue === 'JingDongTakeOutBrand'
      // 获取下拉菜单数据
      getDropMenusData(newValue)
      // 立即清空当前门店信息，避免显示上一个渠道的数据
      storeInfo.value = {} as StoreInfo
      pageLoading.value = true
      selectedChannels.value = [newValue]
      if (['JingDongTakeOutBrand'].includes(newValue)) {
        calculateComponentHeights()
        return
      }
      // 可以在这里调用获取门店详情的方法，根据选中的渠道获取数据
      fetchStoreDetailBySelectedChannel()
      // 刷新评价列表
      onRefresh(false)
    }
  })
  // 添加组件高度计算相关的引用和状态
  const mainContentHeight = ref(0)
  const leftCategoryHeight = ref(0)
  // 计算各组件高度
  const calculateComponentHeights = () => {
    // 使用setTimeout确保DOM已经渲染
    setTimeout(() => {
      // 创建查询选择器
      const query = uni.createSelectorQuery()

      // 获取各组件高度
      // query.select('.sy-order-fixed-header').boundingClientRect()
      query.select('.fixed-header').boundingClientRect()

      // 获取系统信息
      uni.getSystemInfo({
        success: (sysInfo) => {
          query.exec((res) => {
            // 检查结果是否有效
            if (!res || !res.length) {
              // 使用默认值作为备选方案
              const defaultHeight = 400
              mainContentHeight.value = defaultHeight
              leftCategoryHeight.value = defaultHeight
              return
            }

            // 计算固定组件的总高度
            const headerHeight = res[0]?.height || 0 // SyOrderFixedHeader高度
            // 获取底部安全区高度和tabbar高度
            const safeAreaBottom = sysInfo.safeAreaInsets?.bottom || 0
            const tabbarHeight = 70 // tabbar高度（一般为50px）
            // 计算页面总高度和主内容区域可用高度
            const windowHeight = sysInfo.windowHeight
            const fixedComponentsHeight = headerHeight
            const bottomHeight = safeAreaBottom + tabbarHeight
            // 计算主内容区域可用高度（考虑了安全区域和底部导航栏）
            const availableHeight = windowHeight - fixedComponentsHeight - bottomHeight
            // 确保高度不会太小，同时考虑一些额外的空间
            const finalHeight = Math.max(availableHeight - 70, 300) // 减去20px的安全边距

            // 更新主内容区域高度
            mainContentHeight.value = finalHeight
          })
        },
        fail: (err) => {
          console.log('获取系统信息失败:', err)
          // 使用默认高度
          mainContentHeight.value = 400
        },
      })
    }, 300) // 给DOM渲染一些时间
  }
  // 获取下拉菜单数据
  const getDropMenusData = (channelModelValue: string) => {
    if (channelModelValue === 'MeiTuanTakeOutBrand') {
      dropMenuOptions.value = [
        {
          label: '是否回复',
          options: [
            { label: '全部', value: 2 },
            { label: '未回复', value: 0 },
            { label: '已回复', value: 1 },
          ],
        },
        {
          label: '近1个月',
          options: [{ label: '近1个月', value: 1 }],
        },
      ]
    } else if (channelModelValue === 'ELeMeTakeOutBrand') {
      dropMenuOptions.value = [
        {
          label: '近1个月',
          options: [
            { label: '近1个月', value: 1 },
            { label: '近2个月', value: 2 },
            { label: '近3个月', value: 3 },
          ],
        },
      ]
    }
  }

  /*
   * 选中的筛选值（数组，每组数据表示一个筛选项）
   * [是否有回复,评价星级, 有无评价内容, 日期筛选]
   */
  const selectedValues = ref([2, 1, 1, 1])

  watch(
    () => selectedValues.value,
    (newValues) => {
      console.log('筛选条件变化:', newValues)
    },
    { deep: true },
  )

  // 监听 secondModelValue 变化，同步更新 secondModelObj
  watch(shopModelValue, (newValue) => {
    if (newValue) {
      console.log('第二个标签页切换:', newValue)
      // 立即清空当前门店信息，避免显示上一个门店的数据
      storeInfo.value = {} as StoreInfo

      // 查找对应的完整数据对象并更新 secondModelObj
      const selectedItem = seconTab.value.find((item) => item.key === newValue)
      secondModelObj.value = selectedItem || null
      console.log('当前选中的二级选项数据:', secondModelObj.value)
      pageLoading.value = true
      fetchStoreDetailBySelectedChannel()
      onRefresh(false)
    }
  })

  // 根据选中的渠道获取门店详情
  const fetchStoreDetailBySelectedChannel = async () => {
    const shopStore = useShopStore()
    try {
      // 先清空当前门店信息，避免显示上一个渠道的数据
      storeInfo.value = {} as StoreInfo

      if (!shopStore.hasSelectedShop) {
        console.error('未选择门店')
        return
      }

      const params = {
        shopId: shopModelValue.value,
        tenantId: secondModelObj.value.tenantId,
        channelCode: channelModelValue.value, // 使用选中的渠道
      }

      // 判断参数是否为空，如果任一字段为空或空字符串，则不执行后续代码
      // 注意：这里使用 === '' 而不是 !params.xxx，因为 0 不应该被判定为空值
      if (
        isEmptyValue(params.shopId) ||
        isEmptyValue(params.tenantId) ||
        isEmptyValue(params.channelCode)
      ) {
        // 参数不完整时，设置空数据
        storeInfo.value = {} as StoreInfo
        return
      }

      const { data, resultCode } = await fetchStoreDetailByChannel(params)
      console.log('storeDetailRes', data, resultCode)
      if ('' + resultCode === '0' && data) {
        // 处理数据
        processStoreData(data)
      } else {
        // 接口返回异常或无数据，设置空数据
        console.log('接口返回异常或无数据，设置空数据')
        storeInfo.value = {} as StoreInfo
      }
    } catch (error) {
      console.error('根据渠道获取门店详情失败:', error)
      // 发生错误时，也设置空数据
      storeInfo.value = {} as StoreInfo
    }
  }

  // 处理门店数据，设置兼容属性
  const processStoreData = (data: any) => {
    // 如果没有数据或数据为空对象，则不处理
    if (!data || Object.keys(data).length === 0) {
      console.log('门店数据为空，不进行处理')
      storeInfo.value = {} as StoreInfo
      return
    }

    console.log('处理门店数据:', data)

    // 处理电话号码
    if (typeof data.phones === 'string') {
      data.phones = data.phones.split(',')
    }

    // 处理营业时间
    if (typeof data.businessTime === 'string') {
      data.openTime = data.businessTime.split(',')
    }

    // 设置兼容属性
    data.rating = data.avgScore
    data.taste = data.avgTasteScore
    data.packaging = data.avgPackingScore
    data.deliveryRate = data.avgDeliveryScore
    data.category = data.businessCategory
    data.phone = data.phones
    data.status = data.businessStatus === 1 ? '营业中' : '未营业'
    showRateing.value = true
    // 更新门店信息
    storeInfo.value = data
  }

  const initPage = () => {
    getStatusBarHeight()
    getHeaderHeight()
    setTimeout(() => {
      calculateComponentHeights()
    }, 0)
    // getReviewHeaderHeight()
  }

  // 获取状态栏高度
  const getStatusBarHeight = () => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight || 0
    console.log(' systemInfo.statusBarHeight', systemInfo.statusBarHeight)
  }

  // 获取头部区域高度
  const getHeaderHeight = () => {
    const query = uni.createSelectorQuery()
    query
      .select('.fixed-header')
      .boundingClientRect((data) => {
        if (data && !Array.isArray(data)) {
          headerHeight.value = data.height || 0
          console.log('头部高度:', headerHeight.value)
        }
      })
      .exec()
  }

  const getReviewHeaderHeight = () => {
    const query = uni.createSelectorQuery()
    query
      .select('.review-container')
      .boundingClientRect((data) => {
        if (data && !Array.isArray(data)) {
          reviewHeaderHeight.value = data.height || 0
          console.log('头部高度:', headerHeight.value)
        }
      })
      .exec()
  }

  // 初始化门店信息
  const initStoreInfo = () => {
    const shopStore = useShopStore()

    console.log('获取门店信息：', shopStore.hasSelectedShop, shopStore.currentShop)
    try {
      if (shopStore.hasSelectedShop) {
        const shop = shopStore.currentShop
        // 更新当前选择的门店信息
        currentShopInfo.value = {
          name: shop?.name || '',
          id: shop?.id || '',
          address: shop?.raw?.address || '',
          shopDtoList: shop?.raw?.shopDtoList || [],
        } as ShopDto

        // 将 shopDtoList 映射到 seconTab
        if (shop?.raw?.shopDtoList && Array.isArray(shop.raw.shopDtoList)) {
          seconTab.value = shop.raw.shopDtoList.map((item) => ({
            key: item.id || '',
            label: item.name || '',
            ...item,
          }))
          // 默认选中第一个店铺
          if (seconTab.value.length > 0) {
            shopModelValue.value = seconTab.value[0].key
            // 同步更新 secondModelObj
            secondModelObj.value = shop.raw.shopDtoList[0]
          }
        }
      }
    } catch (error) {
      console.error('Error in initStoreInfo:', error)
    }
  }

  // 获取渠道数据
  const getChannelData = async () => {
    try {
      const res = await fetchChannelEnum({
        type: 'evaluate',
      })
      if (res?.data) {
        // 处理渠道数据，将其转换为 StoreTab 格式
        channelOptions.value = res.data.map((item) => ({
          key: item.code,
          label: item.name,
        }))

        // 默认选中第一个渠道
        if (channelOptions.value.length > 0) {
          channelModelValue.value = channelOptions.value[0].key
          isJingdongChannel.value = channelModelValue.value === 'JingDongTakeOutBrand'
          getDropMenusData(channelModelValue.value)
        }
      }
    } catch (error) {
      console.error('获取渠道数据失败:', error)
    }
  }

  // 跳转到选择默认物理门店页
  function goToSelectShop() {
    uni.navigateTo({
      url: '/pages/shop/list/index',
      success: () => {
        console.log('跳转到选择门店页面成功')
      },
      fail: (err) => {
        console.error('跳转到选择门店页面失败:', err)
        uni.showToast({
          title: '跳转失败',
          icon: 'none',
        })
      },
    })
  }

  // 关闭申诉指引
  const appealTxt = ref('暂无申诉入口，如需申诉请到美团外卖商家版操作，操作路径:店铺-顾客评价-申诉')
  const handleConfirmAppeal = () => {
    showAppealGuide.value = !showAppealGuide.value
  }

  // 订单提示
  const orderTip = ref('')
  // 模拟原始评价数据
  const reviewList = ref([])

  // 筛选变化时的处理
  const handleFilterChange = (values, index) => {
    console.log('当前筛选条件：', values, index)
    // 可在此对接接口请求筛选后的数据
    if (channelModelValue.value === 'MeiTuanTakeOutBrand') {
      if (index === 0) {
        selectedValues.value[index] = values.value
      }
    }
    if (channelModelValue.value === 'ELeMeTakeOutBrand') {
      selectedValues.value[index] = values.value
    }
    onRefresh(false)
  }

  // 待回复的评价
  const waitReplyItem = ref(null)
  // 回复点击
  const toChangeVisiableReview = (index) => {
    showReplyInput.value = true
    // 可弹出回复框或跳转回复页面
    waitReplyItem.value = reviewList.value[index]
  }

  // 执行回复
  const handleReply = () => {
    showReplyInput.value = false
    toReplyReview(waitReplyItem.value.evaluateId)
  }

  // 获取评价列表
  const getReviewList = async () => {
    try {
      if (!channelModelValue.value) {
        return
      }
      const Params: Partial<QueryReviewParams> = {
        shopId: shopModelValue.value,
        channelCode: channelModelValue.value,
        pageNum: pagination.value.pageNum,
        pageSize: pagination.value.pageSize,
        tenantId: secondModelObj.value.tenantId,
        month: selectedValues.value[3],
      }
      if (channelModelValue.value === 'MeiTuanTakeOutBrand') {
        Params.replyStatus = selectedValues.value[0] === 2 ? '' : selectedValues.value[0]
      }
      const res = await fetchReviewList(Params as QueryReviewParams)
      console.log('评价列表返回', res)
      pagination.value.isRefreshing = false
      if (res.resultCode === '0') {
        if (res.data.list && res.data.list.length > 0) {
          res.data.list = res.data.list.map((review) => ({
            ...review,
            evaluatePictures: review.evaluatePictures?.slice(0, 9) || [],
          }))
          reviewList.value = [...reviewList.value, ...res.data.list]
        }
        if (!res.data.hasNextPage || res.data.list.length < pagination.value.pageSize) {
          pagination.value.hasMore = false
        } else {
          pagination.value.hasMore = true
        }
      }
    } catch (error) {
      console.error('获取评价列表数据失败:', error)
    } finally {
      // 隐藏全屏加载状态
      setTimeout(() => {
        pageLoading.value = false
      }, 400) // 添加短暂延迟，确保视觉上的流畅性
    }
  }

  // 回复顾客评价
  const toReplyReview = async (curEvaluateId: string) => {
    const params: Partial<ReplyReviewParams> = {
      channelCode: channelModelValue.value,
      evaluateId: curEvaluateId,
      replyContent: replyContentStr.value,
      shopId: shopModelValue.value,
      tenantId: secondModelObj.value.tenantId,
    }
    const res = await replyReview(params as ReplyReviewParams)
    console.log('回复评价返回', res)
    if (res.resultCode === '0') {
      replyContentStr.value = ''
      pagination.value.pageNum = 1
      // 重新获取评价列表
      getReviewList()
    }
  }

  const onLoadMore = () => {
    console.log('加载更多')
    if (!pagination.value.hasMore || pagination.value.isRefreshing) {
      console.log(
        '没有更多数据或正在加载中',
        '有更多数据:',
        pagination.value.hasMore,
        '加载中:',
        pagination.value.isRefreshing,
      )
      return
    }

    pagination.value.pageNum += 1
    console.log('开始加载第', pagination.value.pageNum, '页数据')
    getReviewList()
  }

  const onRefresh = (isRefresh: boolean) => {
    console.log('执行刷新')
    // 先清空评价列表
    reviewList.value = []
    pagination.value.pageNum = 1
    pagination.value.hasMore = true
    pagination.value.isRefreshing = isRefresh
    getReviewList()
  }

  return {
    currentShopInfo,
    storeInfo,
    seconTab,
    channelOptions,
    initStoreInfo,
    getChannelData,
    channelModelValue,
    shopModelValue,
    secondModelObj,
    goToSelectShop,
    statusBarHeight,
    getStatusBarHeight,

    dropMenuOptions,
    selectedValues,
    reviewList,
    handleFilterChange,
    toChangeVisiableReview,
    handleReply,
    showReplyInput,
    replyContentStr,
    showAppealGuide,
    handleConfirmAppeal,
    appealTxt,
    getReviewList,
    getDaysAfter,
    orderTip,
    pagination,
    toReplyReview,
    onLoadMore,
    onRefresh,
    headerHeight,
    reviewHeaderHeight,
    initPage,
    pageLoading,
    showRateing,
    isJingdongChannel,
    selectedChannels,
    mainContentHeight,
  }
}
