.preorder-page {
  padding-top: var(--status-bar-height);
  .preorder-container {
    width: 100%;
    background-color: #f5f5f5;
    min-height: 100vh;
    display: flex;
    flex-direction: column;

    // 标签栏外层容器
    .tabs-header {
      width: 100%;
      display: flex;
      align-items: center;
      background-color: #fff;
      border-bottom: 1px solid #eee;
      padding: 0rpx 20rpx;
      // 左侧固定文本
      .send-time-text {
        width: 98rpx;
        font-size: 28rpx;
        color: #333;
        margin-right: 20rpx;
        white-space: pre-wrap;
      }
      :deep(.wd-tabs) {
        width: calc(100% - 96rpx);
      }
      :deep(.wd-tabs__nav) {
        height: var(--wot-tabs-nav-height, 124rpx);
      }
      .preorder-tabs {
        flex: 1;

        .custom-tab {
          .custom-tab-title {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;

            text {
              line-height: 1.2;
            }
          }
        }
        :deep(.wd-tabs__nav-item-text) {
          white-space: pre-line;
        }
        :deep(.wd-tabs__nav-container) {
          align-items: center;
        }
        :deep(.uni-scroll-view-content) {
          display: flex;
          text-align: center;
        }
        :deep(.wd-tabs__line) {
          bottom: 0rpx;
          height: 10rpx;
        }
        :deep(.uni-scroll-view) {
          ::-webkit-scrollbar {
            display: none;
          }
        }
      }
    }
    .content-area {
      flex: 1;

      .scrollable-content {
        background-color: #f1f1f1;

        .order-card-list {
          margin-top: 20rpx;
        }
        // 无更多数据
        .no-more {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 40rpx 0;

          .no-more-text {
            font-size: 28rpx;
            color: #999999;
          }
        }

        // 空状态
        .empty-state {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 120rpx 0;

          .empty-text {
            font-size: 32rpx;
            color: #999999;
          }
        }
      }
    }
  }
}
