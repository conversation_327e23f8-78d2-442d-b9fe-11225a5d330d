<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '预订单',
    navigationStyle: 'custom',
    'app-plus': {
      titleNView: false,
    },
  },
}
</route>
<template>
  <view class="preorder-page">
    <wd-navbar title="预订单" left-text="" left-arrow @click-left="handleClickBack"></wd-navbar>
    <view class="preorder-container">
      <!-- 顶部日期切换栏 -->
      <view class="tabs-header">
        <!-- 固定左侧文本 -->
        <view class="send-time-text">{{ lineBreakText }}</view>
        <wd-divider style="height: 68rpx; margin: 0rpx" vertical />
        <!-- Wot UI 的 tabs 组件 -->
        <!-- 使用v-for手动渲染tab，实现标题换行显示 -->
        <wd-tabs
          v-model="currentTab"
          custom-class="preorder-tabs"
          slidable="always"
          @change="handleTabChange"
        >
          <block v-for="(item, index) in tabList" :key="index">
            <wd-tab
              :title="`${item.text}\n${currentTabIndex === index && total > 0 ? total : '-'}`"
              :name="item.text"
              class="custom-tab"
            >
              <!-- 每个tab的内容区域 -->
              <view class="tab-content"></view>
            </wd-tab>
          </block>
        </wd-tabs>

        <!-- 动态切换的内容区域 -->
      </view>
      <view class="content-area">
        <!-- 根据当前选中的tab索引显示对应的内容 -->

        <!-- 内容滚动区域 -->
        <scroll-view
          ref="scrollViewRef"
          class="scrollable-content"
          :scroll-y="true"
          @scrolltolower="onLoadMore"
          :refresher-enabled="true"
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onRefresh"
          :scroll-into-view="scrollToView"
          :scroll-with-animation="true"
          :style="{
            height: headerHeight
              ? `calc(100vh - ${2 * headerHeight + statusBarHeight}px)`
              : 'calc(100vh - 150px)',
          }"
        >
          <!-- 登录状态检查 -->
          <!-- <view v-if="!isLoggedIn" class="login-status-message">
              <text class="login-message-text">正在检查登录状态...</text>
            </view> -->
          <!-- 顶部锚点元素 -->
          <view id="top" style="width: 1rpx; height: 1rpx"></view>
          <!-- 订单卡片列表 -->
          <view class="order-card-list">
            <view v-if="!loading">
              <SyOrderCard
                v-for="order in orderList"
                :key="order.id"
                :id="'order-' + order.id"
                :order-data="transformOrderData(order, false)"
                :expanded="orderCardExpandedMap[order.id]"
                :is-after-sale-mode="false"
                @expand-toggle="(expanded) => onOrderCardExpand(order.id, expanded)"
                @call="onCallCustomer"
                @address-click="(address) => onAddressClick(order)"
                @operation-result="onOperationResult"
                @after-sale-approve="onAfterSaleApprove"
                @after-sale-reject="onAfterSaleReject"
                @refund-order="onRefundOrder"
              />
            </view>

            <SyLoading
              v-if="loading"
              :show="true"
              src="/static/images/img/goods-loading.gif"
              toastText="数据加载中..."
              :imgWidth="200"
              :imgHeight="200"
              :showMask="false"
              :fixed="false"
            />
            <!-- 加载状态 -->
            <view v-if="loading" class="loading-container">
              <!-- <wd-loading size="30rpx" />
              <text class="loading-text">加载中...</text> -->
            </view>

            <!-- 空状态 -->
            <view v-if="!loading && orderList.length === 0" class="empty-state">
              <!-- <text class="empty-text">暂无订单数据</text> -->
              <wd-status-tip image="search" tip="暂无订单数据" />
            </view>

            <!-- 无更多数据 -->
            <view v-if="!hasMore && orderList.length > 0 && !loading" class="no-more">
              <text class="no-more-text">没有更多数据了</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import SyOrderCard from '@/components/sy-order-card'
import SyOrderMiniCard from '@/components/sy-order-mini-card'
import { onShow, onLoad } from '@dcloudio/uni-app'
import SyLoading from '@/components/sy-loading/sy-loading.vue'
import {
  statusBarHeight,
  orderList,
  isRefreshing,
  loading,
  hasMore,
  onLoadMore,
  onRefresh,
  onCallCustomer,
  onAddressClick,
  onExpandOrder,
  transformOrderData,
  initPage,
  handlePageShow,
  headerHeight,
  onStatsExpand,
  orderStats,
  isOrderCardsExpanded,
  onOrderExpandToggle,
  onOrderAction,
  orderCardExpandedMap,
  onOrderCardExpand,
  showAsMiniCard,
  onOperationResult,
  onAfterSaleApprove,
  onAfterSaleReject,
  approvePopupConfig,
  rejectPopupConfig,
  handleApproveConfirm,
  handleApproveCancel,
  handleRejectConfirm,
  handleRejectCancel,
  scrollViewRef,
  scrollToView,
  handleTabChange,
  currentTab,
  currentTabIndex,
  onRefundOrder,
  total,
  tabList,
  initTabList,
} from './index'

const lineBreakText = ref('送达\n时间')
// 是否初始化过页面
const hasInitPage = ref(false)

onMounted(() => {
  // 初始化标签数据（“即将到时” + 往后 7 天 ）
  initPage()
})

onLoad(() => {
  if (!hasInitPage.value) {
    initTabList()
    hasInitPage.value = true
    console.log('我执行了onLoad,并标记初始化状态：', hasInitPage.value)
  }
  // 确保初始选中的是即时订单所需要的时间参数
  console.log('我执行了onLoad')
})

onShow(() => {
  console.log('我执行了onShow', currentTabIndex.value)
  handlePageShow()
})

const handleClickBack = () => {
  uni.navigateBack()
}
</script>
<style lang="scss" scoped>
@import './index';
</style>
