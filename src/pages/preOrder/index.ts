import { useUserStore } from '@/store'
import { onShow } from '@dcloudio/uni-app'
import { useShopStore } from '@/store/shop'
import { ToastOptions } from '@/components/sy-new-order-toast/toast'
import {
  fetchOrderTypeEnum,
  fetchChannelEnum,
  fetchOrderStatusEnum,
  fetchRefundOrderStatusEnum,
} from '@/service/order/dict'
import {
  fetchOrderList,
  QueryOrderParams,
  reportMeals,
  confirmOrder,
  auditAfterSale,
} from '@/service/preOrder'
import { channelIconMap, getChannelIcon } from '@/components/sy-channel-selector/utils'
import { transformOrderData as apiTransformOrderData } from '@/utils/transform'
import { getCurrentDate, getDateOfNumDay, getCurrentTime, getLastHour } from '@/utils/datetime'
import { selectedFilters } from '@/pages/index/index'
import { isEmptyValue } from '@/utils/index'

export const currentTab = ref('即将到时') // 当前选中的标签
export const currentTabIndex = ref(0)

// 当前开始日期时间
const selectStartTime = ref(getCurrentTime())
const selectEndTime = ref(getLastHour())

// scroll-view引用
export const scrollViewRef = ref<any>(null)
// scroll-view滚动控制
export const scrollToView = ref<string>('')

// 类型定义
export interface Store {
  id: string
  code: string
  name: string
  address?: string
  avatar?: string
  subShopIds?: string[]
}

export interface Customer {
  name: string
  phone: string
  fullPhone: string
}

export interface Order {
  id: string
  orderNumber: string
  status: string
  type: string
  bizType: string
  deliveryTime: string
  createTime?: string // 添加创建时间字段
  saleChannel?: string
  merchantConfirmTime?: string
  buyerRemark?: string // 添加买家备注字段
  merchant: {
    id: string
    name: string
    avatar: string
  }
  customer: Customer
  address: {
    full: string
    distance?: string
  }
  warning?: {
    text: string
    countdown: {
      minutes: string
      seconds: string
    }
  }
  statusInfo: {
    main: string
    time?: string
    description?: string
  }
  delivery?: {
    text: string
    time: string
    platform?: string
  }
  channel?: string // 添加渠道字段
  items: any[] | string // 修改为可以是数组或字符串
  amount: string
  fees?: {
    // 添加费用字段
    merchantIncome: number
    deliveryFee: number
    packagingFee: number
    totalAmount: number
  }
  apiData?: any // 添加原始API数据字段
}

export interface OrderListResponse {
  list: any[]
  hasNextPage: boolean
  [key: string]: any
}

// 响应式状态
export const statusBarHeight = ref(0)
export const headerHeight = ref(0)

// 加载状态
export const isRefreshing = ref(false)
export const loading = ref(false)
export const hasMore = ref(true)
export const pageNum = ref(1)
export const pageSize = ref(10)
// 订单数
export const total = ref(0)

/**
 * 订单统计信息展开状态
 */
export const isStatsExpanded = ref(false)

/**
 * 订单卡片展开状态
 */
export const isOrderCardsExpanded = ref(false)

/**
 * 订单卡片展开状态映射
 */
export const orderCardExpandedMap = ref<Record<string, boolean>>({})

export const activeStatus = ref<string>('ALL')

export const onLoadMore = () => {
  console.log('加载更多')
  if (!hasMore.value || loading.value) {
    console.log('没有更多数据或正在加载中', '有更多数据:', hasMore.value, '加载中:', loading.value)

    return
  }

  pageNum.value += 1
  console.log('开始加载第', pageNum.value, '页数据')
  fetchOrders()
}

export const onRefresh = () => {
  console.log('下拉刷新')
  pageNum.value = 1
  hasMore.value = true
  isRefreshing.value = true
  fetchOrders()
}

/**
 * 滚动到顶部的函数
 */
export const scrollToTop = () => {
  console.log('执行滚动到顶部')

  try {
    // 设置scrollToView
    scrollToView.value = 'top'

    // 延迟执行滚动，确保DOM已更新
    setTimeout(() => {
      // 方法1：H5环境
      // #ifdef H5
      try {
        const scrollEl = document.querySelector('.scrollable-content')
        if (scrollEl) {
          console.log('H5环境：直接设置scrollTop')
          scrollEl.scrollTop = 0
          scrollEl.scrollTo && scrollEl.scrollTo(0, 0)
        }
      } catch (err) {
        console.error('H5滚动失败:', err)
      }
      // #endif

      // 方法2：使用uni.pageScrollTo
      try {
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 100,
        })
      } catch (err) {
        console.error('pageScrollTo失败:', err)
      }

      // 方法3：尝试使用ref
      try {
        if (scrollViewRef.value) {
          console.log('使用ref设置scrollTop')
          if (typeof scrollViewRef.value.scrollTo === 'function') {
            scrollViewRef.value.scrollTo(0, 0)
          } else if (scrollViewRef.value.scrollTop !== undefined) {
            scrollViewRef.value.scrollTop = 0
          }
        }
      } catch (err) {
        console.error('使用ref滚动失败:', err)
      }

      // 方法4：再次触发scrollToView (重置再设置，确保触发)
      try {
        scrollToView.value = ''
        setTimeout(() => {
          scrollToView.value = 'top'
        }, 50)
      } catch (err) {
        console.error('scrollToView失败:', err)
      }
    }, 100)
  } catch (err) {
    console.error('滚动到顶部失败:', err)
  }
}

export const onCallCustomer = (phone: string) => {
  uni.makePhoneCall({
    phoneNumber: phone,
    fail: (err) => {
      console.error('拨打电话失败:', err)
      uni.showToast({
        title: '拨打电话失败',
        icon: 'error',
      })
    },
  })
}

export const onAddressClick = (order: Order) => {
  console.log('查看地址:', order.address)
}

export const onExpandOrder = (order: Order) => {
  console.log('展开订单:', order.id)
}

// 切换标签回调
export async function handleTabChange(values) {
  currentTab.value = values.name
  currentTabIndex.value = values.index
  // 即将到时得订单为当日提前一个小时
  if (values.index === 0) {
    selectStartTime.value = getCurrentTime()
    selectEndTime.value = getLastHour()
  } else {
    selectStartTime.value = getDateOfNumDay(values.index - 1) + ' 00:00:00'
    selectEndTime.value = getDateOfNumDay(values.index - 1) + ' 23:59:59'
  }

  console.log('切换到：', currentTab.value, values.name, selectStartTime.value, selectEndTime.value)
  orderList.value = []
  total.value = 0
  pageNum.value = 1
  hasMore.value = true
  loading.value = true
  await fetchOrders()
  scrollToTop()
}

// 获取状态栏高度
const getStatusBarHeight = () => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
}

// 获取头部区域高度
export const getHeaderHeight = () => {
  const query = uni.createSelectorQuery()
  query
    .select('.tabs-header')
    .boundingClientRect((data) => {
      if (data && !Array.isArray(data)) {
        headerHeight.value = data.height || 0
        console.log('头部高度:', headerHeight.value)
      }
    })
    .exec()
}

export const fetchOrders = async () => {
  try {
    // 检查登录状态
    const userStore = useUserStore()
    if (!userStore.userInfo?.token) {
      console.log('用户未登录，暂不获取订单数据')
      loading.value = false
      return
    }

    // 处理渠道代码
    let channelCodes
    if (
      selectedFilters.value.multipleChannelCodes &&
      selectedFilters.value.multipleChannelCodes.length > 0
    ) {
      // 确保所有值都是字符串且有效
      channelCodes = selectedFilters.value.multipleChannelCodes
        .map((code) => String(code))
        .filter(
          (code) => code !== 'false' && code !== 'undefined' && code !== 'null' && code !== '',
        )

      // 记录一下当前处理的渠道值
      console.log('处理前的渠道代码:', selectedFilters.value.multipleChannelCodes)
      console.log('处理后的渠道代码:', channelCodes)

      // 如果过滤后数组为空，则设为undefined
      if (channelCodes.length === 0) {
        channelCodes = undefined
      }
    } else {
      channelCodes = undefined
    }

    console.log('最终处理后的渠道代码:', channelCodes)

    // 处理门店ID
    const shopIds = selectedFilters.value.shopIds

    console.log('门店ID:', shopIds)

    const params: Partial<QueryOrderParams> = {
      startSendTime: selectStartTime.value,
      endSendTime: selectEndTime.value,
      isBook: 2,
      pageNum: pageNum.value,
      pageSize: pageSize.value,
    }

    if (!isEmptyValue(shopIds)) {
      params.shopIds = shopIds
    }

    // 渠道代码 - 只有非空时才添加
    if (!isEmptyValue(channelCodes)) {
      params.channelCodes = channelCodes
    }

    console.log('请求参数:', params)
    const res = (await fetchOrderList(params as QueryOrderParams)) as unknown as {
      data: OrderListResponse
    }

    // 直接使用后端返回的数据
    const orderData = res.data?.list || []

    // 如果是第一页且没有数据，直接清空列表
    if (pageNum.value === 1 && orderData.length === 0) {
      orderList.value = []
      total.value = 0
      hasMore.value = false
      console.log('接口返回空数据，清空订单列表')
      loading.value = false
      isRefreshing.value = false
      return
    }

    // 转换数据
    const newOrders = orderData.map((item) => ({
      id: item.id || '',
      orderNumber: item.orderNo || item.tradeNo || '',
      status: item.orderStatus || '',
      type: item.bizTypeName || '',
      bizType: item.bizType || '',
      deliveryTime: item.sendTime ? item.sendTime.substring(0, 16) : '',
      createTime: item.createTime || item.placeTime || '',
      merchant: {
        id: item.shopId || '',
        name: item.shopName || '',
        avatar: '/static/images/img/mt.png',
      },
      customer: {
        name: item.contactName || (item.customerInfo ? item.customerInfo.userName : ''),
        phone: item.phone
          ? item.phone.substring(item.phone.length - 4)
          : item.hiddenPhone
            ? item.hiddenPhone.substring(item.hiddenPhone.length - 4)
            : '',
        fullPhone: item.phone || item.reservedPhone || '',
      },
      address: {
        full: (() => {
          try {
            if (item.extlOrderDetail) {
              const detail = JSON.parse(item.extlOrderDetail)
              return detail.syncThirdOrderReqDto?.address || item.deliveryAddress || ''
            }
          } catch (e) {
            console.error('解析地址信息失败:', e)
          }
          return item.deliveryAddress || ''
        })(),
        distance: '<1km',
      },
      warning:
        item.orderStatus === 'PAYED' || item.orderStatus === '20'
          ? {
              text: '订单即将超时',
              countdown: {
                minutes: '05',
                seconds: '00',
              },
            }
          : undefined,
      statusInfo: {
        main: (() => {
          switch (item.orderStatus) {
            case '10':
              return '待接单'
            case '20':
              return '待出餐'
            case 'WAIT_REPORT_MEALS':
              return '待出餐'
            case 'WAIT_CELL_DELIVERY':
              return '待发配送'
            case 'WAIT_RIDER_ACCEPT':
              return '骑手待接单'
            case 'INSHOP':
              return '待处理'
            case 'WAIT_MERCHANT_CONFIRM':
              return '待商家确认'
            case 'CONFIRM':
              return '已接单'
            case 'COMPLETED':
              return '已送达'
            case '50':
              return '配送中'
            case '80':
              return '已完成'
            case 'RIDER_ACCEPT':
              return '骑手已接单'
            case 'RIDER_ARRIVE_SHOP':
              return '骑手已到店'
            case 'DELIVERING':
              return '配送中'
            case 'DELIVERING_OVERTIME':
              return '配送超时'
            case 'OVERTIME':
              return '超时未送达'
            case '60':
              return '已取餐'
            case 'TAKED':
              return '已取餐'
            case 'FINISHED':
              return '已完成'
            case 'ARRIVED':
              return '已送达'
            case '-10':
              return '已取消'
            case '-20':
              return '已取消'
            case 'CANCELED':
              return '已取消'
            case 'CLOSED':
              return '已关闭'
            default:
              return item.bizStatusDesc || item.statusDesc || '未知状态'
          }
        })(),
        time: item.placeTime ? item.placeTime.substring(11, 16) : '',
        description: '',
      },
      channel: item.saleChannelDesc || '',
      items: item.items || [],
      amount: item.totalAmount || '0',
      fees: {
        merchantIncome: parseFloat(item.incomeAmount || '0'),
        deliveryFee: parseFloat(item.deliveryFee || '0'),
        packagingFee: parseFloat(item.packageAmount || '0'),
        totalAmount: parseFloat(item.totalAmount || '0'),
      },
      // 保存原始API数据，方便上报出餐等功能使用
      apiData: item,
    }))

    // 修改这里：根据页码决定是替换还是追加数据
    if (pageNum.value === 1) {
      orderList.value = newOrders
    } else {
      orderList.value = [...orderList.value, ...newOrders]
    }

    // 使用total和已加载的数据量来判断是否有更多数据
    total.value = res.data?.total || 0
    const loadedCount = orderList.value.length
    hasMore.value = loadedCount < total.value

    // 计算总页数
    const totalPages = Math.ceil(total.value / pageSize.value)
    const currentPage = pageNum.value

    console.log(
      '获取订单列表成功',
      orderList.value.length,
      '总数据量:',
      total,
      '当前页:',
      currentPage,
      '总页数:',
      totalPages,
    )
  } catch (error) {
    console.error('获取订单列表失败:', error)
  } finally {
    // 隐藏全屏加载状态
    setTimeout(() => {
      loading.value = false
      isRefreshing.value = false
    }, 300) // 添加短暂延迟，确保视觉上的流畅性
  }
}

// 数据转换函数：将现有订单数据转换为新组件格式
export const transformOrderData = (order: Order, isAfterSaleMode?: boolean) => {
  if (!order) return {} as any

  // 如果订单对象中已经包含完整的原始API数据，则直接使用新的转换函数
  if (order.apiData) {
    const transformedData = apiTransformOrderData(order.apiData)

    // 如果是售后模式，确保售后数据正确处理
    if (isAfterSaleMode && order.apiData.refundRecords) {
      // 确保售后数据是数组格式
      transformedData.afterSale = Array.isArray(order.apiData.refundRecords)
        ? order.apiData.refundRecords
        : [order.apiData.refundRecords]
    }

    return transformedData
  }

  // 否则使用旧的转换逻辑，将Order格式转换为OrderData格式
  // 尝试解析扩展字段的地址信息
  let address = order.address.full

  try {
    if (order.address && order.address.full && order.address.full.includes('extlOrderDetail')) {
      const extlOrderDetail = JSON.parse(order.address.full)
      if (extlOrderDetail.syncThirdOrderReqDto) {
        address = extlOrderDetail.syncThirdOrderReqDto.address || ''
      }
    }
  } catch (e) {
    console.error('解析地址信息失败:', e)
  }

  // 返回符合组件期望的格式
  return {
    id: order.id,
    orderNo: order.orderNumber,
    deliveryTime: order.deliveryTime,
    status: order.statusInfo.main,
    type: order.type,
    bizType: order.apiData.bizType,
    isBook: order.apiData.isBook,
    saleChannel: order.saleChannel,
    buyerRemark: order.buyerRemark,
    merchantConfirmTime: order.merchantConfirmTime,
    merchant: {
      avatar: order.merchant.avatar || '/static/images/img/mt.png',
      name: order.merchant.name,
      id: order.merchant.id,
    },
    customer: {
      name: order.customer.name,
      phone: order.customer.fullPhone,
    },
    address: {
      detail: address,
      distance: order.address.distance || '<1km',
    },

    orderStatus: {
      text: order.statusInfo.main,
      time: order.statusInfo.time || '',
      description: order.buyerRemark || order.statusInfo.description || '',
    },
    delivery: {
      method: '外卖配送',
      time: order.deliveryTime ? `预计${order.deliveryTime.substring(11, 16)}送达` : '',
      platform: order.channel || '',
    },
    goods: {
      count: order.items?.length || 1,
      summary: order.items?.length > 0 ? order.items[0].name : '商品',
      items: order.items || [],
    },
    fees: {
      merchantIncome: order.fees?.merchantIncome || 0,
      deliveryFee: order.fees?.deliveryFee || 0,
      packagingFee: order.fees?.packagingFee || 0,
      totalAmount: order.fees?.totalAmount || 0,
    },
    times: {
      createTime: order.createTime || '',
      placeTime: order.createTime || '',
      deliveryTime: order.deliveryTime || '',
    },
  }
}

/**
 * 退款
 */
export const onRefundOrder = async (thirdOrderCode: string) => {
  console.log('点击退款按钮:', thirdOrderCode)

  // 跳转到取消订单页面，并传递订单编号参数
  uni.navigateTo({
    url: `/pages/order/cancelOrder/index?thirdOrderCode=${thirdOrderCode}`,
  })
}

// 新的事件处理函数
export const onOrderAction = async (orderData: any, action: string) => {
  console.log('订单操作:', action, orderData)
  switch (action) {
    case 'accept':
      console.log('接单')
      try {
        // 获取渠道单号、订单渠道、门店ID和租户ID
        const thirdOrderCode = orderData?.channelOrderNo
        const channelCode = orderData?.saleChannel
        const shopId = orderData?.merchant?.id
        const tenantId = orderData?.tenantId

        if (!thirdOrderCode || !channelCode || !shopId || !tenantId) {
          uni.showToast({
            title: '订单信息不完整，无法接单',
            icon: 'none',
          })
          return
        }

        // 显示加载提示
        uni.showLoading({
          title: '接单中...',
          mask: true,
        })

        // 调用确认接单接口
        const result = await confirmOrder({
          thirdOrderCode,
          channelCode,
          shopId,
          tenantId,
        })

        if (result && result.resultCode === '0') {
          uni.showToast({
            title: '接单成功',
            icon: 'success',
          })
          // 刷新订单列表
          await fetchOrders()
        } else {
          uni.showToast({
            title: result?.resultMsg || '接单失败',
            icon: 'none',
          })
        }
      } catch (error) {
        console.error('接单失败:', error)
        uni.showToast({
          title: '接单失败，请稍后重试',
          icon: 'none',
        })
      } finally {
        // 隐藏加载提示
        uni.hideLoading()
      }
      break
    case 'report':
      console.log('上报出餐')
      try {
        // 获取渠道单号和订单渠道
        const thirdOrderCode = orderData?.channelOrderNo
        const orderChannel = orderData?.saleChannel

        if (!thirdOrderCode || !orderChannel) {
          uni.showToast({
            title: '订单信息不完整，无法上报出餐',
            icon: 'none',
          })
          return
        }

        // 调用上报出餐接口
        const result = await reportMeals({
          thirdOrderCode,
          orderChannel,
        })
        console.log('result', result)

        if (result && result.resultCode === '0') {
          uni.showToast({
            title: '上报出餐成功',
            icon: 'success',
          })
          // 刷新订单列表
          await fetchOrders()
        } else {
          uni.showToast({
            title: result?.resultMsg || '上报出餐失败',
            icon: 'none',
          })
        }
      } catch (error) {
        console.error('上报出餐失败:', error)
        uni.showToast({
          title: '上报出餐失败，请稍后重试',
          icon: 'none',
        })
      }
      break
    case 'finish':
      console.log('出餐完成')
      break
    case 'refund':
      console.log('退款')
      break
    case 'cancel':
      console.log('取消订单')
      break
    default:
      console.log('未知操作:', action)
  }
}

export const onOrderCardClick = (orderData: any) => {
  console.log('订单卡片点击:', orderData)
  // TODO: 跳转到订单详情页面
}

export const onOrderExpandToggle = (expanded: boolean) => {
  console.log('订单展开状态:', expanded)
  isOrderCardsExpanded.value = expanded
}

// 生命周期相关的初始化函数（供Vue组件调用）
export const initPage = async () => {
  getStatusBarHeight()
  getHeaderHeight()
}

export const tabList = ref([]) // 标签数据
// 初始化标签方法
export function initTabList() {
  const today = new Date()
  const baseTabs = [
    { text: '即将到时', count: 0 }, // 第一个固定为“即将到时”
    { text: '今日', count: 0 },
  ]
  // 往后追加 7 天，凑齐 9 个标签（“即将到时” + 8 天 ）
  for (let i = 1; i < 8; i++) {
    const targetDate = new Date(today.getTime() + i * 24 * 60 * 60 * 1000)
    const month = (targetDate.getMonth() + 1).toString().padStart(2, '0')
    const day = targetDate.getDate().toString().padStart(2, '0')
    if (i === 1) {
      baseTabs.push({
        text: '明日',
        count: 0,
      })
    } else {
      baseTabs.push({
        text: `${day}日`,
        count: 0,
      })
    }
  }
  tabList.value = baseTabs
  currentTab.value = tabList.value[0].text
  selectStartTime.value = getCurrentTime()
  selectEndTime.value = getLastHour()
  console.log('tabList', tabList.value)
}

export const handlePageShow = async () => {
  // 确保页码从1开始
  pageNum.value = 1
  await fetchOrders()
}

// 订单列表数据
export const orderList = ref<Order[]>([])

/**
 * 处理订单统计信息展开/收起
 */
export const onStatsExpand = (expanded: boolean) => {
  console.log('统计信息展开状态变更前:', expanded, JSON.stringify(orderCardExpandedMap.value))

  // 先更新统计信息的展开状态
  isStatsExpanded.value = expanded
  isOrderCardsExpanded.value = expanded

  // 确保 orderList 存在
  if (orderList.value && orderList.value.length > 0) {
    // 创建一个新对象来更新 orderCardExpandedMap，确保触发响应式更新
    const newMap = { ...orderCardExpandedMap.value }

    // 同步更新所有订单卡片的展开状态
    orderList.value.forEach((order) => {
      if (order && order.id) {
        newMap[order.id] = expanded
      }
    })

    // 一次性更新整个对象，确保触发响应式更新
    orderCardExpandedMap.value = newMap
  }

  console.log('统计信息展开状态变更后:', expanded, JSON.stringify(orderCardExpandedMap.value))
}

/**
 * 处理单个订单卡片展开/收起
 */
export const onOrderCardExpand = (orderId: string, expanded: boolean) => {
  orderCardExpandedMap.value[orderId] = expanded
}

// 订单统计信息
export const orderStats = computed(() => {
  const total = orderList.value.length
  const processing = orderList.value.filter((order) =>
    ['20', 'WAIT_REPORT_MEALS', 'WAIT_CELL_DELIVERY', 'WAIT_RIDER_ACCEPT', '80', '60'].includes(
      order.status,
    ),
  ).length
  const cancelled = orderList.value.filter((order) => order.status === '-10').length

  return {
    totalCount: total,
    processingCount: processing,
    cancelledCount: cancelled,
  }
})

// 添加显示模式控制变量
export const showAsMiniCard = ref(true)

/**
 * 处理订单卡片操作结果
 */
export const onOperationResult = async (result: {
  action: string
  success: boolean
  message: string
}) => {
  console.log('订单操作结果:', result)

  // 如果操作成功，刷新订单列表
  if (result.success) {
    // 根据操作类型处理不同的逻辑
    switch (result.action) {
      case 'accept':
      case 'report':
        // 接单和上报出餐成功后刷新订单列表
        await fetchOrders()
        break
      case 'finish':
        // 出餐完成处理
        console.log('出餐完成')
        await fetchOrders()
        break
      case 'refund':
        // 退款处理
        console.log('退款')
        await fetchOrders()
        break
      case 'after-sale-approve':
        // 售后同意处理
        console.log('售后同意')
        await fetchOrders()
        break
      case 'after-sale-reject':
        // 售后拒绝处理
        console.log('售后拒绝')
        await fetchOrders()
        break
      default:
        console.log('未知操作:', result.action)
    }
  }
}

/**
 * 售后同意处理
 */
export const onAfterSaleApprove = async (tradeNo: string) => {
  console.log('售后同意:', tradeNo)

  // 显示确认弹窗
  showApprovePopup(tradeNo)
}

/**
 * 售后拒绝处理
 */
export const onAfterSaleReject = async (tradeNo: string) => {
  console.log('售后拒绝:', tradeNo)

  // 显示确认弹窗
  showRejectPopup(tradeNo)
}

// ==================== 确认弹窗相关 ====================

/**
 * 同意弹窗配置
 */
export const approvePopupConfig = reactive({
  visible: false,
  title: '操作确认',
  content: '同意退款后，该订单款项不再计入结算。定责结果和赔付信息可在【订单-申诉】中查询。',
  cancelText: '取消',
  confirmText: '同意退款',
  tradeNo: '', // 当前操作的tradeNo
})

/**
 * 拒绝弹窗配置
 */
export const rejectPopupConfig = reactive({
  visible: false,
  title: '建议您与顾客沟通协商后处理',
  content:
    '当您未与顾客协商一致，直接拒绝退款用户有权向客服申诉，如被平台审核为商家原因导致订单取消，将影响您的排名。',
  cancelText: '已沟通，拒绝退款',
  confirmText: '取消',
  tradeNo: '', // 当前操作的tradeNo
})

/**
 * 显示同意弹窗
 */
export const showApprovePopup = (tradeNo: string) => {
  approvePopupConfig.tradeNo = tradeNo
  approvePopupConfig.visible = true
}

/**
 * 显示拒绝弹窗
 */
export const showRejectPopup = (tradeNo: string) => {
  rejectPopupConfig.tradeNo = tradeNo
  rejectPopupConfig.visible = true
}

/**
 * 确认同意售后
 */
export const handleApproveConfirm = async () => {
  // 隐藏弹窗
  approvePopupConfig.visible = false

  // 如果有订单ID，执行同意操作
  if (approvePopupConfig.tradeNo) {
    await executeApproveAfterSale(approvePopupConfig.tradeNo)
    // 重置订单ID
    approvePopupConfig.tradeNo = ''
  }
}

/**
 * 取消同意售后
 */
export const handleApproveCancel = () => {
  approvePopupConfig.visible = false
  approvePopupConfig.tradeNo = ''
}

/**
 * 确认拒绝售后
 */
export const handleRejectConfirm = async () => {
  // 拒绝弹窗中，确认按钮是"取消"操作
  rejectPopupConfig.visible = false
  rejectPopupConfig.tradeNo = ''
}

/**
 * 执行拒绝售后
 */
export const handleRejectCancel = async () => {
  // 拒绝弹窗中，取消按钮是"拒绝退款"操作
  // 隐藏弹窗
  rejectPopupConfig.visible = false

  // 如果有订单ID，执行拒绝操作
  if (rejectPopupConfig.tradeNo) {
    await executeRejectAfterSale(rejectPopupConfig.tradeNo)
    // 重置订单ID
    rejectPopupConfig.tradeNo = ''
  }
}

/**
 * 执行售后同意操作（实际调用API）
 */
export const executeApproveAfterSale = async (tradeNo: string) => {
  console.log('执行售后同意:', tradeNo)

  try {
    uni.showLoading({
      title: '处理中...',
      mask: true,
    })

    // 调用售后审核接口
    const result = await auditAfterSale({
      tradeNo,
      refundStatus: 30,
      agreeReason: '同意退款',
    })

    if (result && result.resultCode === '0') {
      // 查找订单
      const orderIndex = orderList.value.findIndex((order) => {
        if (
          order.apiData &&
          order.apiData.refundRecords &&
          order.apiData.refundRecords.length > 0
        ) {
          return order.apiData.refundRecords[0].tradeNo === tradeNo
        }
        return false
      })

      if (orderIndex !== -1) {
        const orderToUpdate = { ...orderList.value[orderIndex] }

        // 更新订单状态
        orderToUpdate.status = 'REFUNDED'
        orderToUpdate.statusInfo = {
          ...orderToUpdate.statusInfo,
          main: '已退款',
        }

        // 更新到列表中
        orderList.value.splice(orderIndex, 1, orderToUpdate)
      }

      uni.showToast({
        title: '同意成功',
        icon: 'success',
      })

      // 刷新订单列表
      await fetchOrders()
    } else {
      uni.showToast({
        title: result?.resultMsg || '操作失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('售后同意失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

/**
 * 执行售后拒绝操作（实际调用API）
 */
export const executeRejectAfterSale = async (tradeNo: string) => {
  console.log('执行售后拒绝:', tradeNo)

  try {
    uni.showLoading({
      title: '处理中...',
      mask: true,
    })

    // 调用售后审核接口
    const result = await auditAfterSale({
      tradeNo,
      refundStatus: 40,
      agreeReason: '已沟通，拒绝退款',
    })

    if (result && result.resultCode === '0') {
      // 查找订单
      const orderIndex = orderList.value.findIndex((order) => {
        if (
          order.apiData &&
          order.apiData.refundRecords &&
          order.apiData.refundRecords.length > 0
        ) {
          return order.apiData.refundRecords[0].tradeNo === tradeNo
        }
        return false
      })

      if (orderIndex !== -1) {
        const orderToUpdate = { ...orderList.value[orderIndex] }

        // 更新订单状态
        orderToUpdate.status = 'REFUND_REJECTED'
        orderToUpdate.statusInfo = {
          ...orderToUpdate.statusInfo,
          main: '退款已拒绝',
        }

        // 更新到列表中
        orderList.value.splice(orderIndex, 1, orderToUpdate)
      }

      uni.showToast({
        title: '拒绝成功',
        icon: 'success',
      })

      // 刷新订单列表
      await fetchOrders()
    } else {
      uni.showToast({
        title: result?.resultMsg || '操作失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('售后拒绝失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}
