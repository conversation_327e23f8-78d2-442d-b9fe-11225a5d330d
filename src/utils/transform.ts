import type { OrderData, RefundRecord } from '@/components/sy-order-card/card'
import { useShopStore } from '@/store/shop'
import { secondsToTime } from './datetime'

/**
 * 订单扩展信息接口
 */
interface ExtlOrderDetail {
  syncThirdOrderReqDto?: {
    address?: string
    distance?: string
    userNote?: string
    [key: string]: any
  }
  [key: string]: any
}

/**
 * 渠道图标映射
 */
export const channelIconMap: Record<string, string> = {
  // 美团外卖
  MeiTuanTakeOutBrand: '/static/images/icons/mt.png',

  // 饿了么
  ELeMeTakeOutBrand: '/static/images/icons/elm.png',

  // 京东秒送
  JingDongTakeOutBrand: '/static/images/icons/jd.png',

  // 抖音小时达
  DouYinXiaoshiDa: '/static/images/icons/dy.png',

  // 小程序
  SYZY: '/static/images/icons/mp.png',

  // 默认
  default: '/static/images/icons/ziying.png',
}

/**
 * 渠道类型颜色映射
 */
export const channelColorMap: Record<
  string,
  { text: string; gradient: string; bg: string; btnBg: string }
> = {
  // 美团外卖
  MeiTuanTakeOutBrand: {
    text: '#000000',
    btnBg: '#F4CD52',
    gradient: 'linear-gradient(180deg, rgba(244, 205, 82, 0.9) 0%, rgba(244, 205, 82, 0) 100%)',
    bg: 'linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), #FFD100',
  },

  // 饿了么
  ELeMeTakeOutBrand: {
    text: '#FFFFFF',
    btnBg: 'rgba(1, 150, 255, 0.902)',
    gradient: 'linear-gradient(180deg, rgba(1, 150, 255, 0.9) 0%, rgba(1, 150, 255, 0) 100%)',
    bg: '#0196FF',
  },

  // 京东
  JingDongTakeOutBrand: {
    text: '#FFFFFF',
    btnBg: '#FE0501',
    gradient: 'linear-gradient(180deg, #FE0501 0%, rgba(254, 5, 1, 0) 100%)',
    bg: '#FE0501',
  },

  // 抖音
  DouYinXiaoshiDa: {
    text: '#FFFFFF',
    btnBg: '#FE720E',
    gradient: 'linear-gradient(180deg, #00CA00 0%, rgba(0, 202, 0, 0) 100%)',
    bg: '#00CA00',
  },

  // 小程序
  SYZY: {
    text: '#FFFFFF',
    btnBg: '#333333',
    gradient: '#fff',
    bg: '#00CA00',
  },

  // 默认
  default: {
    text: '#FFFFFF',
    btnBg: '#333333',
    gradient: '#fff',
    bg: '#00CA00',
  },
}

/**
 * 渠道水印颜色映射
 */
export const channelWatermarkColorMap: Record<string, string> = {
  // 美团外卖
  MeiTuanTakeOutBrand: '#F6EAC6',

  // 饿了么
  ELeMeTakeOutBrand: '#D0E5F4',

  // 京东
  JingDongTakeOutBrand: '#FFD9D9',

  // 抖音小时达
  DouYinXiaoshiDa: '#D5F4D5',

  // 小程序
  SYZY: '#F0F0F0',

  // 默认
  default: '#F0F0F0',
}

/**
 * 租户水印映射配置
 */
export const tenantWatermarkMap: Record<string, { iconClass: string; color: string }> = {
  // 下饭菜租户
  '2523810861030781727': { iconClass: 'icon-xiafancai', color: '#F6EAC6' }, // UAT
  '2523104481219807842': { iconClass: 'icon-xiafancai', color: '#F6EAC6' }, // PROD

  // 火锅租户
  '1351790872278738821': { iconClass: 'icon-huoguo', color: '#FFD9D9' }, // UAT
  '1354135729021061996': { iconClass: 'icon-huoguo', color: '#FFD9D9' }, // PROD

  // 默认
  default: { iconClass: 'icon-xiafancai', color: '#F0F0F0' },
}

/**
 * 获取渠道图标
 * @param channel 渠道代码或名称
 * @returns 渠道图标URL
 */
export function getChannelIcon(channel: string): string {
  // 直接匹配
  if (channelIconMap[channel]) {
    return channelIconMap[channel]
  }

  // 尝试通过关键词智能匹配
  const channelLower = channel.toLowerCase()

  if (channelLower.includes('meituan') || channelLower.includes('美团')) {
    return '/static/images/icons/mt.png'
  }

  if (channelLower.includes('eleme') || channelLower.includes('饿了么')) {
    return '/static/images/icons/elm.png'
  }

  if (
    channelLower.includes('jingdong') ||
    channelLower.includes('jd') ||
    channelLower.includes('京东')
  ) {
    return '/static/images/icons/jd.png'
  }

  if (
    channelLower.includes('TikTokMiniProgram') ||
    channelLower.includes('抖音随心团') ||
    channelLower.includes('抖音小程序')
  ) {
    return '/static/images/icons/sxt.png'
  }
  if (channelLower.includes('WeChatMiniProgram') || channelLower.includes('微信')) {
    return '/static/images/icons/wx.png'
  }
  if (channelLower.includes('AlipayMiniProgram') || channelLower.includes('支付宝')) {
    return '/static/images/icons/zfb.png'
  }
  if (channelLower.includes('DouYinXiaoshiDa') || channelLower.includes('抖音小时达')) {
    return '/static/images/icons/xsd.png'
  }

  // 如果都没匹配到，返回自营图标
  return '/static/images/icons/ziying.png'
}

/**
 * 获取渠道颜色
 * @param channel 渠道代码或名称
 */
export function getChannelColor(channel: string): { text: string; gradient: string; bg: string } {
  // 如果渠道名为空，返回默认颜色
  if (!channel) {
    return channelColorMap.default
  }

  // 直接匹配
  if (channelColorMap[channel]) {
    return channelColorMap[channel]
  }

  // 尝试通过关键词智能匹配
  const channelLower = channel.toLowerCase()

  // 精确匹配关键词，避免误匹配
  if (channelLower.includes('meituan') || channelLower.includes('美团')) {
    // 确保不是其他包含这些字符的渠道
    if (
      !channelLower.includes('eleme') &&
      !channelLower.includes('饿了么') &&
      !channelLower.includes('jd') &&
      !channelLower.includes('京东') &&
      !channelLower.includes('douyin') &&
      !channelLower.includes('抖音')
    ) {
      return channelColorMap.MeiTuanTakeOutBrand
    }
  }

  if (channelLower.includes('eleme') || channelLower.includes('饿了么')) {
    return channelColorMap.ELeMeTakeOutBrand
  }

  if (
    channelLower.includes('jingdong') ||
    channelLower.includes('jd') ||
    channelLower.includes('京东')
  ) {
    return channelColorMap.JingDongTakeOutBrand
  }

  if (channelLower.includes('douyin') || channelLower.includes('抖音')) {
    return channelColorMap.DouYinXiaoshiDa
  }

  if (
    channelLower.includes('syzy') ||
    channelLower.includes('mp') ||
    channelLower.includes('小程序') ||
    channelLower.includes('微信') ||
    channelLower.includes('支付宝')
  ) {
    return channelColorMap.SYZY
  }

  // 如果都没匹配到，返回默认颜色
  return channelColorMap.default
}

/**
 * 获取渠道水印颜色
 * @param channel 渠道代码或名称
 * @returns 水印颜色代码
 */
export function getWatermarkColor(channel: string): string {
  // 如果渠道名为空，返回默认颜色

  if (!channel) {
    return channelWatermarkColorMap.default
  }

  // 直接匹配
  if (channelWatermarkColorMap[channel]) {
    return channelWatermarkColorMap[channel]
  }

  // 尝试通过关键词智能匹配
  const channelLower = channel.toLowerCase()

  // 美团外卖
  if (channelLower.includes('meituan') || channelLower.includes('美团') || channelLower === 'mt') {
    return channelWatermarkColorMap.MeiTuanTakeOutBrand
  }

  // 饿了么
  if (channelLower.includes('eleme') || channelLower.includes('饿了么') || channelLower === 'elm') {
    return channelWatermarkColorMap.ELeMeTakeOutBrand
  }

  // 京东
  if (
    channelLower.includes('jingdong') ||
    channelLower.includes('jd') ||
    channelLower.includes('京东')
  ) {
    return channelWatermarkColorMap.JingDongTakeOutBrand
  }

  // 抖音小时达
  if (channelLower.includes('小时达')) {
    return channelWatermarkColorMap.DouYinXiaoshiDa
  }

  return channelWatermarkColorMap.default
}

/**
 * 获取租户水印信息
 * @param tenantId 租户ID
 * @returns 租户水印配置
 */
export function getTenantWatermark(tenantId?: string): { iconClass: string; color: string } {
  if (!tenantId) {
    return tenantWatermarkMap.default
  }

  // 直接匹配租户ID
  if (tenantWatermarkMap[tenantId]) {
    return tenantWatermarkMap[tenantId]
  }

  // 如果没有匹配到，返回默认配置
  return tenantWatermarkMap.default
}

/**
 * 将订单API数据转换为OrderData格式
 * @param apiData 后端返回的原始订单数据
 * @returns 格式化后的OrderData对象
 */
export function transformOrderData(apiData: any, isAfterSaleMode = false): OrderData {
  // 解析extlOrderDetail字段中的JSON数据
  let extlOrderDetail: ExtlOrderDetail = {}
  try {
    if (apiData.extlOrderDetail) {
      extlOrderDetail = JSON.parse(apiData.extlOrderDetail) as ExtlOrderDetail
    }
  } catch (error) {
    console.error('解析extlOrderDetail失败:', error)
  }

  // 获取地址信息
  const address = apiData.deliveryAddress || extlOrderDetail?.syncThirdOrderReqDto?.address || ''
  let distance = -1

  // 提取商品信息
  // 根据模式选择不同的商品数据源
  const itemsSource = isAfterSaleMode
    ? apiData.tradeItems
    : apiData.thirdItemInfos || apiData.tradeItems

  // 调试日志：便于开发时确认数据源
  // console.log(`[商品数据源] ${isAfterSaleMode ? 'tradeItems (售后模式)' : apiData.thirdItemInfos ? 'thirdItemInfos (订单模式)' : 'tradeItems (订单模式-回退)'}, 商品数量: ${itemsSource?.length || 0}`)

  const items =
    itemsSource?.map((item: any) => {
      // 尝试解析itemDetail中的combos数据
      let combos = []
      try {
        if (item) {
          const itemDetailObj = item
          // 合并数组 property、combos
          const property = itemDetailObj.property || []
          const comboss = itemDetailObj.combos || []
          combos = [...property, ...comboss].map((combo: any) => ({
            name: combo.name || '',
            count: combo.num || 0,
            price: combo.price || combo.addPrice || 0,
            skuName: combo.skuName || '',
            unit: combo.unit || '',
          }))
        }
      } catch (error) {
        console.error('解析itemDetail失败:', error)
      }

      return {
        name: item.itemName || '', // orderInfo.tradeItems[].itemName -> 产品名称
        count: item.itemNum || 0, // orderInfo.tradeItems[].itemNum -> 数量/份
        price: parseFloat(item.itemOrigPrice || item.itemPrice) || 0, // orderInfo.tradeItems[].itemOrigPrice -> 单价/加价
        skuName: item.skuName || '', // orderInfo.tradeItems[].skuName -> 规格
        unit: item.unit || '', // 单位
        totalPrice: parseFloat(item.totalRealPrice) || 0, // orderInfo.tradeItems[].totalRealPrice -> 合计
        isGift: !!item.isGift, // orderInfo.tradeItems[].isGift -> 赠品标识
        combos,
        // 添加原始数据以便后续使用
        raw: item,
      }
    }) || []

  // 构建商品摘要
  const goodsSummary = items.length > 0 ? `${items[0].name}${items.length > 1 ? '等' : ''}` : '商品'

  // 构建状态文本
  const statusText = apiData.orderStatusName || '处理中'
  const statusTime = ''
  const showAcceptButton = false

  // 获取取餐号
  const pickupNumber = apiData.pickupNo || apiData.pickupNumber || ''

  // 获取渠道单号
  const channelOrderNo = apiData.extlOrderSerial || apiData.channelOrderNo || ''

  // 获取第三方流水号
  const takeNo = apiData.takeNo || ''

  // 获取渠道名称
  const channelName = apiData.extlChannelName || apiData.extlChannel || ''

  // 获取渠道图标
  const channelIcon = getChannelIcon(channelName)

  // 获取商家名称
  let merchantName = ''
  if (apiData.shopId) {
    try {
      const shopStore = useShopStore()
      const shopDtoList = (shopStore.currentShop?.raw as any)?.shopDtoList || []
      const matchedShop = shopDtoList.find((shop: any) => shop.id === apiData.shopId)
      merchantName = matchedShop?.name || matchedShop?.alias || ''
      if (
        extlOrderDetail.syncThirdOrderReqDto &&
        matchedShop.latitude &&
        matchedShop.longitude &&
        extlOrderDetail.syncThirdOrderReqDto.latitude &&
        extlOrderDetail.syncThirdOrderReqDto.longitude
      ) {
        const R = 6371000 // 地球平均半径，单位：米
        const lat1 = (matchedShop.latitude * Math.PI) / 180
        const lat2 = (extlOrderDetail.syncThirdOrderReqDto.latitude * Math.PI) / 180
        const lon1 = (matchedShop.longitude * Math.PI) / 180
        const lon2 = (extlOrderDetail.syncThirdOrderReqDto.longitude * Math.PI) / 180
        const a =
          Math.sin((lat2 - lat1) / 2) * Math.sin((lat2 - lat1) / 2) +
          Math.cos(lat1) *
            Math.cos(lat2) *
            Math.sin((lon2 - lon1) / 2) *
            Math.sin((lon2 - lon1) / 2)
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
        console.log('r-c', R, c, a, lat2, lat1, lon1, lon2)

        distance = R * c // 初始计算得到的距离，单位：米tps://blog.csdn.net/m0_63676841/article/details/144889987
        // console.log('');
      }
    } catch (e) {
      console.warn('获取shopDtoList或alias失败', e)
    }
  }
  // 如果未获取到name则回退原有逻辑
  if (!merchantName) {
    merchantName = apiData.shopName || apiData.merchantName || ''
  }

  // 获取渠道颜色
  const channelColor = getChannelColor(channelName)

  // 获取租户水印信息
  const tenantWatermark = getTenantWatermark(apiData.tenantId)

  // 处理售后数据
  let afterSale: RefundRecord[] | undefined
  if (apiData.refundRecords) {
    // 确保售后数据是数组格式
    afterSale = Array.isArray(apiData.refundRecords)
      ? apiData.refundRecords
      : [apiData.refundRecords]
  }

  // 构建OrderData对象
  const orderData: OrderData = {
    id: apiData.id || '',
    extlChannel: apiData.extlChannel || '',
    orderNo: apiData.orderNo || apiData.tradeNo || '', // orderInfo.orderNo -> 订单号
    channelOrderNo, // 渠道单号
    takeNo, // 第三方流水号
    deliveryTime: formatDeliveryTime(apiData.sendTime), // 格式化送达时间为 MM-DD HH:mm
    sendTime: apiData.sendTime || '',
    placeTime: apiData.placeTime || '', // orderInfo.placeTime -> 下单时间
    status: statusText,
    type: apiData.bizTypeName || '', // orderInfo.bizTypeName -> 业务类型
    bizType: apiData.bizType || '', // orderInfo.bizType -> 业务类型code
    isBook: apiData.isBook || '', // orderInfo.isBook -> 2 预订单
    channel: channelName, // orderInfo.extlChannel -> 渠道
    channelColor: {
      ...channelColor,
      watermarkIconClass: tenantWatermark.iconClass,
      watermarkColor: tenantWatermark.color,
    },
    pickupNumber, // 取餐号
    appName: apiData.appName || '外卖', // 添加appName字段，默认为"外卖"
    saleChannel: apiData.saleChannel,
    merchantConfirmTime: apiData.merchantConfirmTime,
    buyerRemark: apiData.buyerRemark,
    deliveryRecord: apiData.deliveryRecord, // 配送信息
    merchant: {
      avatar: channelIcon, // 渠道图标
      name: apiData.shopName || '', // orderInfo.shopName -> 门店名称
      alias: merchantName, // 商家名称（现在优先使用name字段）
      id: apiData.shopId || apiData.id || '', // 商家ID，重要的参数
    },
    customer: {
      name: apiData.contactName || (apiData.customerInfo ? apiData.customerInfo.userName : ''), // orderInfo.customerInfo.userName -> 顾客姓名
      phone: apiData.reservedPhone || apiData.phone,
      fullPhone: apiData.reservedPhone || apiData.phone,

      lastFourPhone: apiData.lastFourPhone || '',
    },
    address: {
      detail: address,
      distance: distance >= 0 ? `${(distance / 1000).toFixed(1)}Km` : '',
    },
    orderStatus: {
      text: statusText,
      time: statusTime,
      code: apiData.orderStatus, // orderInfo.orderStatus -> 订单状态
      tradeStatus: apiData.orderTradeStatus,
      showAcceptButton,
      mealReadyDuration: apiData.mealReadyDuration
        ? secondsToTime(apiData.mealReadyDuration)
        : undefined,
    },
    delivery: {
      method: apiData.isSpecialDelivery ? '专送服务' : '外卖配送', // 根据isSpecialDelivery判断显示
      time: apiData.sendTime ? `预计${formatDeliveryTime(apiData.sendTime)}送达` : '',
      platform: channelName,
      fee: parseFloat(apiData.freightAmount) || 0, // orderInfo.freightAmount -> 配送费/专送服务费
    },
    goods: {
      count: apiData.totalItemNum || items.length || 0,
      summary: goodsSummary,
      items,
    },
    fees: {
      merchantIncome: parseFloat(apiData.orderAmountInfo?.merchantPrice) || 0, // orderInfo.orderAmountInfo.merchantPrice -> 商家实收
      deliveryFee: parseFloat(apiData.freightAmount) || 0, // orderInfo.freightAmount -> 配送费/专送服务费
      packagingFee: apiData.orderAmountInfo?.totalMealFee || 0, // orderInfo.orderAmountInfo.totalMealFee -> 总餐盒费
      totalAmount: parseFloat(apiData.totalAmount) || 0, // orderInfo.totalAmount -> 订单总额
      payAmount:
        parseFloat(apiData.payAmount) ||
        (apiData.payInfo ? parseFloat(apiData.payInfo.payAmount) : 0), // orderInfo.payInfo.payAmount -> 支付金额
      discountAmount:
        parseFloat(apiData.orderAmountInfo?.discountPrice) || // orderInfo.orderAmountInfo.discountPrice -> 优惠总金额
        parseFloat(apiData.orderAmountInfo?.discountPrice) ||
        0,
      commission: parseFloat(apiData.orderAmountInfo?.commission) || 0, // 佣金
      serviceFee: parseFloat(apiData.orderAmountInfo?.thirdServiceFee) || 0, // 服务费
      subsidy: parseFloat(apiData.orderAmountInfo?.subsidy) || 0, // 补贴
    },
    times: {
      createTime: formatDeliveryTime(apiData.createTime),
      placeTime: formatDeliveryTime(apiData.placeTime),
      deliveryTime: formatDeliveryTime(apiData.sendTime),
      endTime: formatDeliveryTime(apiData.endTime), // 订单完成时间
      updateTime: apiData.updateTime,
    },
    tenantId: apiData.tenantId, // 租户ID
    instanceId: apiData.instanceId, // 实例ID
    // 处理新的售后数据结构
    afterSale: afterSale || [],
    orderLatitude: apiData.latitude,
    orderLongitude: apiData.longitude,
    shopId: apiData.shopId,
  }

  // 如果有倒计时需求，可以添加warning信息
  if (apiData.orderStatus === 'PAYED' || apiData.orderStatus === '20') {
    orderData.warning = {
      text: '备餐中，已用时',
      countdown: 300, // 默认5分钟
    }
  }

  return orderData
}

/**
 * 格式化送达时间为 MM-DD HH:mm 格式
 * @param timeString ISO格式的时间字符串
 * @returns 格式化后的时间字符串，如 "03-25 19:44"
 */
function formatDeliveryTime(timeString?: string): string {
  if (!timeString) return ''

  try {
    const date = new Date(timeString)
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')

    return `${month}-${day} ${hours}:${minutes}`
  } catch (error) {
    console.error('格式化送达时间失败:', error)
    // 如果解析失败，尝试提取时间部分
    if (timeString.includes('T')) {
      const [datePart, timePart] = timeString.split('T')
      if (datePart && timePart) {
        const [year, month, day] = datePart.split('-')
        const time = timePart.substring(0, 5)
        if (month && day && time) {
          return `${month}-${day} ${time}`
        }
      }
    }
    return timeString
  }
}

/**
 * 从带有rpx或px单位的字符串中提取数字部分
 * @param value 带单位的尺寸字符串或数字
 * @returns 提取的数字
 */
export function extractNumericValue(value: string | number): number {
  if (typeof value === 'number') {
    return value
  }

  // 移除所有rpx或px单位，只保留数字部分
  return parseInt(String(value).replace(/rpx|px/g, ''))
}

/**
 * 确保尺寸值带有单位
 * @param value 尺寸值（数字或带单位的字符串）
 * @param unit 默认单位，默认为'rpx'
 * @returns 带单位的尺寸字符串
 */
export function ensureUnit(value: string | number, unit = 'rpx'): string {
  if (typeof value === 'number') {
    return `${value}${unit}`
  }

  // 如果已经有单位，则直接返回
  if (/rpx|px|%|vh|vw|em|rem/.test(value)) {
    return value
  }

  // 否则添加默认单位
  return `${value}${unit}`
}
