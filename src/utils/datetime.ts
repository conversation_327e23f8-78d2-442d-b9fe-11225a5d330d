import dayjs from 'dayjs'

// 获取最近n个月的开始日期时间
export const getStartDate = (val: number) => {
  let date
  switch (val) {
    case 1:
      date = dayjs().subtract(1, 'month') // 近一个月
      break
    case 2:
      date = dayjs().subtract(2, 'months') // 近两个月
      break
    case 3:
      date = dayjs().subtract(3, 'months') // 近三个月
  }
  return date.startOf('day').format('YYYY-MM-DD HH:mm:ss')
}

// 获取当前日期时间
export const getCurrentTime = () => dayjs().format('YYYY-MM-DD HH:mm:ss')

// 获取当前日期时间
export const getCurrentDate = () => {
  return dayjs().format('YYYY-MM-DD')
}

/**
 * 计算回复日期距离评论日期多少天后
 * @param commentDate - 评论日期（支持字符串或 Date 对象）
 * @param replyDate - 回复日期（支持字符串或 Date 对象）
 * @returns 天数差（回复在评论之后为正数，之前为负数）
 */
export const getDaysAfter = (commentDate: string | Date, replyDate: string | Date): number => {
  const comment = dayjs(commentDate)
  const reply = dayjs(replyDate)

  return reply.diff(comment, 'day')
}

// 在指定日期基础上加一天
export const addOneDay = (date: string | Date): string => {
  const day = dayjs(date)
  return day.add(1, 'day').format('YYYY-MM-DD')
}

// 在指定日期基础上加n天
export const getDateOfAddNumDay = (date: string | Date, num: number): string => {
  const day = dayjs(date)
  return day.add(num, 'day').format('YYYY-MM-DD')
}

// 获取当前日期后的n天日期
export const getDateOfNumDay = (num: number): string => {
  const day = dayjs()
  return day.add(num, 'day').format('YYYY-MM-DD')
}

// 获取当前时间的后一个小时
export const getLastHour = (): string => {
  const day = dayjs()
  return day.add(1, 'hour').format('YYYY-MM-DD HH:mm:ss')
}

// 时间戳转日期字符串
export const timestampToDate = (timestamp: number): string => {
  return dayjs(timestamp).format('YYYY-MM-DD')
}

// 时间戳转时间字符串
export const timestampToTime = (timestamp: number): string => {
  return dayjs(timestamp).format('HH:mm:ss')
}

export const timestampToDateTime = (timestamp: number): string => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

// 日期格式转换（将形如2025-06-25转为2025.6.25格式）
export const dateStrToFormat = (dateStr: string): string => {
  const [year, month, day] = dateStr.split('-')
  return `${year}.${parseInt(month)}.${day}`
}

/**
 * 将秒数转换为 HH:mm:ss 格式
 * @param seconds 秒数
 * @returns 格式化的时间字符串，如 "00:19:22"
 */
export const secondsToTime = (seconds: number | string): string => {
  // 处理空值或非法值
  if (!seconds && seconds !== 0) return '00:00:00'

  // 转换为数字
  const totalSeconds = typeof seconds === 'string' ? parseFloat(seconds) : seconds

  // 处理非法数字
  if (isNaN(totalSeconds) || totalSeconds < 0) return '00:00:00'

  // 计算时分秒
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const remainingSeconds = Math.floor(totalSeconds % 60)

  // 格式化为两位数
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 判断未来时间是否在一小时内
export function isOutOneHour(dateString: string): boolean {
  const inputDate = dayjs(dateString)
  const now = dayjs()

  // 如果输入时间小于等于当前时间，直接返回true
  if (inputDate.isBefore(now) || inputDate.isSame(now)) {
    return true
  }
  // 计算输入时间的前一个小时
  const inputDateMinusOneHour = inputDate.subtract(1, 'hour')

  // 计算当前时间的前一个小时
  const nowMinusOneHour = now.subtract(1, 'hour')

  // 判断条件：
  // 1. 输入时间的前一小时小于等于当前时间
  // 2. 并且输入时间的前一小时大于等于当前时间的前一小时
  return (
    (inputDateMinusOneHour.isBefore(now) || inputDateMinusOneHour.isSame(now)) &&
    (inputDateMinusOneHour.isAfter(nowMinusOneHour) ||
      inputDateMinusOneHour.isSame(nowMinusOneHour))
  )

  // const inputDate = dayjs(dateString)
  // const now = dayjs()
  // return inputDate.diff(now, 'hour') > 0
}
