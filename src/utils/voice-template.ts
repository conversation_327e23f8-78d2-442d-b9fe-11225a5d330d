/**
 * 语音播报模板模块 (V3 - 前端音效控制版)
 */

// JPush 消息体解析后的有效载荷
export interface VoicePayload {
  notifyType: string
  channel?: string
  takeNo?: number
}

// 定义需要播放开/结尾提示音的业务类型
const NOTIFY_TYPES_WITH_MUSIC: Set<string> = new Set([
  'order_accepted', // 接单通知
  'over_time_wait_receive', // 超时未接单
  'abnormal_delivery', // 配送异常
])

// 渠道枚举值 -> 品牌音频文件名
const channelMap: Record<string, string> = {
  MeiTuanTakeOutBrand: 'brand_meituan',
  ELeMeTakeOutBrand: 'brand_eleme',
  JingDongTakeOutBrand: 'brand_jingdong',
  WeChatMiniProgram: 'brand_wechat',
  AlipayMiniProgram: 'brand_alipay',
  TikTokMiniProgram: 'brand_tiktok_group',
  DouYinXiaoshiDa: 'brand_tiktok_hour',
}

// notifyType -> 业务音频文件名
const notifyTypeMap: Record<string, string> = {
  user_remind: 'text_remind',
  user_cancel: 'text_user_cancel',
  user_apply_refund: 'text_user_apply_refund',
  user_remind_audit_refund: 'text_user_remind_audit_refund',
  abnormal_delivery: 'text_abnormal_delivery',
  book_arrive_time: 'text_book_arrive_time_half_hour',
}

/**
 * 将取餐号数字转换为中文语音片段 (支持0-999)
 */
function numberToVoiceSegments(num: number): string[] {
  if (num < 0) {
    return []
  }

  if (num < 10) {
    const result = [`number/number_${num}`]
    return result
  }

  if (num === 10) {
    const result = ['number/number_10']
    return result
  }

  if (num < 20) {
    const result = [`number/number_10`, `number/number_${num % 10}`]
    return result
  }

  const parts: string[] = []
  const strNum = String(num)

  if (num >= 100) {
    parts.push(`number/number_${strNum[0]}`, 'number/number_100')
    const tens = num % 100
    if (tens > 0) {
      parts.push(...numberToVoiceSegments(tens))
    }
  } else if (num >= 20) {
    const tensDigit = Math.floor(num / 10)
    parts.push(`number/number_${tensDigit}`, 'number/number_10')
    const unitDigit = num % 10
    if (unitDigit > 0) {
      parts.push(`number/number_${unitDigit}`)
    }
  }
  return parts
}

/**
 * 根据消息载荷构建语音播放队列
 */
export function buildVoiceQueue(payload: VoicePayload): string[] {
  const { notifyType, channel, takeNo } = payload

  const queue: string[] = []

  const hasMusic = NOTIFY_TYPES_WITH_MUSIC.has(notifyType)

  if (hasMusic) {
    queue.push('common/music_start')
  }

  if (notifyType === 'order_accepted') {
    queue.push('text/text_accepted_part1')
    if (channel && channelMap[channel]) {
      queue.push(`brand/${channelMap[channel]}`)
    }

    // ⭐ 修复：order_accepted 类型也需要播放取餐号
    if (takeNo !== undefined && takeNo !== null) {
      const numberSegments = numberToVoiceSegments(takeNo)
      queue.push(...numberSegments)
      queue.push('common/hao')
    }

    queue.push('text/text_accepted_part2')
  } else if (notifyType === 'over_time_wait_receive') {
    queue.push('text/text_over_time_wait_receive_part1')
    if (channel && channelMap[channel]) {
      queue.push(`brand/${channelMap[channel]}`)
    }

    // ⭐ 修复：over_time_wait_receive 类型也需要播放取餐号
    if (takeNo !== undefined && takeNo !== null) {
      const numberSegments = numberToVoiceSegments(takeNo)
      queue.push(...numberSegments)
      queue.push('common/hao')
    }

    queue.push('text/text_over_time_wait_receive_part2')
  } else {
    if (channel && channelMap[channel]) {
      queue.push(`brand/${channelMap[channel]}`)
    }
    if (takeNo !== undefined && takeNo !== null) {
      const numberSegments = numberToVoiceSegments(takeNo)
      queue.push(...numberSegments)
      queue.push('common/hao')
    }
    const textSegment = notifyTypeMap[notifyType]
    if (textSegment) {
      queue.push(`text/${textSegment}`)
    }
  }

  if (hasMusic) {
    queue.push('common/music_end')
  }

  const baseVoicePath = '/static/mp3/voice'
  const finalQueue = queue.filter(Boolean).map((segment) => `${baseVoicePath}/${segment}.mp3`)

  return finalQueue
}
