import { playOrderNotification } from './audio'
import { buildVoiceQueue, type VoicePayload } from './voice-template'

let voiceQueue: string[] = []
let currentPlayer: UniApp.InnerAudioContext | null = null
let isPlaying = false
// 路径缓存，避免重复转换
const pathCache = new Map<string, string>()
// 预加载的音频播放器缓存
const preloadedPlayers = new Map<string, UniApp.InnerAudioContext>()
// 播放器池
const playerPool: UniApp.InnerAudioContext[] = []
const POOL_SIZE = 10

/**
 * 将相对路径转换为平台绝对路径（使用HTML5+ IO模块）
 * @param relativePath 相对路径，如 "/static/mp3/voice/brand/brand_meituan.mp3"
 * @returns 平台绝对路径
 */
function convertToAbsolutePath(relativePath: string): Promise<string> {
  return new Promise((resolve) => {
    // 检查缓存
    if (pathCache.has(relativePath)) {
      const cachedPath = pathCache.get(relativePath)!
      resolve(cachedPath)
      return
    }

    // #ifdef APP-PLUS
    if (typeof plus !== 'undefined' && plus.io) {
      try {
        // 使用HTML5+ IO模块转换路径
        const absolutePath = plus.io.convertLocalFileSystemURL(relativePath)
        pathCache.set(relativePath, absolutePath)
        resolve(absolutePath)
        return
      } catch (e) {
        console.warn('⚠️ HTML5+路径转换失败，使用原路径:', e)
      }
    }
    // #endif

    // 如果不是APP环境或转换失败，直接使用原路径
    pathCache.set(relativePath, relativePath)
    resolve(relativePath)
  })
}

/**
 * 预处理音频队列，转换所有路径为绝对路径
 * @param queue 原始音频队列
 * @returns 转换后的绝对路径队列
 */
async function preprocessAudioQueue(queue: string[]): Promise<string[]> {
  const promises = queue.map((path) => convertToAbsolutePath(path))
  const absolutePaths = await Promise.all(promises)
  return absolutePaths
}

/**
 * 创建并配置音频播放器
 */
function createAudioPlayer(): UniApp.InnerAudioContext {
  const player = uni.createInnerAudioContext()
  player.autoplay = false
  player.loop = false
  player.volume = 1.0
  return player
}

/**
 * 初始化播放器池
 */
function initializePlayerPool() {
  if (playerPool.length > 0) return

  for (let i = 0; i < POOL_SIZE; i++) {
    const player = createAudioPlayer()
    playerPool.push(player)
  }
}

/**
 * 从播放器池获取一个播放器
 */
function getPlayerFromPool(): UniApp.InnerAudioContext {
  if (playerPool.length === 0) {
    initializePlayerPool()
  }
  return playerPool.shift() || createAudioPlayer()
}

/**
 * 将播放器返回到池中
 */
function returnPlayerToPool(player: UniApp.InnerAudioContext) {
  if (playerPool.length < POOL_SIZE) {
    // 清理播放器状态
    try {
      player.stop()
    } catch (e) {
      // 忽略停止错误
    }
    playerPool.push(player)
  } else {
    // 池已满，销毁播放器
    try {
      player.destroy()
    } catch (e) {
      // 忽略销毁错误
    }
  }
}

/**
 * 预加载单个音频文件
 * @param audioPath 音频文件路径（已转换为绝对路径）
 */
function preloadAudio(audioPath: string): Promise<UniApp.InnerAudioContext> {
  return new Promise((resolve, reject) => {
    // 检查是否已经预加载
    if (preloadedPlayers.has(audioPath)) {
      resolve(preloadedPlayers.get(audioPath)!)
      return
    }

    const player = getPlayerFromPool()
    let isResolved = false

    const cleanup = () => {
      if (isResolved) return
      isResolved = true
    }

    // 监听加载完成
    player.onCanplay(() => {
      if (isResolved) return
      preloadedPlayers.set(audioPath, player)
      cleanup()
      resolve(player)
    })

    // 监听加载错误
    player.onError((error) => {
      if (isResolved) return
      console.warn(`❌ 音频预加载失败: ${audioPath}`, error)
      returnPlayerToPool(player)
      cleanup()
      reject(error)
    })

    // 设置超时
    setTimeout(() => {
      if (isResolved) return
      console.warn(`⏰ 音频预加载超时: ${audioPath}`)
      returnPlayerToPool(player)
      cleanup()
      reject(new Error('预加载超时'))
    }, 5000)

    try {
      // 检查文件路径是否合理
      if (!audioPath || audioPath.length < 10) {
        throw new Error(`路径无效: ${audioPath}`)
      }

      player.src = audioPath
    } catch (e) {
      console.warn(`💥 设置音频源失败: ${audioPath}`, e)
      returnPlayerToPool(player)
      cleanup()
      reject(e)
    }
  })
}

/**
 * 批量预加载音频文件
 * @param audioPaths 音频文件路径数组
 */
export async function preloadAudioFiles(audioPaths: string[]): Promise<void> {
  initializePlayerPool()

  // 转换路径
  const absolutePaths = await preprocessAudioQueue(audioPaths)

  // 并发预加载，限制并发数量避免资源过载
  const BATCH_SIZE = 3
  let successCount = 0
  let failCount = 0

  for (let i = 0; i < absolutePaths.length; i += BATCH_SIZE) {
    const batch = absolutePaths.slice(i, i + BATCH_SIZE)

    const promises = batch.map((path) =>
      preloadAudio(path)
        .then((player) => {
          successCount++
          return player
        })
        .catch((error) => {
          failCount++
          console.warn(`❌ 预加载失败 (第${failCount}个)，将在播放时重试: ${path}`, error)
          return null
        }),
    )
    await Promise.all(promises)
  }
}

/**
 * 设置播放器事件监听
 */
function setupPlayerEvents(player: UniApp.InnerAudioContext) {
  // 播放结束事件 - 立即播放下一个
  player.onEnded(() => {
    isPlaying = false
    playNextAudio()
  })

  // 播放错误事件 - 跳过当前音频，播放下一个
  player.onError((error) => {
    console.error('🚨 音频播放错误，跳过该片段:', {
      error,
      src: player.src,
      currentTime: player.currentTime,
      duration: player.duration,
    })
    isPlaying = false
    playNextAudio()
  })

  // 播放开始事件
  player.onPlay(() => {
    isPlaying = true
  })
}

/**
 * 播放下一个音频片段（使用预加载的播放器）
 */
function playNextAudio() {
  if (voiceQueue.length === 0) {
    isPlaying = false
    if (currentPlayer) {
      returnPlayerToPool(currentPlayer)
      currentPlayer = null
    }
    return
  }

  const nextSrc = voiceQueue.shift()!
  console.log(`播放下一个音频: ${nextSrc}`)
  console.log(`剩余片段数: ${voiceQueue.length}`)

  // 检查是否有预加载的播放器

  const cacheKeys = Array.from(preloadedPlayers.keys())

  // 检查完全匹配
  const hasExactMatch = preloadedPlayers.has(nextSrc)

  // 检查是否有部分匹配的路径（文件名匹配）
  const targetFileName = nextSrc.split('/').pop() || ''
  const partialMatches = cacheKeys.filter((key) => {
    const keyFileName = key.split('/').pop() || ''
    return keyFileName === targetFileName
  })
  if (partialMatches.length > 0) {
  }
  // 尝试使用预加载的播放器
  let preloadedPlayer: UniApp.InnerAudioContext | null = null
  let usedKey: string | null = null

  if (preloadedPlayers.has(nextSrc)) {
    // 完全匹配
    preloadedPlayer = preloadedPlayers.get(nextSrc)!
    usedKey = nextSrc
  } else if (partialMatches.length > 0) {
    // 文件名匹配
    usedKey = partialMatches[0]
    preloadedPlayer = preloadedPlayers.get(usedKey)!
  }

  if (preloadedPlayer && usedKey) {
    // 如果有当前播放器，先返回到池中
    if (currentPlayer) {
      returnPlayerToPool(currentPlayer)
    }

    // 获取预加载的播放器
    currentPlayer = preloadedPlayer
    preloadedPlayers.delete(usedKey) // 从缓存中移除，避免重复使用

    // 设置事件监听
    setupPlayerEvents(currentPlayer)

    try {
      currentPlayer.play()
      isPlaying = true
    } catch (e) {
      console.error(`💥 预加载音频播放失败: ${nextSrc}`, e)
      setTimeout(() => playNextAudio(), 10)
    }
  } else {
    // 如果没有预加载，使用常规方式
    if (!currentPlayer) {
      currentPlayer = getPlayerFromPool()
      setupPlayerEvents(currentPlayer)
    }

    try {
      currentPlayer.src = nextSrc
      currentPlayer.play()
      isPlaying = true
    } catch (e) {
      console.error(`💥 常规播放失败: ${nextSrc}`, e)
      setTimeout(() => playNextAudio(), 10)
    }
  }
}

/**
 * 停止当前播放并清空队列
 */
function stopCurrentPlayback() {
  voiceQueue = []
  isPlaying = false

  if (currentPlayer) {
    try {
      currentPlayer.stop()
      returnPlayerToPool(currentPlayer)
      currentPlayer = null
    } catch (e) {
      console.error('停止播放器失败:', e)
    }
  }
}

/**
 * 启动语音播报，构建队列并开始播放（预加载优化版）
 * @param payload 语音播报数据
 */
export async function playVoiceAnnouncement(payload: VoicePayload) {
  initializePlayerPool()
  stopCurrentPlayback()

  // 构建原始队列
  const rawQueue = buildVoiceQueue(payload)

  if (rawQueue.length === 0) {
    console.warn('队列为空，播放默认提示音')
    playOrderNotification()
    return
  }

  try {
    // 预处理队列，转换路径为绝对路径
    voiceQueue = await preprocessAudioQueue(rawQueue)

    // 立即开始播放第一个片段
    playNextAudio()
  } catch (error) {
    console.error('预处理队列失败:', error)
    // 如果预处理失败，使用原始队列
    voiceQueue = rawQueue
    playNextAudio()
  }
}

/**
 * 获取所有可能用到的音频文件路径
 */
export function getAllVoiceFiles(): string[] {
  const voiceFiles: string[] = []
  const baseVoicePath = '/static/mp3/voice'

  // 品牌音频 - 注意：Wechat首字母大写
  const brands = ['meituan', 'eleme', 'jingdong', 'Wechat', 'alipay', 'tiktok_group', 'tiktok_hour']
  brands.forEach((brand) => {
    const brandFile = `${baseVoicePath}/brand/brand_${brand}.mp3`
    voiceFiles.push(brandFile)
    console.log(`  ✓ ${brandFile}`)
  })

  // 数字音频 (0-9, 10, 100)
  for (let i = 0; i <= 9; i++) {
    voiceFiles.push(`${baseVoicePath}/number/number_${i}.mp3`)
  }
  voiceFiles.push(`${baseVoicePath}/number/number_10.mp3`)
  voiceFiles.push(`${baseVoicePath}/number/number_100.mp3`)

  // 通用音频
  voiceFiles.push(`${baseVoicePath}/common/hao.mp3`)
  voiceFiles.push(`${baseVoicePath}/common/music_start.mp3`)
  voiceFiles.push(`${baseVoicePath}/common/music_end.mp3`)

  // 业务文本音频
  const textTypes = [
    'remind',
    'user_cancel',
    'user_apply_refund',
    'user_remind_audit_refund',
    'abnormal_delivery',
    'book_arrive_time_half_hour',
    'accepted_part1',
    'accepted_part2',
    'over_time_wait_receive_part1',
    'over_time_wait_receive_part2',
  ]
  textTypes.forEach((type) => {
    voiceFiles.push(`${baseVoicePath}/text/text_${type}.mp3`)
  })

  console.log(`📊 总共构建了 ${voiceFiles.length} 个语音文件路径`)
  return voiceFiles
}

/**
 * 获取预加载状态信息（调试用）
 */
export function getPreloadStatus() {
  const cachedPaths = Array.from(preloadedPlayers.keys())
  const allVoiceFiles = getAllVoiceFiles()

  return {
    cacheSize: preloadedPlayers.size,
    poolSize: playerPool.length,
    cachedPaths: cachedPaths.slice(0, 5), // 只显示前5个，避免日志过长
    allVoiceFiles: allVoiceFiles.length,
    pathCacheSize: pathCache.size,
    sampleCachedPaths:
      cachedPaths.length > 0
        ? {
            first: cachedPaths[0],
            last: cachedPaths[cachedPaths.length - 1],
          }
        : null,
    sampleOriginalPaths:
      allVoiceFiles.length > 0
        ? {
            first: allVoiceFiles[0],
            last: allVoiceFiles[allVoiceFiles.length - 1],
          }
        : null,
  }
}
