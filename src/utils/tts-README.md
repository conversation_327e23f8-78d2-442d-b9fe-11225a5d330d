# TTS 语音播报功能使用说明

## 功能概述

基于 `hm-tts` 插件实现的文字转语音播报功能，支持智能前置音乐播放。

## 核心功能

### 1. 基础 TTS 播报

- `initTTS()`: 初始化 TTS 插件
- `speakText(text)`: 播放指定文字内容
- `stopTTS()`: 停止当前播放

### 2. 智能语音播报

- `playTTSAnnouncement(payload)`: 对象参数版，支持前置音乐自动判断
- `playTTSContent(content, needMusic?)`: 字符串参数版，向前兼容

## 前置音乐判断逻辑

系统会根据以下优先级判断是否播放前置音乐：

1. **手动指定**: `needMusic` 参数直接指定
2. **业务类型判断**: 根据 `notifyType` 判断
3. **内容关键词判断**: 检测消息内容中的关键词

### 需要前置音乐的业务类型

```typescript
const NOTIFY_TYPES_WITH_MUSIC = [
  'order_accepted', // 接单通知
  'over_time_wait_receive', // 超时未接单
  'abnormal_delivery', // 配送异常
]
```

### 关键词检测

系统会检测以下关键词：

- 接单相关：接单、订单已接、order_accepted
- 超时相关：超时、未接单、over_time、wait_receive
- 异常相关：配送异常、异常、abnormal_delivery

## 使用方法

### 1. 初始化（在 App.vue onLaunch 中）

```typescript
import { initTTS } from '@/utils/tts'

onLaunch(async () => {
  const success = await initTTS()
  if (success) {
    console.log('TTS 初始化成功')
  }
})
```

### 2. 对象参数版（推荐）

```typescript
import { playTTSAnnouncement } from '@/utils/tts'
import type { TTSPayload } from '@/utils/tts'

// 智能播报（自动判断前置音乐）
const payload: TTSPayload = {
  content: '美团18号订单，超时未接单，请及时处理',
  notifyType: 'over_time_wait_receive',
  channel: 'MeiTuanTakeOutBrand',
  takeNo: 18,
}
playTTSAnnouncement(payload)

// 手动指定前置音乐
playTTSAnnouncement({
  content: '您有新的订单消息',
  needMusic: true,
})

// 简单播报（无前置音乐）
playTTSAnnouncement({
  content: '订单状态已更新',
})
```

### 3. 字符串参数版（兼容）

```typescript
import { playTTSContent } from '@/utils/tts'

// 自动判断
playTTSContent('接单通知：美团订单已接收')

// 手动指定
playTTSContent('您有新消息', true) // 强制播放前置音乐
playTTSContent('普通提示', false) // 强制不播放前置音乐
```

### 4. 在极光推送中使用

```typescript
// App.vue 中的自定义消息处理
jpushModule.addCustomMessageListener((result: any) => {
  // 解析结构化数据
  const payload: TTSPayload = {
    content: result.content,
    notifyType: extras.notifyType,
    channel: extras.channel,
    takeNo: extras.takeNo ? parseInt(extras.takeNo) : undefined,
  }

  // 智能播报（自动判断前置音乐）
  playTTSAnnouncement(payload)
})
```

## TTSPayload 接口说明

```typescript
interface TTSPayload {
  content: string // 播放内容（必填）
  notifyType?: string // 通知类型（可选，用于自动判断前置音乐）
  channel?: string // 渠道（可选，预留扩展）
  takeNo?: number // 取餐号（可选，预留扩展）
  needMusic?: boolean // 是否需要前置音乐（可选，手动指定）
}
```

## 自动判断示例

| 内容示例                    | notifyType          | 是否播放前置音乐 | 判断依据     |
| --------------------------- | ------------------- | ---------------- | ------------ |
| "美团18号，接单成功"        | `order_accepted`    | ✅               | 业务类型匹配 |
| "订单超时未接单"            | -                   | ✅               | 关键词检测   |
| "配送异常，请处理"          | `abnormal_delivery` | ✅               | 业务类型匹配 |
| "订单状态已更新"            | -                   | ❌               | 无匹配条件   |
| "普通消息", needMusic: true | -                   | ✅               | 手动指定     |

## 注意事项

1. **平台兼容性**: TTS 功能仅在 APP 端可用，其他端会输出警告信息
2. **初始化**: 必须在使用前调用 `initTTS()` 进行初始化
3. **异常处理**: 播放失败时会自动记录错误日志，不影响程序运行
4. **前置音乐**: 前置音乐文件路径为 `/static/mp3/voice/common/music_start.mp3`
5. **向前兼容**: 保留了 `playTTSContent` 函数以兼容现有代码

## 调试信息

系统会输出详细的调试信息，包括：

- 前置音乐判断逻辑和结果
- TTS 播放成功/失败状态
- 参数解析和处理过程

开发时可通过控制台查看相关日志进行调试。
