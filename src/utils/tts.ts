/**
 * TTS文字转语音工具函数
 * 基于 hm-tts 插件实现
 */

// TTS文字转语音工具函数

// 定义需要播放前置音乐的业务类型
const NOTIFY_TYPES_WITH_MUSIC: Set<string> = new Set([
  'order_accepted', // 接单通知
  'over_time_wait_receive', // 超时未接单
  'abnormal_delivery', // 配送异常
])

// TTS模块实例
let ttsModule: any = null

/**
 * 语音播报参数接口
 */
export interface TTSPayload {
  content: string // 播放内容
  notifyType?: string // 通知类型
  channel?: string // 渠道
  takeNo?: number // 取餐号
  needMusic?: boolean // 是否需要前置音乐（可选，不传则自动判断）
}

/**
 * 检查内容是否包含需要前置音乐的关键词
 * @param content 消息内容
 * @returns 是否需要播放前置音乐
 */
function shouldPlayMusic(content: string): boolean {
  if (!content) return false
  
  const lowerContent = content.toLowerCase()
  
  // 检查是否包含关键词
  const musicKeywords = [
    '接单', '订单已接', 'order_accepted',
    '超时', '未接单', 'over_time', 'wait_receive',
    '配送异常', '异常', 'abnormal_delivery'
  ]
  
  return musicKeywords.some(keyword => lowerContent.includes(keyword))
}

/**
 * 根据notifyType判断是否需要前置音乐
 * @param notifyType 通知类型
 * @returns 是否需要播放前置音乐
 */
function shouldPlayMusicByType(notifyType?: string): boolean {
  if (!notifyType) return false
  return NOTIFY_TYPES_WITH_MUSIC.has(notifyType)
}

/**
 * 初始化TTS插件
 * @returns Promise<boolean> 是否初始化成功
 */
export function initTTS(): Promise<boolean> {
  return new Promise((resolve) => {
    // #ifdef APP-PLUS
    try {
      ttsModule = uni.requireNativePlugin('hm-tts-TtsModule')
      if (!ttsModule) {
        console.error('❌ hm-tts-TtsModule件未找到')
        resolve(false)
        return
      }

      ttsModule.init((ret: any) => {
        if (ret.success) {
          console.log('✅ TTS插件初始化成功:', ret)
          resolve(true)
        } else {
          console.error('❌ TTS插件初始化失败:', ret)
          resolve(false)
        }
      })
    } catch (error) {
      console.error('❌ TTS插件初始化异常:', error)
      resolve(false)
    }
    // #endif

    // #ifndef APP-PLUS
    console.warn('⚠️ 非APP环境，TTS功能不可用')
    resolve(false)
    // #endif
  })
}

/**
 * 文字转语音播放
 * @param text 要播放的文字内容
 * @returns Promise<boolean> 是否播放成功
 */
export function speakText(text: string): Promise<boolean> {
  return new Promise((resolve) => {
    // #ifdef APP-PLUS
    if (!ttsModule) {
      console.error('❌ TTS插件未初始化')
      resolve(false)
      return
    }

    if (!text || text.trim().length === 0) {
      console.warn('⚠️ 播放文本为空')
      resolve(false)
      return
    }

    try {
      ttsModule.speak(
        {
          text: text.trim(),
        },
        (ret: any) => {
          if (ret.success) {
            console.log('✅ TTS播放成功:', text)
            resolve(true)
          } else {
            console.error('❌ TTS播放失败:', ret, text)
            resolve(false)
          }
        },
      )
    } catch (error) {
      console.error('❌ TTS播放异常:', error, text)
      resolve(false)
    }
    // #endif

    // #ifndef APP-PLUS
    console.warn('⚠️ 非APP环境，TTS功能不可用，文本:', text)
    resolve(false)
    // #endif
  })
}

/**
 * 停止TTS播放
 */
export function stopTTS(): void {
  // #ifdef APP-PLUS
  if (ttsModule) {
    try {
      ttsModule.stop()
      console.log('🛑 TTS播放已停止')
    } catch (error) {
      console.error('❌ 停止TTS播放失败:', error)
    }
  }
  // #endif
}

/**
 * 播放音频文件（用于前置音乐）
 * @param audioPath 音频文件路径
 * @returns Promise<void>
 */
function playAudioFile(audioPath: string): Promise<void> {
  return new Promise((resolve) => {
    const audioContext = uni.createInnerAudioContext()

    audioContext.src = audioPath
    audioContext.autoplay = true

    audioContext.onPlay(() => {
      console.log('🎵 音频播放开始:', audioPath)
    })

    audioContext.onEnded(() => {
      console.log('🎵 音频播放结束:', audioPath)
      try {
        audioContext.destroy()
      } catch (e) {
        console.error('销毁音频实例失败:', e)
      }
      resolve()
    })

    audioContext.onError((error) => {
      console.error('❌ 音频播放失败:', audioPath, error)
      try {
        audioContext.destroy()
      } catch (e) {
        console.error('销毁音频实例失败:', e)
      }
      resolve() // 即使失败也继续执行
    })
  })
}

/**
 * 播放TTS播报（支持前置音乐，对象参数版）
 * @param payload TTS播报参数对象
 */
export async function playTTSAnnouncement(payload: TTSPayload): Promise<void> {
  const { content, notifyType, needMusic } = payload
  
  if (!content || content.trim().length === 0) {
    console.warn('⚠️ TTS播报内容为空，跳过播放')
    return
  }

  try {
    // 判断是否需要前置音乐的优先级：
    // 1. 手动指定 needMusic
    // 2. 根据 notifyType 判断
    // 3. 根据 content 内容判断
    let shouldPlay = false
    
    if (needMusic !== undefined) {
      // 手动指定
      shouldPlay = needMusic
      console.log('🎵 手动指定前置音乐:', shouldPlay)
    } else if (notifyType) {
      // 根据业务类型判断
      shouldPlay = shouldPlayMusicByType(notifyType)
      console.log('🎵 根据业务类型判断前置音乐:', notifyType, shouldPlay)
    } else {
      // 根据内容关键词判断
      shouldPlay = shouldPlayMusic(content)
      console.log('🎵 根据内容判断前置音乐:', shouldPlay)
    }
    
    // 播放前置音乐
    if (shouldPlay) {
      console.log('🎵 播放前置音乐')
      await playAudioFile('/static/mp3/voice/common/music_start.mp3')

      // 音乐和TTS之间的间隔
      await new Promise((resolve) => setTimeout(resolve, 50))
    }

    console.log('🔊 开始TTS播放:', content)
    await speakText(content)
  } catch (error) {
    console.error('❌ TTS播报失败:', error, payload)
  }
}

/**
 * 播放TTS播报（兼容字符串参数版本）
 * @param content 要播放的文字内容
 * @param needMusic 是否需要前置音乐，如果不传则自动判断
 */
export async function playTTSContent(content: string, needMusic?: boolean): Promise<void> {
  await playTTSAnnouncement({ content, needMusic })
}
