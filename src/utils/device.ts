import { useDeviceStore } from '@/store/device'
import { saveDeviceAPI } from '@/service/user'

/**
 * 设置设备离线状态
 * @param reason 离线原因，用于日志记录
 */
export const setDeviceOffline = async (reason: string = '未知原因') => {
  try {
    const deviceStore = useDeviceStore()
    const deviceInfo = deviceStore.deviceInfo

    if (deviceInfo?.registerId) {
      console.log(`[${reason}] 设置设备离线状态, deviceId:`, deviceInfo.registerId)

      await saveDeviceAPI(
        deviceInfo.registerId, // deviceId
        undefined, // phone (不修改)
        0, // loginStatus: 0 表示离线
        undefined, // loginShopId (不修改)
      )

      console.log(`[${reason}] 设备离线状态设置成功`)
      return true
    } else {
      console.warn(`[${reason}] 设备ID不存在，无法设置离线状态`)
      return false
    }
  } catch (error) {
    console.error(`[${reason}] 设备离线状态设置失败:`, error)
    return false
  }
}

/**
 * 设置设备在线状态
 * @param phone 手机号（可选）
 * @param reason 上线原因，用于日志记录
 */
export const setDeviceOnline = async (phone?: string, reason: string = '登录成功') => {
  try {
    const deviceStore = useDeviceStore()
    const deviceInfo = deviceStore.deviceInfo

    if (deviceInfo?.registerId) {
      console.log(`[${reason}] 设置设备在线状态, deviceId:`, deviceInfo.registerId)

      await saveDeviceAPI(
        deviceInfo.registerId, // deviceId
        phone, // phone
        1, // loginStatus: 1 表示在线
        undefined, // loginShopId (不修改)
      )

      console.log(`[${reason}] 设备在线状态设置成功`)
      return true
    } else {
      console.warn(`[${reason}] 设备ID不存在，无法设置在线状态`)
      return false
    }
  } catch (error) {
    console.error(`[${reason}] 设备在线状态设置失败:`, error)
    return false
  }
}

/**
 * 设置设备门店ID
 * @param shopId 门店ID
 * @param reason 设置原因，用于日志记录
 */
export const setDeviceShop = async (shopId: string, reason: string = '选择门店') => {
  try {
    const deviceStore = useDeviceStore()
    const deviceInfo = deviceStore.deviceInfo

    if (deviceInfo?.registerId) {
      console.log(`[${reason}] 设置设备门店ID, deviceId:`, deviceInfo.registerId, 'shopId:', shopId)

      await saveDeviceAPI(
        deviceInfo.registerId, // deviceId
        undefined, // phone (不修改)
        undefined, // loginStatus (不修改)
        shopId, // loginShopId
      )

      console.log(`[${reason}] 设备门店ID设置成功`)
      return true
    } else {
      console.warn(`[${reason}] 设备ID不存在，无法设置门店ID`)
      return false
    }
  } catch (error) {
    console.error(`[${reason}] 设备门店ID设置失败:`, error)
    return false
  }
}
