/**
 * 音频播放工具函数
 */

/**
 * 播放音频
 * @param src 音频文件路径
 * @param loop 是否循环播放，默认为 false
 * @returns 返回音频实例
 */
export function playAudio(src: string, loop: boolean = false): UniApp.InnerAudioContext {
  // 直接创建新的音频实例
  const audioContext = uni.createInnerAudioContext()

  // 设置属性
  audioContext.src = src
  audioContext.loop = loop
  audioContext.autoplay = true

  // 添加事件监听
  audioContext.onPlay(() => {})

  audioContext.onEnded(() => {
    // 播放结束后自动销毁实例（非循环模式）
    if (!loop) {
      setTimeout(() => {
        try {
          audioContext.destroy()
        } catch (e) {
          console.error('销毁音频实例失败:', e)
        }
      }, 100)
    }
  })

  audioContext.onError((res) => {
    console.error('音频播放失败:', res.errMsg, '错误码:', res.errCode)
  })

  return audioContext
}

/**
 * 播放新订单提示音
 * @param loop 是否循环播放，默认为 false
 * @returns 返回音频实例
 */
export function playOrderNotification(loop: boolean = false): UniApp.InnerAudioContext {
  return playAudio('/static/mp3/audio_order_tips.mp3', loop)
}

/**
 * 停止并销毁音频实例
 * @param audioContext 音频实例
 */
export function stopAudio(audioContext: UniApp.InnerAudioContext): void {
  if (audioContext) {
    try {
      audioContext.stop()
      audioContext.destroy()
    } catch (e) {
      console.error('停止音频播放失败:', e)
    }
  }
}

/**
 * 获取背景音频管理器实例
 * @returns 返回背景音频管理器实例
 */
export function getBackgroundAudioManager(): UniApp.BackgroundAudioManager {
  return uni.getBackgroundAudioManager()
}

// 全局背景音频管理器实例
let globalBgAudioManager: UniApp.BackgroundAudioManager | null = null
// 记录当前背景音频的状态
let isBackgroundAudioPlaying = false
// 记录当前背景音频的配置
let currentAudioOptions: {
  src: string
  title: string
  singer?: string
  coverImgUrl?: string
  loop?: boolean
} | null = null

/**
 * 播放背景音频（即使在后台也能播放）
 * @param options 背景音频选项
 * @returns 返回背景音频管理器实例
 */
export function playBackgroundAudio(options: {
  src: string
  title: string
  singer?: string
  coverImgUrl?: string
  loop?: boolean
}): UniApp.BackgroundAudioManager {
  // 处理音频路径，确保使用绝对路径
  let audioSrc = options.src
  if (audioSrc.startsWith('/')) {
    // 转换为绝对路径
    // #ifdef APP-PLUS
    audioSrc = plus.io.convertLocalFileSystemURL(audioSrc)
    // #endif

    // #ifdef H5
    audioSrc = location.origin + audioSrc
    // #endif

    // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
    // 小程序环境无需转换路径
    // #endif
  }

  // 保存当前配置
  currentAudioOptions = {
    ...options,
    src: audioSrc, // 使用转换后的路径
  }

  // 检查当前是否正在播放相同的音频
  if (globalBgAudioManager && isBackgroundAudioPlaying && globalBgAudioManager.src === audioSrc) {
    return globalBgAudioManager
  }

  // 创建新实例前先停止和清理旧实例
  if (globalBgAudioManager) {
    try {
      // 标记为非播放状态
      isBackgroundAudioPlaying = false

      // 停止当前播放
      globalBgAudioManager.stop()

      // 清除事件监听 (uni-app 可能不支持 off 方法，所以使用 try-catch)
      try {
        // @ts-expect-error - uni-app类型定义不完整，实际上有offEnded方法
        if (globalBgAudioManager.offEnded) globalBgAudioManager.offEnded()
        // @ts-expect-error - uni-app类型定义不完整，实际上有offError方法
        if (globalBgAudioManager.offError) globalBgAudioManager.offError()
        // @ts-expect-error - uni-app类型定义不完整，实际上有offStop方法
        if (globalBgAudioManager.offStop) globalBgAudioManager.offStop()
        // @ts-expect-error - uni-app类型定义不完整，实际上有offPause方法
        if (globalBgAudioManager.offPause) globalBgAudioManager.offPause()
        // @ts-expect-error - uni-app类型定义不完整，实际上有offPlay方法
        if (globalBgAudioManager.offPlay) globalBgAudioManager.offPlay()
      } catch (e) {
        console.warn('清除背景音频事件监听失败，可能不支持 off 方法:', e)
      }
    } catch (e) {
      console.error('清除背景音频实例失败:', e)
    }
  }

  // 获取新的背景音频管理器实例
  const bgAudioManager = getBackgroundAudioManager()
  globalBgAudioManager = bgAudioManager

  // 必填参数
  bgAudioManager.title = options.title || '提示音'

  // 选填参数
  if (options.singer) bgAudioManager.singer = options.singer
  if (options.coverImgUrl) {
    // 处理封面图片路径
    let coverImgUrl = options.coverImgUrl
    if (coverImgUrl && coverImgUrl.startsWith('/')) {
      // #ifdef APP-PLUS
      coverImgUrl = plus.io.convertLocalFileSystemURL(coverImgUrl)
      // #endif

      // #ifdef H5
      coverImgUrl = location.origin + coverImgUrl
      // #endif
    }
    bgAudioManager.coverImgUrl = coverImgUrl
  }

  // 监听播放开始
  bgAudioManager.onPlay(() => {
    isBackgroundAudioPlaying = true
  })

  // 监听播放结束
  bgAudioManager.onEnded(() => {
    isBackgroundAudioPlaying = false

    if (options.loop) {
      // 如果需要循环播放，延迟一会儿后重新设置 src 自动播放
      setTimeout(() => {
        if (bgAudioManager && currentAudioOptions) {
          // 重新设置所有属性
          bgAudioManager.title = currentAudioOptions.title || '提示音'
          if (currentAudioOptions.singer) bgAudioManager.singer = currentAudioOptions.singer
          if (currentAudioOptions.coverImgUrl)
            bgAudioManager.coverImgUrl = currentAudioOptions.coverImgUrl

          // 最后设置 src 触发自动播放
          bgAudioManager.src = currentAudioOptions.src

          // 标记为播放状态
          isBackgroundAudioPlaying = true
        } else {
          console.warn('循环播放失败：背景音频实例或配置已丢失')
        }
      }, 1000)
    }
  })

  // 监听播放停止
  bgAudioManager.onStop(() => {
    isBackgroundAudioPlaying = false
    bgAudioManager.play()
  })

  // 监听播放暂停
  bgAudioManager.onPause(() => {
    isBackgroundAudioPlaying = false
  })

  // 监听播放错误
  bgAudioManager.onError((res) => {
    console.error('背景音频播放失败 - onError 事件:', res)
    isBackgroundAudioPlaying = false

    // 尝试使用备用方案播放
    playFallbackOrderNotification(options.loop)
  })

  // 设置音频源并开始播放
  bgAudioManager.src = audioSrc

  return bgAudioManager
}

/**
 * 播放背景订单提示音（即使在后台也能播放）
 * @param loop 是否循环播放，默认为 false
 * @returns 返回背景音频管理器实例
 */
export function playBackgroundOrderNotification(
  loop: boolean = false,
): UniApp.BackgroundAudioManager {
  return playBackgroundAudio({
    src: '/static/mp3/audio_order_tips.mp3',
    title: '新订单提示音',
    loop,
  })
}

/**
 * 停止背景音频播放
 * @param bgAudioManager 背景音频管理器实例
 */
export function stopBackgroundAudio(bgAudioManager: UniApp.BackgroundAudioManager): void {
  if (bgAudioManager) {
    try {
      bgAudioManager.stop()
      isBackgroundAudioPlaying = false
    } catch (e) {
      console.error('停止背景音频播放失败:', e)
    }
  }
}

/**
 * 播放订单提示音（备用方案，使用 InnerAudioContext）
 * @param loop 是否循环播放，默认为 false
 * @returns 返回音频实例
 */
export function playFallbackOrderNotification(loop: boolean = false): UniApp.InnerAudioContext {
  const audioContext = uni.createInnerAudioContext()

  // 设置属性
  audioContext.src = '/static/mp3/audio_order_tips.mp3'
  audioContext.loop = loop
  audioContext.autoplay = true

  // 添加事件监听
  audioContext.onPlay(() => {})

  audioContext.onEnded(() => {
    // 播放结束后自动销毁实例（非循环模式）
    if (!loop) {
      setTimeout(() => {
        try {
          audioContext.destroy()
        } catch (e) {
          console.error('销毁备用音频实例失败:', e)
        }
      }, 100)
    }
  })

  audioContext.onError((res) => {
    console.error('备用音频播放失败:', res.errMsg, '错误码:', res.errCode)
  })

  return audioContext
}
