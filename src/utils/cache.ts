/**
 * 距离缓存管理工具
 * 用于缓存和管理订单地址距离计算结果
 */

// 缓存键前缀
const DISTANCE_CACHE_PREFIX = 'distance_cache_'

/**
 * 距离缓存项结构
 */
interface DistanceCacheItem {
  distance: number // 距离值（米）
  duration: number // 时间（秒）
  timestamp: number // 缓存时间戳
}

/**
 * 缓存统计信息
 */
interface CacheStats {
  count: number // 缓存项数量
  oldestTime: Date | null // 最早的缓存时间
  newestTime: Date | null // 最新的缓存时间
}

/**
 * 保存距离缓存
 * @param originId 起点ID
 * @param destId 终点ID
 * @param distance 距离（米）
 * @param duration 时间（秒）
 */
export function saveDistanceCache(
  originId: string,
  destId: string,
  distance: number,
  duration: number,
): void {
  try {
    const key = `${DISTANCE_CACHE_PREFIX}${originId}_${destId}`
    const cacheItem: DistanceCacheItem = {
      distance,
      duration,
      timestamp: Date.now(),
    }

    uni.setStorageSync(key, JSON.stringify(cacheItem))
  } catch (error) {
    console.error('保存距离缓存失败:', error)
  }
}

/**
 * 获取距离缓存
 * @param originId 起点ID
 * @param destId 终点ID
 * @returns 缓存的距离信息，如果不存在则返回null
 */
export function getDistanceCache(originId: string, destId: string): DistanceCacheItem | null {
  try {
    const key = `${DISTANCE_CACHE_PREFIX}${originId}_${destId}`
    const data = uni.getStorageSync(key)

    if (!data) return null

    const cacheItem: DistanceCacheItem = JSON.parse(data)
    return cacheItem
  } catch (error) {
    console.error('获取距离缓存失败:', error)
    return null
  }
}

/**
 * 清除过期的距离缓存
 * @param days 过期天数，默认7天
 * @returns 清除的缓存项数量
 */
export function clearExpiredDistanceCache(days: number = 7): number {
  try {
    const expireTime = Date.now() - days * 24 * 60 * 60 * 1000
    const storage = uni.getStorageInfoSync()
    let clearedCount = 0

    storage.keys.forEach((key) => {
      // 只处理距离缓存项
      if (key.startsWith(DISTANCE_CACHE_PREFIX)) {
        try {
          const data = uni.getStorageSync(key)
          if (data) {
            const cacheItem: DistanceCacheItem = JSON.parse(data)

            // 如果缓存时间早于过期时间，则删除
            if (cacheItem.timestamp < expireTime) {
              uni.removeStorageSync(key)
              clearedCount++
            }
          }
        } catch (e) {
          // 如果单个缓存项解析失败，继续处理其他项
          console.warn(`处理缓存项 ${key} 失败:`, e)
        }
      }
    })

    return clearedCount
  } catch (error) {
    console.error('清除过期距离缓存失败:', error)
    return 0
  }
}

/**
 * 获取距离缓存统计信息
 * @returns 缓存统计信息
 */
export function getDistanceCacheStats(): CacheStats {
  try {
    const storage = uni.getStorageInfoSync()
    const stats: CacheStats = {
      count: 0,
      oldestTime: null,
      newestTime: null,
    }

    let oldestTimestamp = Number.MAX_SAFE_INTEGER
    let newestTimestamp = 0

    storage.keys.forEach((key) => {
      // 只统计距离缓存项
      if (key.startsWith(DISTANCE_CACHE_PREFIX)) {
        try {
          const data = uni.getStorageSync(key)
          if (data) {
            const cacheItem: DistanceCacheItem = JSON.parse(data)
            stats.count++

            // 更新最早和最新的时间戳
            if (cacheItem.timestamp < oldestTimestamp) {
              oldestTimestamp = cacheItem.timestamp
              stats.oldestTime = new Date(cacheItem.timestamp)
            }

            if (cacheItem.timestamp > newestTimestamp) {
              newestTimestamp = cacheItem.timestamp
              stats.newestTime = new Date(cacheItem.timestamp)
            }
          }
        } catch (e) {
          // 如果单个缓存项解析失败，继续处理其他项
          console.warn(`统计缓存项 ${key} 失败:`, e)
        }
      }
    })

    return stats
  } catch (error) {
    console.error('获取距离缓存统计信息失败:', error)
    return { count: 0, oldestTime: null, newestTime: null }
  }
}

/**
 * 清除所有距离缓存
 * @returns 清除的缓存项数量
 */
export function clearAllDistanceCache(): number {
  try {
    const storage = uni.getStorageInfoSync()
    let clearedCount = 0

    storage.keys.forEach((key) => {
      // 只处理距离缓存项
      if (key.startsWith(DISTANCE_CACHE_PREFIX)) {
        uni.removeStorageSync(key)
        clearedCount++
      }
    })

    return clearedCount
  } catch (error) {
    console.error('清除所有距离缓存失败:', error)
    return 0
  }
}
