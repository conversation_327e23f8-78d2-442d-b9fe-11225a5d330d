import { CustomRequestOptions } from '@/interceptors/request'
import { useUserStore } from '@/store'
import { setDeviceOffline } from '@/utils/device'

// 全局标记位，防止重复处理401错误
let isHandling401 = false

/**
 * 请求方法: 主要是对 uni.request 的封装，去适配 openapi-ts-request 的 request 方法
 * @param options 请求参数
 * @returns 返回 Promise 对象
 */
const http = <T>(options: CustomRequestOptions) => {
  // 获取用户 store
  const userStore = useUserStore()

  // 1. 返回 Promise 对象
  return new Promise<T>((resolve, reject) => {
    // 如果正在处理401错误，直接拒绝请求
    if (isHandling401) {
      reject(new Error('正在处理登录失效，请稍后重试'))
      return
    }

    uni.request({
      ...options,
      dataType: 'json',
      // #ifndef MP-WEIXIN
      responseType: 'json',
      // #endif
      // 响应成功
      success(res) {
        console.log('res====success', res)
        const isObject = typeof res.data === 'object' && res.data !== null
        let resultCode401 = false
        if (isObject) {
          resultCode401 =
            (res.data as any).resultCode === 401 || (res.data as any).resultCode === '401'
        }
        if (res.statusCode >= 200 && res.statusCode < 300 && !resultCode401) {
          resolve(res.data as T)
        } else if (res.statusCode === 401 || resultCode401) {
          // 401错误处理 - 防止重复执行
          if (!isHandling401) {
            isHandling401 = true

            console.log('检测到 401 错误，触发弹窗显示')

            // 设置设备离线状态
            setDeviceOffline('401错误').finally(() => {
              // 处理完成后重置标记位
              setTimeout(() => {
                isHandling401 = false
              }, 1000) // 1秒后重置，避免短时间内重复触发
            })

            // 触发全局事件显示 401 弹窗，而不是直接跳转
            try {
              uni.$emit('show401Dialog', {
                statusCode: res.statusCode,
                resultCode: (res.data as any)?.resultCode,
              })
            } catch (error) {
              console.error('触发 401 弹窗事件失败:', error)
              // 如果事件触发失败，fallback 到直接处理
              userStore.clearUserInfo()
              uni.reLaunch({ url: '/pages/login/detail/index' })
            }
          }
          reject(res)
        } else {
          !options.hideErrorToast &&
            uni.showToast({
              icon: 'none',
              title: (res.data as T & { msg?: string })?.msg || '请求错误',
            })
          reject(res)
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
    })
  })
}

/*
 * openapi-ts-request 工具的 request 跨客户端适配方法
 */
export default function request<T = unknown>(
  url: string,
  options: Omit<CustomRequestOptions, 'url'> & {
    params?: Record<string, unknown>
    headers?: Record<string, unknown>
  },
) {
  const requestOptions = {
    url,
    ...options,
  }

  if (options.params) {
    requestOptions.query = requestOptions.params
    delete requestOptions.params
  }

  if (options.headers) {
    requestOptions.header = options.headers
    delete requestOptions.headers
  }

  return http<T>(requestOptions)
}
