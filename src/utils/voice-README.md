# 语音播报功能使用说明

## 功能概述

本模块实现了一个由前端控制的、模板化的语音播报功能，可以根据极光推送（JPush）消息动态生成语音播放队列。

## 文件结构

```
src/utils/
├── voice-template.ts    # 语音模板模块 - 负责构建播放队列
├── voice-player.ts      # 语音播放器模块 - 负责按序播放
└── audio.ts            # 基础音频播放工具
```

## 音频文件结构

```
src/static/mp3/voice/
├── brand/              # 品牌音频（美团、饿了么等）
├── number/             # 数字音频（0-9, 10, 100）
├── common/             # 通用音频（"号"、开始/结束提示音）
└── text/               # 业务文本音频（提醒、取消等）
```

## 使用方法

### 1. 在 JPush 消息处理中使用

```typescript
import { playVoiceAnnouncement } from '@/utils/voice-player'
import type { VoicePayload } from '@/utils/voice-template'

// 解析 JPush 消息
const payload: VoicePayload = {
  notifyType: 'user_remind', // 必填：业务类型
  channel: 'MeiTuanTakeOutBrand', // 可选：渠道
  takeNo: 123, // 可选：取餐号
}

// 播放语音播报
playVoiceAnnouncement(payload)
```

### 2. 测试语音播报

```typescript
// 在页面中触发测试事件
uni.$emit('test-voice-announcement', {
  notifyType: 'user_remind',
  channel: 'MeiTuanTakeOutBrand',
  takeNo: 123,
})
```

## 配置说明

### 需要开/结尾提示音的业务类型

在 `voice-template.ts` 中的 `NOTIFY_TYPES_WITH_MUSIC` 配置：

```typescript
const NOTIFY_TYPES_WITH_MUSIC: Set<string> = new Set([
  'order_accepted', // 接单通知
  'over_time_wait_receive', // 超时未接单
  'abnormal_delivery', // 配送异常
])
```

### 渠道映射

支持的渠道及对应的音频文件：

- `MeiTuanTakeOutBrand` → `brand_meituan.mp3`
- `ELeMeTakeOutBrand` → `brand_eleme.mp3`
- `JingDongTakeOutBrand` → `brand_jingdong.mp3`
- 等等...

### 业务类型映射

支持的业务类型及对应的音频文件：

- `user_remind` → `text_remind.mp3`
- `user_cancel` → `text_user_cancel.mp3`
- `abnormal_delivery` → `text_abnormal_delivery.mp3`
- 等等...

## 播放逻辑

1. **有提示音的业务类型**：开始提示音 → 业务内容 → 结束提示音
2. **普通业务类型**：品牌名 → 取餐号 + "号" → 业务文本
3. **特殊业务类型**：有专门的分段播放逻辑（如接单通知）

## 扩展说明

要添加新的业务类型或渠道，只需在 `voice-template.ts` 中更新相应的映射表即可。
