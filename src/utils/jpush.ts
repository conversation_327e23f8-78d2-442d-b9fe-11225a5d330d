/**
 * 极光推送工具函数
 */
import { playOrderNotification } from './audio'

// 消息类型枚举
export enum MessageType {
  NEW_ORDER = 'new_order', // 新订单
  REFUND = 'refund', // 退款
  SYSTEM = 'system', // 系统消息
}

/**
 * 处理极光推送消息
 * @param message 推送消息对象
 */
export function handleJPushMessage(message: any): void {
  try {
    // 解析消息内容
    const messageData = typeof message === 'string' ? JSON.parse(message) : message

    // 根据消息类型处理
    switch (messageData.type) {
      case MessageType.NEW_ORDER:
        // 播放新订单提示音
        playOrderNotification()
        // 可以在这里添加其他处理逻辑，如更新状态、显示通知等
        console.log('收到新订单:', messageData)
        break

      case MessageType.REFUND:
        // 处理退款消息
        console.log('收到退款消息:', messageData)
        break

      case MessageType.SYSTEM:
        // 处理系统消息
        console.log('收到系统消息:', messageData)
        break

      default:
        console.log('收到未知类型消息:', messageData)
    }
  } catch (error) {
    console.error('处理极光推送消息失败:', error, message)
  }
}

/**
 * 初始化极光推送
 * 需要在 App.vue 的 onLaunch 中调用
 */
export function initJPush(): void {
  // 判断环境，仅在 App 环境下初始化极光推送
  // #ifdef APP-PLUS
  try {
    const jPush = uni.requireNativePlugin('JG-JPush')
    if (!jPush) {
      console.error('极光推送插件未找到')
      return
    }

    // 初始化极光推送
    jPush.initJPushService()

    // 监听接收通知事件
    jPush.addReceiveNotificationListener((result: any) => {
      console.log('收到通知:', result)
    })

    // 监听接收自定义消息事件
    jPush.addReceiveMessageListener((result: any) => {
      console.log('收到自定义消息:', result)
      handleJPushMessage(result)
    })

    // 监听点击通知事件
    jPush.addReceiveNotificationListener((result: any) => {
      console.log('点击通知:', result)
    })

    console.log('极光推送初始化成功')
  } catch (error) {
    console.error('极光推送初始化失败:', error)
  }
  // #endif
}
