import { pages, subPackages, tabBar } from '@/pages.json'
import { isMpWeixin } from './platform'

export const getLastPage = () => {
  // getCurrentPages() 至少有1个元素，所以不再额外判断
  // const lastPage = getCurrentPages().at(-1)
  // 上面那个在低版本安卓中打包会报错，所以改用下面这个【虽然我加了 src/interceptions/prototype.ts，但依然报错】
  const pages = getCurrentPages()
  return pages[pages.length - 1]
}

/** 判断当前页面是否是 tabbar 页  */
export const getIsTabbar = () => {
  if (!tabBar) {
    return false
  }
  if (!tabBar.list.length) {
    // 通常有 tabBar 的话，list 不能有空，且至少有2个元素，这里其实不用处理
    return false
  }
  const lastPage = getLastPage()
  const currPath = lastPage.route
  return !!tabBar.list.find((e) => e.pagePath === currPath)
}

/**
 * 获取当前页面路由的 path 路径和 redirectPath 路径
 * path 如 '/pages/login/index'
 * redirectPath 如 '/pages/demo/base/route-interceptor'
 */
export const currRoute = () => {
  const lastPage = getLastPage()
  const currRoute = (lastPage as any).$page
  // console.log('lastPage.$page:', currRoute)
  // console.log('lastPage.$page.fullpath:', currRoute.fullPath)
  // console.log('lastPage.$page.options:', currRoute.options)
  // console.log('lastPage.options:', (lastPage as any).options)
  // 经过多端测试，只有 fullPath 靠谱，其他都不靠谱
  const { fullPath } = currRoute as { fullPath: string }
  // console.log(fullPath)
  // eg: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor (小程序)
  // eg: /pages/login/index?redirect=%2Fpages%2Froute-interceptor%2Findex%3Fname%3Dfeige%26age%3D30(h5)
  return getUrlObj(fullPath)
}

const ensureDecodeURIComponent = (url: string) => {
  if (url.startsWith('%')) {
    return ensureDecodeURIComponent(decodeURIComponent(url))
  }
  return url
}
/**
 * 解析 url 得到 path 和 query
 * 比如输入url: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor
 * 输出: {path: /pages/login/index, query: {redirect: /pages/demo/base/route-interceptor}}
 */
export const getUrlObj = (url: string) => {
  const [path, queryStr] = url.split('?')
  // console.log(path, queryStr)

  if (!queryStr) {
    return {
      path,
      query: {},
    }
  }
  const query: Record<string, string> = {}
  queryStr.split('&').forEach((item) => {
    const [key, value] = item.split('=')
    // console.log(key, value)
    query[key] = ensureDecodeURIComponent(value) // 这里需要统一 decodeURIComponent 一下，可以兼容h5和微信y
  })
  return { path, query }
}
/**
 * 得到所有的需要登录的 pages，包括主包和分包的
 * 这里设计得通用一点，可以传递 key 作为判断依据，默认是 needLogin, 与 route-block 配对使用
 * 如果没有传 key，则表示所有的 pages，如果传递了 key, 则表示通过 key 过滤
 */
export const getAllPages = (key = 'needLogin') => {
  // 这里处理主包
  const mainPages = [
    ...pages
      .filter((page) => !key || page[key])
      .map((page) => ({
        ...page,
        path: `/${page.path}`,
      })),
  ]
  // 这里处理分包
  const subPages: any[] = []
  subPackages.forEach((subPageObj) => {
    // console.log(subPageObj)
    const { root } = subPageObj

    subPageObj.pages
      .filter((page) => !key || page[key])
      .forEach((page: { path: string } & Record<string, any>) => {
        subPages.push({
          ...page,
          path: `/${root}/${page.path}`,
        })
      })
  })
  const result = [...mainPages, ...subPages]
  // console.log(`getAllPages by ${key} result: `, result)
  return result
}

/**
 * 得到所有的需要登录的 pages，包括主包和分包的
 * 只得到 path 数组
 */
export const getNeedLoginPages = (): string[] => getAllPages('needLogin').map((page) => page.path)

/**
 * 得到所有的需要登录的 pages，包括主包和分包的
 * 只得到 path 数组
 */
export const needLoginPages: string[] = getAllPages('needLogin').map((page) => page.path)

/**
 * 根据微信小程序当前环境，判断应该获取的 baseUrl
 */
export const getEnvBaseUrl = () => {
  // 请求基准地址
  let baseUrl = import.meta.env.VITE_SERVER_BASEURL

  // 微信小程序端环境区分
  if (isMpWeixin) {
    const {
      miniProgram: { envVersion },
    } = uni.getAccountInfoSync()

    switch (envVersion) {
      case 'develop':
        baseUrl = import.meta.env.VITE_SERVER_BASEURL__WEIXIN_DEVELOP || baseUrl
        break
      case 'trial':
        baseUrl = import.meta.env.VITE_SERVER_BASEURL__WEIXIN_TRIAL || baseUrl
        break
      case 'release':
        baseUrl = import.meta.env.VITE_SERVER_BASEURL__WEIXIN_RELEASE || baseUrl
        break
    }
  }

  return baseUrl
}

/**
 * 根据微信小程序当前环境，判断应该获取的 UPLOAD_BASEURL
 */
export const getEnvBaseUploadUrl = () => {
  // 请求基准地址
  let baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL

  // 微信小程序端环境区分
  if (isMpWeixin) {
    const {
      miniProgram: { envVersion },
    } = uni.getAccountInfoSync()

    switch (envVersion) {
      case 'develop':
        baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL__WEIXIN_DEVELOP || baseUploadUrl
        break
      case 'trial':
        baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL__WEIXIN_TRIAL || baseUploadUrl
        break
      case 'release':
        baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL__WEIXIN_RELEASE || baseUploadUrl
        break
    }
  }

  return baseUploadUrl
}

/**
 * 字符串转base64
 */
export const rstr2b64 = (input: string): string => {
  const b64pad = ''
  const tab = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
  let output = ''
  const len = input.length
  for (let i = 0; i < len; i += 3) {
    const triplet =
      (input.charCodeAt(i) << 16) |
      (i + 1 < len ? input.charCodeAt(i + 1) << 8 : 0) |
      (i + 2 < len ? input.charCodeAt(i + 2) : 0)
    for (let j = 0; j < 4; j++) {
      if (i * 8 + j * 6 > input.length * 8) {
        output += b64pad
      } else {
        output += tab.charAt((triplet >>> (6 * (3 - j))) & 0x3f)
      }
    }
  }
  return output
}

// base64转字符串
export const base64Decode = (input: string): string => {
  if (typeof atob === 'function') {
    return atob(input)
  }
}

// 添加一个辅助函数来检查值是否为空
export const isEmptyValue = (value: any): boolean => {
  if (value === null || value === undefined || value === '') {
    return true
  }
  if (Array.isArray(value) && value.length === 0) {
    return true
  }
  if (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0) {
    return true
  }
  return false
}

// 判断手机号格式
export const validPhone = (phoneNumber: string) => {
  const phoneReg = /^1[3-9]\d{9}$/
  if (phoneReg.test(phoneNumber)) {
    return true
  } else {
    return false
  }
}

// 获取状态栏高度
export const getStatusBarHeight = () => {
  const systemInfo = uni.getSystemInfoSync()
  return systemInfo.statusBarHeight || 0
}

// 获取当前环境域名
export const getApiDomainPre = (): string => {
  return import.meta.env.VITE_SERVER_BASEPRE || 'imarketing.cloud.dtyunxi.com'
}

// 获取当前环境（production、development）
export const getEnvMode = (): string => {
  return import.meta.env.MODE
}

// HTTP 请求工具
export * from './http'

// 音频播放工具
export * from './audio'

// 语音播放器
export * from './voice-player'

// 语音模板
export * from './voice-template'

// TTS 语音播报
export { initTTS, speakText, stopTTS, playTTSAnnouncement, playTTSContent } from './tts'
export type { TTSPayload } from './tts'

// 极光推送
export * from './jpush'

// 平台工具
export * from './platform'

// 数据转换工具
export * from './transform'

// 请求工具（旧版）
export * from './request'

/**
 * 隐私号处理工具函数
 */

/**
 * 检查是否为隐私号格式
 * @param phoneNumber 电话号码
 * @returns 是否为隐私号格式
 */
export const isPrivacyPhone = (phoneNumber: string): boolean => {
  if (!phoneNumber) return false
  return phoneNumber.includes('_') || phoneNumber.includes(',') || phoneNumber.includes('-')
}

/**
 * 解析隐私号，提取主号码和分机号
 * @param phoneNumber 隐私号如 "13148112418_0778" 或 "13148112418,0778" 或 "13045975938-411"
 * @returns { mainPhone: string, extension: string } 主号码和分机号
 */
export const parsePrivacyPhone = (
  phoneNumber: string,
): { mainPhone: string; extension: string } => {
  if (!phoneNumber) return { mainPhone: '', extension: '' }

  let separator = ''
  if (phoneNumber.includes('_')) {
    separator = '_'
  } else if (phoneNumber.includes(',')) {
    separator = ','
  } else if (phoneNumber.includes('-')) {
    separator = '-'
  } else {
    return { mainPhone: phoneNumber, extension: '' }
  }

  const [mainPhone, extension] = phoneNumber.split(separator)
  return {
    mainPhone: mainPhone || '',
    extension: extension || '',
  }
}

/**
 * 格式化隐私号显示
 * @param phoneNumber 隐私号如 "13148112418_0778" 或 "13148112418,0778" 或 "13045975938-411"
 * @returns 格式化后的显示文本如 "13148112418转0778"
 */
export const formatPrivacyPhone = (phoneNumber: string): string => {
  if (!isPrivacyPhone(phoneNumber)) return phoneNumber

  const { mainPhone, extension } = parsePrivacyPhone(phoneNumber)
  if (!mainPhone || !extension) return phoneNumber

  return `${mainPhone}转${extension}`
}

/**
 * 获取隐私号的分机号
 * @param phoneNumber 隐私号如 "13148112418_0778" 或 "13148112418,0778" 或 "13045975938-411"
 * @returns 分机号如 "0778"
 */
export const getPrivacyPhoneExtension = (phoneNumber: string): string => {
  if (!isPrivacyPhone(phoneNumber)) return ''

  const { extension } = parsePrivacyPhone(phoneNumber)
  return extension
}

/**
 * 获取隐私号的主号码
 * @param phoneNumber 隐私号如 "13148112418_0778" 或 "13148112418,0778" 或 "13045975938-411"
 * @returns 主号码如 "13148112418"
 */
export const getPrivacyPhoneMain = (phoneNumber: string): string => {
  if (!isPrivacyPhone(phoneNumber)) return phoneNumber

  const { mainPhone } = parsePrivacyPhone(phoneNumber)
  return mainPhone
}

/**
 * 递归检查本地存储中的指定键值
 * @param key 要检查的存储键
 * @param callback 找到值时的回调函数
 * @param maxTime 最大等待时间(毫秒)，默认30秒
 * @param interval 检查间隔(毫秒)，默认1000毫秒
 * @param startTime 开始检查的时间戳，内部使用，无需传递
 * @param resetAfterResolve 在找到值或超时后是否重置缓存，默认为true
 * @returns 返回一个Promise，resolve返回获取到的值，或者在超时后返回null
 */
export const watchStorageKey = <T = any>(
  key: string,
  callback?: (value: T | null) => void,
  maxTime: number = 30000,
  interval: number = 1000,
  startTime: number = Date.now(),
  resetAfterResolve: boolean = true,
): Promise<T | null> => {
  return new Promise((resolve) => {
    // 检查是否超过最大等待时间
    const currentTime = Date.now()
    const elapsed = currentTime - startTime

    if (elapsed >= maxTime) {
      console.log(`检查本地存储键 "${key}" 超时(${maxTime}ms)`)
      // 超时时如果需要清除缓存
      if (resetAfterResolve) {
        try {
          uni.removeStorageSync(key)
          console.log(`已清除超时的存储键 "${key}"`)
        } catch (error) {
          console.error(`清除存储键 "${key}" 时出错:`, error)
        }
      }
      callback?.(null)
      resolve(null)
      return
    }

    // 尝试获取存储中的值
    try {
      const value = uni.getStorageSync(key)

      // 如果找到值且不为空
      if (!isEmptyValue(value)) {
        console.log(`成功获取本地存储键 "${key}" 的值:`, value)
        // 如果需要清除缓存
        if (resetAfterResolve) {
          try {
            uni.removeStorageSync(key)
            console.log(`已清除已使用的存储键 "${key}"`)
          } catch (error) {
            console.error(`清除存储键 "${key}" 时出错:`, error)
          }
        }
        callback?.(value as T)
        resolve(value as T)
        return
      }

      // 如果没有找到值，则等待指定间隔后再次检查
      setTimeout(() => {
        watchStorageKey<T>(key, callback, maxTime, interval, startTime, resetAfterResolve)
          .then(resolve)
          .catch(() => resolve(null))
      }, interval)
    } catch (error) {
      console.error(`检查本地存储键 "${key}" 时出错:`, error)
      callback?.(null)
      resolve(null)
    }
  })
}
