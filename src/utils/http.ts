import { CustomRequestOptions } from '@/interceptors/request'
import { useUserStore } from '@/store'
import { setDeviceOffline } from '@/utils/device'

// 全局标记位，防止重复处理401错误
let isHandling401 = false

export const http = <T>(options: CustomRequestOptions) => {
  // 1. 返回 Promise 对象
  return new Promise<IResData<T>>((resolve, reject) => {
    // 如果正在处理401错误，直接拒绝请求
    if (isHandling401) {
      reject(new Error('正在处理登录失效，请稍后重试'))
      return
    }

    uni.request({
      ...options,
      dataType: 'json',
      // #ifndef MP-WEIXIN
      responseType: 'json',
      timeout: 12000,
      // #endif
      // 响应成功
      success(res) {
        console.log('res====success', res)
        const isObject = typeof res.data === 'object' && res.data !== null
        let resultCode401 = false
        if (isObject) {
          resultCode401 =
            (res.data as any).resultCode === 401 || (res.data as any).resultCode === '401'
        }
        if (res.statusCode >= 200 && res.statusCode < 300 && !resultCode401) {
          // 2.1 提取核心数据 res.data
          resolve(res.data as IResData<T>)
        } else if (res.statusCode === 401 || resultCode401) {
          // 401错误处理 - 防止重复执行
          if (!isHandling401) {
            isHandling401 = true

            console.log('检测到 401 错误，触发弹窗显示')

            // 设置设备离线状态
            setDeviceOffline('401错误')
              .then(() => {
                console.log('401错误处理完成')
              })
              .catch((error) => {
                console.error('401错误处理失败:', error)
              })
              .finally(() => {
                // 处理完成后重置标记位
                setTimeout(() => {
                  isHandling401 = false
                }, 1000) // 1秒后重置，避免短时间内重复触发
              })

            // 触发全局事件显示 401 弹窗，而不是直接跳转
            try {
              uni.$emit('show401Dialog', {
                statusCode: res.statusCode,
                resultCode: (res.data as any)?.resultCode,
              })
            } catch (error) {
              console.error('触发 401 弹窗事件失败:', error)
              // 如果事件触发失败，fallback 到直接处理
              const userStore = useUserStore()
              userStore.clearUserInfo()
              uni.reLaunch({ url: '/pages/login/detail/index' })
            }
          }
          reject(res)
        } else {
          // 其他错误 -> 根据后端错误信息轻提示
          !options.hideErrorToast &&
            uni.showToast({
              icon: 'none',
              title: isObject
                ? (res.data as any).msg || (res.data as any).resultMsg || '请求错误'
                : '请求错误',
            })
          reject(res)
        }
      },
      // 响应失败
      fail(err) {
        console.log('res====fail', err)
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
      complete(resOrErr) {
        console.log('res====complete', resOrErr)
      },
    })
  })
}

/**
 * GET 请求
 * @param url 后台地址
 * @param query 请求query参数
 * @param header 请求头，默认为json格式
 * @returns
 */
export const httpGet = <T>(
  url: string,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    method: 'GET',
    header,
  })
}

/**
 * POST 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数，post请求也支持query，很多微信接口都需要
 * @param header 请求头，默认为json格式
 * @returns
 */
export const httpPost = <T>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
  header?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    data,
    method: 'POST',
    header,
  })
}

http.get = httpGet
http.post = httpPost
