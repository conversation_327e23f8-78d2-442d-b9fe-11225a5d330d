{"name": "海底捞全单汇-UAT", "appid": "__UNI__2704FCB", "description": "", "versionName": "1.0.0.1020", "versionCode": "10", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Push": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.POST_NOTIFICATIONS\"/>", "<uses-permission android:name=\"android.permission.FOREGROUND_SERVICE\"/>"], "schemes": "unijidanbao", "minSdkVersion": 30, "targetSdkVersion": 30, "abiFilters": ["armeabi-v7a", "arm64-v8a"], "keystore": "{\"storeFile\":\"haidilao_shop.keystore\",\"storePassword\":\"4FnFGHOj\",\"keyAlias\":\"__uni__2704fcb\",\"keyPassword\":\"4FnFGHOj\"}", "package": "{\"packageName\":\"com.haidilao.shop\"}"}, "ios": {"UIBackgroundModes": "audio,location"}, "sdkConfigs": {}, "icons": {"android": {"hdpi": "src/static/app/icons/72x72.png", "xhdpi": "src/static/app/icons/96x96.png", "xxhdpi": "src/static/app/icons/144x144.png", "xxxhdpi": "src/static/app/icons/192x192.png"}, "ios": {"appstore": "src/static/app/icons/1024x1024.png", "ipad": {"app": "src/static/app/icons/76x76.png", "app@2x": "src/static/app/icons/152x152.png", "notification": "src/static/app/icons/20x20.png", "notification@2x": "src/static/app/icons/40x40.png", "proapp@2x": "src/static/app/icons/167x167.png", "settings": "src/static/app/icons/29x29.png", "settings@2x": "src/static/app/icons/58x58.png", "spotlight": "src/static/app/icons/40x40.png", "spotlight@2x": "src/static/app/icons/80x80.png"}, "iphone": {"app@2x": "src/static/app/icons/120x120.png", "app@3x": "src/static/app/icons/180x180.png", "notification@2x": "src/static/app/icons/40x40.png", "notification@3x": "src/static/app/icons/60x60.png", "settings@2x": "src/static/app/icons/58x58.png", "settings@3x": "src/static/app/icons/87x87.png", "spotlight@2x": "src/static/app/icons/80x80.png", "spotlight@3x": "src/static/app/icons/120x120.png"}}}}, "compatible": {"ignoreVersion": true}, "nativePlugins": {"JG-JCore": {"JPUSH_APPKEY_ANDROID": "b3487b90a9af23469ec1f169", "JPUSH_APPKEY_IOS": "b3487b90a9af23469ec1f169", "JPUSH_CHANNEL_IOS": "", "JPUSH_CHANNEL_ANDROID": "", "__plugin_info__": {"name": "JG-JCore", "description": "极光推送JCore插件", "platforms": "Android,iOS", "url": "https://ext.dcloud.net.cn/plugin?id=4035", "android_package_name": "com.haidilao.shop", "ios_bundle_id": "com.haidilao.shop", "isCloud": false, "bought": -1, "pid": "", "parameters": {"JPUSH_APPKEY_ANDROID": {"des": "[Android]极光 portal 配置应用信息时分配的 AppKey", "key": "JPUSH_APPKEY", "value": "b3487b90a9af23469ec1f169"}, "JPUSH_APPKEY_IOS": {"des": "[iOS]极光 portal 配置应用信息时分配的 AppKey", "key": "JCore:APP_KEY", "value": "b3487b90a9af23469ec1f169"}, "JPUSH_CHANNEL_IOS": {"des": "[iOS]用于统计分发渠道，不需要可填默认值 developer-default", "key": "JCore:CHANNEL", "value": ""}, "JPUSH_CHANNEL_ANDROID": {"des": "[Android]用于统计分发渠道，不需要可填默认值 developer-default", "key": "JPUSH_CHANNEL", "value": ""}}}}, "JG-JPush": {"JPUSH_XIAOMI_APPKEY": "", "JPUSH_MEIZU_APPID": "", "JPUSH_OPPO_APPSECRET": "", "JPUSH_OPPO_APPKEY": "", "JPUSH_ADVERTISINGID_IOS": "", "JPUSH_MEIZU_APPKEY": "", "JPUSH_OPPO_APPID": "", "JPUSH_DEFAULTINITJPUSH_IOS": "", "JPUSH_VIVO_APPKEY": "", "JPUSH_VIVO_APPID": "", "JPUSH_ISPRODUCTION_IOS": "", "JPUSH_XIAOMI_APPID": "", "JPUSH_HUAWEI_APPID": "", "JPUSH_HONOR_APPID": "", "JPUSH_GOOGLE_API_KEY": "", "JPUSH_GOOGLE_APP_ID": "", "JPUSH_GOOGLE_PROJECT_NUMBER": "", "JPUSH_GOOGLE_PROJECT_ID": "", "JPUSH_GOOGLE_STORAGE_BUCKET": "", "__plugin_info__": {"name": "J<PERSON><PERSON><PERSON><PERSON>", "description": "极光推送Hbuilder插件", "platforms": "Android,iOS", "url": "https://ext.dcloud.net.cn/plugin?id=4035", "android_package_name": "com.haidilao.shop", "ios_bundle_id": "com.haidilao.shop", "isCloud": false, "bought": -1, "pid": "", "parameters": {"JPUSH_XIAOMI_APPKEY": {"des": "厂商 xia<PERSON> <PERSON><PERSON>,示例：***********", "key": "XIAOMI_APPKEY", "value": ""}, "JPUSH_MEIZU_APPID": {"des": "厂商 meizu appId,示例：***********", "key": "MEIZU_APPID", "value": ""}, "JPUSH_OPPO_APPSECRET": {"des": "厂商 oppo appSecret,示例：***********", "key": "OPPO_APPSECRET", "value": ""}, "JPUSH_OPPO_APPKEY": {"des": "厂商 oppo appkey,示例：***********", "key": "OPPO_APPKEY", "value": ""}, "JPUSH_ADVERTISINGID_IOS": {"des": "[iOS]广告标识符（IDFA） 如果不需要使用IDFA，可不填", "key": "JPush:ADVERTISINGID", "value": ""}, "JPUSH_MEIZU_APPKEY": {"des": "厂商 me<PERSON>u <PERSON><PERSON>,示例：***********", "key": "MEIZU_APPKEY", "value": ""}, "JPUSH_OPPO_APPID": {"des": "厂商 oppo appId,示例：***********", "key": "OPPO_APPID", "value": ""}, "JPUSH_DEFAULTINITJPUSH_IOS": {"des": "[iOS]是否默认初始化，是填true，不是填false或者不填", "key": "JPush:DEFAULTINITJPUSH", "value": ""}, "JPUSH_VIVO_APPKEY": {"des": "厂商 vivo appkey,示例：12345678", "key": "com.vivo.push.api_key", "value": ""}, "JPUSH_VIVO_APPID": {"des": "厂商 vivo appId,示例：12345678", "key": "com.vivo.push.app_id", "value": ""}, "JPUSH_ISPRODUCTION_IOS": {"des": "[iOS]是否是生产环境，是填true,不是填false或者不填", "key": "JPush:ISPRODUCTION", "value": ""}, "JPUSH_XIAOMI_APPID": {"des": "厂商 xiaomi appId,示例：***********", "key": "XIAOMI_APPID", "value": ""}, "JPUSH_HUAWEI_APPID": {"des": "厂商 huawei appId,示例：HUAWEI-12345678", "key": "HUAWEI_APPID", "value": ""}, "JPUSH_HONOR_APPID": {"des": "厂商 honor appId,示例：HONOR-12345678", "key": "HONOR_APPID", "value": ""}}, "JPUSH_GOOGLE_API_KEY": {"des": "厂商google api_key,示例:g-12346578", "key": "google_api_key", "value": ""}, "JPUSH_GOOGLE_APP_ID": {"des": "厂商google mobilesdk_app_id,示例：g-12346578", "key": "google_app_id", "value": ""}, "JPUSH_GOOGLE_PROJECT_NUMBER": {"des": "厂商google project_number,示例：g-12346578", "key": "gcm_defaultSenderId", "value": ""}, "JPUSH_GOOGLE_PROJECT_ID": {"des": "厂商google project_id ,示例：g-12346578", "key": "project_id", "value": ""}, "JPUSH_GOOGLE_STORAGE_BUCKET": {"des": "厂商google storage_bucket,示例：g-12346578", "key": "google_storage_bucket", "value": ""}}}, "hm-tts": {"name": "hm-tts", "id": "hm-tts", "version": "1.1.0", "description": "中文TTS语音插件", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"android": {"plugins": [{"type": "module", "name": "hm-tts-TtsModule", "class": "com.hmen.cloud.tts.TtsModule"}], "integrateType": "aar", "minSdkVersion": 21}}}, "Xkeep-Life": {"name": "海底捞全单汇保活", "id": "Xkeep-Life", "version": "1.0.0", "description": "应用保活插件", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"android": {"plugins": [{"type": "module", "name": "Xkeep-Life-uniplugin_module", "class": "io.dcloud.uniplugin.TestModule"}], "integrateType": "aar", "minSdkVersion": "21"}}}}}, "quickapp": {}, "mp-weixin": {"appid": "wxa2abb91f64032a2b", "setting": {"urlCheck": false}, "usingComponents": true}, "mp-alipay": {"usingComponents": true, "styleIsolation": "shared"}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3", "dcloudAppkey": "b2444fe6327461971d4c0a490e3ffe0e", "h5": {"router": {"base": "/"}}}