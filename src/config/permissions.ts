/**
 * 权限码配置
 * 统一管理系统中所有的权限码
 */

export const PERMISSION_CODES = {
  // ========== TabBar 菜单权限 ==========
  /** 订单 TabBar */
  MENU_INDEX: 'jidanbao_menu_index',
  /** 菜品管理 TabBar */
  MENU_DISH_MANAGEMENT: 'jidanbao_menu_dishManagement',
  /** 评价 TabBar */
  MENU_REVIEW: 'jidanbao_menu_review',
  /** 门店 TabBar */
  MENU_STORE_DETAIL: 'jidanbao_menu_storeDetail',

  // ========== 订单页面权限 ==========
  /** 订单标签 */
  ORDER_TAB: 'jidanbao_menu_index_order',
  /** 售后标签 */
  AFTER_SALE_TAB: 'jidanbao_menu_index_afterSale',
  /** 申诉标签 */
  DAMAGE_APPEAL_TAB: 'jidanbao_menu_index_damageAppeal',

  // ========== 订单操作权限 ==========
  /** 接单按钮 */
  ORDER_ACCEPT_BTN: 'jidanbao_menu_index_order_acceptbtn',
  /** 退款按钮 */
  ORDER_REFUND_BTN: 'jidanbao_menu_index_order_refundbtn',
  /** 售后操作按钮 */
  ORDER_AFTER_SALE_ACTIONS_BTN: 'jidanbao_menu_index_order_afterSaleActionsbtn',

  // ========== 评价页面权限 ==========
  /** 回复按钮 */
  REVIEW_REPLY_BTN: 'jidanbao_menu_review_replybtn',

  // ========== 门店页面权限 ==========
  /** 设置按钮 */
  STORE_SETTING_BTN: 'jidanbao_menu_storeDetail_settingbtn',

  // ========== 设置页面权限 ==========
  /** 消息铃声设置 */
  SETTING_MESSAGE_RING: 'jidanbao_menu_storeDetail_messageRing',
  /** 营业状态设置 */
  SETTING_BUSINESS_STATUS: 'setting-business-status',
  /** 店铺统计 */
  SHOP_STATISTICS: 'shop-statistics',
} as const

/**
 * 权限码类型
 */
export type PermissionCode = (typeof PERMISSION_CODES)[keyof typeof PERMISSION_CODES]

/**
 * 权限组配置
 * 将相关权限码分组管理
 */
export const PERMISSION_GROUPS = {
  /** TabBar 菜单权限组 */
  TABBAR: [
    PERMISSION_CODES.MENU_INDEX,
    PERMISSION_CODES.MENU_DISH_MANAGEMENT,
    PERMISSION_CODES.MENU_REVIEW,
    PERMISSION_CODES.MENU_STORE_DETAIL,
  ],

  /** 订单相关权限组 */
  ORDER: [
    PERMISSION_CODES.ORDER_TAB,
    PERMISSION_CODES.AFTER_SALE_TAB,
    PERMISSION_CODES.DAMAGE_APPEAL_TAB,
    PERMISSION_CODES.ORDER_ACCEPT_BTN,
    PERMISSION_CODES.ORDER_REFUND_BTN,
    PERMISSION_CODES.ORDER_AFTER_SALE_ACTIONS_BTN,
  ],

  /** 评价相关权限组 */
  REVIEW: [PERMISSION_CODES.REVIEW_REPLY_BTN],

  /** 门店相关权限组 */
  STORE: [
    PERMISSION_CODES.STORE_SETTING_BTN,
    PERMISSION_CODES.SETTING_MESSAGE_RING,
    PERMISSION_CODES.SETTING_BUSINESS_STATUS,
    PERMISSION_CODES.SHOP_STATISTICS,
  ],
} as const

/**
 * 权限描述映射
 * 用于调试和文档
 */
export const PERMISSION_DESCRIPTIONS = {
  [PERMISSION_CODES.MENU_INDEX]: '订单菜单权限',
  [PERMISSION_CODES.MENU_DISH_MANAGEMENT]: '菜品管理菜单权限',
  [PERMISSION_CODES.MENU_REVIEW]: '评价菜单权限',
  [PERMISSION_CODES.MENU_STORE_DETAIL]: '门店菜单权限',
  [PERMISSION_CODES.ORDER_TAB]: '订单标签权限',
  [PERMISSION_CODES.AFTER_SALE_TAB]: '售后标签权限',
  [PERMISSION_CODES.DAMAGE_APPEAL_TAB]: '申诉标签权限',
  [PERMISSION_CODES.ORDER_ACCEPT_BTN]: '订单接单按钮权限',
  [PERMISSION_CODES.ORDER_REFUND_BTN]: '订单退款按钮权限',
  [PERMISSION_CODES.ORDER_AFTER_SALE_ACTIONS_BTN]: '售后操作按钮权限',
  [PERMISSION_CODES.REVIEW_REPLY_BTN]: '评价回复按钮权限',
  [PERMISSION_CODES.STORE_SETTING_BTN]: '门店设置按钮权限',
  [PERMISSION_CODES.SETTING_MESSAGE_RING]: '消息铃声设置权限',
  [PERMISSION_CODES.SETTING_BUSINESS_STATUS]: '营业状态设置权限',
  [PERMISSION_CODES.SHOP_STATISTICS]: '店铺统计权限',
} as const

/**
 * 获取权限描述
 */
export function getPermissionDescription(code: string): string {
  return PERMISSION_DESCRIPTIONS[code as PermissionCode] || `未知权限: ${code}`
}

/**
 * 检查权限码是否有效
 */
export function isValidPermissionCode(code: string): code is PermissionCode {
  return Object.values(PERMISSION_CODES).includes(code as PermissionCode)
}

/**
 * 获取权限组包含的所有权限码
 */
export function getPermissionsByGroup(group: keyof typeof PERMISSION_GROUPS): readonly string[] {
  return PERMISSION_GROUPS[group]
}
