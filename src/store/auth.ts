import { defineStore } from 'pinia'
import { getAccess, type PermissionItem } from '@/service/user'
import { useUserStore } from '@/store/user'

interface AuthState {
  /** 权限码列表 */
  permissionCodes: string[]
  /** 权限数据是否已加载 */
  isPermissionLoaded: boolean
  /** 原始权限数据 */
  rawPermissions: PermissionItem[]
}

// 权限请求锁，防止并发
let _permissionLoading = false

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    permissionCodes: [],
    isPermissionLoaded: false,
    rawPermissions: [],
  }),
  getters: {
    /**
     * 检查用户是否拥有指定权限码。
     * @param state Pinia state
     * @returns (code: string | string[]) => boolean
     */
    hasPermission(state) {
      return (code: string | string[]): boolean => {
        if (!state.isPermissionLoaded) {
          return false // 权限加载完成前，默认无权限
        }

        const codes = Array.isArray(code) ? code : [code]
        if (codes.length === 0) {
          return true // 未要求任何权限，则默认有权限
        }

        // 检查是否拥有 codes 数组中的所有权限
        return codes.every((c) => state.permissionCodes.includes(c))
      }
    },

    /**
     * 检查用户是否拥有指定权限码中的任意一个。
     * @param state Pinia state
     * @returns (codes: string[]) => boolean
     */
    hasAnyPermission(state) {
      return (codes: string[]): boolean => {
        if (!state.isPermissionLoaded) {
          return false // 权限加载完成前，默认无权限
        }

        if (codes.length === 0) {
          return true // 未要求任何权限，则默认有权限
        }

        // 检查是否拥有 codes 数组中的任意一个权限
        return codes.some((c) => state.permissionCodes.includes(c))
      }
    },

    /**
     * 获取按钮权限数据
     * @param state Pinia state
     * @returns PermissionItem[]
     */
    buttonPermissions(state) {
      return state.rawPermissions.filter((item) => item.resourceType === 2 && item.buttonDto)
    },

    /**
     * 获取菜单权限数据
     * @param state Pinia state
     * @returns PermissionItem[]
     */
    menuPermissions(state) {
      return state.rawPermissions.filter((item) => item.resourceType === 1 && item.menuDto)
    },

    /**
     * 根据父级权限码获取子权限
     * @param state Pinia state
     * @returns (parentCode: string) => PermissionItem[]
     */
    getChildPermissions(state) {
      return (parentCode: string): PermissionItem[] => {
        return state.rawPermissions.filter((item) => item.buttonDto?.parentCode === parentCode)
      }
    },
  },
  actions: {
    /**
     * 从后端获取用户权限码并存储。
     */
    async fetchPermissions() {
      if (this.isPermissionLoaded || _permissionLoading) return
      _permissionLoading = true
      try {
        // 获取用户Store中的phone
        const userStore = useUserStore()
        const phone = userStore.userInfo?.phone

        // 调用权限接口，传入phone参数
        const response = await getAccess(phone)

        // 获取权限数据数组（从IResData.data中获取）
        const permissionData = response.data.userAccessVo.accessSet

        // 存储原始权限数据
        this.rawPermissions = permissionData

        // 从权限数据数组中提取 resourceCode 作为权限码
        this.permissionCodes = permissionData.map((item) => item.resourceCode).filter(Boolean)

        console.log('权限数据:', permissionData)
        console.log('提取的权限码:', this.permissionCodes)
        console.log('按钮权限:', this.buttonPermissions)
        console.log('菜单权限:', this.menuPermissions)
      } catch (error) {
        console.error('获取用户权限失败:', error)
        this.permissionCodes = []
        this.rawPermissions = []
      } finally {
        this.isPermissionLoaded = true
        _permissionLoading = false
      }
    },

    /**
     * 清理权限信息（退出登录时调用）。
     */
    clearPermissions() {
      this.permissionCodes = []
      this.isPermissionLoaded = false
      this.rawPermissions = []
    },

    /**
     * 重新加载权限数据
     */
    async reloadPermissions() {
      this.isPermissionLoaded = false
      await this.fetchPermissions()
    },

    /**
     * 检查特定权限是否存在（同步方法，用于模板中）
     * @param code 权限码
     * @returns boolean
     */
    checkPermission(code: string): boolean {
      return this.hasPermission(code)
    },

    /**
     * 检查多个权限是否都存在（同步方法，用于模板中）
     * @param codes 权限码数组
     * @returns boolean
     */
    checkAllPermissions(codes: string[]): boolean {
      return this.hasPermission(codes)
    },

    /**
     * 检查多个权限是否存在任意一个（同步方法，用于模板中）
     * @param codes 权限码数组
     * @returns boolean
     */
    checkAnyPermission(codes: string[]): boolean {
      return this.hasAnyPermission(codes)
    },
  },
  // 开启数据持久化，只持久化关键权限数据
  persist: {
    paths: ['permissionCodes', 'isPermissionLoaded', 'rawPermissions'],
  },
})
