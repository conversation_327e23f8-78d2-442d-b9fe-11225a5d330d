import { defineStore } from 'pinia'
import type { ShopItem } from '@/service/shop/types'

/**
 * 门店状态管理
 */
export const useShopStore = defineStore('shop', {
  state: () => ({
    // 当前选中的门店信息
    currentShop: null as ShopItem | null,
  }),

  getters: {
    // 获取当前选中门店ID
    currentShopId: (state) => state.currentShop?.id || '',
    // 获取当前选中门店code
    currentShopCode: (state) => state.currentShop?.code || '',

    // 获取当前选中门店名称
    currentShopName: (state) => state.currentShop?.name || '未选择门店',

    // 判断是否已选择门店
    hasSelectedShop: (state) => !!state.currentShop,
  },

  actions: {
    /**
     * 设置当前选中的门店
     * @param shop 门店信息
     */
    setCurrentShop(shop: ShopItem) {
      this.currentShop = shop
    },
  },

  // 开启数据持久化
  persist: {
    // 指定需要持久化的数据
    paths: ['currentShop'],
  },
})
