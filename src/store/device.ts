import { defineStore } from 'pinia'
import { ref } from 'vue'
interface DeviceInfo {
  registerId: string
}
const initialState: DeviceInfo = { registerId: '' }
export const useDeviceStore = defineStore(
  'device',
  () => {
    const deviceInfo = ref<DeviceInfo>({ ...initialState })
    const setRegisterId = (id: string) => {
      deviceInfo.value.registerId = id
    }
    const resetDeviceInfo = () => {
      deviceInfo.value = { ...initialState }
    }
    return { deviceInfo, setRegisterId, resetDeviceInfo }
  },
  { persist: true },
)
