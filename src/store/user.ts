import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 确保包含token字段
interface IUserInfo {
  nickname: string
  avatar: string
  tenantIds: string
  userIds: string
  token?: string
  phone?: string
}

const initState: IUserInfo = {
  nickname: '',
  avatar: '',
  tenantIds: '',
  userIds: '',
  token: '',
  phone: '',
}

export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<IUserInfo>({ ...initState })

    const setUserInfo = (val: IUserInfo) => {
      userInfo.value = val
    }

    const clearUserInfo = () => {
      userInfo.value = { ...initState }
    }
    // 一般没有reset需求，不需要的可以删除
    const reset = () => {
      userInfo.value = { ...initState }
    }
    const isLogined = computed(() => !!userInfo.value.token)
    console.log('isLogined', isLogined.value)

    // 是否重新登录过
    const isAgainLogin = ref(false)

    const setIsAgainLogin = (value: boolean) => {
      isAgainLogin.value = value
    }

    return {
      userInfo,
      setUserInfo,
      clearUserInfo,
      isLogined,
      reset,
      isAgainLogin,
      setIsAgainLogin,
    }
  },
  {
    persist: true,
  },
)
