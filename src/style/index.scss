@import './iconfont.css';

.test {
  // 可以通过 @apply 多个样式封装整体样式
  @apply mt-4 ml-4;

  padding-top: 4px;
  color: red;
}

:root,
page {
  // 修改按主题色
  --wot-color-theme: #f33429;

  // 修改按钮背景色
  --wot-button-primary-bg-color: #f33429;
  // 修改checkbox背景色
  --wot-checkbox-bg-color: #f33429;
}

// 修复滚动条问题
page {
  height: 100%;
  overflow: hidden;
}

// 修复 tabbar 布局问题
.wd-tabbar {
  box-sizing: border-box !important;
}

.wd-tabbar__placeholder {
  box-sizing: border-box !important;
  height: auto !important;
}

// 修复页面布局问题
.layout-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

// 内容区域自适应高度
.content-container {
  flex: 1;
  overflow-y: auto;
}
