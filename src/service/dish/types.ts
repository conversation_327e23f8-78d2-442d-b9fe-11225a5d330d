/**
 * 商品数据相关类型定义
 */

// 商品状态类型
export type DishStatus = '已上架' | '已下架' | '已售罄' | string

// 商品分类类型
export interface DishCategory {
  id: string | number
  name: string
  sort?: number
  dishCount?: number
  originalData?: {
    itemList?: string[]
  }
}

// 商品标签类型
export interface DishTag {
  id: string | number
  name: string
}

// 商品数据类型
export interface DishItem {
  id: string | number
  name: string
  image: string
  price: number
  originalPrice?: number
  tags: string[] | DishTag[]
  status: DishStatus
  categoryId: string | number
  categoryName?: string
  description?: string
  stock?: number
  sales?: number
  attributes?: Record<string, any>[]
}

// 商品列表查询参数
export interface QueryDishListParams {
  shopId: string | number
  tenantId: string | number
  channelCode: string
  shopCode?: string
  categoryId?: string | number
  status?: DishStatus | number // 支持数字状态
  keyword?: string
  pageNum?: number
  pageSize?: number
}

// 商品列表响应数据
export interface DishListResponse {
  // 旧的字段，保留以兼容
  list?: DishItem[]
  total?: number
  pageNum?: number
  pageSize?: number

  // 新的字段
  categoryList?: CategoryListItem[]
  totalOffShelfCount?: number
  totalOnShelfCount?: number
  totalSoldOutCount?: number
}

// 分类列表项
export interface CategoryListItem {
  categoryName: string
  itemList: CategoryDishItem[]
  sort?: number | string
}

// 分类中的商品项
export interface CategoryDishItem {
  imageUrl: string
  itemCode: string
  itemName: string
  minSalePrice: number
  soldOut: number
  status: number
}

// 商品分类列表响应数据
export interface DishCategoryResponse {
  list: DishCategory[]
  total: number
}
