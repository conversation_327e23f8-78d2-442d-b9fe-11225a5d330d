import { http } from '@/utils/http'
import type { QueryDishListParams, DishListResponse, DishCategoryResponse } from './types'

/**
 * 获取商品列表
 * @param params 查询参数
 * @returns 商品列表数据
 */
export function fetchDishList(params: QueryDishListParams) {
  return http.post<DishListResponse>('/v1/item/queryOnSaleItem', params)
}

/**
 * 获取商品分类列表
 * @param params 查询参数 {shopId, tenantId, channelCode}
 * @returns 商品分类列表数据
 */
export function fetchDishCategories(
  params: Pick<QueryDishListParams, 'shopId' | 'tenantId' | 'channelCode'>,
) {
  return http.post<DishCategoryResponse>('/v1/dish/queryDishCategories', params)
}

/**
 * 获取商品详情
 * @param params 查询参数 {shopId, tenantId, channelCode, dishId}
 * @returns 商品详情数据
 */
export function fetchDishDetail(
  params: Pick<QueryDishListParams, 'shopId' | 'tenantId' | 'channelCode'> & {
    dishId: string | number
  },
) {
  return http.post<any>('/v1/dish/queryDishDetail', params)
}

/**
 * 更新商品状态
 * @param params 请求参数 {shopId, tenantId, channelCode, dishId, status}
 * @returns 更新结果
 */
export function updateDishStatus(
  params: Pick<QueryDishListParams, 'shopId' | 'tenantId' | 'channelCode'> & {
    dishId: string | number
    status: string
  },
) {
  return http.post<any>('/v1/dish/updateDishStatus', params)
}
