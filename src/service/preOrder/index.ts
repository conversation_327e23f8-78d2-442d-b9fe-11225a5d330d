import { http } from '@/utils/http'

export interface QueryOrderParams {
  shopIds: Array<string>
  channelCodes: []
  bizTypes: []
  orderStatus: string
  startSendTime: string
  endSendTime: string
  isBook: number
  pageNum: number
  pageSize: number

  startPlaceTime: string
  endPlaceTime: string
  orderByPlaceTimeDesc: boolean
}

/** 订单分页查询 */
export function fetchOrderList(params: QueryOrderParams) {
  return http.post<any>('/v1/order/queryPageChannelOrder', params)
}

/** 上报出餐 */
export function reportMeals(params: { thirdOrderCode: string; orderChannel: string }) {
  return http.post<any>('/v1/order/reportMeals', params)
}

/** 确认接单 */
export function confirmOrder(params: {
  thirdOrderCode: string
  channelCode: string
  shopId: string
  tenantId: string
}) {
  return http.post<any>('/v1/order/confirmOrder', params)
}

/** 查询订单状态时间（订单跟踪） */
export function fetchOrderStatusTime(params: { thirdOrderCode: string }) {
  return http.post<any>('/v1/order/queryOrderStatusTime', params)
}

/** 售后审核 */
export function auditAfterSale(params: {
  tradeNo: string
  refundStatus: number
  agreeReason: string
}) {
  return http.post<any>('/v1/refundOrder/audit', {
    ...params,
    handlerName: 'baseRefundAuditOperationHandler',
  })
}

/** 查询可退商品 */
export function getPartRefundProducts(params: { thirdOrderCode: string }) {
  return http.get<any>(
    `/v1/refundOrder/getPartRefundProducts?thirdOrderCode=${params.thirdOrderCode}`,
  )
}

/** 查询退款原因 */
export function queryRefundReasons() {
  return http.get<any>('/v1/dict/queryRefundReason')
}

/** 提交售后单 */
export function createRefund(params: {
  tradeNo: string
  refundItems: any[]
  refundBizType: string | number
  refundReason: string
  refundReasonCode: string
  refundPrice: string | number
  instanceId?: string
  tenantId?: string
}) {
  return http.post<any>('/v1/refundOrder/createRefund', params)
}
