import { http } from '@/utils/http'

interface Params {
  [key: string]: any
}

export interface AppealParams {
  extlOrderSerial?: string
  orderCode?: string
  saleChannel?: string
  appealType?: string
  appealDescribe?: string
  appealImages?: string
  appealTime?: string
  tenantId?: string
  extlRefundSerial?: string
  appealAmount?: number
  shopId?: string
  refundTime?: string
}
/** 获取餐损申诉类型 */
export function queryDamageAppealTypeList(channelCode: string) {
  const payload: Params = { channelCode }
  return http.get<any>('/v1/dict/queryDamageAppealType', payload)
}

export function submitAppleal(params: AppealParams) {
  return http.post<any>('/v1/order/appealOrder', params)
}
