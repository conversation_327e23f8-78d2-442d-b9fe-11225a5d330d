import { http } from '@/utils/http'
import { getApiDomainPre } from '../../utils/index'

// 密码登录
export const passwordLoginAPI = (userName: string, password: string) => {
  return http.post<any>('/v1/users/login/ssoLogin', {
    loginType: 1,
    userName,
    password,
    type: 'user',
  })
}

// 根据手机号查租户
export const queryTenantsByPhoneAPI = (phone: string) => {
  return http.get<any>(`/v1/users/login//ssouser/phone/query`, { phone })
}

// 发送验证码
export const sendVerifyCodeAPI = (phone: string, instanceId: string, tenantId: string) => {
  return http.post<any>('/v1/users/login/verify/send', {
    phone,
    instanceId,
    tenantId,
    type: 1,
  })
}

// 验证码登录
export const codeLoginAPI = (
  phone: string,
  checkCode: string,
  checkCodeUniqueId: string,
  tenantId: string,
) => {
  return http.post<any>('/v1/users/login/ssoLogin', {
    checkCode,
    checkCodeUniqueId,
    loginType: '3',
    phone,
    tenantId,
    type: 'tenants',
  })
}

// 根据SSO用户ID获取用户信息
export const getUserInfoByIdAPI = (ssoUserId: string) => {
  return http.get<any>(`/v1/users/login/getUserInfoById/${ssoUserId}`)
}

// 修改登录密码
export const updatePasswordAPI = (
  loginName: string,
  password: string,
  newPassword: string,
  confirmPassword: string,
  type: 4,
) => {
  const header = { HostName: getApiDomainPre() }
  return http.post<any>(
    '/v1/users/login/modifyPasswd',
    {
      password,
      newPassword,
      confirmPassword,
      loginName,
      type,
    },
    {},
    header,
  )
}

// 获取登录配置
export const getLoginConfigAPI = (tenantId: string) => {
  return http.get<any>(`/v1/users/login/findLoginSettings/${tenantId}`)
}
