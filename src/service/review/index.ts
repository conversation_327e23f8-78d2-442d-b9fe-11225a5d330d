import { http } from '@/utils/http'

export interface QueryReviewParams {
  startTime: string
  endTime: string
  channelCode: string
  shopId: string
  tenantId: string
  pageNum: number
  pageSize: number
  [key: string]: any
}

export interface ReplyReviewParams {
  channelCode: string
  evaluateId: string
  replyContent: string
  shopId: string
  tenantId: string
}

/** 评价分页查询 */
export function fetchReviewList(params: QueryReviewParams) {
  return http.post<any>('/v1/evaluate/queryEvaluatePage', params)
}

/** 回复评价 */
export function replyReview(params: ReplyReviewParams) {
  return http.post<any>('/v1/evaluate/replyEvaluation', params)
}
