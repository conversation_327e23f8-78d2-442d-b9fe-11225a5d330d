import { http } from '@/utils/http'

interface SaveDevicePayload {
  deviceId: string
  phone?: string
  loginStatus?: number // 1-在线 0-离线
  loginShopId?: string // 门店ID
}

interface SaveDeviceResponse {
  [key: string]: any
}

/**
 * 权限数据中的按钮信息
 */
interface ButtonDto {
  action: string | null
  code: string
  createPerson: string
  createTime: string
  customizeText: string | null
  customized: number
  extFields: Record<string, any>
  extension: string
  icon: string
  id: string
  instanceId: string
  menuId: string
  name: string
  parentCode: string
  routePath: string
  sortNo: number | null
  status: number
  tenantId: number
  updatePerson: string
  updateTime: string
}

/**
 * 权限项数据结构
 */
export interface PermissionItem {
  buttonDto: ButtonDto | null
  code: string | null
  createPerson: string
  createTime: string
  extFields: Record<string, any>
  extension: string | null
  id: string
  instanceId: string
  menuDto: any | null
  permissions: number
  resourceCode: string
  resourceId: string
  resourceType: number
  tenantId: string
  updatePerson: string
  updateTime: string
}

/**
 * 保存设备信息
 * @param deviceId 设备ID
 * @param phone 手机号（可选）
 * @param loginStatus 登录状态：1-在线 0-离线（可选）
 * @param loginShopId 门店ID（可选）
 */
export function saveDeviceAPI(
  deviceId: string,
  phone?: string,
  loginStatus?: number,
  loginShopId?: string,
) {
  const payload: SaveDevicePayload = { deviceId }

  // 只添加有值的参数
  if (phone !== undefined) payload.phone = phone
  if (loginStatus !== undefined) payload.loginStatus = loginStatus
  if (loginShopId !== undefined) payload.loginShopId = loginShopId

  return http.post<SaveDeviceResponse>('/v1/user/saveDevice', payload)
}

/**
 * @description 获取当前用户的权限数据列表
 * @param phone 用户手机号（可选）
 * @returns Promise<IResData<PermissionItem[]>>
 */
export function getAccess(phone?: string) {
  const params: Record<string, any> = {}
  if (phone) {
    params.phone = phone
  }

  return http.get<PermissionItem[]>('/v1/user/access', params)
}
