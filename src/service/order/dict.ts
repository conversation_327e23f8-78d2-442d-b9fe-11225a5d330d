import { http } from '@/utils/http'

/** 获取订单类型枚举 */
export function fetchOrderTypeEnum(params?: Record<string, any>) {
  return http.get<any>('/v1/dict/queryBizType', params)
}

/** 获取渠道类型枚举 */
export function fetchChannelEnum(params?: Record<string, any>) {
  return http.get<any>('/v1/dict/queryChannel', params)
}

/** 获取订单状态枚举 */
export function fetchOrderStatusEnum(params?: Record<string, any>) {
  return http.get<any>('/v1/dict/queryOrderStatus', params)
}

/** 获取售后状态枚举 */
export function fetchRefundOrderStatusEnum(params?: Record<string, any>) {
  return http.get<any>('/v1/dict/queryRefundOrderStatus', params)
}

/** 根据渠道和门店信息获取门店详情 */
export function fetchStoreDetailByChannel(params?: Record<string, any>) {
  return http<any>({
    url: '/v1/shop/queryShopDetail',
    method: 'POST',
    data: params,
    hideErrorToast: true,
  })
}
