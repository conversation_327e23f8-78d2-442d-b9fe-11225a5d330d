import { http } from '@/utils/http'

export interface QueryOrderParams {
  shopIds: string[]
  channelCodes?: string[]
  bizType?: string
  bizTypes?: string[]
  orderStatus?: string
  startPlaceTime: string
  endPlaceTime: string
  orderByPlaceTimeDesc?: boolean
  pageNum?: number
  pageSize?: number
  extlOrderSerial?: string
  appealStatus?: string
  startAppealTime?: string
  endAppealTime?: string
  [key: string]: any
}

/** 订单分页查询 */
export function fetchOrderList(params: QueryOrderParams) {
  return http.post<any>('/v1/order/queryPageChannelOrder', params)
}

/** 根据订单号查询单个订单 */
export function fetchOrderBySerial(extlOrderSerial: string) {
  return http.get<any>(`/v1/order/detailChannelOrder/${extlOrderSerial}`)
}

/** 根据第三方订单号查询单个订单 */
export function fetchOrderByThirdOrderCode(params: {
  thirdOrderCode: string
  shopIds?: string[]
  shopCodes?: string[]
}) {
  return http.post<any>('/v1/order/queryPageChannelOrder', {
    extlOrderSerial: params.thirdOrderCode,
    shopIds: params.shopIds || [],
    shopCodes: params.shopCodes || undefined,
    pageNum: 1,
    pageSize: 1,
  })
}

/** 上报出餐 */
export function reportMeals(params: { thirdOrderCode: string; orderChannel: string }) {
  return http.post<any>('/v1/order/reportMeals', params)
}

/** 确认接单 */
export function confirmOrder(params: {
  thirdOrderCode: string
  channelCode: string
  shopId: string
  tenantId: string
}) {
  return http.post<any>('/v1/order/confirmOrder', params)
}

/** 查询订单状态时间（订单跟踪） */
export function fetchOrderStatusTime(params: { thirdOrderCode: string }) {
  return http.post<any>('/v1/order/queryOrderStatusTime', params)
}

/** 售后审核 */
export function auditAfterSale(params: {
  tradeNo: string
  refundStatus: number
  agreeReason: string
  instanceId?: string
  tenantId?: string
}) {
  return http.post<any>('/v1/refundOrder/audit', {
    ...params,
    handlerName: 'baseRefundAuditOperationHandler',
  })
}

/** 查询可退商品 */
export function getPartRefundProducts(params: { thirdOrderCode: string }) {
  return http.get<any>(
    `/v1/refundOrder/getPartRefundProducts?thirdOrderCode=${params.thirdOrderCode}`,
  )
}

/** 查询退款原因 */
export function queryRefundReasons() {
  return http.get<any>('/v1/dict/queryRefundReason')
}

/** 提交售后单 */
export function createRefund(params: {
  tradeNo: string
  refundItems: any[]
  refundBizType: string | number
  refundReason: string
  refundReasonCode: string
  refundPrice: string | number
  instanceId?: string
  tenantId?: string
}) {
  return http.post<any>('/v1/refundOrder/createRefund', params)
}

// 餐损订单分页查询
export function fetchDamageOrderList(params: QueryOrderParams) {
  return http.post<any>('/v1/order/queryPageDamageOrder', params)
}
