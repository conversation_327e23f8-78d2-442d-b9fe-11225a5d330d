/* eslint-disable */
// @ts-ignore
import request from '@/utils/request'
import { CustomRequestOptions } from '@/interceptors/request'
import { http } from '@/utils/http'

import * as API from './types'

/**
 * 门店管理员查询门店列表
 * GET /v1/shop/manager/queryManagerShopList
 */
export async function queryManagerShopList({
  params,
  options,
}: {
  params?: API.QueryManagerShopListParams
  options?: CustomRequestOptions
}) {
  return request<API.QueryManagerShopListResponse>('/v1/shop/manager/queryManagerShopList', {
    method: 'GET',
    params: (params as Record<string, unknown>) || {},
    ...(options || {}),
  })
}

/**
 * 门店管理员分页查询门店列表（新接口）
 * POST /v1/shop/manager/queryManagerShopPage
 */
export async function queryManagerShopPage(params: API.QueryManagerShopPageParams) {
  return http.post<API.QueryManagerShopPageResponse>(
    '/v1/shop/manager/queryManagerShopPage',
    params,
  )
}
