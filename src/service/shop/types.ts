/* eslint-disable */
// @ts-ignore

/**
 * 后端返回的原始门店数据结构
 */
export interface RawShopItem {
  /** 门店id */
  id: string
  /** 门店代码 */
  code: string
  /** 门店名称 */
  name: string | null
  /** 地址 */
  address: string
  /** 地址级别 */
  addressLevel: string
  /** 地址级别名称 */
  addressLevelName: string | null
  /** 别名 */
  alias: string | null
  /** 城市代码 */
  cityCode: string
  /** 城市名称 */
  cityName: string | null
  /** 区县代码 */
  countyCode: string
  /** 区县名称 */
  countyName: string | null
  /** 公司ID */
  companyId: string | null
  /** 公司名称 */
  companyName: string | null
  /** 创建人 */
  createPerson: string
  /** 创建时间 */
  createTime: string
  /** 扩展字段 */
  extFields: {
    /** 组织名称 */
    orgName: string
    /** POS门店代码 */
    posShopCode: string
    /** 云犀门店代码 */
    yunxiShopCode: string
    /** 联系电话 */
    contactPhone: string
    /** 其他扩展字段 */
    [key: string]: any
  }
}

/**
 * 前端使用的门店数据类型定义
 */
export interface ShopItem {
  /** 门店ID（使用code作为唯一标识） */
  id: string
  /** 门店代码 */
  code: string
  /** 门店名称（优先级：name > alias > orgName+address组合） */
  name: string
  /** 原始数据（保留完整信息以备后用） */
  raw?: RawShopItem
}

/**
 * 门店管理员查询门店列表API响应类型
 */
export interface QueryManagerShopListResponse {
  /** 响应数据数组 */
  data: RawShopItem[]
  /** 异常应用信息 */
  exceptCauseApp: string | null
  /** 异常IP信息 */
  exceptCauseIp: string | null
  /** 异常类 */
  exceptClass: string | null
  /** 扩展字段 */
  extFields: Record<string, any>
  /** 结果代码 */
  resultCode: string
  /** 结果消息 */
  resultMsg: string
}

/**
 * 门店管理员查询门店列表API请求参数类型（如果需要）
 */
export interface QueryManagerShopListParams {
  /** 页码 */
  pageNum?: number
  /** 每页数量 */
  pageSize?: number
  /** 搜索关键字 */
  keyword?: string
}

/**
 * 门店管理员分页查询门店列表API请求参数类型
 */
export interface QueryManagerShopPageParams {
  /** 页码 */
  pageNum: number
  /** 每页数量 */
  pageSize: number
  /** 搜索关键字（门店名称） */
  keyword?: string
}

/**
 * 门店管理员分页查询门店列表API响应类型
 */
export interface QueryManagerShopPageResponse {
  /** 响应数据 */
  data: {
    /** 门店数据数组 */
    list: RawShopItem[]
    /** 总数 */
    total: number
    /** 当前页码 */
    pageNum: number
    /** 每页数量 */
    pageSize: number
    /** 是否有下一页 */
    hasNextPage: boolean
  }
  /** 异常应用信息 */
  exceptCauseApp: string | null
  /** 异常IP信息 */
  exceptCauseIp: string | null
  /** 异常类 */
  exceptClass: string | null
  /** 扩展字段 */
  extFields: Record<string, any>
  /** 结果代码 */
  resultCode: string
  /** 结果消息 */
  resultMsg: string
}
