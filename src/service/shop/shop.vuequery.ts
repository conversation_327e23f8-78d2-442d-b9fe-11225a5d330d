/* eslint-disable */
// @ts-ignore
import { queryOptions, useQuery } from '@tanstack/vue-query'
import type { DefaultError } from '@tanstack/vue-query'
import { CustomRequestOptions } from '@/interceptors/request'

import * as apis from './shop'
import * as API from './types'

/**
 * 门店管理员查询门店列表 - vue-query配置
 * GET /v1/shop/manager/queryManagerShopList
 */
export function queryManagerShopListQueryOptions(options?: {
  params?: API.QueryManagerShopListParams
  options?: CustomRequestOptions
}) {
  return queryOptions({
    queryKey: ['queryManagerShopList', options?.params],
    queryFn: async () => {
      return apis.queryManagerShopList({
        params: options?.params,
        options: options?.options,
      })
    },
  })
}

/**
 * 使用门店管理员查询门店列表的useQuery hook
 */
export function useQueryManagerShopList(options?: {
  params?: API.QueryManagerShopListParams
  options?: CustomRequestOptions
  enabled?: boolean
}) {
  return useQuery({
    ...queryManagerShopListQueryOptions({
      params: options?.params,
      options: options?.options,
    }),
    enabled: options?.enabled !== false,
  })
}
