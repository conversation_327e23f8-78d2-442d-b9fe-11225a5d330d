import { ref, watch } from 'vue'
import * as mqtt from 'mqtt/dist/mqtt'

export function useMqtt() {
  const client = ref<mqtt.MqttClient | null>(null)
  const isConnected = ref(false)
  const isConnecting = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5
  let messageCallback: ((topic: string, payload: any) => void) | null = null
  let reconnectTimer: number | null = null

  const connect = (url: string, options: any): Promise<void> => {
    return new Promise((resolve, reject) => {
      try {
        if (client.value?.connected) {
          console.log('MQTT 客户端已连接，无需重复操作')
          resolve()
          return
        }

        if (isConnecting.value) {
          console.log('MQTT 正在连接中，请稍候...')
          reject(new Error('MQTT 正在连接中'))
          return
        }

        isConnecting.value = true
        console.log('正在连接 MQTT Broker...', { url, clientId: options.clientId })

        // 创建MQTT客户端连接
        client.value = mqtt.connect(url, {
          clientId:
            options.clientId || `uni_mqtt_${Math.random().toString(36).substr(2, 9)}_${Date.now()}`,
          username: options.username,
          password: options.password,
          keepalive: options.keepalive || 60,
          connectTimeout: options.connectTimeout || 10000,
          clean: false,
          cleanSession: false,
          reconnectPeriod: 5000,
        })

        // 监听连接成功
        client.value.on('connect', () => {
          console.log('MQTT 连接成功！')
          isConnected.value = true
          isConnecting.value = false
          reconnectAttempts.value = 0

          // 清除重连定时器
          if (reconnectTimer) {
            clearTimeout(reconnectTimer)
            reconnectTimer = null
          }

          resolve()
        })

        // 监听重连
        client.value.on('reconnect', () => {
          console.log('MQTT 正在重连...')
          isConnected.value = false
          isConnecting.value = true
          reconnectAttempts.value++

          // 如果重连次数超过限制，停止重连
          if (reconnectAttempts.value >= maxReconnectAttempts) {
            console.error(`MQTT 重连失败，已达到最大重连次数 ${maxReconnectAttempts}`)
            if (client.value) {
              client.value.end()
            }
            isConnecting.value = false
          }
        })

        // 监听连接错误
        client.value.on('error', (err: Error) => {
          console.error('MQTT 连接错误:', err)
          isConnected.value = false
          isConnecting.value = false
          reject(err)
        })

        // 监听连接关闭
        client.value.on('close', () => {
          console.log('MQTT 连接已关闭')
          isConnected.value = false
          isConnecting.value = false
        })

        // 监听消息接收
        client.value.on('message', (topic: string, payload: any) => {
          console.log(`收到消息, 主题: ${topic}`)

          if (messageCallback) {
            try {
              // 处理不同类型的payload
              let message: string
              if (typeof payload === 'string') {
                message = payload
              } else if (payload && typeof payload.toString === 'function') {
                message = payload.toString()
              } else {
                message = String(payload)
              }

              const parsedPayload = JSON.parse(message)
              messageCallback(topic, parsedPayload)
            } catch (error) {
              console.error('解析 MQTT 消息失败 (非JSON格式):', payload)
              messageCallback(topic, payload)
            }
          }
        })

        // 设置连接超时
        setTimeout(() => {
          if (!isConnected.value && isConnecting.value) {
            console.error('MQTT 连接超时')
            isConnecting.value = false
            reject(new Error('MQTT 连接超时'))
          }
        }, options.connectTimeout || 10000)
      } catch (error) {
        console.error('MQTT 连接初始化失败:', error)
        isConnected.value = false
        isConnecting.value = false
        reject(error)
      }
    })
  }

  const subscribe = (topic: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      console.log('subscribe', topic)
      console.log('client.value', client.value)
      console.log('client.value?.connected', client.value?.connected)

      if (client.value?.connected) {
        client.value.subscribe(topic, (err: any) => {
          if (!err) {
            console.log(`成功订阅主题: ${topic}`)
            resolve()
          } else {
            console.error(`订阅主题失败: ${topic}`, err)
            reject(err)
          }
        })
      } else {
        const error = new Error(`MQTT 未连接，无法订阅主题: ${topic}`)
        console.warn(error.message)
        reject(error)
      }
    })
  }

  const unsubscribe = (topic: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (client.value?.connected) {
        client.value.unsubscribe(topic, (err: any) => {
          if (!err) {
            console.log(`成功取消订阅主题: ${topic}`)
            resolve()
          } else {
            console.error(`取消订阅失败: ${topic}`, err)
            reject(err)
          }
        })
      } else {
        const error = new Error(`MQTT 未连接，无法取消订阅主题: ${topic}`)
        console.warn(error.message)
        reject(error)
      }
    })
  }

  const onMessage = (callback: (topic: string, payload: any) => void) => {
    messageCallback = callback
  }

  const disconnect = () => {
    if (client.value) {
      try {
        // 清除重连定时器
        if (reconnectTimer) {
          clearTimeout(reconnectTimer)
          reconnectTimer = null
        }

        client.value.end()
        isConnected.value = false
        isConnecting.value = false
        reconnectAttempts.value = 0
        client.value = null
        console.log('MQTT 连接已主动断开')
      } catch (error) {
        console.error('MQTT 断开连接时发生错误:', error)
        isConnected.value = false
        isConnecting.value = false
        reconnectAttempts.value = 0
        client.value = null
      }
    }
  }

  // 手动重连方法
  const reconnect = async (url: string, options: any): Promise<void> => {
    if (isConnecting.value) {
      console.log('MQTT 正在连接中，无法重连')
      return
    }

    console.log('手动重连 MQTT...')
    disconnect()

    // 等待一秒后重连
    await new Promise((resolve) => setTimeout(resolve, 1000))

    return connect(url, options)
  }

  // 获取连接状态信息
  const getConnectionInfo = () => {
    return {
      isConnected: isConnected.value,
      isConnecting: isConnecting.value,
      reconnectAttempts: reconnectAttempts.value,
      maxReconnectAttempts,
    }
  }

  return {
    isConnected,
    isConnecting,
    reconnectAttempts,
    connect,
    disconnect,
    reconnect,
    subscribe,
    unsubscribe,
    onMessage,
    getConnectionInfo,
  }
}
