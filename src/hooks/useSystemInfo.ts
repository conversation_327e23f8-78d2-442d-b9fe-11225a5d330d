import { ref } from 'vue'

/**
 * 系统信息钩子
 * @returns 系统相关信息
 */
export function useSystemInfo() {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync()

  // 状态栏高度
  const statusBarHeight = ref(systemInfo.statusBarHeight || 20)

  // 导航栏高度
  const navBarHeight = ref(44) // 默认值

  // 屏幕宽高
  const screenWidth = ref(systemInfo.screenWidth || 375)
  const screenHeight = ref(systemInfo.screenHeight || 667)

  // 窗口安全区域
  const safeArea = ref(
    systemInfo.safeArea || {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      width: screenWidth.value,
      height: screenHeight.value,
    },
  )

  return {
    statusBarHeight,
    navBarHeight,
    screenWidth,
    screenHeight,
    safeArea,
    systemInfo,
  }
}
