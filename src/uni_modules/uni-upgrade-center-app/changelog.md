## 0.9.6（2025-04-01）
- 新增 升级中心适配鸿蒙 uni-app x **需要 HBuilderX 4.61+**
## 0.9.5（2025-02-06）
- 新增 完善下载失败时的处理逻辑
## 0.9.4（2024-12-28）
- 修复 腾讯云在使用扩展存储时报错的 Bug
## 0.9.3（2024-12-23）
- 修复 升级中心在大屏上的显示效果
## 0.9.2（2024-11-06）
- 更新 部分 ts 类型
## 0.9.1（2024-11-01）
- 更新 支持 HarmonyOS Next 设备整包更新、wgt 更新。需要 `HBuilderX 4.32+` [详情](https://doc.dcloud.net.cn/uniCloud/upgrade-center.html#uni-upgrade-center-app-harmonyos)
## 0.9.0（2024-10-30）
- **重要更新** 在 uni-app x 项目中弃用之前弹窗方案使用[dialogPage](https://doc.dcloud.net.cn/uni-app-x/api/dialog-page.html)实现，需要 `HBuilderX 4.31+`
## 0.8.5（2024-10-26）
- 优化 去除不必要代码
## 0.8.4（2024-10-26）
- 修复  uni-app x 项目升级到 4.31 alpha 后中间有空隙的Bug
## 0.8.3（2024-07-31）
- 修复 部分类型报错
## 0.8.2（2024-07-15）
- 更新 static 下的静态图片放入 static/app 目录下，防止编译除 app 平台以外的平台时带入
## 0.8.1（2024-04-28）
- 修复 在 HX 4.0.3+ uni-app x 项目运行到 Android 调不起安装的Bug
## 0.8.0（2024-04-15）
- 修复 更新弹窗 data 中新增初始化字段
## 0.7.9（2024-03-15）
- 移除无用代码
- 调整 is_silently 类型为可为 null
## 0.7.8（2024-01-04）
- 新增 移除无用代码
## 0.7.7（2024-01-04）
- 新增 uni-app x 项目中新增 @show 回调
## 0.7.6（2023-12-21）
- 修复 iOS使用升级中心云打包时报错（使用新版的 [uts-progressNotification](https://ext.dcloud.net.cn/plugin?name=uts-progressNotification) 插件，如果之前下载过请删除 `uts-progressNotification\utssdk\app-ios` 文件夹）
## 0.7.5（2023-12-12）
- 新增 通知栏进度条使用 uts-progressNotification 插件
- 新增 依赖 uni-installApk、uts-progressNotification。使用前要安装插件三方依赖
## 0.7.4（2023-11-29）
- 修复 uni-app-x 项目中由上版引发的无法升级的Bug
## 0.7.3（2023-11-27）
- 修复 在 uni-app x 中无更新时报错的Bug
## 0.7.2（2023-11-20）
- 新增 插件根目录 utils 文件夹中新增 check-update-nvue.js 文件（vue2 的 nvue 页面请引用该文件）
## 0.7.1（2023-11-17）
- 修复 运行至浏览器 ts 语法报错
## 0.7.0（2023-11-10）
- 新增 兼容 uni-app x 项目 [详情](https://uniapp.dcloud.net.cn/uniCloud/upgrade-center.html)
## 0.6.5（2023-10-27）
- 修复 安装 wgt 报错 manifest.json 文件不存在的Bug
## 0.6.4（2023-09-01）
chore: 优化代码结构
## 0.6.3（2023-08-30）
- 修复 下载 wgt 时如果后缀名不正确，重命名后安装
## 0.6.2（2022-11-21）
- 处理 cloudfunctions 目录
## 0.6.1（2022-08-17）
- 修复 后台添加应用市场，但都没有启用的情况下报错的Bug （需要 uni-admin 1.9.3+）
## 0.6.0（2022-07-19）
- 新增 支持多应用商店配置（需要 uni-admin 1.9.3+）
## 0.4.1（2022-05-27）
- 修复 上版引出的报错问题
## 0.4.0（2022-05-27）
- 新增 Android 支持跳转手机自带商店，填写升级包地址时请填写跳转商店链接
- 新增 改为云对象调用方式，使用更直观
## 0.3.3（2022-04-14）
- 修复  调用 check-update，当 code 为 0 时没有回调
## 0.3.2（2022-01-12）
- 优化显示逻辑
## 0.3.1（2021-11-24）
- 修复 vue3 上图片不显示的Bug
## 0.3.0（2021-11-18）
- 移除 wgt 安装成功后提示，防止重启过快弹框不消失
## 0.2.2（2021-08-25）
- 兼容vue3.0
## 0.2.1（2021-07-26）
- 修复  使用腾讯云并手动填写地址时，导致下载链接失效的bug
## 0.2.0（2021-07-13）
- 更新文档  关于报错local_storage_key 为空，请不要将页面路径设置为pages.json中第一项
## 0.1.9（2021-06-28）
- 更新文档
- 修复  wgt安装失败时，按钮状态不对
## 0.1.8（2021-06-16）
- 修复  跳转安装时，导致上次下载的apk还没安装就被删掉的bug
## 0.1.7（2021-06-03）
- 修改  移除static中的图片
## 0.1.6（2021-06-03）
- 修改  下载更新按钮使用CSS渐变色
## 0.1.5（2021-04-22）
- 更新check-update函数。现在返回一个Promise，有更新时成功回调，其他情况错误回调
## 0.1.4（2021-04-13）
- 更新文档。明确云函数调用结果
## 0.1.3（2021-04-13）
- 解耦云函数与弹框处理。utils中新增 call-check-version.js，可用于单独检测是否有更新
## 0.1.2（2021-04-07）
- 更新版本对比函数 compare
## 0.1.1（2021-04-07）
- 修复 腾讯云空间下载链接不能下载问题
## 0.1.0（2021-04-07）
- 新增使用uni.showModal提示升级示例
- 修改iOS升级提示方式
## 0.0.7（2021-04-02）
- 修复在iOS上打开弹框报错
## 0.0.6（2021-04-01）
- 兼容旧版本安卓
## 0.0.5（2021-04-01）
- 修复低版本安卓上进度条错位
## 0.0.4（2021-04-01）
- 更新readme
- 修复check-update语法错误
## 0.0.3（2021-04-01）
- 新增前台更新弹框，详见readme
- 更新前台检查更新方法

## 0.0.2（2021-03-29）
- 更新文档
- 移除 dependencies

## 0.0.1（2021-03-25）
- 升级中心前台检查更新
