/**
 * 验证版本号比较修复效果
 */

/**
 * 版本号标准化函数
 */
function normalizeVersion(version: string): string {
  if (!version || typeof version !== 'string') {
    return '0.0.0.0'
  }

  const parts = version.split('.')

  // 确保版本号有4段，不足的用0补齐
  while (parts.length < 4) {
    parts.push('0')
  }

  // 只取前4段，多余的截掉
  const normalizedParts = parts.slice(0, 4).map((part) => {
    const num = parseInt(part) || 0
    return num.toString()
  })

  return normalizedParts.join('.')
}

/**
 * 版本比较函数
 */
function compareVersion(version1: string, version2: string): number {
  if (!version1 || typeof version1 !== 'string') {
    version1 = '0.0.0.0'
  }
  if (!version2 || typeof version2 !== 'string') {
    version2 = '0.0.0.0'
  }

  const v1Parts = version1.split('.').map(Number)
  const v2Parts = version2.split('.').map(Number)
  const maxLength = Math.max(v1Parts.length, v2Parts.length)

  for (let i = 0; i < maxLength; i++) {
    const v1 = v1Parts[i] || 0
    const v2 = v2Parts[i] || 0
    if (v1 > v2) return 1
    if (v1 < v2) return -1
  }
  return 0
}

/**
 * 验证修复效果
 */
export function verifyVersionFix(): void {
  console.group('🔧 版本号比较修复验证')

  // 原问题场景
  const currentVersion = '1.0.0.1007'
  const remoteVersion = '1.0.0.1006'

  console.log('📋 问题描述:')
  console.log(`  当前版本: ${currentVersion}`)
  console.log(`  远程版本: ${remoteVersion}`)
  console.log(`  问题: 错误提示需要更新`)

  console.log('')
  console.log('🔄 修复前的逻辑:')

  // 模拟修复前的错误逻辑（截取前三段）
  const oldCurrentVersion = currentVersion.split('.').slice(0, 3).join('.')
  const oldNeedUpdate = compareVersion(remoteVersion, oldCurrentVersion) > 0

  console.log(`  处理后当前版本: ${oldCurrentVersion} (被截取)`)
  console.log(`  远程版本: ${remoteVersion}`)
  console.log(
    `  比较结果: ${oldNeedUpdate ? '需要更新' : '无需更新'} ${oldNeedUpdate ? '❌' : '✅'}`,
  )

  console.log('')
  console.log('✨ 修复后的逻辑:')

  // 修复后的正确逻辑
  const normalizedCurrent = normalizeVersion(currentVersion)
  const normalizedRemote = normalizeVersion(remoteVersion)
  const newNeedUpdate = compareVersion(normalizedRemote, normalizedCurrent) > 0

  console.log(`  标准化当前版本: ${normalizedCurrent}`)
  console.log(`  标准化远程版本: ${normalizedRemote}`)
  console.log(
    `  比较结果: ${newNeedUpdate ? '需要更新' : '无需更新'} ${newNeedUpdate ? '❌' : '✅'}`,
  )

  console.log('')
  console.log('📊 验证结果:')

  if (oldNeedUpdate && !newNeedUpdate) {
    console.log('✅ 修复成功! 问题已解决')
    console.log('   - 修复前: 错误提示更新')
    console.log('   - 修复后: 正确判断无需更新')
  } else if (!oldNeedUpdate && !newNeedUpdate) {
    console.log('⚠️  问题可能未复现，但逻辑是正确的')
  } else {
    console.log('❌ 修复失败，问题仍然存在')
  }

  console.groupEnd()
}

/**
 * 测试多种版本场景
 */
export function testVersionScenarios(): void {
  console.group('🧪 多场景版本比较测试')

  const scenarios = [
    // 问题场景
    { current: '1.0.0.1007', remote: '1.0.0.1006', description: '问题场景: 当前版本更高' },
    { current: '1.0.0.1006', remote: '1.0.0.1007', description: '反向场景: 远程版本更高' },
    { current: '1.0.0.1007', remote: '1.0.0.1007', description: '相同版本场景' },

    // 混合格式
    { current: '1.0.0', remote: '1.0.0.1006', description: '三段式 vs 四段式' },
    { current: '1.0.0.1007', remote: '1.0.0', description: '四段式 vs 三段式' },

    // 边界情况
    { current: '*******', remote: '1.0.0', description: '四段式末位为0 vs 三段式' },
    { current: '2.0.0', remote: '1.9.9.9999', description: '主版本号不同' },
  ]

  console.log('开始测试各种版本比较场景...\n')

  scenarios.forEach((scenario, index) => {
    const { current, remote, description } = scenario

    const normalizedCurrent = normalizeVersion(current)
    const normalizedRemote = normalizeVersion(remote)
    const needUpdate = compareVersion(normalizedRemote, normalizedCurrent) > 0

    console.log(`场景 ${index + 1}: ${description}`)
    console.log(`  当前: ${current} → ${normalizedCurrent}`)
    console.log(`  远程: ${remote} → ${normalizedRemote}`)
    console.log(`  结果: ${needUpdate ? '需要更新' : '无需更新'}`)
    console.log('')
  })

  console.groupEnd()
}

/**
 * 运行完整验证
 */
export function runFullVerification(): void {
  console.log('🚀 开始版本号比较修复完整验证\n')

  verifyVersionFix()
  console.log('')
  testVersionScenarios()

  console.log('📝 验证建议:')
  console.log('1. 在真机上测试实际更新场景')
  console.log('2. 确认日志中版本号处理正确')
  console.log('3. 验证不会误提示更新')
}

// 导出验证函数到全局（用于控制台调试）
declare global {
  interface Window {
    verifyVersionFix: typeof verifyVersionFix
    testVersionScenarios: typeof testVersionScenarios
    runFullVerification: typeof runFullVerification
  }
}

// #ifdef H5
if (typeof window !== 'undefined') {
  window.verifyVersionFix = verifyVersionFix
  window.testVersionScenarios = testVersionScenarios
  window.runFullVerification = runFullVerification
}
// #endif
