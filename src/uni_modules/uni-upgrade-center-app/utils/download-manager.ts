/**
 * 下载管理器
 * 集成进度通知功能
 */

// #ifdef APP-ANDROID
import {
  createNotificationProgress,
  cancelNotificationProgress,
  finishNotificationProgress,
} from '@/uni_modules/uts-progressNotification/utssdk/index.uts'
// #endif

export interface DownloadOptions {
  url: string
  title?: string
  onProgress?: (progress: number, downloadedSize: number, totalSize: number) => void
  onSuccess?: (filePath: string) => void
  onFail?: (error: any) => void
}

export interface DownloadResult {
  success: boolean
  filePath?: string
  error?: any
}

/**
 * 带进度通知的下载管理器
 */
export class DownloadManager {
  private downloadTask: UniApp.DownloadTask | null = null
  private isDownloading = false

  /**
   * 开始下载
   */
  async download(options: DownloadOptions): Promise<DownloadResult> {
    if (this.isDownloading) {
      throw new Error('正在下载中，请等待当前下载完成')
    }

    this.isDownloading = true
    const { url, title = '正在下载更新', onProgress, onSuccess, onFail } = options

    return new Promise<DownloadResult>((resolve, reject) => {
      console.log('开始下载:', url)

      this.downloadTask = uni.downloadFile({
        url: url,
        success: (res) => {
          console.log('下载完成:', res)
          this.isDownloading = false

          if (res.statusCode === 200) {
            // #ifdef APP-ANDROID
            finishNotificationProgress({
              title: '下载完成',
              content: '点击安装新版本',
              onClick: () => {
                console.log('用户点击了下载完成通知')
              },
            })
            // #endif

            onSuccess?.(res.tempFilePath)
            resolve({
              success: true,
              filePath: res.tempFilePath,
            })
          } else {
            const error = new Error(`下载失败，状态码: ${res.statusCode}`)
            console.error('下载失败:', error)

            // #ifdef APP-ANDROID
            cancelNotificationProgress()
            // #endif

            onFail?.(error)
            resolve({
              success: false,
              error,
            })
          }
        },
        fail: (err) => {
          console.error('下载失败:', err)
          this.isDownloading = false

          // #ifdef APP-ANDROID
          cancelNotificationProgress()
          // #endif

          onFail?.(err)
          resolve({
            success: false,
            error: err,
          })
        },
      })

      // 监听下载进度
      this.downloadTask.onProgressUpdate((res) => {
        const progress = Math.round((res.bytesWritten / res.totalBytesExpectedToWrite) * 100)
        console.log(`下载进度: ${progress}%`, {
          downloaded: res.bytesWritten,
          total: res.totalBytesExpectedToWrite,
        })

        // 更新进度通知
        // #ifdef APP-ANDROID
        createNotificationProgress({
          title: title,
          content: `${progress}%`,
          progress: progress,
          onClick: () => {
            console.log('用户点击了下载进度通知')
          },
        })
        // #endif

        // 调用进度回调
        onProgress?.(progress, res.bytesWritten, res.totalBytesExpectedToWrite)
      })
    })
  }

  /**
   * 取消下载
   */
  cancel(): void {
    if (this.downloadTask) {
      this.downloadTask.abort()
      this.downloadTask = null
      this.isDownloading = false

      // #ifdef APP-ANDROID
      cancelNotificationProgress()
      // #endif

      console.log('下载已取消')
    }
  }

  /**
   * 是否正在下载
   */
  get downloading(): boolean {
    return this.isDownloading
  }
}

// 全局下载管理器实例
export const downloadManager = new DownloadManager()
