/**
 * APP更新功能测试脚本
 * 用于验证修复后的更新功能是否正常工作
 */

import { checkAndHandleUpdate, clearUpgradeCache } from './app-updater'

/**
 * 测试APP更新功能
 */
export async function testAppUpdate(): Promise<void> {
  console.group('🔧 开始测试APP更新功能')

  try {
    // 1. 清除缓存，确保测试环境干净
    console.log('1️⃣ 清除升级配置缓存...')
    clearUpgradeCache()

    // 2. 检查当前应用信息
    console.log('2️⃣ 获取当前应用信息...')
    const systemInfo = uni.getSystemInfoSync()
    console.log('当前应用信息:', {
      appId: systemInfo.appId,
      appVersion: systemInfo.appVersion,
      appVersionCode: systemInfo.appVersionCode,
      platform: systemInfo.platform,
    })

    // 3. 测试更新检查
    console.log('3️⃣ 开始检查更新...')
    await checkAndHandleUpdate(false) // 不自动下载

    console.log('✅ 更新检查测试完成')
  } catch (error) {
    console.error('❌ 更新检查测试失败:', error)
    console.error('错误详情:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
    })
  } finally {
    console.groupEnd()
  }
}

/**
 * 测试版本比较功能
 */
export function testVersionCompare(): void {
  console.group('🔧 测试版本比较功能')

  // 模拟版本号标准化函数
  function normalizeVersion(version: string): string {
    if (!version || typeof version !== 'string') {
      return '0.0.0.0'
    }

    const parts = version.split('.')

    // 确保版本号有4段，不足的用0补齐
    while (parts.length < 4) {
      parts.push('0')
    }

    // 只取前4段，多余的截掉
    const normalizedParts = parts.slice(0, 4).map((part) => {
      const num = parseInt(part) || 0
      return num.toString()
    })

    return normalizedParts.join('.')
  }

  // 模拟版本比较函数
  function compareVersion(version1: string, version2: string): number {
    if (!version1 || typeof version1 !== 'string') {
      version1 = '0.0.0.0'
    }
    if (!version2 || typeof version2 !== 'string') {
      version2 = '0.0.0.0'
    }

    const v1Parts = version1.split('.').map(Number)
    const v2Parts = version2.split('.').map(Number)
    const maxLength = Math.max(v1Parts.length, v2Parts.length)

    for (let i = 0; i < maxLength; i++) {
      const v1 = v1Parts[i] || 0
      const v2 = v2Parts[i] || 0
      if (v1 > v2) return 1
      if (v1 < v2) return -1
    }
    return 0
  }

  // 测试用例
  const testCases = [
    // 基本三段式版本号比较
    { current: '1.0.0', remote: '1.0.1', expectedUpdate: true, description: '标准三段式版本号' },
    { current: '1.0.1', remote: '1.0.0', expectedUpdate: false, description: '当前版本更高' },
    { current: '1.0.0', remote: '1.0.0', expectedUpdate: false, description: '版本相同' },

    // 四段式版本号比较（修复重点）
    {
      current: '1.0.0.1007',
      remote: '1.0.0.1006',
      expectedUpdate: false,
      description: '当前四段式版本更高',
    },
    {
      current: '1.0.0.1006',
      remote: '1.0.0.1007',
      expectedUpdate: true,
      description: '远程四段式版本更高',
    },
    {
      current: '1.0.0.1007',
      remote: '1.0.0.1007',
      expectedUpdate: false,
      description: '四段式版本相同',
    },

    // 混合格式比较
    {
      current: '1.0.0',
      remote: '1.0.0.1006',
      expectedUpdate: true,
      description: '三段式 vs 四段式（远程更高）',
    },
    {
      current: '1.0.0.1007',
      remote: '1.0.0',
      expectedUpdate: true,
      description: '四段式 vs 三段式（当前更高）',
    },

    // 边界情况
    {
      current: '*******',
      remote: '1.0.0',
      expectedUpdate: false,
      description: '四段式末位为0 vs 三段式',
    },
    {
      current: '1.0.0',
      remote: '*******',
      expectedUpdate: false,
      description: '三段式 vs 四段式末位为0',
    },
  ]

  console.log('🧪 开始测试版本比较逻辑...')

  let passedTests = 0
  let totalTests = testCases.length

  testCases.forEach((testCase, index) => {
    const { current, remote, expectedUpdate, description } = testCase

    // 标准化版本号
    const normalizedCurrent = normalizeVersion(current)
    const normalizedRemote = normalizeVersion(remote)

    // 执行版本比较
    const needUpdate = compareVersion(normalizedRemote, normalizedCurrent) > 0

    // 验证结果
    const passed = needUpdate === expectedUpdate

    console.log(`测试 ${index + 1}: ${description}`)
    console.log(`  当前版本: ${current} → ${normalizedCurrent}`)
    console.log(`  远程版本: ${remote} → ${normalizedRemote}`)
    console.log(`  期望更新: ${expectedUpdate}, 实际结果: ${needUpdate}`)
    console.log(`  测试结果: ${passed ? '✅ 通过' : '❌ 失败'}`)
    console.log('')

    if (passed) {
      passedTests++
    }
  })

  console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`)

  if (passedTests === totalTests) {
    console.log('✅ 所有测试通过！版本比较逻辑修复成功')
  } else {
    console.log('❌ 部分测试失败，需要进一步检查')
  }

  console.groupEnd()
}

/**
 * 专门测试问题场景：当前版本 1.0.0.1007，远程版本 1.0.0.1006
 */
export function testSpecificIssue(): void {
  console.group('🎯 测试具体问题场景')

  const currentVersion = '1.0.0.1007'
  const remoteVersion = '1.0.0.1006'

  console.log('问题场景：')
  console.log(`  当前版本: ${currentVersion}`)
  console.log(`  远程版本: ${remoteVersion}`)
  console.log(`  期望结果: 不应该提示更新（当前版本更高）`)

  // 模拟标准化处理
  function normalizeVersion(version: string): string {
    if (!version || typeof version !== 'string') {
      return '0.0.0.0'
    }
    const parts = version.split('.')
    while (parts.length < 4) {
      parts.push('0')
    }
    const normalizedParts = parts.slice(0, 4).map((part) => {
      const num = parseInt(part) || 0
      return num.toString()
    })
    return normalizedParts.join('.')
  }

  function compareVersion(version1: string, version2: string): number {
    const v1Parts = version1.split('.').map(Number)
    const v2Parts = version2.split('.').map(Number)
    const maxLength = Math.max(v1Parts.length, v2Parts.length)

    for (let i = 0; i < maxLength; i++) {
      const v1 = v1Parts[i] || 0
      const v2 = v2Parts[i] || 0
      if (v1 > v2) return 1
      if (v1 < v2) return -1
    }
    return 0
  }

  const normalizedCurrent = normalizeVersion(currentVersion)
  const normalizedRemote = normalizeVersion(remoteVersion)
  const needUpdate = compareVersion(normalizedRemote, normalizedCurrent) > 0

  console.log('')
  console.log('处理结果：')
  console.log(`  标准化当前版本: ${normalizedCurrent}`)
  console.log(`  标准化远程版本: ${normalizedRemote}`)
  console.log(`  版本比较结果: ${needUpdate ? '需要更新' : '无需更新'}`)

  if (!needUpdate) {
    console.log('✅ 修复成功！不会错误提示更新')
  } else {
    console.log('❌ 修复失败！仍然会错误提示更新')
  }

  console.groupEnd()
}

/**
 * 运行所有测试
 */
export async function runAllTests(): Promise<void> {
  console.log('🚀 开始运行APP更新修复验证测试')

  // 测试具体问题场景
  testSpecificIssue()

  // 测试版本比较
  testVersionCompare()

  // 等待一秒后测试更新功能
  setTimeout(async () => {
    await testAppUpdate()
  }, 1000)
}

// 在控制台中可以调用的测试函数
declare global {
  interface Window {
    testAppUpdate: typeof testAppUpdate
    testSpecificIssue: typeof testSpecificIssue
    clearUpgradeCache: typeof clearUpgradeCache
    runAllTests: typeof runAllTests
  }
}

// #ifdef H5
if (typeof window !== 'undefined') {
  window.testAppUpdate = testAppUpdate
  window.testSpecificIssue = testSpecificIssue
  window.clearUpgradeCache = clearUpgradeCache
  window.runAllTests = runAllTests
}
// #endif
