/**
 * App 更新管理器
 * 整合升级检查、下载管理和安装功能
 */

import callCheckVersion, { UniUpgradeCenterResult } from './call-check-version'
import { platform_iOS } from './utils'

// #ifdef UNI-APP-X
import { openSchema } from '@/uni_modules/uts-openSchema'
// #endif

// #ifdef APP-ANDROID
declare const plus: any
// #endif

export interface UpdateProgress {
  progress: number
  downloadedSize: number
  totalSize: number
}

export interface UpdateCallbacks {
  onProgress?: (progress: UpdateProgress) => void
  onSuccess?: () => void
  onFail?: (error: any) => void
}

/**
 * App 更新管理器类
 */
export class AppUpdater {
  private downloadTask: UniApp.DownloadTask | null = null
  private isDownloading = false

  /**
   * 检查更新
   */
  async checkUpdate(): Promise<UniUpgradeCenterResult> {
    console.log('开始检查应用更新...')
    try {
      const result = await callCheckVersion()
      console.log('更新检查结果:', result)
      return result
    } catch (error) {
      console.error('检查更新失败:', error)
      throw error
    }
  }

  /**
   * 显示更新弹窗并处理用户选择
   * @param updateInfo 更新信息
   * @param useCustomDialog 是否使用自定义弹窗（升级中心页面）
   */
  async showUpdateDialog(
    updateInfo: UniUpgradeCenterResult,
    useCustomDialog = false,
  ): Promise<boolean> {
    const { title, contents, is_mandatory, url, type, platform } = updateInfo

    const isWGT = type === 'wgt'
    const isiOS = !isWGT ? platform.includes(platform_iOS) : false

    // 如果使用自定义弹窗
    if (useCustomDialog) {
      return this.showCustomUpdateDialog(updateInfo)
    }

    // #ifndef UNI-APP-X
    const confirmText = isiOS ? '立即跳转更新' : '立即下载更新'
    // #endif
    // #ifdef UNI-APP-X
    const confirmText = '立即下载更新'
    // #endif

    return new Promise((resolve) => {
      uni.showModal({
        title: title || '发现新版本',
        content: contents || '建议您立即更新到最新版本',
        showCancel: !is_mandatory,
        confirmText,
        cancelText: '稍后更新',
        success: (res) => {
          if (res.confirm) {
            resolve(true)
          } else {
            resolve(false)
          }
        },
        fail: () => {
          resolve(false)
        },
      })
    })
  }

  /**
   * 显示自定义升级弹窗
   */
  private async showCustomUpdateDialog(updateInfo: UniUpgradeCenterResult): Promise<boolean> {
    return new Promise((resolve) => {
      // 存储升级信息到本地
      const localStorageKey = `__upgrade_info_${Date.now()}`
      uni.setStorageSync(localStorageKey, updateInfo)

      // 跳转到升级页面
      uni.navigateTo({
        url: `/uni_modules/uni-upgrade-center-app/pages/upgrade-popup?local_storage_key=${localStorageKey}`,
        success: () => {
          console.log('跳转到升级页面成功')
        },
        fail: (err) => {
          console.error('跳转到升级页面失败:', err)
          resolve(false)
        },
      })

      // 监听升级页面的关闭事件
      uni.$once('upgrade-dialog-result', (result: boolean) => {
        resolve(result)
      })

      // 设置超时，避免永久等待
      setTimeout(() => {
        resolve(false)
      }, 300000) // 5分钟超时
    })
  }

  /**
   * 执行更新下载和安装
   */
  async performUpdate(
    updateInfo: UniUpgradeCenterResult,
    callbacks?: UpdateCallbacks,
  ): Promise<void> {
    const { url, type, platform, is_mandatory } = updateInfo
    const isWGT = type === 'wgt'
    const isiOS = !isWGT ? platform.includes(platform_iOS) : false

    // iOS 直接跳转 App Store
    if (isiOS) {
      // #ifndef UNI-APP-X
      plus.runtime.openURL(url)
      // #endif
      // #ifdef UNI-APP-X
      openSchema(url)
      // #endif
      return
    }

    // Android 下载更新包
    console.log('开始下载更新包:', url)

    try {
      const filePath = await this.downloadUpdate(url, callbacks?.onProgress)
      console.log('下载完成，开始安装:', filePath)

      await this.installUpdate(filePath, isWGT, is_mandatory)
      callbacks?.onSuccess?.()
    } catch (error) {
      console.error('更新失败:', error)
      callbacks?.onFail?.(error)

      uni.showModal({
        title: '更新失败',
        content: error.message || '下载或安装过程中发生错误',
        showCancel: false,
      })
    }
  }

  /**
   * 下载更新包
   */
  private downloadUpdate(
    url: string,
    onProgress?: (progress: UpdateProgress) => void,
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      if (this.isDownloading) {
        reject(new Error('正在下载中，请等待'))
        return
      }

      this.isDownloading = true

      // 显示下载提示
      uni.showToast({
        title: '开始下载更新...',
        icon: 'loading',
        duration: 2000,
      })

      this.downloadTask = uni.downloadFile({
        url: url,
        success: (res) => {
          this.isDownloading = false

          if (res.statusCode === 200) {
            console.log('下载成功:', res.tempFilePath)
            resolve(res.tempFilePath)
          } else {
            const error = new Error(`下载失败，状态码: ${res.statusCode}`)
            reject(error)
          }
        },
        fail: (err) => {
          this.isDownloading = false
          console.error('下载失败:', err)
          reject(new Error('网络错误，下载失败'))
        },
      })

      // 监听下载进度
      if (this.downloadTask && this.downloadTask.onProgressUpdate) {
        this.downloadTask.onProgressUpdate((res) => {
          const progress = {
            progress: Math.round((res.totalBytesWritten / res.totalBytesExpectedToWrite) * 100),
            downloadedSize: res.totalBytesWritten,
            totalSize: res.totalBytesExpectedToWrite,
          }

          console.log(`下载进度: ${progress.progress}%`)
          onProgress?.(progress)

          // 显示进度 Toast
          if (progress.progress % 10 === 0 || progress.progress === 100) {
            uni.showToast({
              title: `下载中 ${progress.progress}%`,
              icon: 'loading',
              duration: 1000,
            })
          }
        })
      }
    })
  }

  /**
   * 安装更新包
   */
  private installUpdate(filePath: string, isWGT: boolean, isMandatory: boolean): Promise<void> {
    return new Promise((resolve, reject) => {
      // #ifndef UNI-APP-X
      if (typeof plus === 'undefined') {
        reject(new Error('plus 环境不可用'))
        return
      }

      plus.runtime.install(
        filePath,
        {
          force: false,
        },
        () => {
          console.log('安装成功')

          if (isMandatory) {
            // 强制更新，安装后直接重启
            // #ifdef APP-PLUS
            plus.runtime.restart()
            // #endif
            // #ifdef APP-HARMONY
            uni.showModal({
              title: '安装成功',
              content: '请手动重启应用',
              showCancel: false,
              success: () => {
                plus.runtime.quit()
              },
            })
            // #endif
            resolve()
          } else {
            // 非强制更新，询问是否重启
            uni.showModal({
              title: '安装成功',
              content: '是否立即重启应用？',
              confirmText: '立即重启',
              cancelText: '稍后重启',
              success: (res) => {
                if (res.confirm) {
                  // #ifdef APP-PLUS
                  plus.runtime.restart()
                  // #endif
                  // #ifdef APP-HARMONY
                  plus.runtime.quit()
                  // #endif
                }
                resolve()
              },
            })
          }
        },
        (err) => {
          console.error('安装失败:', err)
          reject(new Error(`安装失败: ${err.message || '未知错误'}`))
        },
      )
      // #endif

      // #ifdef UNI-APP-X
      uni.installApk({
        filePath: filePath,
        success: () => {
          console.log('安装成功 (uni-app x)')
          uni.showModal({
            title: '安装成功',
            content: '请手动重启应用',
            showCancel: false,
          })
          resolve()
        },
        fail: (err) => {
          console.error('安装失败 (uni-app x):', err)
          reject(new Error(`安装失败: ${err.errMsg || '未知错误'}`))
        },
      })
      // #endif
    })
  }

  /**
   * 取消下载
   */
  cancelDownload(): void {
    if (this.downloadTask) {
      this.downloadTask.abort()
      this.downloadTask = null
      this.isDownloading = false

      uni.showToast({
        title: '下载已取消',
        icon: 'none',
      })
    }
  }

  /**
   * 是否正在下载
   */
  get downloading(): boolean {
    return this.isDownloading
  }

  /**
   * 静默更新（仅限 wgt 包）
   */
  async silentUpdate(updateInfo: UniUpgradeCenterResult): Promise<void> {
    if (updateInfo.type !== 'wgt' || !updateInfo.is_silently) {
      console.log('不支持静默更新')
      return
    }

    console.log('开始静默更新...')

    try {
      const filePath = await this.downloadUpdate(updateInfo.url)

      // #ifndef UNI-APP-X
      if (typeof plus !== 'undefined') {
        plus.runtime.install(filePath, {
          force: false,
        })
        console.log('静默更新安装完成，下次启动生效')
      }
      // #endif
    } catch (error) {
      console.error('静默更新失败:', error)
    }
  }
}

// 全局更新管理器实例
export const appUpdater = new AppUpdater()

/**
 * 检查并处理更新的便捷函数
 * @param autoDownload 是否自动下载更新
 * @param useCustomDialog 是否使用自定义弹窗
 */
export async function checkAndHandleUpdate(
  autoDownload = false,
  useCustomDialog = false,
): Promise<void> {
  const updater = new AppUpdater()

  try {
    console.log('🔄 开始检查应用更新...')

    // 检查更新
    const updateInfo = await updater.checkUpdate()

    if (updateInfo.code === 0) {
      console.log('✅ 当前已是最新版本')
      return
    }

    console.log('🆕 发现新版本:', updateInfo.version)
    console.log('📝 更新内容:', updateInfo.contents)
    console.log('🔒 强制更新:', updateInfo.is_mandatory)

    let shouldUpdate = autoDownload

    if (!autoDownload) {
      // 显示更新弹窗
      shouldUpdate = await updater.showUpdateDialog(updateInfo, useCustomDialog)
    }

    if (shouldUpdate) {
      console.log('💾 用户确认更新，开始下载...')

      await updater.performUpdate(updateInfo, {
        onProgress: (progress) => {
          console.log(`📥 下载进度: ${progress.progress}%`)
        },
        onSuccess: () => {
          console.log('✅ 更新成功')
        },
        onFail: (error) => {
          console.error('❌ 更新失败:', error)
        },
      })
    } else {
      console.log('⏭️ 用户取消更新')
    }
  } catch (error) {
    console.error('❌ 检查更新失败:', error)
    throw error
  }
}

/**
 * 清除升级配置缓存（调试用）
 */
export function clearUpgradeCache(): void {
  try {
    uni.removeStorageSync('__upgrade_config_cache__')
    console.log('✅ 升级配置缓存已清除')
  } catch (error) {
    console.error('❌ 清除升级配置缓存失败:', error)
  }
}
