import { UPDATE_CONFIG_URL, UPDATE_CONFIG_CACHE_TIME, UPDATE_CONFIG_CACHE_KEY } from './config'

export type StoreListItem = {
  enable: boolean
  id: string
  name: string
  scheme: string
  priority: number // 优先级
}

export type UniUpgradeCenterResult = {
  _id: string
  appid: string
  name: string
  title: string
  contents: string
  url: string // 安装包下载地址
  platform: Array<string> // Array<'Android' | 'iOS' | 'Harmony'>
  version: string // 版本号 1.0.0
  uni_platform: string // "android" | "ios" | 'harmony'
  stable_publish: boolean // 是否是稳定版
  is_mandatory: boolean // 是否强制更新
  is_silently: boolean | null // 是否静默更新
  create_env: string // "upgrade-center"
  create_date: number
  message: string
  code: number

  type: string // "native_app" | "wgt"
  store_list: StoreListItem[] | null
  min_uni_version: string | null // 升级 wgt 的最低 uni-app 版本
}

/**
 * OSS 升级配置文件的数据结构
 */
export type OSSUpgradeConfig = {
  code: number
  message: string
  appid: string
  name: string
  version: string // 最新版本号
  versionCode: number // 最新版本代码
  forceUpdate: boolean // 是否强制更新
  silent: boolean // 是否静默更新
  platform: string[] // 支持的平台
  title: string // 更新弹窗标题
  contents: string // 更新内容
  url: string // 完整安装包下载地址
  wgtUrl?: string // wgt热更新包下载地址
  minVersion?: string // 支持wgt热更新的最低版本号
}

export type OSSResponseWrapper = {
  resultCode: number
  data: OSSUpgradeConfig
}

/**
 * 比较两个版本号
 * @param version1 版本号1
 * @param version2 版本号2
 * @returns 1: version1 > version2, -1: version1 < version2, 0: 相等
 */
function compareVersion(version1: string, version2: string): number {
  // 添加空值检查
  if (!version1 || typeof version1 !== 'string') {
    console.warn('compareVersion: version1 为空或不是字符串:', version1)
    version1 = '0.0.0'
  }

  if (!version2 || typeof version2 !== 'string') {
    console.warn('compareVersion: version2 为空或不是字符串:', version2)
    version2 = '0.0.0'
  }

  try {
    const v1Parts = version1.split('.').map(Number)
    const v2Parts = version2.split('.').map(Number)

    const maxLength = Math.max(v1Parts.length, v2Parts.length)

    for (let i = 0; i < maxLength; i++) {
      const v1 = v1Parts[i] || 0
      const v2 = v2Parts[i] || 0

      if (v1 > v2) return 1
      if (v1 < v2) return -1
    }

    return 0
  } catch (error) {
    console.error('compareVersion 执行出错:', error, 'version1:', version1, 'version2:', version2)
    return 0
  }
}

/**
 * 标准化版本号格式
 * 将版本号标准化为四段式格式，确保比较的一致性
 * @param version 原始版本号
 * @returns 标准化后的版本号
 */
function normalizeVersion(version: string): string {
  if (!version || typeof version !== 'string') {
    return '0.0.0.0'
  }

  const parts = version.split('.')

  // 确保版本号有4段，不足的用0补齐
  while (parts.length < 4) {
    parts.push('0')
  }

  // 只取前4段，多余的截掉
  const normalizedParts = parts.slice(0, 4).map((part) => {
    const num = parseInt(part) || 0
    return num.toString()
  })

  return normalizedParts.join('.')
}

/**
 * 验证OSS配置的完整性
 */
function validateOSSConfig(config: any): config is OSSUpgradeConfig {
  if (!config || typeof config !== 'object') {
    console.error('配置验证失败: 配置为空或不是对象', config)
    return false
  }

  const requiredFields = ['version', 'versionCode', 'appid', 'name', 'url']
  const missingFields = requiredFields.filter((field) => !config[field])

  if (missingFields.length > 0) {
    console.error('配置验证失败: 缺少必需字段', missingFields, config)
    return false
  }

  // 验证版本号格式
  if (typeof config.version !== 'string' || !config.version.match(/^\d+\.\d+\.\d+/)) {
    console.error('配置验证失败: 版本号格式错误', config.version)
    return false
  }

  return true
}

/**
 * 将OSS配置转换为升级中心的结果格式
 */
function transformOSSConfigToResult(
  ossConfig: OSSUpgradeConfig,
  currentAppInfo: any,
  needUpdate: boolean,
): UniUpgradeCenterResult {
  const systemInfo = uni.getSystemInfoSync()
  const platform = systemInfo.platform.toLowerCase()

  // 修复：非强制更新也使用完整安装包，不使用热更新
  // 只有强制更新时才考虑使用热更新
  let useWgtUpdate = false
  let downloadUrl = ossConfig.url
  let updateType = 'native_app'

  // 只有在强制更新时才考虑热更新
  if (ossConfig.forceUpdate && ossConfig.wgtUrl && ossConfig.minVersion) {
    // 检查当前版本是否支持热更新
    try {
      if (compareVersion(currentAppInfo.version, ossConfig.minVersion) >= 0) {
        useWgtUpdate = true
        downloadUrl = ossConfig.wgtUrl
        updateType = 'wgt'
        console.log('强制更新使用热更新包 (wgt)')
      } else {
        console.log('当前版本不支持热更新，使用完整安装包')
      }
    } catch (error) {
      console.warn('热更新版本比较失败，使用整包更新:', error)
    }
  } else if (!ossConfig.forceUpdate) {
    console.log('非强制更新，使用完整安装包 (APK)')
  }

  return {
    _id: `oss_${Date.now()}`,
    appid: ossConfig.appid,
    name: ossConfig.name,
    title: ossConfig.title,
    contents: ossConfig.contents,
    url: downloadUrl,
    platform: ossConfig.platform,
    version: ossConfig.version,
    uni_platform: platform,
    stable_publish: true,
    is_mandatory: ossConfig.forceUpdate,
    is_silently: ossConfig.silent && useWgtUpdate, // 只有wgt更新才支持静默更新
    create_env: 'oss-upgrade-center',
    create_date: Date.now(),
    message: needUpdate ? '发现新版本' : '已是最新版本',
    code: needUpdate ? 1 : 0,
    type: updateType,
    store_list: null,
    min_uni_version: ossConfig.minVersion || null,
  }
}

/**
 * 获取缓存的配置
 */
function getCachedConfig(): OSSUpgradeConfig | null {
  try {
    const cached = uni.getStorageSync(UPDATE_CONFIG_CACHE_KEY)
    if (cached && cached.timestamp && Date.now() - cached.timestamp < UPDATE_CONFIG_CACHE_TIME) {
      // 验证缓存配置的完整性
      if (validateOSSConfig(cached.data)) {
        console.log('使用缓存的升级配置')
        return cached.data
      } else {
        console.warn('缓存的配置格式不正确，清除缓存')
        // 清除无效缓存
        uni.removeStorageSync(UPDATE_CONFIG_CACHE_KEY)
      }
    }
  } catch (e) {
    console.error('获取缓存配置失败:', e)
  }
  return null
}

/**
 * 缓存配置
 */
function setCachedConfig(config: OSSUpgradeConfig): void {
  try {
    uni.setStorageSync(UPDATE_CONFIG_CACHE_KEY, {
      data: config,
      timestamp: Date.now(),
    })
  } catch (e) {
    console.error('缓存配置失败:', e)
  }
}

/**
 * 从OSS获取升级配置
 * @param appInfo 应用信息
 * @param forceRefresh 是否强制刷新，不使用缓存
 */
async function fetchUpgradeConfigFromOSS(
  appInfo: any,
  forceRefresh = false,
): Promise<OSSUpgradeConfig> {
  // 如果不强制刷新，先尝试从缓存获取
  if (!forceRefresh) {
    const cached = getCachedConfig()
    if (cached) {
      console.log('使用缓存的升级配置')
      return cached
    }
  } else {
    console.log('强制刷新，跳过缓存直接从OSS获取最新配置')
  }

  // 构建请求参数
  const params = {
    appid: appInfo.appid,
    name: appInfo.name,
    version: appInfo.version,
    versionCode: appInfo.versionCode,
    platform: uni.getSystemInfoSync().platform.toLowerCase(),
    timestamp: Date.now(),
  }

  console.log('请求升级配置，参数:', params)

  try {
    const res = await uni.request({
      url: UPDATE_CONFIG_URL,
      method: 'GET',
      data: params,
      timeout: 10000,
    })
    console.log('请求升级配置，参数:', params, '返回结果:', res)

    if (res.statusCode === 200 && res.data) {
      let config: OSSUpgradeConfig

      // 类型检查和数据解析
      const responseData = res.data as any

      // 检查是否有外层包装
      if (
        responseData &&
        typeof responseData === 'object' &&
        responseData.resultCode !== undefined &&
        responseData.data
      ) {
        // 有外层包装的情况
        if (responseData.resultCode === 0) {
          config = responseData.data as OSSUpgradeConfig
        } else {
          throw new Error(`API错误: resultCode=${responseData.resultCode}`)
        }
      } else {
        // 直接是配置数据的情况
        config = responseData as OSSUpgradeConfig
      }

      console.log('获取到升级配置:', config)

      // 验证配置完整性
      if (!validateOSSConfig(config)) {
        throw new Error('OSS返回的配置格式不正确或缺少必需字段')
      }

      // 缓存配置（即使是强制刷新，也要更新缓存）
      setCachedConfig(config)

      return config
    } else {
      throw new Error(`HTTP ${res.statusCode}: 获取升级配置失败`)
    }
  } catch (error) {
    console.error('请求升级配置失败:', error)
    throw error
  }
}

export default function (): Promise<UniUpgradeCenterResult> {
  // #ifdef APP
  return new Promise<UniUpgradeCenterResult>((resolve, reject) => {
    const systemInfo = uni.getSystemInfoSync()
    const appId = systemInfo.appId
    const appVersion = systemInfo.appVersion

    if (!appId || !appVersion) {
      reject(new Error('无法获取应用信息'))
      return
    }

    console.log('开始检查更新, AppID:', appId, 'AppVersion:', appVersion)

    // #ifndef UNI-APP-X
    if (
      typeof appId === 'string' &&
      typeof appVersion === 'string' &&
      appId.length > 0 &&
      appVersion.length > 0
    ) {
      plus.runtime.getProperty(appId, function (widgetInfo) {
        console.log('获取到的widgetInfo:', widgetInfo)
        console.log('系统版本信息:', { appVersion, appVersionCode: systemInfo.appVersionCode })

        // 优先使用系统版本信息，widgetInfo作为补充
        let finalVersion = appVersion || widgetInfo?.version || '1.0.0'
        let finalVersionCode = parseInt(systemInfo.appVersionCode || widgetInfo?.versionCode || '1')

        // 标准化版本号格式，保持四段式版本号的完整性
        finalVersion = normalizeVersion(finalVersion)

        console.log('最终使用的版本信息:', { finalVersion, finalVersionCode })

        const currentAppInfo = {
          appid: appId,
          name: widgetInfo?.name || systemInfo.appName || '应用',
          version: finalVersion,
          versionCode: finalVersionCode,
          wgtVersion: widgetInfo?.version || finalVersion,
        }

        console.log('当前应用信息:', currentAppInfo)

        // 使用立即执行的异步函数包装
        ;(async () => {
          try {
            // 从OSS获取升级配置（强制刷新）
            const ossConfig = await fetchUpgradeConfigFromOSS(currentAppInfo, true)

            // 标准化远程版本号
            const normalizedRemoteVersion = normalizeVersion(ossConfig.version)

            // 检查是否需要更新
            let needUpdate = false
            try {
              needUpdate = compareVersion(normalizedRemoteVersion, currentAppInfo.version) > 0
            } catch (error) {
              console.error('版本比较失败:', error)
              needUpdate = false
            }

            console.log('版本比较结果:', {
              current: currentAppInfo.version,
              latest: ossConfig.version,
              normalizedLatest: normalizedRemoteVersion,
              needUpdate,
            })

            // 转换为升级中心格式并返回
            const result = transformOSSConfigToResult(ossConfig, currentAppInfo, needUpdate)
            console.log('最终返回结果:', result)

            resolve(result)
          } catch (error) {
            console.error('检查更新失败:', error)
            reject(error)
          }
        })()
      })
    } else {
      reject(new Error('应用ID或版本号为空'))
    }
    // #endif

    // #ifdef UNI-APP-X
    if (
      typeof appId === 'string' &&
      typeof appVersion === 'string' &&
      appId.length > 0 &&
      appVersion.length > 0
    ) {
      // 标准化版本号格式
      const normalizedVersion = normalizeVersion(appVersion)
      const versionCode = parseInt(systemInfo.appVersionCode || '0')

      const currentAppInfo = {
        appid: appId,
        name: systemInfo.appName || '',
        version: normalizedVersion,
        versionCode: versionCode,
        wgtVersion: '0.0.0.0', // uni-app x 不支持wgt
      }

      console.log('当前应用信息 (uni-app x):', currentAppInfo)

      // 使用立即执行的异步函数包装
      ;(async () => {
        try {
          // 从OSS获取升级配置（强制刷新）
          const ossConfig = await fetchUpgradeConfigFromOSS(currentAppInfo, true)

          // uni-app x 不支持热更新，强制使用整包更新
          ossConfig.wgtUrl = undefined
          ossConfig.minVersion = undefined
          ossConfig.silent = false

          // 标准化远程版本号
          const normalizedRemoteVersion = normalizeVersion(ossConfig.version)

          // 检查是否需要更新
          let needUpdate = false
          try {
            needUpdate = compareVersion(normalizedRemoteVersion, currentAppInfo.version) > 0
          } catch (error) {
            console.error('版本比较失败 (uni-app x):', error)
            needUpdate = false
          }

          console.log('版本比较结果 (uni-app x):', {
            current: currentAppInfo.version,
            latest: ossConfig.version,
            normalizedLatest: normalizedRemoteVersion,
            needUpdate,
          })

          // 转换为升级中心格式并返回
          const result = transformOSSConfigToResult(ossConfig, currentAppInfo, needUpdate)
          console.log('最终返回结果 (uni-app x):', result)

          resolve(result)
        } catch (error) {
          console.error('检查更新失败 (uni-app x):', error)
          reject(error)
        }
      })()
    } else {
      reject(new Error('应用ID或版本号为空'))
    }
    // #endif
  })
  // #endif
}
