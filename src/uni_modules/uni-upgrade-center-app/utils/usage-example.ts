/**
 * 使用示例：如何在项目中集成升级中心
 */

import { checkAndHandleUpdate, appUpdater } from './app-updater'

/**
 * 示例1: 在 App.vue 中自动检查更新
 */
export function setupAutoUpdate() {
  // 在应用启动时检查更新
  uni.onLaunch(() => {
    setTimeout(() => {
      checkAndHandleUpdate()
    }, 1000) // 延迟1秒执行，避免影响启动速度
  })
}

/**
 * 示例2: 在设置页面添加"检查更新"按钮
 */
export function setupManualUpdate() {
  const checkForUpdate = async () => {
    try {
      uni.showLoading({ title: '检查更新中...' })

      const updateInfo = await appUpdater.checkUpdate()
      uni.hideLoading()

      if (updateInfo.code === 0) {
        uni.showModal({
          title: '提示',
          content: '当前已是最新版本',
          showCancel: false,
        })
        return
      }

      // 显示更新弹窗
      const shouldUpdate = await appUpdater.showUpdateDialog(updateInfo)

      if (shouldUpdate) {
        await appUpdater.performUpdate(updateInfo, {
          onProgress: (progress) => {
            console.log(`更新进度: ${progress.progress}%`)
          },
          onSuccess: () => {
            console.log('更新成功')
          },
          onFail: (error) => {
            console.error('更新失败:', error)
          },
        })
      }
    } catch (error) {
      uni.hideLoading()
      uni.showModal({
        title: '检查更新失败',
        content: '请检查网络连接后重试',
        showCancel: false,
      })
    }
  }

  return { checkForUpdate }
}

/**
 * 示例3: 高级用法 - 自定义更新流程
 */
export function setupAdvancedUpdate() {
  let downloadProgress = 0
  let isDownloading = false

  const startUpdate = async () => {
    try {
      // 1. 检查更新
      const updateInfo = await appUpdater.checkUpdate()

      if (updateInfo.code === 0) {
        console.log('无需更新')
        return
      }

      // 2. 判断更新类型
      if (updateInfo.is_silently && updateInfo.type === 'wgt') {
        // 静默更新
        console.log('开始静默更新')
        await appUpdater.silentUpdate(updateInfo)
        return
      }

      // 3. 显示自定义更新弹窗
      const shouldUpdate = await showCustomUpdateDialog(updateInfo)

      if (shouldUpdate) {
        isDownloading = true

        await appUpdater.performUpdate(updateInfo, {
          onProgress: (progress) => {
            downloadProgress = progress.progress
            updateProgressUI(progress)
          },
          onSuccess: () => {
            isDownloading = false
            onUpdateSuccess()
          },
          onFail: (error) => {
            isDownloading = false
            onUpdateFail(error)
          },
        })
      }
    } catch (error) {
      console.error('更新过程失败:', error)
    }
  }

  const showCustomUpdateDialog = (updateInfo: any): Promise<boolean> => {
    return new Promise((resolve) => {
      uni.showModal({
        title: updateInfo.title || '发现新版本',
        content: `版本: ${updateInfo.version}\n\n${updateInfo.contents}`,
        confirmText: updateInfo.is_mandatory ? '立即更新' : '立即更新',
        cancelText: '稍后提醒',
        showCancel: !updateInfo.is_mandatory,
        success: (res) => {
          resolve(res.confirm)
        },
      })
    })
  }

  const updateProgressUI = (progress: any) => {
    // 这里可以更新您的自定义进度条UI
    console.log(`下载进度: ${progress.progress}%`)

    // 也可以使用 uni.showToast 显示进度
    if (progress.progress % 20 === 0) {
      uni.showToast({
        title: `下载中 ${progress.progress}%`,
        icon: 'loading',
        duration: 1000,
      })
    }
  }

  const onUpdateSuccess = () => {
    uni.showModal({
      title: '更新成功',
      content: '应用将重启以应用更新',
      showCancel: false,
      success: () => {
        // 可以在这里执行一些清理工作
      },
    })
  }

  const onUpdateFail = (error: any) => {
    uni.showModal({
      title: '更新失败',
      content: error.message || '更新过程中发生错误',
      showCancel: false,
    })
  }

  return {
    startUpdate,
    isDownloading: () => isDownloading,
    getProgress: () => downloadProgress,
  }
}

/**
 * 示例4: 在页面中使用 (适用于 Vue 组件)
 */
export function useAppUpdate() {
  const isUpdating = uni.ref(false)
  const updateProgress = uni.ref(0)

  const checkUpdate = async () => {
    try {
      isUpdating.value = true

      const updateInfo = await appUpdater.checkUpdate()

      if (updateInfo.code !== 0) {
        const shouldUpdate = await appUpdater.showUpdateDialog(updateInfo)

        if (shouldUpdate) {
          await appUpdater.performUpdate(updateInfo, {
            onProgress: (progress) => {
              updateProgress.value = progress.progress
            },
            onSuccess: () => {
              isUpdating.value = false
              uni.showToast({ title: '更新成功', icon: 'success' })
            },
            onFail: (error) => {
              isUpdating.value = false
              uni.showToast({ title: '更新失败', icon: 'error' })
            },
          })
        } else {
          isUpdating.value = false
        }
      } else {
        isUpdating.value = false
        uni.showToast({ title: '已是最新版本', icon: 'success' })
      }
    } catch (error) {
      isUpdating.value = false
      uni.showToast({ title: '检查更新失败', icon: 'error' })
    }
  }

  return {
    isUpdating,
    updateProgress,
    checkUpdate,
  }
}

/**
 * 工具函数：获取当前应用版本信息
 */
export function getCurrentAppInfo() {
  const systemInfo = uni.getSystemInfoSync()
  return {
    appId: systemInfo.appId,
    appVersion: systemInfo.appVersion,
    platform: systemInfo.platform,
    appName: systemInfo.appName || '',
  }
}

/**
 * 工具函数：检查是否需要提示更新
 */
export function shouldPromptUpdate(): boolean {
  const lastCheckTime = uni.getStorageSync('lastUpdateCheckTime') || 0
  const now = Date.now()
  const oneDayMs = 24 * 60 * 60 * 1000 // 一天的毫秒数

  // 如果距离上次检查已超过一天，则需要提示
  return now - lastCheckTime > oneDayMs
}

/**
 * 工具函数：记录本次检查更新的时间
 */
export function recordUpdateCheckTime(): void {
  uni.setStorageSync('lastUpdateCheckTime', Date.now())
}
