# uni-upgrade-center-app 使用说明

## 🎯 功能概述

基于 OSS 配置文件的 uni-app 应用升级中心，已修复版本获取和JSON解析问题。

## 🔧 已修复的问题

### 1. 版本信息获取失败

- **问题**：`Error: 无法获取应用版本信息`
- **原因**：`plus.runtime.getProperty` 回调中 `widgetInfo.version` 可能为空
- **解决方案**：
  - 优先使用 `systemInfo.appVersion` 和 `systemInfo.appVersionCode`
  - `widgetInfo` 作为补充信息
  - 处理 `1.0.0.1007` 格式的版本号（自动截取前3段）

### 2. JSON数据结构不匹配

- **问题**：OSS返回的JSON有外层 `resultCode` 和 `data` 包装
- **解决方案**：
  - 自动检测是否有外层包装
  - 支持两种JSON格式：`{resultCode: 0, data: {...}}` 或直接 `{...}`
  - 添加详细的错误处理和日志

### 3. 四段式版本号比较错误 ⭐ **最新修复**

- **问题**：当前版本 `1.0.0.1007`，远程版本 `1.0.0.1006`，错误提示需要更新
- **原因**：
  - 原版本处理逻辑将四段式版本号截取为三段式（`1.0.0.1007` → `1.0.0`）
  - 版本比较时 `1.0.0` 与 `1.0.0.1006` 比较，误判为需要更新
- **解决方案**：
  - 实现版本号标准化函数 `normalizeVersion()`
  - 统一将版本号标准化为四段式格式（不足补0，超出截取）
  - 确保本地和远程版本号使用相同的格式进行比较
  - 支持三段式、四段式版本号的正确比较

### 版本号标准化规则

```typescript
// 标准化前 → 标准化后
'1.0.0' → '*******'
'1.0.0.1007' → '1.0.0.1007'
'*******.5' → '*******'
'1.0' → '*******'
```

### 版本比较示例

```typescript
// 修复前（错误）
current: '1.0.0.1007' → '1.0.0'    (被截取)
remote:  '1.0.0.1006'
result:  '1.0.0.1006' > '1.0.0' → 错误提示更新

// 修复后（正确）
current: '1.0.0.1007' → '1.0.0.1007'  (标准化)
remote:  '1.0.0.1006' → '1.0.0.1006'  (标准化)
result:  '1.0.0.1006' < '1.0.0.1007' → 正确，无需更新
```

## 📋 OSS配置文件格式

您的 OSS 文件应该使用以下格式：

```json
{
  "resultCode": 0,
  "data": {
    "code": 0,
    "message": "获取成功",
    "appid": "__UNI__2704FCB",
    "name": "海底捞外送商家端",
    "version": "1.0.2",
    "versionCode": 102,
    "forceUpdate": false,
    "silent": false,
    "platform": ["Android"],
    "title": "发现新版本",
    "contents": "1. 优化用户体验；\\n2. 修复了一些已知问题；\\n3. 增加了新功能。",
    "url": "https://your-oss.com/path/to/your-app-1.0.2.apk",
    "wgtUrl": "https://your-oss.com/path/to/your-app.wgt",
    "minVersion": "1.0.0"
  }
}
```

## 🚀 使用方法

### 1. 在 App.vue 中启用自动更新

```vue
<script setup lang="ts">
import { onLaunch } from '@dcloudio/uni-app'

onLaunch(() => {
  // 延迟执行，避免影响启动速度
  setTimeout(async () => {
    try {
      // 导入更新模块
      const { checkAndHandleUpdate } = await import(
        '@/uni_modules/uni-upgrade-center-app/utils/app-updater'
      )

      // 检查并处理更新
      await checkAndHandleUpdate()
    } catch (error) {
      console.log('更新检查失败:', error)
    }
  }, 2000) // 延迟2秒执行
})
</script>
```

### 2. 在设置页面添加手动检查更新

```vue
<template>
  <view class="settings-page">
    <wd-cell-group>
      <wd-cell title="检查更新" :value="currentVersion" is-link @click="handleCheckUpdate" />
    </wd-cell-group>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const currentVersion = ref('1.0.0')

// 获取当前版本
const getCurrentVersion = () => {
  const systemInfo = uni.getSystemInfoSync()
  currentVersion.value = systemInfo.appVersion || '1.0.0'
}

// 手动检查更新
const handleCheckUpdate = async () => {
  try {
    uni.showLoading({ title: '检查更新中...' })

    const { appUpdater } = await import('@/uni_modules/uni-upgrade-center-app/utils/app-updater')

    const updateInfo = await appUpdater.checkUpdate()
    uni.hideLoading()

    if (updateInfo.code === 0) {
      uni.showToast({
        title: '当前已是最新版本',
        icon: 'none',
      })
    } else {
      // 显示更新弹窗
      await appUpdater.showUpdateDialog(updateInfo)
    }
  } catch (error) {
    uni.hideLoading()
    console.error('检查更新失败:', error)
    uni.showToast({
      title: '检查更新失败',
      icon: 'none',
    })
  }
}

// 页面加载时获取当前版本
getCurrentVersion()
</script>
```

### 3. 测试更新功能

```typescript
// 在控制台中测试
import {
  runAllTests,
  testSpecificIssue,
} from '@/uni_modules/uni-upgrade-center-app/utils/test-update'

// 运行完整测试
runAllTests()

// 专门测试四段式版本号问题
testSpecificIssue()
```

#### 测试函数说明

- `runAllTests()`: 运行所有测试，包括版本比较和更新检查
- `testSpecificIssue()`: 专门测试当前版本 1.0.0.1007 vs 远程版本 1.0.0.1006 的场景
- `testVersionCompare()`: 测试各种版本号比较场景
- `testAppUpdate()`: 测试完整的更新流程

## 🔍 调试说明

### 查看详细日志

更新系统会输出详细的日志信息，包括：

1. 当前应用信息获取过程
2. OSS配置请求和响应
3. 版本比较结果
4. 更新决策过程

### 常见问题排查

#### 1. 版本信息获取失败

- 检查应用是否在真机或模拟器中运行
- 查看控制台中的 `widgetInfo` 和系统版本信息日志
- 确认 `manifest.json` 中的版本配置正确

#### 2. 网络请求失败

- 检查OSS文件URL是否可访问
- 确认网络连接正常
- 查看请求参数和响应日志

#### 3. JSON解析错误

- 确认OSS文件格式正确
- 检查是否有多余的字符或格式错误
- 使用在线JSON验证工具验证格式

## 📝 配置说明

### config.ts 文件

```typescript
// OSS 配置文件URL
export const UPDATE_CONFIG_URL =
  'https://saas-sy-miniprogram-1317635756.cos.ap-guangzhou.myqcloud.com/haidilao/config/prod/APP/update.json'

// 缓存时间（30分钟）
export const UPDATE_CONFIG_CACHE_TIME = 30 * 60 * 1000

// 缓存键名
export const UPDATE_CONFIG_CACHE_KEY = '__upgrade_config_cache__'
```

### 版本比较规则

- 支持标准的三段式版本号：`1.0.0`
- 自动处理四段式版本号：`1.0.0.1007` → `1.0.0`
- 版本比较使用语义化版本规则

### 更新策略

1. **整包更新**：下载完整APK/IPA文件
2. **热更新**：下载WGT包（仅Android支持）
3. **强制更新**：用户无法取消的更新
4. **静默更新**：WGT包静默安装（仅Android）

## 🔄 更新流程

1. **检查更新**：从OSS获取最新配置
2. **版本比较**：对比当前版本和最新版本
3. **显示弹窗**：提示用户是否更新
4. **下载文件**：显示进度条下载安装包
5. **安装更新**：自动安装或提示用户安装

## ⚠️ 注意事项

1. **测试环境**：请在真机上测试更新功能
2. **网络环境**：确保目标用户网络可访问OSS文件
3. **权限设置**：Android需要安装未知来源应用权限
4. **版本控制**：建议使用标准的三段式版本号

## 🎉 完成！

现在您的应用已经支持：

- ✅ 自动检查更新
- ✅ 强制更新
- ✅ 热更新（WGT包）
- ✅ 下载进度显示
- ✅ 详细的错误处理和日志
