# uni-upgrade-center-app

基于 OSS 配置文件的 uni-app 应用升级中心，无需依赖 uniCloud 云函数。

## 功能特性

- ✅ **无云函数依赖**：直接从 OSS 读取升级配置，无需搭建后端服务
- ✅ **强制更新支持**：支持强制更新，用户无法取消
- ✅ **热更新支持**：支持 wgt 热更新包，更新速度更快
- ✅ **静默更新**：支持 wgt 包静默更新（仅 Android）
- ✅ **进度提示**：下载过程中显示进度条和通知
- ✅ **平台适配**：支持 Android、iOS、uni-app x 多平台
- ✅ **缓存机制**：配置文件本地缓存，减少网络请求

## 配置说明

### 1. OSS 配置文件格式

在您的 OSS 存储上创建一个 JSON 配置文件，格式如下：

```json
{
  "code": 0,
  "message": "获取成功",
  "appid": "__UNI__XXXXXX",
  "name": "海底捞外送商家端",
  "version": "1.0.2",
  "versionCode": 102,
  "forceUpdate": false,
  "silent": false,
  "platform": ["Android"],
  "title": "发现新版本",
  "contents": "1. 优化用户体验；\\n2. 修复了一些已知问题；\\n3. 增加了新功能。",
  "url": "https://your-oss.com/path/to/your-app-1.0.2.apk",
  "wgtUrl": "https://your-oss.com/path/to/your-app.wgt",
  "minVersion": "1.0.0"
}
```

### 2. 配置字段说明

| 字段        | 类型     | 必填 | 说明                          |
| ----------- | -------- | ---- | ----------------------------- |
| code        | number   | ✅   | 状态码，0=成功                |
| message     | string   | ✅   | 响应消息                      |
| appid       | string   | ✅   | 应用 ID                       |
| name        | string   | ✅   | 应用名称                      |
| version     | string   | ✅   | 最新版本号（如：1.0.2）       |
| versionCode | number   | ✅   | 最新版本代码（如：102）       |
| forceUpdate | boolean  | ✅   | 是否强制更新                  |
| silent      | boolean  | ✅   | 是否静默更新（仅对 wgt 有效） |
| platform    | string[] | ✅   | 支持的平台                    |
| title       | string   | ✅   | 更新弹窗标题                  |
| contents    | string   | ✅   | 更新内容说明                  |
| url         | string   | ✅   | 完整安装包下载地址            |
| wgtUrl      | string   | ❌   | wgt 热更新包下载地址          |
| minVersion  | string   | ❌   | 支持 wgt 热更新的最低版本     |

### 3. 修改配置文件

编辑 `src/uni_modules/uni-upgrade-center-app/utils/config.ts`：

```typescript
// OSS 上的升级配置文件地址
export const UPDATE_CONFIG_URL = 'https://your-oss.com/path/to/update.json'
```

## 使用方法

### 方式一：便捷函数（推荐）

在 `App.vue` 中使用：

```vue
<script setup lang="ts">
import { onLaunch } from '@dcloudio/uni-app'
import { checkAndHandleUpdate } from '@/uni_modules/uni-upgrade-center-app/utils/app-updater'

onLaunch(() => {
  // 应用启动时检查更新
  setTimeout(() => {
    checkAndHandleUpdate()
  }, 1000)
})
</script>
```

### 方式二：详细控制

```vue
<script setup lang="ts">
import { ref } from 'vue'
import { appUpdater } from '@/uni_modules/uni-upgrade-center-app/utils/app-updater'

const updateProgress = ref(0)
const isUpdating = ref(false)

// 手动检查更新
const checkForUpdate = async () => {
  try {
    const updateInfo = await appUpdater.checkUpdate()

    if (updateInfo.code === 0) {
      uni.showToast({ title: '已是最新版本', icon: 'success' })
      return
    }

    // 显示更新弹窗
    const shouldUpdate = await appUpdater.showUpdateDialog(updateInfo)

    if (shouldUpdate) {
      isUpdating.value = true

      await appUpdater.performUpdate(updateInfo, {
        onProgress: (progress) => {
          updateProgress.value = progress.progress
        },
        onSuccess: () => {
          isUpdating.value = false
          uni.showToast({ title: '更新成功', icon: 'success' })
        },
        onFail: (error) => {
          isUpdating.value = false
          uni.showToast({ title: '更新失败', icon: 'error' })
        },
      })
    }
  } catch (error) {
    uni.showToast({ title: '检查更新失败', icon: 'error' })
  }
}
</script>

<template>
  <view>
    <button @click="checkForUpdate">检查更新</button>
    <view v-if="isUpdating">更新进度: {{ updateProgress }}%</view>
  </view>
</template>
```

### 方式三：原生升级弹窗

保持原有的升级弹窗使用方式：

```vue
<script setup lang="ts">
import checkUpdate from '@/uni_modules/uni-upgrade-center-app/utils/check-update'

onLaunch(() => {
  setTimeout(() => {
    checkUpdate()
  }, 1000)
})
</script>
```

## API 文档

### AppUpdater 类

#### 方法

- `checkUpdate()`: 检查更新，返回更新信息
- `showUpdateDialog(updateInfo)`: 显示更新弹窗
- `performUpdate(updateInfo, callbacks)`: 执行更新
- `cancelDownload()`: 取消下载
- `silentUpdate(updateInfo)`: 静默更新（仅 wgt）

#### 属性

- `downloading`: 是否正在下载

### 便捷函数

#### checkAndHandleUpdate(autoDownload)

自动检查并处理更新流程。

参数：

- `autoDownload` (boolean): 是否自动下载，默认 false

## 更新策略

### 1. 版本比较

系统会自动比较 OSS 配置中的 `version` 字段与当前应用版本，判断是否需要更新。

### 2. 热更新优先

如果满足以下条件，优先使用 wgt 热更新：

- 配置了 `wgtUrl` 和 `minVersion`
- 当前版本 >= `minVersion`
- 平台支持热更新

### 3. 静默更新

当 `silent: true` 且使用 wgt 更新时，会在后台静默下载并安装，下次启动生效。

### 4. 强制更新

当 `forceUpdate: true` 时，用户无法取消更新弹窗，必须完成更新。

## 平台支持

| 平台      | 整包更新 | 热更新 | 静默更新 | 进度通知 |
| --------- | -------- | ------ | -------- | -------- |
| Android   | ✅       | ✅     | ✅       | ✅       |
| iOS       | ✅       | ❌     | ❌       | ❌       |
| uni-app x | ✅       | ❌     | ❌       | ❌       |

## 注意事项

1. **iOS 更新**：iOS 平台会直接跳转到 App Store，无法进行应用内更新
2. **热更新限制**：只有 Android 平台支持 wgt 热更新
3. **网络要求**：确保设备能正常访问您的 OSS 存储
4. **版本号格式**：建议使用标准的版本号格式（如：1.0.0）
5. **缓存策略**：配置文件会缓存 30 分钟，可在 `config.ts` 中修改

## 更新日志

### v2.0.0 (基于 OSS)

- 🎉 移除 uniCloud 依赖，改为基于 OSS 配置文件
- 🎉 新增版本号智能比较
- 🎉 新增配置文件缓存机制
- 🎉 重构下载管理器
- 🎉 完善错误处理和用户反馈

### v1.x.x (原版本)

- 基于 uniCloud 云函数的升级中心

## 许可证

MIT License
