<route lang="json5">
{
  style: {
    navigationBarTitleText: '表格组件演示',
  },
}
</route>

<template>
  <view class="page-container p-32rpx">
    <view class="section mb-40rpx">
      <view class="section-title mb-20rpx">横向滚动 + 固定表头</view>
      <sy-table
        :data="tableData"
        :columns="wideColumns"
        :border="true"
        :fixed-header="true"
        :height="300"
        :scrollable="true"
      />
    </view>

    <view class="section mb-40rpx">
      <view class="section-title mb-20rpx">边框对齐测试（横向滚动 + 固定表头）</view>
      <sy-table
        :data="tableData"
        :columns="manyColumnsForBorderTest"
        :border="true"
        :fixed-header="true"
        :height="800"
        :scrollable="true"
      />
      <view class="text-desc mt-10rpx">该示例测试横向滚动+固定表头时的边框对齐问题</view>
    </view>

    <view class="section mb-40rpx">
      <view class="section-title mb-20rpx">行选择</view>
      <sy-table
        ref="selectTableRef"
        :data="tableData"
        :columns="basicColumns"
        :border="true"
        :selectable="true"
        @selection-change="handleSelectionChange"
      />
      <view class="flex gap-20rpx mt-20rpx">
        <wd-button size="small" @click="getSelectedRows">获取选中行</wd-button>
        <wd-button size="small" @click="clearSelection">清除选中</wd-button>
      </view>
    </view>

    <view class="section mb-40rpx">
      <view class="section-title mb-20rpx">自定义渲染</view>
      <sy-table :data="tableData" :columns="customColumns" :border="true" />
    </view>

    <view class="section mb-40rpx">
      <view class="section-title mb-20rpx">文本处理</view>
      <sy-table :data="tableData" :columns="textColumns" :border="true" />
    </view>

    <view class="section mb-40rpx">
      <view class="section-title mb-20rpx">样式配置</view>
      <sy-table
        :data="tableData"
        :columns="styleColumns"
        :border="true"
        border-color="#e0e0e0"
        header-height="60"
        header-font-size="28"
        header-bg-color="#f5f5f5"
        cell-font-size="26"
        cell-min-height="60"
      />
    </view>

    <view class="mb-32rpx">
      <text class="text-32rpx font-bold">文本换行和省略示例</text>
    </view>

    <view class="mb-32rpx">
      <sy-table :data="longTextData" :columns="ellipsisColumns" :border="true" :scrollable="true" />
    </view>

    <view class="section mb-40rpx">
      <view class="section-title mb-20rpx">无宽度列平分视图</view>
      <sy-table :data="tableData" :columns="autoWidthColumns" border />
    </view>

    <!-- 固定表头竖向滚动场景示例 -->
    <view class="mb-32rpx mt-40rpx">
      <text class="text-32rpx font-bold">固定表头竖向滚动场景示例</text>
    </view>

    <view class="section mb-40rpx">
      <view class="section-title mb-20rpx">场景1：通过设置最大可见行数实现固定表头竖向滚动</view>
      <sy-table
        :data="manyRowsData"
        :columns="basicColumns"
        :border="true"
        :max-visible-rows="10"
        fixed-header-mode="rows"
      />
      <view class="text-desc mt-10rpx">
        该示例使用行数模式(fixed-header-mode="rows")，设置最大可见行数为5行，当数据超过5行时自动启用固定表头竖向滚动
      </view>
    </view>

    <view class="section mb-40rpx">
      <view class="section-title mb-20rpx">场景2：通过设置高度实现固定表头竖向滚动</view>
      <sy-table
        :data="manyRowsData"
        :columns="basicColumns"
        :border="true"
        :height="300"
        fixed-header-mode="height"
      />
      <view class="text-desc mt-10rpx">
        该示例使用高度模式(fixed-header-mode="height")，设置表格高度为300rpx，当实际内容高度超过300rpx时自动启用固定表头竖向滚动
      </view>
    </view>

    <view class="section mb-40rpx">
      <view class="section-title mb-20rpx">
        场景3：行数模式 - 数据少于最大可见行数（不需要固定表头）
      </view>
      <sy-table
        :data="fewRowsData"
        :columns="basicColumns"
        :border="true"
        :max-visible-rows="4"
        fixed-header-mode="rows"
      />
      <view class="text-desc mt-10rpx">
        该示例使用行数模式(fixed-header-mode="rows")，设置最大可见行数为5行，但数据只有3行，因此不需要固定表头竖向滚动
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import SyTable from '@/components/sy-table'
import type { TableColumn, TableData, SortInfo } from '@/components/sy-table'

// 表格数据
const tableData = ref([
  {
    id: '1',
    name: '张三',
    age: 25,
    address: '北京市朝阳区',
    phone: '13800138000',
    email: '<EMAIL>',
    department: '技术部',
    position: '前端开发',
    status: 'active',
    createTime: '2023-01-15',
    salary: 15000,
    description:
      '这是一段很长的文本，用于测试单元格内容的省略和换行效果。这是一段很长的文本，用于测试单元格内容的省略和换行效果。',
  },
  {
    id: '2',
    name: '李四',
    age: 30,
    address: '上海市浦东新区',
    phone: '13900139000',
    email: '<EMAIL>',
    department: '产品部',
    position: '产品经理',
    status: 'inactive',
    createTime: '2023-02-20',
    salary: 18000,
    description:
      '这是一段很长的文本，用于测试单元格内容的省略和换行效果。这是一段很长的文本，用于测试单元格内容的省略和换行效果。',
  },
  {
    id: '3',
    name: '王五',
    age: 28,
    address: '广州市天河区',
    phone: '13700137000',
    email: '<EMAIL>',
    department: '市场部',
    position: '市场专员',
    status: 'active',
    createTime: '2023-03-10',
    salary: 12000,
    description:
      '这是一段很长的文本，用于测试单元格内容的省略和换行效果。这是一段很长的文本，用于测试单元格内容的省略和换行效果。',
  },
  {
    id: '4',
    name: '赵六',
    age: 35,
    address: '深圳市南山区',
    phone: '13600136000',
    email: '<EMAIL>',
    department: '销售部',
    position: '销售经理',
    status: 'active',
    createTime: '2023-04-05',
    salary: 20000,
    description:
      '这是一段很长的文本，用于测试单元格内容的省略和换行效果。这是一段很长的文本，用于测试单元格内容的省略和换行效果。',
  },
  {
    id: '5',
    name: '钱七',
    age: 26,
    address: '杭州市西湖区',
    phone: '13500135000',
    email: '<EMAIL>',
    department: '技术部',
    position: '后端开发',
    status: 'inactive',
    createTime: '2023-05-15',
    salary: 16000,
    description:
      '这是一段很长的文本，用于测试单元格内容的省略和换行效果。这是一段很长的文本，用于测试单元格内容的省略和换行效果。',
  },
])

// 生成更多行数据用于测试固定表头竖向滚动
const manyRowsData = ref<TableData[]>([])
for (let i = 1; i <= 20; i++) {
  manyRowsData.value.push({
    id: i.toString(),
    name: `用户${i}`,
    age: 20 + i,
    address: `测试地址${i}`,
    phone: `1380013${i.toString().padStart(4, '0')}`,
    email: `user${i}@example.com`,
    department: i % 3 === 0 ? '技术部' : i % 3 === 1 ? '产品部' : '市场部',
    position: i % 4 === 0 ? '经理' : '员工',
    status: i % 2 === 0 ? 'active' : 'inactive',
    createTime: `2023-${((i % 12) + 1).toString().padStart(2, '0')}-${((i % 28) + 1).toString().padStart(2, '0')}`,
    salary: 10000 + i * 1000,
    description: `这是第${i}行的描述文本，用于测试单元格内容的省略和换行效果。`,
  })
}

// 少量行数据，用于测试行数模式下不需要固定表头的情况
const fewRowsData = ref<TableData[]>(manyRowsData.value.slice(0, 3))

// 测试固定表头模式
const testFixedHeaderMode = (mode: string) => {
  uni.showToast({
    title: `当前使用${mode}模式`,
    icon: 'none',
  })
}

// 基础列配置
const basicColumns = ref<TableColumn[]>([
  {
    prop: 'name',
    label: '姓名',
    // width: 120,
  },
  {
    prop: 'age',
    label: '年龄',
    // width: 80,
    sortable: true,
  },
  {
    prop: 'address',
    label: '地址',
    // width: 300,
  },
])

// 宽列配置（用于横向滚动示例）
const wideColumns = ref<TableColumn[]>([
  {
    prop: 'name',
    label: '姓名',
    width: 120,
  },
  {
    prop: 'age',
    label: '年龄',
    width: 160,
  },
  {
    prop: 'address',
    label: '地址',
    width: 300,
  },
  {
    prop: 'phone',
    label: '电话',
    width: 180,
  },
  {
    prop: 'email',
    label: '邮箱',
    width: 250,
  },
  {
    prop: 'department',
    label: '部门',
    width: 150,
  },
  {
    prop: 'position',
    label: '职位',
    width: 150,
  },
  {
    prop: 'createTime',
    label: '入职时间',
    width: 180,
  },
  {
    prop: 'salary',
    label: '薪资',
    width: 150,
    align: 'right',
    formatter: (row, column, cellValue) => {
      return `¥${cellValue}`
    },
  },
])

// 自定义渲染列配置
const customColumns = ref<TableColumn[]>([
  {
    prop: 'name',
    label: '姓名',
    width: 120,
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    formatter: (row, column, cellValue) => {
      return cellValue === 'active' ? '在职' : '离职'
    },
  },
  {
    prop: 'salary',
    label: '薪资',
    // width: 120,
    align: 'right',
    formatter: (row, column, cellValue) => {
      return `¥${cellValue}`
    },
  },
  {
    prop: 'operation',
    label: '操作',
    width: 200,
    renderCell: (row) => {
      return h('view', { class: 'flex gap-10rpx' }, [
        h(
          'wd-button',
          {
            size: 'small',
            type: 'primary',
            onClick: (e: Event) => {
              e.stopPropagation()
              handleEdit(row)
            },
          },
          '编辑',
        ),
        h(
          'wd-button',
          {
            size: 'small',
            type: 'danger',
            onClick: (e: Event) => {
              e.stopPropagation()
              handleDelete(row)
            },
          },
          '删除',
        ),
      ])
    },
  },
])

// 文本处理列配置
const textColumns = ref<TableColumn[]>([
  {
    prop: 'name',
    label: '姓名',
    width: 120,
  },
  {
    prop: 'description',
    label: '单行省略',
    width: 200,
    ellipsis: true,
  },
  {
    prop: 'description',
    label: '两行省略',
    width: 200,
    ellipsis: 2,
  },
  {
    prop: 'description',
    label: '文本换行',
    width: 200,
    wrapText: true,
  },
])

// 表格引用
const selectTableRef = ref()

// 选中行
const selectedRows = ref<TableData[]>([])

// 事件处理函数
const handleRowClick = (row: TableData, index: number) => {
  uni.showToast({
    title: `点击了 ${row.name}`,
    icon: 'none',
  })
}

const handleSortChange = (sortInfo: SortInfo) => {
  console.log('排序变化', sortInfo)
  uni.showToast({
    title: `${sortInfo.prop} ${sortInfo.order || '默认'}排序`,
    icon: 'none',
  })
}

const handleSelectionChange = (selection: TableData[]) => {
  selectedRows.value = selection
  console.log('选中的行:', selection)
}

const getSelectedRows = () => {
  uni.showToast({
    title: `选中了 ${selectedRows.value.length} 行`,
    icon: 'none',
  })
}

const clearSelection = () => {
  selectTableRef.value?.clearSelection()
}

const handleEdit = (row: TableData) => {
  uni.showToast({
    title: `编辑 ${row.name}`,
    icon: 'none',
  })
}

const handleDelete = (row: TableData) => {
  uni.showModal({
    title: '提示',
    content: `确定要删除 ${row.name} 吗？`,
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: `已删除 ${row.name}`,
          icon: 'none',
        })
      }
    },
  })
}

// 在 script 部分添加样式配置相关的列配置
const styleColumns = ref<TableColumn[]>([
  {
    prop: 'name',
    label: '姓名',
    width: 120,
    headerColor: '#1890ff',
    headerFontWeight: 600,
    cellColor: '#333333',
  },
  {
    prop: 'age',
    label: '年龄',
    width: 100,
    align: 'center',
    headerBgColor: '#e6f7ff',
    cellFontWeight: 'bold',
  },
  {
    prop: 'address',
    label: '地址',
    width: 200,
    cellFontSize: 24,
    cellColor: '#666666',
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    align: 'center',
    renderCell: (row) => {
      const isAdult = row.age > 25
      return h(
        'view',
        {
          style: {
            color: isAdult ? '#52c41a' : '#ff4d4f',
            fontWeight: 'bold',
            padding: '4rpx 12rpx',
            borderRadius: '4rpx',
            backgroundColor: isAdult ? 'rgba(82, 196, 26, 0.1)' : 'rgba(255, 77, 79, 0.1)',
            display: 'inline-block',
          },
        },
        isAdult ? '成年' : '未成年',
      )
    },
  },
])

// 对齐方式示例的列配置
const alignColumns = ref([
  {
    prop: 'id',
    label: 'ID',
    width: 80,
    align: 'center',
    headerFontSize: 32,
    headerColor: '#222222',
  },
  {
    prop: 'name',
    label: '姓名',
    width: 120,
    align: 'left',
  },
  {
    prop: 'age',
    label: '年龄',
    width: 80,
    align: 'center',
  },
  {
    prop: 'address',
    label: '地址',
    align: 'right',
  },
])

// 长文本数据
const longTextData = ref([
  {
    id: 1,
    title: '这是一个很长的标题，需要换行显示或者省略',
    description:
      '这是一段很长的描述文本，用于测试文本换行和省略功能。这是一段很长的描述文本，用于测试文本换行和省略功能。',
    date: '2023-06-01',
  },
  {
    id: 2,
    title: '另一个长标题示例，测试省略号功能',
    description:
      '这是第二段很长的描述文本，同样用于测试文本换行和省略功能。这是第二段很长的描述文本，同样用于测试文本换行和省略功能。',
    date: '2023-06-02',
  },
])

// 文本换行和省略示例的列配置
const ellipsisColumns = ref([
  { prop: 'id', label: 'ID', width: 80 },
  {
    prop: 'title',
    label: '标题(单行省略)',
    width: 200,
    ellipsis: true, // 单行省略
  },
  {
    prop: 'description',
    label: '描述(两行省略)',
    width: 300,
    ellipsis: 2, // 显示两行，超出省略
  },
  {
    prop: 'description',
    label: '描述(允许换行)',
    width: 300,
    wrapText: true, // 允许文本换行
    ellipsis: false, // 不省略
  },
  { prop: 'date', label: '日期', width: 120 },
])

// 边框对齐测试的列配置
const manyColumnsForBorderTest = ref<TableColumn[]>([
  {
    prop: 'name',
    label: '姓名',
    width: 120,
  },
  {
    prop: 'age',
    label: '年龄',
    width: 160,
  },
  {
    prop: 'address',
    label: '地址',
    width: 300,
  },
  {
    prop: 'phone',
    label: '电话',
    width: 180,
  },
  {
    prop: 'email',
    label: '邮箱',
    width: 250,
  },
  {
    prop: 'department',
    label: '部门',
    width: 150,
  },
  {
    prop: 'position',
    label: '职位',
    width: 150,
  },
  {
    prop: 'createTime',
    label: '入职时间',
    width: 180,
  },
  {
    prop: 'salary',
    label: '薪资',
    width: 150,
    align: 'right',
    formatter: (row, column, cellValue) => {
      return `¥${cellValue}`
    },
  },
])

// 无宽度列配置 - 测试自动平分
const autoWidthColumns = ref<TableColumn[]>([
  { prop: 'name', label: '姓名' },
  { prop: 'age', label: '年龄' },
  { prop: 'address', label: '地址' },
])
</script>

<style lang="scss" scoped>
.page-container {
  height: 90vh;
  padding-bottom: env(safe-area-inset-bottom);
  overflow: scroll;
}

.section {
  padding: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.text-desc {
  font-size: 24rpx;
  color: #666;
}
</style>
