/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/index/index" |
       "/pages/about/about" |
       "/pages/damageAppeal/index" |
       "/pages/dishManagement/index" |
       "/pages/general/index" |
       "/pages/operationGuide/index" |
       "/pages/preOrder/index" |
       "/pages/product/index" |
       "/pages/review/index" |
       "/pages/storeDetail/index" |
       "/pages/damageAppeal/appealRule/index" |
       "/pages/general/messageRing/index" |
       "/pages/general/orderNotify/index" |
       "/pages/general/ringPreview/index" |
       "/pages/general/sysSetting/index" |
       "/pages/login/detail/index" |
       "/pages/login/fixPassword/index" |
       "/pages/login/index/index" |
       "/pages/order/cancelOrder/index" |
       "/pages/order/search/index" |
       "/pages/shop/list/index" |
       "/settings/pages/privacy/index" |
       "/uni_modules/uni-upgrade-center-app/pages/upgrade-popup";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/index/index" | "/pages/dishManagement/index" | "/pages/review/index" | "/pages/storeDetail/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
