export interface StoreTab {
  key: string
  label: string
}

export interface StoreEntry {
  key: string
  label: string
  icon: string
}

export interface StoreInfo {
  id: string
  name: string
  address: string
  phone: string[]
  openTime: string[]
  // 评分相关字段
  rating?: number | null // 总体评分
  taste?: number | null // 口味评分
  packaging?: number | null // 包装评分
  deliveryRate?: string | null // 配送满意度
  hideRateing?: boolean | false

  avgScore?: number | null // 总体评分
  avgTasteScore?: number | null // 口味评分
  avgPackingScore?: number | null // 包装评分
  // 其他业务字段
  sales?: number
  rate?: string
  businessType?: string
  category?: string
  status?: string

  imageUrl: string
  businessCategory: string
  businessStatus?: number | null
  promotionInfo?: string
}
