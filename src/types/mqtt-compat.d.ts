/**
 * MQTT 兼容性处理相关类型定义
 */

declare global {
  namespace UniApp {
    interface ConnectSocketOption {
      url: string
      data?: any
      header?: object
      protocols?: string[]
      method?: string
      success?: (result: any) => void
      fail?: (result: any) => void
      complete?: (result: any) => void
    }

    interface SocketTask {
      send(option: {
        data: string | ArrayBuffer
        success?: (result: any) => void
        fail?: (result: any) => void
        complete?: (result: any) => void
      }): void
      close(option?: {
        code?: number
        reason?: string
        success?: (result: any) => void
        fail?: (result: any) => void
        complete?: (result: any) => void
      }): void
      onOpen(callback: (result: any) => void): void
      onClose(callback: (result: any) => void): void
      onError(callback: (result: any) => void): void
      onMessage(callback: (result: any) => void): void
    }
  }
}

export {}
