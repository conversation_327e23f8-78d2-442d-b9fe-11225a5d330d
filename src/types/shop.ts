export interface ShopDto {
  id: string
  name: string
  address: string
  addressLevel: string
  addressLevelName: string | null
  alias: string | null
  cityCode: string
  cityName: string | null
  countyCode: string
  countyName: string | null
  companyId: string | null
  companyName: string | null
  createPerson: string
  createTime: string
  extFields: Record<string, any>
  tenantId: string
  shopDtoList?: ShopDto[]
  raw: {
    tenantId: string
    shopDtoList?: ShopDto[]
  }
}
