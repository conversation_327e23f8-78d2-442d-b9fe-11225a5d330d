export interface StoreInfo {
  address: string // 门店地址
  avgDeliveryScore: number // 配送评分
  avgPackingScore: number // 包装评分
  avgScore: number // 商家评分
  avgTasteScore: number // 口味评分
  businessCategory: string // 经营品类
  businessStatus: number // 营业状态 1-营业中 0-未营业
  businessTime: string // 营业时间,多个用,分隔
  deliveryRangeJson?: string // 配送范围JSON
  imageUrl: string // 门店头像
  phones: string[] // 门店电话
  promotionInfo: string // 门店公告

  // 以下为兼容旧代码的属性
  name?: string
  rating?: number // 与 avgScore 对应
  taste?: number // 与 avgTasteScore 对应
  packaging?: number // 与 avgPackingScore 对应
  deliveryRate?: string // 配送率
  sales?: number // 销售量
  rate?: string // 评分率
  businessType?: string // 业务类型
  category?: string // 与 businessCategory 对应
  status?: string // 与 businessStatus 对应的文字描述
  phone?: string[] // 与 phones 对应
  openTime?: string[] // 与 businessTime 对应的格式化数组
}

export interface StoreTab {
  key: string
  label: string
  [key: string]: any // 允许存储其他属性
}

export interface StoreEntry {
  key: string
  label: string
  icon: string
}
