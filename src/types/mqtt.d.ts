declare module 'mqtt/dist/mqtt' {
  export interface MqttClient {
    connected: boolean
    on(event: 'connect', cb: () => void): this
    on(event: 'reconnect', cb: () => void): this
    on(event: 'close', cb: () => void): this
    on(event: 'error', cb: (error: Error) => void): this
    on(event: 'message', cb: (topic: string, payload: Buffer | string) => void): this
    subscribe(topic: string, cb?: (error?: Error) => void): this
    unsubscribe(topic: string, cb?: (error?: Error) => void): this
    end(): void
  }

  export interface ConnectOptions {
    clientId?: string
    username?: string
    password?: string
    keepalive?: number
    connectTimeout?: number
    clean?: boolean
    cleanSession?: boolean
    reconnectPeriod?: number
  }

  export function connect(url: string, options?: ConnectOptions): MqttClient
}

// 为了兼容性，也声明一下默认导出
declare module 'mqtt' {
  export * from 'mqtt/dist/mqtt'
}
